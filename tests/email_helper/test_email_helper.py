from unittest.mock import patch

import pytest

from common.types import UploadFileStatus
from email_helper.email_helper import build_subject_and_body  # type: ignore


class TestEmailHelper:
    @pytest.fixture
    def email_values(self):
        return {
            "process_name": "Test Process",
            "process_id": "12345",
            "date_str": "2023-01-01",
            "time_str": "12:00 PM",
            "result_link": "https://example.com/results/12345",
            "message": "Error occurred during processing",
        }

    def test_build_subject_and_body_pending(self, email_values):
        status = UploadFileStatus.PENDING.value
        subject, body = build_subject_and_body(email_values, status)

        assert "Background Process Test Process Started" == subject
        assert "Dear User" in body
        assert "Test Process" in body
        assert "2023-01-01" in body
        assert "12:00 PM" in body
        assert "12345" in body
        assert "https://example.com/results/12345" in body

    def test_build_subject_and_body_completed(self, email_values):
        status = UploadFileStatus.COMPLETED.value
        subject, body = build_subject_and_body(email_values, status)

        assert "Background Process Test Process Completed" == subject
        assert "Dear User" in body
        assert "successfully completed" in body
        assert "Test Process" in body
        assert "2023-01-01" in body
        assert "12:00 PM" in body
        assert "12345" in body
        assert "https://example.com/results/12345" in body

    def test_build_subject_and_body_failed(self, email_values):
        status = UploadFileStatus.FAILED.value
        subject, body = build_subject_and_body(email_values, status)

        assert "Background Process Test Process Failed to be Completed" == subject
        assert "Dear User" in body
        assert "encountered an issue" in body
        assert "Test Process" in body
        assert "2023-01-01" in body
        assert "12:00 PM" in body
        assert "12345" in body
        assert "https://example.com/results/12345" in body
        assert "Error occurred during processing" in body

    def test_build_subject_and_body_partially(self, email_values):
        status = UploadFileStatus.PARTIALLY.value
        subject, body = build_subject_and_body(email_values, status)

        assert "Background Process Test Process Partially Completed" == subject
        assert "Dear User" in body
        assert "partially completed" in body
        assert "Test Process" in body
        assert "2023-01-01" in body
        assert "12:00 PM" in body
        assert "12345" in body
        assert "https://example.com/results/12345" in body

    def test_build_subject_and_body_invalid_status(self, email_values):
        with pytest.raises(ValueError) as exc_info:
            build_subject_and_body(email_values, "INVALID_STATUS")

        assert "not define in body_map/subject_map" in str(exc_info.value)

    @patch("email_helper.email_helper.logger")
    def test_build_subject_and_body_missing_value(self, mock_logger, email_values):
        incomplete_values = {
            k: v for k, v in email_values.items() if k != "process_name"
        }

        with pytest.raises(ValueError):
            build_subject_and_body(incomplete_values, UploadFileStatus.PENDING.value)
