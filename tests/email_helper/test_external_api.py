from unittest.mock import MagicMock, patch

import pytest
from fastapi import status
from platform_api_client import PlatformAPIError

from auth.exceptions import ForbiddenError, Unauthorized
from email_helper.adapters.external_api import (  # type: ignore
    FakeMailServiceAPI,
    MailServiceAPI,
)
from email_helper.exceptions import MailAPIError  # type: ignore


class TestMailServiceAPI:
    @pytest.fixture
    def api_client_mock(self):
        return MagicMock()

    @pytest.fixture
    def mail_service(self, api_client_mock):
        return MailServiceAPI(api_client_mock)

    @pytest.fixture
    def email_data(self):
        return {
            "subject": "Test Subject",
            "name_from": "Test Sender",
            "recipients": ["<EMAIL>"],
            "html_body": "<p>Test email body</p>",
        }

    def test_send_mail_success(self, mail_service, api_client_mock, email_data):
        mock_response = MagicMock()
        mock_response.status_code = 202
        mock_response.json.return_value = {"status": "sent"}
        api_client_mock.post.return_value = mock_response

        result = mail_service.send_mail(**email_data)

        assert result == {"status": "sent"}
        api_client_mock.post.assert_called_once()

    def test_send_mail_api_error(self, mail_service, api_client_mock, email_data):
        mock_response = MagicMock()
        mock_response.status_code = 400
        api_client_mock.post.return_value = mock_response

        with pytest.raises(MailAPIError):
            mail_service.send_mail(**email_data)

    def test_send_mail_unauthorized(self, mail_service, api_client_mock, email_data):
        api_client_mock.post.side_effect = PlatformAPIError(
            status_code=status.HTTP_401_UNAUTHORIZED, message="Unauthorized"
        )

        with pytest.raises(Unauthorized):
            mail_service.send_mail(**email_data)

    def test_send_mail_forbidden(self, mail_service, api_client_mock, email_data):
        api_client_mock.post.side_effect = PlatformAPIError(
            status_code=status.HTTP_403_FORBIDDEN, message="Forbidden"
        )

        with pytest.raises(ForbiddenError):
            mail_service.send_mail(**email_data)

    def test_send_mail_connection_error(
        self, mail_service, api_client_mock, email_data
    ):
        api_client_mock.post.side_effect = ConnectionError("Connection failed")

        with pytest.raises(ConnectionError, match="Failed to connect to the Mail API"):
            mail_service.send_mail(**email_data)


class TestFakeMailServiceAPI:
    @pytest.fixture
    def fake_mail_service(self):
        return FakeMailServiceAPI()

    @pytest.fixture
    def email_data(self):
        return {
            "subject": "Test Subject",
            "name_from": "Test Sender",
            "recipients": ["<EMAIL>"],
            "html_body": "<p>Test email body</p>",
        }

    @patch("email_helper.adapters.external_api.logger")
    def test_fake_send_mail(self, mock_logger, fake_mail_service, email_data):
        result = fake_mail_service.send_mail(**email_data)

        assert result == {"status": "fake_sent", "recipients": email_data["recipients"]}

        mock_logger.info.assert_any_call("[FAKE EMAIL SENT]")
        mock_logger.info.assert_any_call(f"Subject: {email_data['subject']}")
        mock_logger.info.assert_any_call(f"From: {email_data['name_from']}")
        mock_logger.info.assert_any_call(f"To: {email_data['recipients']}")
        mock_logger.info.assert_any_call(f"HTML: {email_data['html_body']}")
