from unittest.mock import <PERSON><PERSON><PERSON>

import pytest
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from platform_api_client import <PERSON><PERSON>IError

from api.openapi import schemas
from api.openapi.endpoints import generate_access_token
from api.openapi.examples import GET_TOKEN_RESPONSE
from openapi.exceptions import APIError, NotFound, OpenAPIUnauthorized
from openapi.services import AbstractOpenApiAuthService


class TestOpenApi:
    def test_generate_access_token_success(self):
        openapi_service_mock = MagicMock(spec=AbstractOpenApiAuthService)
        openapi_service_mock.generate_access_token.return_value = schemas.TokenResponse(
            **GET_TOKEN_RESPONSE
        )
        expected_response = schemas.TokenResponse(**GET_TOKEN_RESPONSE)
        response = generate_access_token(openapi_auth_service=openapi_service_mock)
        assert response == expected_response
        assert isinstance(response, schemas.TokenResponse)

    def test_generate_access_token_unauthorized(self):
        openapi_service_mock = MagicMock(spec=AbstractOpenApiAuthService)
        openapi_service_mock.generate_access_token.side_effect = OpenAPIUnauthorized()
        with pytest.raises(HTTPException) as exc_info:
            generate_access_token(
                openapi_auth_service=openapi_service_mock,
            )
        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert exc_info.value.detail == "Invalid user credentials."

    def test_generate_access_token_internal_api_error(self):
        openapi_service_mock = MagicMock(spec=AbstractOpenApiAuthService)
        openapi_service_mock.generate_access_token.side_effect = APIError()
        with pytest.raises(HTTPException) as exc_info:
            generate_access_token(
                openapi_auth_service=openapi_service_mock,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "Internal API Bad request error."

    def test_generate_access_token_not_found(self):
        openapi_service_mock = MagicMock(spec=AbstractOpenApiAuthService)
        openapi_service_mock.generate_access_token.side_effect = NotFound()
        with pytest.raises(HTTPException) as exc_info:
            generate_access_token(
                openapi_auth_service=openapi_service_mock,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "Internal API Not Found Error."

    def test_generate_access_token_platform_api_error(self):
        openapi_service_mock = MagicMock(spec=AbstractOpenApiAuthService)
        openapi_service_mock.generate_access_token.side_effect = PlatformAPIError(
            message="External API error.", status_code=400
        )
        with pytest.raises(HTTPException) as exc_info:
            generate_access_token(
                openapi_auth_service=openapi_service_mock,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "External API error."

    def test_generate_access_token_value_error(self):
        openapi_service_mock = MagicMock(spec=AbstractOpenApiAuthService)
        openapi_service_mock.generate_access_token.side_effect = ValueError()
        with pytest.raises(HTTPException) as exc_info:
            generate_access_token(
                openapi_auth_service=openapi_service_mock,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "We are unable to process your request."
