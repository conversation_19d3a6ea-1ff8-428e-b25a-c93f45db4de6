from copy import deepcopy

import pytest
from fastapi import status

from api.rating.deps import db_app_repository
from rating.adapters.usage_repository import (
    AbstractAppRepository,
    InMemoryAppRepository,
)


@pytest.fixture
def repository() -> AbstractAppRepository:
    return InMemoryAppRepository()


@pytest.fixture(autouse=True)
def override_dependencies(client, repository):
    overrides = client.app.dependency_overrides
    backup_deps = deepcopy(overrides)
    overrides[db_app_repository] = lambda: repository
    yield
    client.app.dependency_overrides = backup_deps


class TestAppVersion:
    def test_create(self, create_app_version):
        response = create_app_version("SPOG UI", "1.0.1")
        assert response.status_code == status.HTTP_201_CREATED

    def test_create_with_error(self, create_app_version):
        msg = "missing 2 required positional arguments: 'name' and 'version'"
        with pytest.raises(TypeError) as e:
            create_app_version()
        assert msg in str(e)

    def test_create_without_version_name(self, create_app_version):
        msg = "missing 1 required positional argument: 'name'"
        with pytest.raises(TypeError) as e:
            create_app_version(version="1.2.3")
        assert msg in str(e)

    def test_create_without_version_tag(self, create_app_version):
        msg = "missing 1 required positional argument: 'version'"
        with pytest.raises(TypeError) as e:
            create_app_version(name="SPOG UI test")
        assert msg in str(e)

    def test_get(self, api_factory, create_app_version):
        create_app_version("SPOG UI", "1.0.1")
        response = api_factory("get", method="GET")
        assert response.status_code == status.HTTP_200_OK

    def test_get_response(self, api_factory, create_app_version):
        create_app_version("SPOG UI", "1.0.1")
        response = api_factory("get", method="GET")
        assert response.json()["name"] == "SPOG UI"
        assert response.json()["version"] == "1.0.1"
