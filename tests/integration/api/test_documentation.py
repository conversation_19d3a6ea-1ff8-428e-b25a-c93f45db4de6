from fastapi import status


def test_swagger_ui_url_path(client):
    url = client.app.url_path_for("swagger_ui_html")
    assert url == "/docs"


def test_swagger_ui_is_ok(client):
    url = client.app.url_path_for("swagger_ui_html")
    response = client.get(url)
    assert response.status_code == status.HTTP_200_OK
    assert response.headers["content-type"] == "text/html; charset=utf-8"


def test_openapi_json_url_path(client):
    url = client.app.url_path_for("openapi")
    assert url == "/v1/glass/openapi.json"


def test_openapi_json_is_ok(client):
    url = client.app.url_path_for("openapi")
    response = client.get(url)
    assert response.status_code == status.HTTP_200_OK
    assert response.headers["content-type"] == "application/json"
