import uuid
from copy import deepcopy
from dataclasses import dataclass
from datetime import datetime
from typing import Iterator
from unittest.mock import MagicMock

from fastapi import Request
from fastapi.testclient import TestClient
from pydantic import EmailStr
from requests import Response

from api import deps
from api.authorization.examples import PERMISSIONS
from api.automation.examples import RULE_DETAIL_MODEL, RULE_RESPONSE, RULE_STATUS
from api.rate_plans.examples import CREATE_RATE_PLAN_REQUEST
from api.resolvers import AccountResolver, FakeAccountResolver
from api.schema_types import IMSI
from app.main import app
from auth.dto import Account, AuthenticatedUser, Client, Distributor
from auth.services import FakeAuthService
from authorization.domain.ports import AbstractAuthorizationAPI
from automation.domain import model as automation_model
from automation.services import AutomationService
from common.constants import CTDU, DUESL
from common.types import ICCID
from common.utils import set_trace_id
from sim.domain import model
from sync_redis.service import RedisService

import pytest  # isort: skip

set_trace_id(uuid.uuid4())


@pytest.fixture
def create_distributor_staff_user():
    def factory(organization_id: int = 1) -> AuthenticatedUser:
        return AuthenticatedUser(
            id=uuid.uuid4(),
            organization=Distributor(id=organization_id),
            role="User",
            email="<EMAIL>",
        )

    return factory


@pytest.fixture
def create_distributor_client_user():
    def factory(organization_id: int = 1, account_id: int = 1) -> AuthenticatedUser:
        return AuthenticatedUser(
            id=uuid.uuid4(),
            organization=Client(
                id=organization_id,
                account=Account(
                    id=account_id,
                ),
            ),
            role="Admin",
            email="<EMAIL>",
        )

    return factory


@pytest.fixture
def api_factory(client):
    def factory(
        endpoint: str,
        method: str = "GET",
        request_kwargs: dict | None = None,
        url_kwargs: dict | None = None,
    ) -> Response:
        request_kwargs = {} if request_kwargs is None else request_kwargs
        url_kwargs = {} if url_kwargs is None else url_kwargs
        url = client.app.url_path_for(endpoint, **url_kwargs)
        method = method.lower()
        api_call = getattr(client, method)
        return api_call(url, **request_kwargs)

    return factory


@pytest.fixture
def api_security():
    def api_permission(route):
        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = f"https://testclient.com{route}"
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "GET"

        return mock_auth, mock_request

    return api_permission


@pytest.fixture(scope="session")
def client() -> TestClient | Iterator[TestClient]:
    """A test client for the API"""
    app.dependency_overrides[deps.get_auth_service] = deps._fake_auth_service

    with TestClient(app) as c:
        yield c


@pytest.fixture
def authenticate(client):
    original_overrides = deepcopy(app.dependency_overrides)

    def _patch_dependency(user):

        client.app.dependency_overrides[
            deps.get_auth_service
        ] = lambda: FakeAuthService(user=user)

    try:
        yield _patch_dependency
    finally:
        app.dependency_overrides = original_overrides

    return _patch_dependency


@pytest.fixture
def create_rate_plan_api_factory(api_factory):
    def factory(account_id):
        return api_factory(
            "create_rate_plan",
            method="POST",
            request_kwargs={
                "json": {**CREATE_RATE_PLAN_REQUEST, "accountId": account_id}
            },
        )

    return factory


@pytest.fixture
def create_sim_range_file(api_factory):
    def factory(imsi_first, length):
        response = api_factory(
            "generate_test_sim_cards",
            method="POST",
            request_kwargs={
                "json": {
                    "imsiFirst": imsi_first,
                    "length": length,
                }
            },
        )

        return response.text

    return factory


@pytest.fixture
def upload_sim_range_file(api_factory):
    def factory(sim_range_file):
        return api_factory(
            "create_range",
            method="POST",
            request_kwargs={
                "files": {
                    "file": ("sim_range.csv", sim_range_file, "multipart/form-data")
                },
                "params": {"formFactor": "STANDARD", "msisdnFactor": "INTERNATIONAL"},
            },
        )

    return factory


@pytest.fixture
def create_allocation_api_factory(api_factory):
    def factory(account_id, range_id, rate_plan_id, quantity):
        return api_factory(
            "create_allocation",
            method="POST",
            request_kwargs={
                "json": {
                    "title": "Reference",
                    "accountId": account_id,
                    "rangeId": range_id,
                    "ratePlanId": rate_plan_id,
                    "quantity": quantity,
                }
            },
        )

    return factory


@pytest.fixture
def create_app_version(api_factory):
    def factory(name, version):
        return api_factory(
            "create",
            method="POST",
            request_kwargs={"json": {"name": name, "version": version}},
        )

    return factory


@dataclass
class Address:
    host: str
    port: int


@pytest.fixture
def request_mock():
    mock_request = MagicMock()
    mock_request.client = Address(host="127.0.0.1", port=44508)

    return mock_request


@pytest.fixture
def create_reallocation_audit_fixture() -> model.ReAllocationResult:
    results = [
        model.SIMCardAudit(
            uuid=uuid.UUID("59363a8b-1de2-4afe-957b-9ad3ac84781e"),
            imsi=IMSI("***************"),
            iccid=ICCID("8944538532046590117"),
            msisdn=model.MSISDN("***************"),
            request_type="Re-allocation",
            prior_value="1",
            new_value="2",
            field="Account",
            action="Re-allocation",
            client_ip="***********",
            created_by=EmailStr("<EMAIL>"),
            prior_rate_plan=10,
            new_rate_plan=20,
            allocation_date=datetime(2024, 3, 21),
            audit_date=datetime(2024, 3, 21),
            message="Re-allocation",
            status="Pending",
            work_id="************",
            prior_status="Active",
        ),
    ]
    reallocation_validation = model.ReAllocationResult(
        validSIM=1,
        errorSIM=0,
        message=(
            "1 IMSIs will be re-allocated and 0 "
            "IMSIs already assigned to choosen Rate Plan."
        ),
        valid_imsi_list=[results[0].imsi],
    )

    return reallocation_validation


@pytest.fixture
def rate_plan_change_audit_fixture() -> model.RatePlanChangeSimLimitResult:
    rate_plan_change_validation = model.RatePlanChangeSimLimitResult(
        message=("Rate Plan will be changed"),
    )

    return rate_plan_change_validation


@pytest.fixture
def valid_rate_plan_change_audit_fixture() -> model.RatePlanChangeSimLimitResult:
    rate_plan_change_validation = model.RatePlanChangeSimLimitResult(
        message=("Rate Plan can't be changed")
    )

    return rate_plan_change_validation


@pytest.fixture
def redis_service_mock():
    redis_service_mock = MagicMock(spec=RedisService)
    return redis_service_mock


@pytest.fixture
def automation_service_mock(make_rule_type_models):

    automation_service_mock = MagicMock(spec=AutomationService)
    expected_model_response = automation_model.RuleDetails(**RULE_RESPONSE)
    automation_service_mock.create_rule.return_value = (
        automation_model.CreateRuleDetails(rules_uuid=[expected_model_response])
    )
    automation_service_mock.get_rule.return_value = (
        automation_model.RuleDetailResponseList(result=[RULE_DETAIL_MODEL])
    )
    automation_service_mock.update_rule_status_by_rule_uuid.return_value = (
        automation_model.RuleStatus(**RULE_STATUS)
    )
    automation_service_mock.update_rule.return_value = automation_model.RuleDetails(
        **RULE_RESPONSE
    )

    category_input_data = make_rule_type_models()
    automation_service_mock.get_rule_type.return_value = [
        iter(category_input_data),
        len(category_input_data),
    ]
    return automation_service_mock


@pytest.fixture
def automation_service_mock_error(make_rule_type_models):
    def factory(error_type, message):
        automation_service_mock = MagicMock(spec=AutomationService)
        automation_service_mock.create_rule.return_value = automation_model.RuleDetails(
            **RULE_RESPONSE
        )
        automation_service_mock.get_rule.return_value = (
            automation_model.RuleDetailResponseList(result=[RULE_DETAIL_MODEL])
        )
        automation_service_mock.update_rule_status_by_rule_uuid.return_value = (
            automation_model.RuleStatus(**RULE_STATUS)
        )
        automation_service_mock.update_rule.return_value = automation_model.RuleDetails(
            **RULE_RESPONSE
        )

        category_input_data = make_rule_type_models()
        automation_service_mock.get_rule_type.return_value = [
            iter(category_input_data),
            len(category_input_data),
        ]

        automation_service_mock.create_rule.side_effect = error_type(message)
        automation_service_mock.get_rule_by_uuid.side_effect = error_type(message)
        automation_service_mock.get_rule.side_effect = error_type(message)
        automation_service_mock.update_rule_status_by_rule_uuid.side_effect = (
            error_type(message)
        )
        automation_service_mock.update_rule.side_effect = error_type(message)
        automation_service_mock.get_rule_type.side_effect = error_type(message)
        automation_service_mock.get_rule_category.side_effect = error_type(message)
        return automation_service_mock

    return factory


@pytest.fixture
def make_notification_models():
    notifications = [
        {"id": 1, "notification": "SMS"},
        {"id": 2, "notification": "Email"},
        {"id": 3, "notification": "Notification"},
    ]
    return [
        automation_model.Notifications(**notification) for notification in notifications
    ]


@pytest.fixture
def make_action_models():
    def factory(
        actions=[
            "Send SMS",
            "Send Email",
            "Send Notification",
        ]
    ):
        return [
            automation_model.Actions(id=i + 1, action=action)
            for i, action in enumerate(actions)
        ]

    return factory


@pytest.fixture
def account_resolver_mock():
    account_resolver = MagicMock(spec=AccountResolver)
    account_resolver.get_collection.return_value = map(
        FakeAccountResolver._get_account, [1]
    )
    return account_resolver


@pytest.fixture
def order_search_pagination_mock():
    mock_ordering = MagicMock()
    mock_searching = MagicMock()
    mock_pagination = MagicMock()

    mock_ordering.field = "name"
    mock_ordering.order = "ASC"
    mock_searching.field = "name"
    mock_searching.search = "Test"
    mock_pagination.page = 1
    mock_pagination.page_size = 50

    return mock_ordering, mock_searching, mock_pagination


@pytest.fixture
def make_rule_type_models():
    def factory(
        rule_types=["Usage Monitoring", "SIM Provisioning", "Network and System"]
    ):
        return [
            automation_model.RuleType(
                id=i + 1,
                rule_type=rule_type,
            )
            for i, rule_type in enumerate(rule_types)
        ]

    return factory


@pytest.fixture
def make_rule_category_models():
    def factory(
        categories=[
            "Cycle To Date Data Usage",
            "Cycle To Date Voice Usage",
            "Recent SMS Usage",
        ]
    ):
        return [
            automation_model.RuleCategoryDetails(
                id=i + 1,
                category=category,
                rule_type="SIM Provisioning",
                rule_type_id=1,
                rule_category_code=CTDU,
            )
            for i, category in enumerate(categories)
        ]

    return factory


@pytest.fixture
def make_rule_definition_models():
    def factory(
        definitions=[
            ("Data usage exceeds a specified limit xxx KB", "KB"),
            (" Voice usage threshold is xxx minutes", "mint"),
        ]
    ):
        return [
            automation_model.RuleDefinitionDetails(
                id=i + 1,
                definition=definition,
                category="Cycle To Date Data Usage",
                rule_definition_code=DUESL,
                rule_category_code=CTDU,
            )
            for i, (definition, unit) in enumerate(definitions)
        ]

    return factory
