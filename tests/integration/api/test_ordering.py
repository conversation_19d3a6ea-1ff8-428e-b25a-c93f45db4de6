from typing import Iterator

import pytest
from fastapi import Depends, FastAPI, Query
from fastapi.testclient import TestClient
from pydantic import BaseModel

from common.ordering import Ordering
from common.pagination import Pagination


@pytest.fixture
def app() -> FastAPI:
    return FastAPI()


@pytest.fixture()
def client(app) -> TestClient | Iterator[TestClient]:
    """A test client for the API"""

    with TestClient(app) as c:
        yield c


class DummyItem(BaseModel):
    field: str
    order: str


@pytest.mark.parametrize(
    "ordering_fields,query,status_code,data",
    (
        (("imsi", "id"), {"ordering": "imsi"}, 200, {"field": "imsi", "order": "ASC"}),
        (("imsi", "id"), {"ordering": "+imsi"}, 422, {}),
        (("imsi", "id"), {"ordering": "imsI"}, 422, {}),
        (("imsi", "id"), {"ordering": "ID"}, 422, {}),
        (
            ("imsi", "id"),
            {"ordering": "-imsi"},
            200,
            {"field": "imsi", "order": "DESC"},
        ),
        (("id",), {"ordering": "-id"}, 200, {"field": "id", "order": "DESC"}),
    ),
)
def test_parameters_passed_in_endpoint(
    app, client, ordering_fields, query, status_code, data
):
    @app.get("/items")
    def items(
        ordering: Ordering = Depends(
            Ordering.query(
                DummyItem,
                default_ordering=ordering_fields[0],
                ordering_fields=ordering_fields,
            )
        )
    ):
        return ordering.dict()

    response = client.get(app.url_path_for(items.__name__), params=query)
    assert response.status_code == status_code
    if response.status_code == 200:
        assert response.json() == data


def test_ordering_with_pagination_query_parameters(app, client):
    @app.get("/items")
    def items(
        pagination: Pagination = Depends(Pagination.query()),
        ordering: Ordering = Depends(
            Ordering.query(
                DummyItem, default_ordering="id", ordering_fields=("id", "created_at")
            )
        ),
        id: int = Query(1),
        services: list[str] = Query(),
    ):
        return pagination.dict() | ordering.dict() | {"services": services, "id": id}

    order_param = {"ordering": "-id"}
    order_parsed_params = {"order": "DESC", "field": "id"}
    query_params = {
        "page": 3,
        "page_size": 20,
        "id": 10,
        "services": ["VOICE_MO", "VOICE_MT"],
    }
    response = client.get(
        app.url_path_for(items.__name__), params=query_params | order_param
    )
    assert response.json() == query_params | order_parsed_params
