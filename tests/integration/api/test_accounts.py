from unittest.mock import MagicMock

import pytest
from fastapi import HTT<PERSON>Ex<PERSON>, status

from accounts.adapters.exceptions import AccountDeletionError
from accounts.domain import dto, model
from accounts.domain.exceptions import (
    AccountAlreadyExists,
    AccountDoesNotExist,
    MediaError,
    SimAccountDataNotFound,
)
from accounts.services import AbstractAccountService
from api.accounts.endpoints import (
    create_account,
    delete_account,
    get_sim_account_info,
    list_accounts,
    update_account,
    view_account,
)
from api.accounts.examples import (
    ACCOUNT_2,
    CREATE_ACCOUNT,
    SIM_ACCOUNT_INFO_DETAILS,
    TEST_ACCOUNT_RESPONSE,
    UPDATE_ACCOUNT,
)
from api.accounts.schemas import CreateAccount, UpdateAccount
from api.schema_types import CamelBaseModel
from authorization.domain.ports import AbstractAuthorizationAPI


class FakeAccount(CamelBaseModel, dto.Account):
    @classmethod
    def from_dto(
        cls,
        account: dto.Account,
    ) -> "FakeAccount":
        return cls(
            **account.dict(),
        )


class GetFakeAccount(CamelBaseModel, dto.GetAccount):
    @classmethod
    def from_dto(
        cls,
        account: dto.GetAccount,
    ) -> "GetFakeAccount":
        return cls(
            **account.dict(),
        )


@pytest.fixture
def test_account_details():
    mock_account_data = FakeAccount(**TEST_ACCOUNT_RESPONSE)
    mock_account_data.id = 123
    mock_account_data.name = "Test Account"

    mock_account_service = MagicMock(spec=AbstractAccountService)
    return mock_account_service, mock_account_data


@pytest.fixture
def test_get_account_details():
    mock_account_data = GetFakeAccount(**ACCOUNT_2)
    mock_account_data.id = 123
    mock_account_data.name = "Test Account"

    mock_account_service = MagicMock(spec=AbstractAccountService)
    return mock_account_service, mock_account_data


@pytest.fixture
def order_search_pagination_mock():
    mock_ordering = MagicMock()
    mock_searching = MagicMock()
    mock_pagination = MagicMock()

    mock_ordering.field = "name"
    mock_ordering.order = "ASC"
    mock_searching.field = "name"
    mock_searching.search = "Test"
    mock_pagination.page = 1
    mock_pagination.page_size = 50

    return mock_ordering, mock_searching, mock_pagination


@pytest.fixture
def mock_account_statistic_resolver():
    mock_account_statistic_resolver = MagicMock()
    mock_statistic = {
        "sims_total": 5,
        "plans_total": 3,
        "active_sims_total": 6,
        "users_total": 6,
    }
    mock_account_statistic_resolver.return_value = lambda _: mock_statistic

    return mock_account_statistic_resolver


class TestAccount:
    def test_create_account_success(self, test_account_details, api_security):

        account_data = CreateAccount(**CREATE_ACCOUNT)

        mock_account_service, mock_account_data = test_account_details
        mock_account_service.create_account.return_value = 123
        mock_account_service.get.return_value = mock_account_data

        mock_auth, mock_request = api_security("/v1/glass/accounts")
        mock_request.method = "POST"

        result = create_account(
            request=mock_request,
            account=account_data,
            account_service=mock_account_service,
            authorization=mock_auth,
        )
        assert result.id == 123
        assert result.name == "Test Account"

        mock_account_service.create_account.assert_called_once_with(account_data)
        mock_account_service.get.assert_called_once_with(123)

    def test_create_account_media_error(self, api_security):
        account_data = CreateAccount(**CREATE_ACCOUNT)
        mock_account_service = MagicMock(spec=AbstractAccountService)
        mock_account_service.create_account.side_effect = MediaError("Media error")

        mock_auth, mock_request = api_security("/v1/glass/accounts")
        mock_request.method = "POST"

        with pytest.raises(HTTPException) as exc_info:
            create_account(
                request=mock_request,
                account=account_data,
                account_service=mock_account_service,
                authorization=mock_auth,
            )

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert str(exc_info.value.detail) == "Media error"

    def test_create_account_already_exists(self, api_security):
        account_data = CreateAccount(**CREATE_ACCOUNT)
        mock_account_service = MagicMock(spec=AbstractAccountService)
        mock_account_service.create_account.side_effect = AccountAlreadyExists()

        mock_auth, mock_request = api_security("/v1/glass/accounts")
        mock_request.method = "POST"

        with pytest.raises(HTTPException) as exc_info:
            create_account(
                request=mock_request,
                account=account_data,
                account_service=mock_account_service,
                authorization=mock_auth,
            )

        assert exc_info.value.status_code == status.HTTP_409_CONFLICT

    def test_update_account_success(
        self, test_account_details, mock_account_statistic_resolver, api_security
    ):

        mock_account_service, mock_account_data = test_account_details
        mock_account_data.name = "Update Test Account"
        mock_account_service.update.return_value = mock_account_data

        mock_auth, mock_request = api_security("/v1/glass/accounts/{account_id}")
        mock_request.method = "PUT"

        mock_update_account = UpdateAccount(**UPDATE_ACCOUNT)
        result = update_account(
            request=mock_request,
            account_id=123,
            account_data=mock_update_account,
            account_service=mock_account_service,
            authorization=mock_auth,
            account_statistic_resolver=mock_account_statistic_resolver,
        )

        assert result.name == "Update Test Account"
        assert result.sims_total == 5
        assert result.plans_total == 3
        assert result.active_sims_total == 6
        assert result.users_total == 6
        mock_account_service.update.assert_called_once_with(
            123, UpdateAccount(**UPDATE_ACCOUNT)
        )
        mock_account_statistic_resolver.assert_called_once_with([mock_account_data])

    def test_update_account_media_error(self, api_security):
        mock_account_service = MagicMock(spec=AbstractAccountService)
        mock_account_service.update.side_effect = MediaError("Media error")

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)

        mock_auth, mock_request = api_security("/v1/glass/accounts/{account_id}")
        mock_request.method = "PUT"

        with pytest.raises(HTTPException) as exc_info:
            update_account(
                request=mock_request,
                account_id=123,
                account_data=UpdateAccount(**UPDATE_ACCOUNT),
                account_service=mock_account_service,
                authorization=mock_auth,
            )

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Media error" in exc_info.value.detail

    def test_update_account_account_does_not_exist(self, api_security):
        mock_account_service = MagicMock(spec=AbstractAccountService)
        mock_account_service.update.side_effect = AccountDoesNotExist()

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)

        mock_auth, mock_request = api_security("/v1/glass/accounts/{account_id}")
        mock_request.method = "PUT"

        with pytest.raises(HTTPException) as exc_info:
            update_account(
                request=mock_request,
                account_id=123,
                account_data=UpdateAccount(**UPDATE_ACCOUNT),
                account_service=mock_account_service,
                authorization=mock_auth,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND

    def test_update_account_account_already_exists(self, api_security):
        mock_account_service = MagicMock(spec=AbstractAccountService)
        mock_account_service.update.side_effect = AccountAlreadyExists()

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)

        mock_auth, mock_request = api_security("/v1/glass/accounts/{account_id}")
        mock_request.method = "PUT"
        with pytest.raises(HTTPException) as exc_info:
            update_account(
                request=mock_request,
                account_id=123,
                account_data=UpdateAccount(**UPDATE_ACCOUNT),
                account_service=mock_account_service,
                authorization=mock_auth,
            )

        assert exc_info.value.status_code == status.HTTP_409_CONFLICT

    def test_list_account_ok(
        self,
        test_get_account_details,
        order_search_pagination_mock,
        mock_account_statistic_resolver,
        api_security,
    ):

        mock_account_service, mock_account_data = test_get_account_details
        mock_account_service.account_count.return_value = 10
        mock_account_data.name = "Test Account"
        mock_account_service.list.return_value = [mock_account_data]

        mock_auth, mock_request = api_security("/v1/glass/accounts")
        mock_request.method = "GET"

        mock_ordering, mock_searching, mock_pagination = order_search_pagination_mock

        result = list_accounts(
            request=mock_request,
            account_service=mock_account_service,
            authorization=mock_auth,
            ordering=mock_ordering,
            searching=mock_searching,
            pagination=mock_pagination,
        )

        assert result.results[0].name == "Test Account"
        assert result.results[0].sims_total == 0
        assert result.results[0].plans_total == 0
        assert result.results[0].active_sims_total == 0
        assert result.results[0].users_total == 0

    def test_list_accounts_error(
        self,
        test_account_details,
        order_search_pagination_mock,
        mock_account_statistic_resolver,
        api_security,
    ):

        mock_account_service, mock_account_data = test_account_details
        mock_account_service.account_count.return_value = 10
        mock_account_service.account_count.side_effect = Exception("Test Case Error")
        mock_account_service.list.return_value = [mock_account_data]

        mock_auth, mock_request = api_security("/v1/glass/accounts")
        mock_request.method = "GET"

        mock_ordering, mock_searching, mock_pagination = order_search_pagination_mock

        with pytest.raises(HTTPException) as exc_info:
            list_accounts(
                request=mock_request,
                account_service=mock_account_service,
                authorization=mock_auth,
                ordering=mock_ordering,
                searching=mock_searching,
                pagination=mock_pagination,
            )

        assert exc_info.value.status_code == 500
        assert "Test Case Error" in exc_info.value.detail

    def test_view_account_ok(
        self,
        test_account_details,
        mock_account_statistic_resolver,
        api_security,
    ):

        mock_account_service, mock_account_data = test_account_details
        mock_account_data.name = "Test Account"
        mock_account_service.get.return_value = mock_account_data

        mock_auth, mock_request = api_security("/v1/glass/accounts/1")
        mock_request.method = "GET"

        result = view_account(
            request=mock_request,
            account_id=1,
            account_service=mock_account_service,
            authorization=mock_auth,
            account_statistic_resolver=mock_account_statistic_resolver,
        )
        assert result.name == "Test Account"
        assert result.sims_total == 5
        assert result.plans_total == 3
        assert result.active_sims_total == 6
        assert result.users_total == 6

    def test_view_account_not_found(
        self,
        test_account_details,
        mock_account_statistic_resolver,
        api_security,
    ):

        mock_account_service, mock_account_data = test_account_details
        mock_account_data.name = "Test Account"
        mock_account_service.get.side_effect = AccountDoesNotExist

        mock_auth, mock_request = api_security("/v1/glass/accounts/1")
        mock_request.method = "GET"

        with pytest.raises(HTTPException) as exc_info:
            view_account(
                request=mock_request,
                account_id=1,
                account_service=mock_account_service,
                authorization=mock_auth,
                account_statistic_resolver=mock_account_statistic_resolver,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND

    def test_delete_account_ok(
        self,
        test_account_details,
        api_security,
    ):

        mock_account_service, mock_account_data = test_account_details
        mock_account_data.name = "Test Account"
        mock_account_service.delete.return_value = {
            "message": "We have received your request."
        }

        mock_auth, mock_request = api_security("/v1/glass/accounts/1")
        mock_request.method = "DELETE"

        result = delete_account(
            request=mock_request,
            account_id=1,
            account_service=mock_account_service,
            authorization=mock_auth,
        )
        assert result["message"] in "We have received your request."

    def test_delete_account_not_found(
        self,
        test_account_details,
        api_security,
    ):

        mock_account_service, mock_account_data = test_account_details
        mock_account_data.name = "Test Account"
        mock_account_service.delete.side_effect = AccountDoesNotExist

        mock_auth, mock_request = api_security("/v1/glass/accounts/1")
        mock_request.method = "DELETE"

        with pytest.raises(HTTPException) as exc_info:
            delete_account(
                request=mock_request,
                account_id=1,
                account_service=mock_account_service,
                authorization=mock_auth,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND

    def test_delete_account_deletion_error(
        self,
        test_account_details,
        api_security,
    ):

        mock_account_service, mock_account_data = test_account_details
        mock_account_data.name = "Test Account"
        mock_account_service.delete.side_effect = AccountDeletionError

        mock_auth, mock_request = api_security("/v1/glass/accounts/1")
        mock_request.method = "DELETE"

        with pytest.raises(HTTPException) as exc_info:
            delete_account(
                request=mock_request,
                account_id=1,
                account_service=mock_account_service,
                authorization=mock_auth,
            )

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST


class TestSimInfo:
    def test_get_sim_info_success(self, api_security):
        expected_response = model.SimAccountInfoDetails(**SIM_ACCOUNT_INFO_DETAILS)
        account_service_mock = MagicMock(spec=AbstractAccountService)
        account_service_mock.get_sim_account_info.return_value = expected_response

        search = "***************"
        mock_auth, mock_request = api_security("/v1/glass/accounts")
        mock_request.method = "GET"

        response = get_sim_account_info(
            request=mock_request,
            search=search,
            account_service=account_service_mock,
            authorization=mock_auth,
        )

        assert response == expected_response
        assert isinstance(response, model.SimAccountInfoDetails)

    def test_sim_info_sim_account_data_not_found(self, api_security):
        account_service_mock = MagicMock(spec=AbstractAccountService)
        account_service_mock.get_sim_account_info.side_effect = SimAccountDataNotFound

        search = "***************"
        mock_auth, mock_request = api_security("/v1/glass/accounts")
        mock_request.method = "GET"

        with pytest.raises(HTTPException) as exc_info:
            get_sim_account_info(
                request=mock_request,
                search=search,
                account_service=account_service_mock,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND

    def test_sim_info_sim_account_general_exception(self, api_security):
        account_service_mock = MagicMock(spec=AbstractAccountService)
        account_service_mock.get_sim_account_info.side_effect = Exception

        search = "***************"
        mock_auth, mock_request = api_security("/v1/glass/accounts")
        mock_request.method = "GET"

        with pytest.raises(HTTPException) as exc_info:
            get_sim_account_info(
                request=mock_request,
                search=search,
                account_service=account_service_mock,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
