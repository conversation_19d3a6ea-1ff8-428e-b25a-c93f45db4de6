from typing import Iterator

import pytest
from fastapi import Depends, FastAPI, Query
from fastapi.openapi.utils import get_openapi
from fastapi.testclient import TestClient
from pydantic import BaseModel
from starlette import status

from common.pagination import InvalidPage, PaginatedResponse, Pagination


@pytest.fixture
def app() -> FastAPI:
    return FastAPI()


@pytest.fixture()
def client(app) -> TestClient | Iterator[TestClient]:
    """A test client for the API"""

    with TestClient(app) as c:
        yield c


@pytest.mark.parametrize("page,offset", [(1, 0), (2, 2), (3, 4)])
def test_pagination_offset(page, offset):
    pagination = Pagination(page=page, page_size=2)
    assert pagination.offset == offset


@pytest.mark.parametrize(
    "query,data",
    (
        ({}, {"page": 1, "page_size": 20}),
        ({"page": 10}, {"page": 10, "page_size": 20}),
        ({"page": 10, "page_size": 25}, {"page": 10, "page_size": 25}),
    ),
)
def test_parameters_passed_in_endpoint(app, client, query, data):
    @app.get("/items")
    def items(pagination: Pagination = Depends(Pagination.query(default_page_size=20))):
        return pagination.dict()

    response = client.get(app.url_path_for(items.__name__), params=query)
    assert response.json() == data


@pytest.mark.parametrize(
    "query,error",
    [
        ({"page": -1}, {"loc": "page", "type": "not_ge", "limit": 1}),
        ({"page": 0}, {"loc": "page", "type": "not_ge", "limit": 1}),
        ({"page_size": -1}, {"loc": "page_size", "type": "not_ge", "limit": 1}),
        ({"page_size": 0}, {"loc": "page_size", "type": "not_ge", "limit": 1}),
        ({"page_size": 100}, {"loc": "page_size", "type": "not_le", "limit": 10}),
    ],
)
def test_pagination_query_validation(app, client, query, error):
    @app.get("/items")
    def items(
        pagination: Pagination = Depends(
            Pagination.query(default_page_size=5, max_page_size=10)
        )
    ):
        return pagination.dict()

    response = client.get(app.url_path_for(items.__name__), params=query)
    assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    actual_error = response.json()["detail"][0]

    actual_error.pop("msg")
    assert actual_error == {
        "ctx": {"limit_value": error["limit"]},
        "loc": ["query", error["loc"]],
        "type": "value_error.number." + error["type"],
    }


def test_pagination_with_other_query_parameters(app, client):
    @app.get("/items")
    def items(
        pagination: Pagination = Depends(Pagination.query()),
        foo: str = Query(),
        bar: int = Query(1),
    ):
        return pagination.dict() | {"foo": foo, "bar": bar}

    query_params = {"page": 2, "page_size": 10, "foo": "foo param", "bar": 10}
    response = client.get(app.url_path_for(items.__name__), params=query_params)
    assert response.json() == query_params


def test_pagination_schema(app, client):
    @app.get("items")
    def items(pagination: Pagination = Depends(Pagination.query())):
        ...

    openapi_schema = get_openapi(
        title="Custom title",
        version="2.5.0",
        description="This is a very custom OpenAPI schema",
        routes=app.routes,
    )
    assert openapi_schema["paths"]["items"]["get"]["parameters"] == [
        {
            "in": "query",
            "name": "page",
            "required": False,
            "schema": {
                "default": 1,
                "minimum": 1,
                "title": "Page",
                "type": "integer",
            },
        },
        {
            "in": "query",
            "name": "page_size",
            "required": False,
            "schema": {
                "default": 50,
                "minimum": 1,
                "maximum": 200,
                "title": "Page Size",
                "type": "integer",
            },
        },
    ]


@pytest.mark.parametrize("page,results", [(1, [1, 2]), (2, [3, 4]), (3, [5])])
def test_paginated_response_from_full_sequence(page, results):
    items = [1, 2, 3, 4, 5]
    response = PaginatedResponse.from_full_sequence(
        pagination=Pagination(page=page, page_size=2),
        items=items,
    )
    assert response.last_page == 3
    assert response.results == results


def test_paginated_response_boundary_error():
    items = [1, 2, 3, 4, 5]

    with pytest.raises(InvalidPage):
        PaginatedResponse.from_full_sequence(
            pagination=Pagination(page=4, page_size=2),
            items=items,
        )


def test_paginated_response_schema(app):
    class DummyItem(BaseModel):
        foo: int
        bar: str

    class Items(PaginatedResponse):
        results: list[DummyItem]

    @app.get("/items", response_model=Items)
    def items():
        ...

    openapi_schema = get_openapi(
        title="Custom title",
        version="2.5.0",
        description="This is a very custom OpenAPI schema",
        routes=app.routes,
    )
    components = openapi_schema["components"]["schemas"]
    assert "page" in components["Items"]["properties"]
    assert "page_size" in components["Items"]["properties"]
    assert "last_page" in components["Items"]["properties"]
    assert "results" in components["Items"]["properties"]
    assert components["Items"]["properties"]["results"]["items"] == {
        "$ref": "#/components/schemas/DummyItem"
    }
