import os

import pytest

from common.file_storage import LocalFileStorage
from rating.storage_sync import CDRFolder, CDRStorage


@pytest.fixture
def generate_usage_body():
    return {
        "imsiFirst": "123456789101112",
        "length": 500,
        "country": "GBP",
        "month": "2023-02",
        "carriers": ["GBPST"],
    }


def test_import_monthly_cdr_from_local(client, tmp_path, generate_usage_body):
    url = client.app.url_path_for("generate_test_monthly_usage")
    response = client.post(url, json=generate_usage_body)

    # Create correct monthly usage file
    (tmp_path / "monthly_usage.csv").write_bytes(response.content)
    assert os.path.isfile(tmp_path / "monthly_usage.csv")

    # Create incorrect monthly usage file
    with open(tmp_path / "fake_monthly_usage.csv", "w+b") as temp_file:
        temp_file.write(b"Fake testing data")
    assert os.path.isfile(tmp_path / "fake_monthly_usage.csv")

    assert not os.path.isdir(tmp_path / CDRFolder.COMPLETED.value)
    assert not os.path.isdir(tmp_path / CDRFolder.FAILED.value)

    local_storage = LocalFileStorage(root_path=tmp_path.as_posix())
    cdr_storage = CDRStorage(local_storage)
    list(cdr_storage.extract_new_records(prefix_path=None))

    assert os.path.isdir(tmp_path / CDRFolder.COMPLETED.value)
    assert os.path.isdir(tmp_path / CDRFolder.FAILED.value)
