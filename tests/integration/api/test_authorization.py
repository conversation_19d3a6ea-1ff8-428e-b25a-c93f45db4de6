import uuid
from copy import deepcopy
from unittest.mock import <PERSON><PERSON>ock

import pytest
from fastapi import HTTPException

from accounts.adapters.repository import InMemoryAccountRepository
from accounts.domain.ports import AbstractAccountRepository
from api.authorization.deps import authorization_service
from api.authorization.endpoints import (
    create_role,
    delete_role,
    get_permissions_role_id,
    get_roles,
)
from api.authorization.examples import GROUP_ROLE_RESPONSE
from auth.exceptions import ForbiddenError, NotFound
from authorization.adapters.repository import (
    AbstractAuthRepository,
    DatabaseAuthRepository,
)
from authorization.domain.model import (
    GroupRole,
    PermissionsResponse,
    RoleRequest,
    RoleResponse,
)
from authorization.exceptions import (
    GroupNotFound,
    GroupRoleNotFound,
    MaxNumberInvalid,
    PolicyAlreadyExist,
    PolicyNotFound,
    RoleDeletionError,
    RoleNotFound,
)
from authorization.services import HTTPAuthorizationAPI
from common.pagination import InvalidPage, Pagination


@pytest.fixture
def account_repository() -> AbstractAccountRepository:
    return InMemoryAccountRepository()


@pytest.fixture
def auth_repository() -> AbstractAuthRepository:
    return MagicMock(spec=DatabaseAuthRepository)


@pytest.fixture(autouse=True)
def override_dependencies(client, account_repository, auth_repository):
    overrides = client.app.dependency_overrides
    backup_deps = deepcopy(overrides)
    overrides[authorization_service] = lambda: HTTPAuthorizationAPI(
        client, account_repository, auth_repository
    )
    yield
    client.app.dependency_overrides = backup_deps


ROLE_REQUEST = RoleRequest(
    name="admin",
    description="admin",
    roleGroup="Account",
    permission=["df279b03-0bcf-4ab5-9059-a72b2d2321a5"],
)


class TestAuthorizationAPI:
    def test_get_permissions_role_id(self, api_security):
        role_id = "df279b03-0bcf-4ab5-9059-a72b2d2321a5"

        mock_auth, mock_request = api_security(
            "/v1/glass/authorization/{role_id}/permission"
        )
        mock_auth.get_permissions.return_value = PermissionsResponse(
            id="df279b03-0bcf-4ab5-9059-a72b2d2321a5",
            name="admin",
            roleGroup="Account",
            description="admin",
            permissions=["df279b03-0bcf-4ab5-9059-a72b2d2321a5"],
        )
        mock_request.method = "GET"
        result = get_permissions_role_id(
            request=mock_request,
            role_id=role_id,
            authorization=mock_auth,
        )
        assert result.id == uuid.UUID("df279b03-0bcf-4ab5-9059-a72b2d2321a5")
        assert result.name == "admin"
        assert result.roleGroup == "Account"
        assert result.description == "admin"

    def test_get_roles(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/authorization/role")
        mock_request.method = "GET"
        mock_auth.get_roles.return_value = (
            [
                GroupRole(
                    id=role.id,
                    name=role.name,
                    description=role.description,
                    group=role.group,
                    permissions=role.permissions,
                    userCount=role.userCount,
                    createdBy=role.createdBy,
                )
                for role in GROUP_ROLE_RESPONSE
            ],
            1,
        )
        result = get_roles(
            request=mock_request,
            authorization=mock_auth,
            pagination=Pagination(page=1, page_size=10),
        )
        assert result.page == 1
        assert result.page_size == 10
        assert result.last_page == 1
        assert result.total_count == 1
        assert result.results[0].id == uuid.UUID("8b376afc-9014-49ff-ad1f-4589e122deda")
        assert result.results[0].name == "admin"
        assert result.results[0].isDefault is False
        assert result.results[0].description == "admin"
        assert result.results[0].group == "My Organization"
        assert result.results[0].permissions == 0
        assert result.results[0].userCount == 0
        assert result.results[0].createdBy == "admin"

    def test_create_role(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/authorization/role")
        mock_auth.create_role.return_value = RoleResponse(
            id=uuid.UUID("df279b03-0bcf-4ab5-9059-a72b2d2321a5"),
            name=ROLE_REQUEST.name,
            description=ROLE_REQUEST.description,
            composite=False,
            clientRole=True,
            containerId="df279b03-0bcf-4ab5-9059-a72b2d2321a5",
            userCount=0,
        )
        mock_request.method = "POST"
        result = create_role(
            request=mock_request,
            role_request=ROLE_REQUEST,
            authorization=mock_auth,
        )
        assert result.id == uuid.UUID("df279b03-0bcf-4ab5-9059-a72b2d2321a5")
        assert result.name == "admin"
        assert result.description == "admin"
        assert result.composite is False
        assert result.clientRole is True
        assert result.containerId == "df279b03-0bcf-4ab5-9059-a72b2d2321a5"
        assert result.userCount == 0

    def test_delete_role(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/authorization/role")
        mock_auth.delete_role.return_value = None
        mock_request.method = "DELETE"
        result = delete_role(
            request=mock_request,
            role_uuid=uuid.UUID("df279b03-0bcf-4ab5-9059-a72b2d2321a5"),
            authorization=mock_auth,
        )
        assert result is None

    def test_get_roles_not_found(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/authorization/role")
        mock_request.method = "GET"

        mock_auth.get_roles.side_effect = NotFound
        with pytest.raises(HTTPException) as e:
            get_roles(
                request=mock_request,
                authorization=mock_auth,
                pagination=Pagination(page=1, page_size=10),
            )
        assert e.value.status_code == 404
        assert e.value.detail == "NotFound"

    def test_get_roles_maxnumber(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/authorization/role")
        mock_request.method = "GET"

        mock_auth.get_roles.side_effect = MaxNumberInvalid
        with pytest.raises(HTTPException) as e:
            get_roles(
                request=mock_request,
                authorization=mock_auth,
                pagination=Pagination(page=1, page_size=11),
            )
        assert e.value.status_code == 400
        assert e.value.detail == "Page size should be multiple of 10."

    def test_get_roles_group_not_found(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/authorization/role")
        mock_request.method = "GET"

        mock_auth.get_roles.side_effect = GroupNotFound(
            "df279b03-0bcf-4ab5-9059-a72b2d2321a5"
        )
        with pytest.raises(HTTPException) as e:
            get_roles(
                request=mock_request,
                authorization=mock_auth,
                pagination=Pagination(page=1, page_size=10),
            )
        assert e.value.status_code == 404
        assert e.value.detail == "Group not found."

    def test_get_roles_forbidden(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/authorization/role")
        mock_request.method = "GET"

        mock_auth.get_roles.side_effect = ForbiddenError
        with pytest.raises(HTTPException) as e:
            get_roles(
                request=mock_request,
                authorization=mock_auth,
                pagination=Pagination(page=1, page_size=10),
            )
        assert e.value.status_code == 403
        assert e.value.detail == "Forbidden."

    def test_get_roles_invalid_page(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/authorization/role")
        mock_request.method = "GET"

        mock_auth.get_roles.side_effect = InvalidPage
        with pytest.raises(HTTPException) as e:
            get_roles(
                request=mock_request,
                authorization=mock_auth,
                pagination=Pagination(page=11, page_size=10),
            )
        assert e.value.status_code == 400
        assert e.value.detail == "Invalid page: "

    def test_get_roles_value_error(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/authorization/role")
        mock_request.method = "GET"

        mock_auth.get_roles.side_effect = ValueError
        with pytest.raises(HTTPException) as e:
            get_roles(
                request=mock_request,
                authorization=mock_auth,
                pagination=Pagination(page=1, page_size=10),
            )
        assert e.value.status_code == 400
        assert e.value.detail == "We couldn't process the request."

    def test_get_roles_general_error(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/authorization/role")
        mock_request.method = "GET"

        mock_auth.get_roles.side_effect = Exception
        with pytest.raises(HTTPException) as e:
            get_roles(
                request=mock_request,
                authorization=mock_auth,
                pagination=Pagination(page=1, page_size=10),
            )
        assert e.value.status_code == 400
        assert e.value.detail == "Couldn't process the request"

    def test_get_permissions_role_id_policy_not_found(self, api_security):
        mock_auth, mock_request = api_security(
            "/v1/glass/authorization/{role_id}/permission"
        )
        mock_request.method = "GET"

        role_id = "df279b03-0bcf-4ab5-9059-a72b2d2321a5"
        mock_auth.get_permissions.side_effect = PolicyNotFound(role_id=role_id)
        with pytest.raises(HTTPException) as e:
            get_permissions_role_id(
                request=mock_request,
                role_id=role_id,
                authorization=mock_auth,
            )
        assert e.value.status_code == 404
        assert e.value.detail == "Policy Not Found"

    def test_get_permissions_role_id_role_not_found(self, api_security):
        mock_auth, mock_request = api_security(
            "/v1/glass/authorization/{role_id}/permission"
        )
        mock_request.method = "GET"

        role_id = "df279b03-0bcf-4ab5-9059-a72b2d2321a5"
        mock_auth.get_permissions.side_effect = RoleNotFound(role_id=role_id)
        with pytest.raises(HTTPException) as e:
            get_permissions_role_id(
                request=mock_request,
                role_id=role_id,
                authorization=mock_auth,
            )
        assert e.value.status_code == 404
        assert e.value.detail == "Role Not Found"

    def test_get_permissions_role_id_forbidden(self, api_security):
        mock_auth, mock_request = api_security(
            "/v1/glass/authorization/{role_id}/permission"
        )
        mock_request.method = "GET"

        role_id = "df279b03-0bcf-4ab5-9059-a72b2d2321a5"
        mock_auth.get_permissions.side_effect = ForbiddenError
        with pytest.raises(HTTPException) as e:
            get_permissions_role_id(
                request=mock_request,
                role_id=role_id,
                authorization=mock_auth,
            )
        assert e.value.status_code == 403
        assert e.value.detail == "Forbidden."

    def test_get_permissions_role_id_notfound(self, api_security):
        mock_auth, mock_request = api_security(
            "/v1/glass/authorization/{role_id}/permission"
        )
        mock_request.method = "GET"

        role_id = "df279b03-0bcf-4ab5-9059-a72b2d2321a5"
        mock_auth.get_permissions.side_effect = NotFound
        with pytest.raises(HTTPException) as e:
            get_permissions_role_id(
                request=mock_request,
                role_id=role_id,
                authorization=mock_auth,
            )
        assert e.value.status_code == 404
        assert e.value.detail == "NotFound"

    def test_get_permissions_role_id_value_error(self, api_security):
        mock_auth, mock_request = api_security(
            "/v1/glass/authorization/{role_id}/permission"
        )
        mock_request.method = "GET"

        role_id = "df279b03-0bcf-4ab5-9059-a72b2d2321a5"
        mock_auth.get_permissions.side_effect = ValueError
        with pytest.raises(HTTPException) as e:
            get_permissions_role_id(
                request=mock_request,
                role_id=role_id,
                authorization=mock_auth,
            )
        assert e.value.status_code == 400
        assert e.value.detail == "We couldn't process the request."

    def test_get_permissions_role_id_general_error(self, api_security):
        mock_auth, mock_request = api_security(
            "/v1/glass/authorization/{role_id}/permission"
        )
        mock_request.method = "GET"

        role_id = "df279b03-0bcf-4ab5-9059-a72b2d2321a5"
        mock_auth.get_permissions.side_effect = Exception
        with pytest.raises(HTTPException) as e:
            get_permissions_role_id(
                request=mock_request,
                role_id=role_id,
                authorization=mock_auth,
            )
        assert e.value.status_code == 400
        assert e.value.detail == "Couldn't process the request"

    def test_create_role_group_role_not_found(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/authorization/role")
        mock_auth.create_role.side_effect = GroupRoleNotFound(
            group_id="df279b03-0bcf-4ab5-9059-a72b2d2321a5"
        )
        mock_request.method = "POST"
        with pytest.raises(HTTPException) as e:
            create_role(
                request=mock_request,
                role_request=ROLE_REQUEST,
                authorization=mock_auth,
            )
        assert e.value.status_code == 404
        assert e.value.detail == "Group roles not found."

    def test_create_role_group_not_found(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/authorization/role")
        mock_auth.create_role.side_effect = GroupNotFound(
            group_id="df279b03-0bcf-4ab5-9059-a72b2d2321a5"
        )
        mock_request.method = "POST"
        with pytest.raises(HTTPException) as e:
            create_role(
                request=mock_request,
                role_request=ROLE_REQUEST,
                authorization=mock_auth,
            )
        assert e.value.status_code == 404
        assert e.value.detail == "Group not found."

    def test_create_role_policy_already_exist(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/authorization/role")
        mock_auth.create_role.side_effect = PolicyAlreadyExist
        mock_request.method = "POST"
        with pytest.raises(HTTPException) as e:
            create_role(
                request=mock_request,
                role_request=ROLE_REQUEST,
                authorization=mock_auth,
            )
        assert e.value.status_code == 409
        assert e.value.detail == "Role already exist."

    def test_create_role_forbidden(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/authorization/role")
        mock_auth.create_role.side_effect = ForbiddenError
        mock_request.method = "POST"
        with pytest.raises(HTTPException) as e:
            create_role(
                request=mock_request,
                role_request=ROLE_REQUEST,
                authorization=mock_auth,
            )
        assert e.value.status_code == 403
        assert e.value.detail == "Forbidden."

    def test_create_role_notfound(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/authorization/role")
        mock_auth.create_role.side_effect = NotFound
        mock_request.method = "POST"
        with pytest.raises(HTTPException) as e:
            create_role(
                request=mock_request,
                role_request=ROLE_REQUEST,
                authorization=mock_auth,
            )
        assert e.value.status_code == 404
        assert e.value.detail == "NotFound"

    def test_create_role_value_error(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/authorization/role")
        mock_auth.create_role.side_effect = ValueError
        mock_request.method = "POST"
        with pytest.raises(HTTPException) as e:
            create_role(
                request=mock_request,
                role_request=ROLE_REQUEST,
                authorization=mock_auth,
            )
        assert e.value.status_code == 400
        assert e.value.detail == "We coudn't process your request."

    def test_create_role_general_error(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/authorization/role")
        mock_auth.create_role.side_effect = Exception
        mock_request.method = "POST"
        with pytest.raises(HTTPException) as e:
            create_role(
                request=mock_request,
                role_request=ROLE_REQUEST,
                authorization=mock_auth,
            )
        assert e.value.status_code == 400
        assert e.value.detail == "Coudn't process your request."

    def test_delete_role_forbidden(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/authorization/role")
        mock_auth.delete_role.side_effect = ForbiddenError
        mock_request.method = "DELETE"
        with pytest.raises(HTTPException) as e:
            delete_role(
                request=mock_request,
                role_uuid=uuid.UUID("df279b03-0bcf-4ab5-9059-a72b2d2321a5"),
                authorization=mock_auth,
            )
        assert e.value.status_code == 403
        assert e.value.detail == "Forbidden."

    def test_delete_role_notfound(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/authorization/role")
        mock_auth.delete_role.side_effect = NotFound
        mock_request.method = "DELETE"
        with pytest.raises(HTTPException) as e:
            delete_role(
                request=mock_request,
                role_uuid=uuid.UUID("df279b03-0bcf-4ab5-9059-a72b2d2321a5"),
                authorization=mock_auth,
            )
        assert e.value.status_code == 404
        assert e.value.detail == "NotFound"

    def test_delete_role_role_deletion_error(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/authorization/role")
        mock_auth.delete_role.side_effect = RoleDeletionError
        mock_request.method = "DELETE"
        with pytest.raises(HTTPException) as e:
            delete_role(
                request=mock_request,
                role_uuid=uuid.UUID("df279b03-0bcf-4ab5-9059-a72b2d2321a5"),
                authorization=mock_auth,
            )
        assert e.value.status_code == 400
        assert (
            e.value.detail
            == "Role df279b03-0bcf-4ab5-9059-a72b2d2321a5 could not be deleted."
        )

    def test_delete_role_value_error(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/authorization/role")
        mock_auth.delete_role.side_effect = ValueError
        mock_request.method = "DELETE"
        with pytest.raises(HTTPException) as e:
            delete_role(
                request=mock_request,
                role_uuid=uuid.UUID("df279b03-0bcf-4ab5-9059-a72b2d2321a5"),
                authorization=mock_auth,
            )
        assert e.value.status_code == 400
        assert e.value.detail == "We coudn't process your request."

    def test_delete_role_general_error(self, api_security):
        mock_auth, mock_request = api_security("/v1/glass/authorization/role")
        mock_auth.delete_role.side_effect = Exception
        mock_request.method = "DELETE"
        with pytest.raises(HTTPException) as e:
            delete_role(
                request=mock_request,
                role_uuid=uuid.UUID("df279b03-0bcf-4ab5-9059-a72b2d2321a5"),
                authorization=mock_auth,
            )
        assert e.value.status_code == 400
        assert e.value.detail == "Coudn't process your request."
