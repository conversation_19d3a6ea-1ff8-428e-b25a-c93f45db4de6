from copy import deepcopy
from unittest.mock import MagicMock

import pytest
from fastapi import HTT<PERSON>Exception, status

from accounts.services import MediaService
from api.sim.deps import get_sim_repository
from api.sim.market_share_endpoints import (
    get_market_share,
    get_market_share_by_account,
    get_market_share_imsi,
)
from auth.exceptions import NotFound, Unauthorized
from cdrdata.adapters.repository import AbstractCdrRepository
from common.exceptions import Unprocessable
from email_helper.adapters.external_api import AbstractMailService  # type: ignore
from rate_plans.adapters.rate_plan_repository import AbstractRatePlanRepository
from sim.adapters.externalapi import (
    AbstractAuditService,
    AbstractMarketShareAPI,
    AbstractSIMProvisioningAPI,
)
from sim.adapters.repository import AbstractSimRepository, InMemorySimRepository
from sim.domain.model import Carrier, MarketShareCarrier, MarketShareUsage
from sim.exceptions import IMSIDoesNotExit
from sim.services import SimService
from sync_redis.adapters.externalapi import HTTPRedisAPI


@pytest.fixture
def mock_market_share_usage():
    def factory(usage1, usage2):
        carrier_object = Carrier("GBRME")
        carrier_usage_list = [
            MarketShareUsage(carrier=carrier_object, usage=usage1),
            MarketShareUsage(carrier=carrier_object, usage=usage2),
        ]
        total_usage = usage1 + usage2
        return MarketShareCarrier(totalUsage=total_usage, summary=carrier_usage_list)

    return factory


@pytest.fixture
def repository() -> AbstractSimRepository:
    return InMemorySimRepository()


@pytest.fixture(autouse=True)
def override_dependencies(client, repository):
    overrides = client.app.dependency_overrides
    backup_deps = deepcopy(overrides)
    overrides[get_sim_repository] = lambda: repository
    yield
    client.app.dependency_overrides = backup_deps


@pytest.fixture
def mock_market_share_usage_with_blank_summary():
    def factory():
        return MarketShareCarrier(totalUsage=4875, summary=[])

    return factory


def mock_market_share_usage_data(sim_service, mock_market_share_usage):

    sim_service.get_market_share_imsi = MagicMock(return_value=mock_market_share_usage)
    from_date = "2023-04-01"
    to_date = "2023-04-30"
    imsi = "***************"

    return sim_service, from_date, to_date, imsi


class TestMarketShare:
    def setup_method(self):
        self.simcard = SimService(
            AbstractSimRepository,
            AbstractRatePlanRepository,
            AbstractCdrRepository,
            AbstractSIMProvisioningAPI,
            AbstractMarketShareAPI,
            AbstractAuditService,
            MediaService,
            HTTPRedisAPI,
            AbstractMailService,
        )

    def test_get_market_share_with_success_200(
        self, mock_market_share_usage, api_security
    ):
        usage1 = 7854
        usage2 = 9857
        mock_market_share_usage = mock_market_share_usage(usage1, usage2)
        self.simcard.get_market_share = MagicMock(return_value=mock_market_share_usage)

        mock_auth, mock_request = api_security("/v1/glass/market/accounts")
        mock_request.method = "GET"

        from_date = "2023-04-01"
        to_date = "2023-04-30"
        result = get_market_share(
            mock_request, from_date, to_date, self.simcard, mock_auth
        )
        total_usage = usage1 + usage2
        assert result.totalUsage == total_usage
        assert result.summary[0].usage == 7854
        assert result.summary[0].carrier == "GBRME"

    def test_get_market_share_by_account_with_success_200(
        self,
        mock_market_share_usage,
        api_security,
    ):
        usage1 = 7854
        usage2 = 9857
        mock_market_share_usage = mock_market_share_usage(usage1, usage2)
        self.simcard.get_market_share_by_account = MagicMock(
            return_value=mock_market_share_usage
        )

        mock_auth, mock_request = api_security("/v1/glass/market/account/{account_id}")
        mock_request.method = "GET"

        from_date = "2023-04-01"
        to_date = "2023-04-30"
        account_id = 1
        result = get_market_share_by_account(
            mock_request, account_id, from_date, to_date, self.simcard, mock_auth
        )
        total_usage = usage1 + usage2
        assert result.totalUsage == total_usage
        assert result.summary[0].usage == 7854
        assert result.summary[0].carrier == "GBRME"

    def test_get_market_share_imsi_with_success_200(
        self, mock_market_share_usage, api_security
    ):
        usage1 = 7854
        usage2 = 9857
        mock_market_share_usage = mock_market_share_usage(usage1, usage2)
        self.simcard.get_market_share_imsi = MagicMock(
            return_value=mock_market_share_usage
        )
        self.simcard.side_effect = IMSIDoesNotExit("imsi Does not exist")

        mock_auth, mock_request = api_security("/v1/glass/market/accounts/imsi")
        mock_request.method = "GET"

        from_date = "2023-04-01"
        to_date = "2023-04-30"
        imsi = "***************"
        result = get_market_share_imsi(
            mock_request,
            imsi=imsi,
            from_date=from_date,
            to_date=to_date,
            sim_service=self.simcard,
            authorization=mock_auth,
        )

        total_usage = usage1 + usage2
        assert result.totalUsage == total_usage
        assert result.summary[0].usage == 7854
        assert result.summary[0].carrier == "GBRME"

    def test_get_market_share_imsi_not_found_404(
        self, mock_market_share_usage, api_security
    ):
        usage1 = 7854
        usage2 = 9857
        mock_market_share_usage = mock_market_share_usage(usage1, usage2)
        sim_service, from_date, to_date, imsi = mock_market_share_usage_data(
            sim_service=self.simcard, mock_market_share_usage=mock_market_share_usage
        )
        sim_service.get_market_share_imsi.side_effect = IMSIDoesNotExit(
            "imsi does not exist"
        )

        mock_auth, mock_request = api_security("/v1/glass/market/accounts/imsi")
        mock_request.method = "GET"

        with pytest.raises(HTTPException) as exc_info:
            get_market_share_imsi(
                mock_request,
                imsi=imsi,
                from_date=from_date,
                to_date=to_date,
                sim_service=sim_service,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "imsi does not exist" in exc_info.value.detail

    def test_get_market_share_not_found_404(
        self, mock_market_share_usage, api_security
    ):
        usage1 = 7854
        usage2 = 9857
        mock_market_share_usage = mock_market_share_usage(usage1, usage2)
        sim_service, from_date, to_date, imsi = mock_market_share_usage_data(
            sim_service=self.simcard, mock_market_share_usage=mock_market_share_usage
        )
        sim_service.get_market_share_imsi.side_effect = NotFound("Not Found")

        mock_auth, mock_request = api_security("/v1/glass/market/accounts/imsi")
        mock_request.method = "GET"

        with pytest.raises(HTTPException) as exc_info:
            get_market_share_imsi(
                mock_request,
                imsi=imsi,
                from_date=from_date,
                to_date=to_date,
                sim_service=sim_service,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert "Not Found" in exc_info.value.detail

    def test_get_market_share_unauthorised_401(
        self, mock_market_share_usage, api_security
    ):
        usage1 = 7854
        usage2 = 9857
        mock_market_share_usage = mock_market_share_usage(usage1, usage2)
        sim_service, from_date, to_date, imsi = mock_market_share_usage_data(
            sim_service=self.simcard, mock_market_share_usage=mock_market_share_usage
        )
        sim_service.get_market_share_imsi.side_effect = Unauthorized("Access Denied")

        mock_auth, mock_request = api_security("/v1/glass/market/accounts/imsi")
        mock_request.method = "GET"

        with pytest.raises(HTTPException) as exc_info:
            get_market_share_imsi(
                mock_request,
                imsi=imsi,
                from_date=from_date,
                to_date=to_date,
                sim_service=sim_service,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_401_UNAUTHORIZED
        assert "Access Denied" in exc_info.value.detail

    def test_get_market_share_unprocessable_422(
        self, mock_market_share_usage, api_security
    ):
        usage1 = 7854
        usage2 = 9857
        mock_market_share_usage = mock_market_share_usage(usage1, usage2)
        sim_service, from_date, to_date, imsi = mock_market_share_usage_data(
            sim_service=self.simcard, mock_market_share_usage=mock_market_share_usage
        )
        sim_service.get_market_share_imsi.side_effect = Unprocessable(
            "Unprocessable Entity"
        )

        mock_auth, mock_request = api_security("/v1/glass/market/accounts/imsi")
        mock_request.method = "GET"

        with pytest.raises(HTTPException) as exc_info:
            get_market_share_imsi(
                mock_request,
                imsi=imsi,
                from_date=from_date,
                to_date=to_date,
                sim_service=self.simcard,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        assert "Unprocessable Entity" in exc_info.value.detail

    def test_get_market_share_bad_request_400(
        self, mock_market_share_usage, api_security
    ):
        usage1 = 7854
        usage2 = 9857
        mock_market_share_usage = mock_market_share_usage(usage1, usage2)
        sim_service, from_date, to_date, imsi = mock_market_share_usage_data(
            sim_service=self.simcard, mock_market_share_usage=mock_market_share_usage
        )
        sim_service.get_market_share_imsi.side_effect = Exception("Error: Bad Request")

        mock_auth, mock_request = api_security("/v1/glass/market/accounts/imsi")
        mock_request.method = "GET"

        with pytest.raises(HTTPException) as exc_info:
            get_market_share_imsi(
                mock_request,
                imsi=imsi,
                from_date=from_date,
                to_date=to_date,
                sim_service=self.simcard,
                authorization=mock_auth,
            )
        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert "Error: Bad Request" in exc_info.value.detail
