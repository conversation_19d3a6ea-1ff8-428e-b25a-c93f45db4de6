import unittest
from tempfile import SpooledTemporaryFile
from unittest.mock import Magic<PERSON><PERSON>, Mock, patch
from uuid import uuid4

import pytest
from fastapi import BackgroundTasks, HTTPException, Request, status
from fastapi.testclient import TestClient

from accounts.services import AbstractAccountService
from api.authorization.examples import PERMISSIONS
from api.sim.endpoints import (
    create_empty_range,
    create_range,
    delete_range,
    get_ranges,
    get_sim_remains,
    get_sim_status,
    remove_allocation,
    remove_allocations,
)
from api.sim.examples import CREATE_RANGE_REQUEST, RANGES
from api.sim.schemas import ResponseModel, SIMCardsRemains
from app.main import app
from authorization.domain.ports import AbstractAuthorizationAPI
from common.constants import REQUEST_MESSAGE
from common.ordering import Ordering
from common.pagination import Pagination
from sim.domain import model
from sim.exceptions import (
    AllocationDeletionNotAllowed,
    AllocationDoesNotExist,
    PplUnknownSubscriber,
    RangeDoesNotExist,
    RangeIntegrityError,
    SimCardsNotFound,
)
from sim.services import AbstractSimService
from streaming.streaming import AbstractKafkaAPI


class FormFactor:
    MICRO = "micro"
    NANO = "nano"

    def __str__(self):
        return self.name


class TestGetRanges(unittest.TestCase):
    def test_get_ranges_success(self):

        mock_sim_service = Mock()
        # mock_ranges = RANGES
        mock_sim_service.get_ranges.return_value = ([], 0)

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = "https://testclient.com/v1/glass/sim/ranges"
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "GET"
        pagination = Pagination(page_size=1, page_offset=0, page=1)
        ordering = Ordering(order="ASC", order_by=[("created_at", "asc")], field="asc")
        searching = None
        # Act
        result = get_ranges(
            request=mock_request,
            sim_service=mock_sim_service,
            authorization=mock_auth,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )

        # Assert
        self.assertEqual(result.total_count, 0)
        self.assertEqual(result.results, [])

    def test_get_ranges_empty_result(self):
        # Arrange
        mock_sim_service = Mock()
        mock_sim_service.get_ranges.return_value = ([], 0)

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = "https://testclient.com/v1/glass/sim/ranges"
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "GET"

        pagination = Pagination(page=1, page_size=10, page_offset=0)
        ordering = Ordering(order="ASC", order_by=[("created_at", "asc")], field="asc")
        searching = None
        result = get_ranges(
            request=mock_request,
            sim_service=mock_sim_service,
            authorization=mock_auth,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )

        # Assert
        self.assertEqual(list(result.results), [])

    def test_get_ranges_error(self):
        # Arrange
        mock_sim_service = Mock()
        mock_sim_service.get_ranges.side_effect = HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = "https://testclient.com/v1/glass/sim/ranges"
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "GET"

        # Act and Assert
        with self.assertRaises(HTTPException) as context:
            get_ranges(
                request=mock_request,
                sim_service=mock_sim_service,
                authorization=mock_auth,
            )

        self.assertEqual(context.exception.status_code, 500)
        self.assertEqual(context.exception.detail, "Internal Server Error")

    @pytest.mark.skip(reason="Dictionary object issue")
    def test_create_empty_range_success(self):
        # Arrange
        mock_sim_service = Mock(spec=AbstractSimService)
        create_range_request = CREATE_RANGE_REQUEST
        expected_result = RANGES

        # Act

        result = create_empty_range(
            range_in=dict(create_range_request), sim_service=mock_sim_service
        )

        range_in_data = {"title": "Reference", "formFactor": "MICRO"}

        # Assert
        mock_sim_service.create_empty_range.assert_called_once_with(
            range_in_data, created_by="John Billing"
        )
        self.assertEqual(result, expected_result)

    def read_file(self, file_path) -> SpooledTemporaryFile:
        with open(file_path, "rb") as f:
            file_data = f.read()
        spooled_tempfile = SpooledTemporaryFile(mode="w+b")
        spooled_tempfile.write(file_data)
        spooled_tempfile.seek(0)
        return spooled_tempfile

    def test_create_range_success(self):
        mock_sim_service = Mock()
        form_data = Mock()
        form_data.file = Mock()
        form_data.file.file = Mock()
        form_data.title = "Reference"
        form_data.form_factor = "MICRO"
        form_data.file.filename = Mock()

        mock_sim_service.add_upload_file_status = Mock()

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_endpoint = Mock()
        mock_endpoint.__name__ = "create_range"

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/documents"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "POST"
        mock_request.scope = {"endpoint": mock_endpoint}

        mock_background_tasks = MagicMock(spec=BackgroundTasks)

        trace_id = uuid4()

        mock_account_service = MagicMock(spec=AbstractAccountService)
        mock_streaming_service = MagicMock(spec=AbstractKafkaAPI)

        with patch("api.sim.endpoints.SIMCardCSVParser") as mock_parser, patch(
            "api.sim.endpoints.validate_sim_cards"
        ) as mock_validate, patch(
            "api.sim.endpoints.validate_file_extension", return_value=True
        ), patch(
            "api.sim.endpoints.get_client_ip", return_value="127.0.0.1"
        ):

            mock_parser.return_value = "sim_cards_object"
            mock_validate.return_value = "valid_sim_cards"

            response = create_range(
                request=mock_request,
                background_tasks=mock_background_tasks,
                form_data=form_data,
                sim_service=mock_sim_service,
                authorization=mock_auth,
                trace_id=trace_id,
                account_service=mock_account_service,
                streaming_service=mock_streaming_service,
            )

        mock_sim_service.add_upload_file_status.assert_called_once()

        mock_background_tasks.add_task.assert_called_once_with(
            mock_sim_service.create_range,
            title=form_data.title,
            form_factor=form_data.form_factor,
            sim_cards="valid_sim_cards",
            client_ip="127.0.0.1",
            trace_id=trace_id,
            source_endpoint="Create Range",
            account_service=mock_account_service,
            streaming_service=mock_streaming_service,
        )

        assert isinstance(response, ResponseModel)
        assert response.message == REQUEST_MESSAGE
        assert response.request_id == str(trace_id)

    def test_create_range_failure_sim_error(self):
        # Mock dependencies to raise a SimError
        mock_sim_service = Mock()
        mock_sim_service.create_range.side_effect = HTTPException(
            status_code=status.HTTP_404_NOT_FOUND
        )

        # Create test client and call the endpoint
        client = TestClient(app)
        response = client.post(
            "/create_range", json={"title": "Test Range", "form_factor": "Micro"}
        )

        assert response.status_code == status.HTTP_404_NOT_FOUND

        # Check that the response body contains the error message from SimError
        assert response.json() == {"detail": "Not Found"}

    def test_create_range_failure_parsing_error(self):
        mock_sim_service = Mock()
        form_data = Mock()
        mock_sim_service.create_range.side_effect = HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST
        )

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/documents"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "POST"

        with self.assertRaises(HTTPException) as context:
            create_range(
                request=mock_request,
                form_data=form_data,
                sim_service=mock_sim_service,
                authorization=mock_auth,
                background_tasks=MagicMock(spec=BackgroundTasks),
            )

        self.assertEqual(context.exception.status_code, 400)
        self.assertEqual(context.exception.detail, "We Couldn't process the request.")

    def test_get_sim_status_success(self):
        # Mock dependencies
        mock_sim_service = Mock()
        imsi = "123456789012345"
        mock_sim_service.sim_status.return_value = model.SIMStatusResponse(
            reference_id="123",
            imsi="123456789012345",
            msisdn="9876543210",
            sim_status="ACTIVE",
        )

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/cards/status/{imsi}"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "GET"

        # Call the function
        response = get_sim_status(
            request=mock_request,
            imsi=imsi,
            sim_service=mock_sim_service,
            authorization=mock_auth,
        )

        # Check that the response is successful
        # assert response == model.SIMStatusResponse
        assert response.reference_id == 123
        assert response.imsi == "123456789012345"
        assert response.msisdn == "9876543210"
        assert response.sim_status == "ACTIVE"

    def test_get_sim_status_sim_cards_not_found(self):
        # Mock dependencies to raise SimCardsNotFound
        mock_sim_service = Mock()
        imsi = "123456789012345"
        mock_sim_service.sim_status.side_effect = SimCardsNotFound(
            "SIM cards not found"
        )

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/cards/status/{imsi}"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "GET"

        # Call the function and expect a 404 error
        with pytest.raises(HTTPException) as exc_info:
            get_sim_status(
                request=mock_request,
                imsi=imsi,
                sim_service=mock_sim_service,
                authorization=mock_auth,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "SIM cards not found"

    def test_get_sim_status_unknown_subscriber(self):
        # Mock dependencies to raise PplUnknownSubscriber
        mock_sim_service = Mock()
        imsi = "123456789012345"
        mock_sim_service.sim_status.side_effect = PplUnknownSubscriber(imsi)

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/cards/status/{imsi}"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "GET"

        # Call the function and expect a 400 error
        with pytest.raises(HTTPException) as exc_info:
            get_sim_status(
                request=mock_request,
                imsi=imsi,
                sim_service=mock_sim_service,
                authorization=mock_auth,
            )

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert exc_info.value.detail == "123456789012345 IMSI not allocated to BT."

    def test_delete_range_success(self):
        # Mock dependencies
        mock_sim_service = Mock()
        range_id = 1

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/ranges/{id}"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "DELETE"

        # Call the function
        delete_range(
            request=mock_request,
            range_id=range_id,
            sim_service=mock_sim_service,
            authorization=mock_auth,
        )

        # Check that the mock service was called with the correct parameters
        mock_sim_service.remove_range.assert_called_once_with(range_id)

    def test_delete_range_not_found(self):
        # Mock dependencies to raise RangeDoesNotExist
        mock_sim_service = Mock()
        range_id = 1
        mock_sim_service.remove_range.side_effect = RangeDoesNotExist(range_id)

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/ranges/{id}"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "DELETE"

        # Call the function and expect a 404 error
        with pytest.raises(HTTPException) as exc_info:
            delete_range(
                request=mock_request,
                range_id=range_id,
                sim_service=mock_sim_service,
                authorization=mock_auth,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "Range with id:1 does not exist."

    def test_delete_range_conflict(self):
        # Mock dependencies to raise RangeIntegrityError
        mock_sim_service = Mock()
        range_id = 1
        mock_sim_service.remove_range.side_effect = RangeIntegrityError(
            "Range integrity error"
        )

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/ranges/{id}"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "DELETE"

        # Call the function and expect a 409 error
        with pytest.raises(HTTPException) as exc_info:
            delete_range(
                request=mock_request,
                range_id=range_id,
                sim_service=mock_sim_service,
                authorization=mock_auth,
            )

        assert exc_info.value.status_code == status.HTTP_409_CONFLICT
        assert exc_info.value.detail == "Range integrity error"

    def test_remove_allocations_success(self):
        # Mock dependencies
        mock_sim_service = Mock()
        range_id = 1
        mock_sim_service.remove_allocations_by_range_id.return_value = None

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/allocations"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "DELETE"

        # Call the function
        result = remove_allocations(
            request=mock_request,
            range_id=range_id,
            sim_service=mock_sim_service,
            authorization=mock_auth,
        )

        # Check that the mock service was called with the correct parameters
        mock_sim_service.remove_allocations_by_range_id.assert_called_once_with(
            range_id
        )

        # Check that the result is None (as indicated by the function signature)
        assert result is None

    def test_remove_allocations_not_found(self):
        # Mock dependencies to raise RangeDoesNotExist
        mock_sim_service = Mock()
        range_id = 5
        mock_sim_service.remove_allocations_by_range_id.side_effect = RangeDoesNotExist(
            range_id
        )

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/allocations"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "DELETE"

        # Call the function and expect a 404 error
        with pytest.raises(HTTPException) as exc_info:
            remove_allocations(
                request=mock_request,
                range_id=range_id,
                sim_service=mock_sim_service,
                authorization=mock_auth,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "Range with id:5 does not exist."

    def test_remove_allocations_not_found_with_404(self):
        # Mock dependencies to raise RangeDoesNotExist
        mock_sim_service = Mock()
        allocation_id = 1
        mock_sim_service.remove_allocation.side_effect = AllocationDoesNotExist(
            allocation_id
        )

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/allocations"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "DELETE"

        # Call the function and expect a 404 error
        with pytest.raises(HTTPException) as exc_info:
            remove_allocation(
                request=mock_request,
                allocation_id=allocation_id,
                sim_service=mock_sim_service,
                authorization=mock_auth,
            )

        assert exc_info.value.status_code == status.HTTP_404_NOT_FOUND
        assert exc_info.value.detail == "Not Found"

    def test_remove_allocations_not_found_with_400(self):
        mock_sim_service = Mock()
        range_id = 1
        allocation_id = 1
        mock_sim_service.remove_allocation.side_effect = AllocationDeletionNotAllowed(
            range_id, allocation_id
        )

        mock_auth = MagicMock(spec=AbstractAuthorizationAPI)
        mock_auth.is_authorized.return_value = PERMISSIONS

        mock_request = MagicMock(spec=Request)
        mock_request.url_for.return_value = (
            "https://testclient.com/v1/glass/sim/allocations"
        )
        mock_request.base_url.scheme = "https"
        mock_request.base_url.netloc = "testclient.com"
        mock_request.method = "DELETE"

        # Call the function and expect a 404 error
        with pytest.raises(HTTPException) as exc_info:
            remove_allocation(
                request=mock_request,
                allocation_id=allocation_id,
                sim_service=mock_sim_service,
                authorization=mock_auth,
            )

        assert exc_info.value.status_code == status.HTTP_400_BAD_REQUEST
        assert (
            exc_info.value.detail
            == "Allocation with id: 1 must be the last in the range with id: 1."
        )

    @pytest.mark.skip("Need to fix")
    def test_get_sim_remains_success(self):
        # Mock dependencies
        mock_sim_service = Mock()
        mock_sim_service.get_sim_remains.return_value = [
            ("Provider1", FormFactor.MICRO, 10),
            ("Provider1", FormFactor.NANO, 20),
            ("Provider2", FormFactor.MICRO, 5),
        ]

        # Call the function
        response = get_sim_remains(sim_service=mock_sim_service)

        # Check that the response is a list of SIMCardsRemains
        assert isinstance(response, list)
        for item in response:
            assert isinstance(item, SIMCardsRemains)

        # Check that the values are correct based on the mock data
        assert response == [
            SIMCardsRemains(provider="Provider1", micro=10, nano=20),
            # nano is not present in this case
            SIMCardsRemains(provider="Provider2", micro=5, nano=None),
        ]

    def test_get_sim_remains_with_404(self):
        mock_sim_service = Mock()

        def override_dependency():
            return mock_sim_service

        with TestClient(app) as client:
            app.dependency_overrides[mock_sim_service.sim_service] = override_dependency

            response = client.get("/cards/remains")
            assert response.status_code == 404
