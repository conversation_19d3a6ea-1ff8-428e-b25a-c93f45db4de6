import pytest
from starlette import status

from api.health_check import HEALTH_CHECK_SECRET_HEADER
from app.config import settings


def test_ping_url_path(client):
    url = client.app.url_path_for("ping")
    assert url == "/v1/ping"


def test_healthy_url_path(client):
    url = client.app.url_path_for("healthy")
    assert url == "/v1/healthy"


@pytest.mark.parametrize("url_name", ["ping", "healthy"])
def test_health_check_without_secret_is_404(client, url_name):
    url = client.app.url_path_for(url_name)
    response = client.get(url)
    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {"detail": "Not Found"}


@pytest.mark.parametrize("url_name", ["ping", "healthy"])
def test_health_check_with_secret_is_ok(client, url_name):
    url = client.app.url_path_for("ping")
    response = client.get(
        url,
        headers={HEALTH_CHECK_SECRET_HEADER: settings.HEALTH_CHECK_SECRET},
    )
    assert response.status_code == status.HTTP_200_OK
