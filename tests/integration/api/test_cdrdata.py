import os
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON>ck
from uuid import UUID

import pytest
from botocore.exceptions import ClientError, ParamValidationError, ValidationError
from fastapi import HTTPException, Request

from api.cdrdata.endpoints import (
    download_file_from_s3,
    get_duplicate_cdr,
    real_time_usage_documents,
)
from api.cdrdata.examples import CDR_DATA_XML
from cdrdata.domain import model
from common.exceptions import AnalyticsAPIError
from common.types import Month


@pytest.fixture
def mock_cdr():
    mock_cdr_service = Mock()
    mock_cdr_storage = Mock()
    mock_error_cdr_storage = Mock()
    mock_request = MagicMock(spec=Request)
    mock_request.url_for.return_value = "https://testclient.com/v1/glass/cdr/documents"
    mock_request.base_url.scheme = "https"
    mock_request.base_url.netloc = "testclient.com"
    mock_request.method = "POST"
    mock_request.client.host = "127.0.0.1"
    return (
        mock_cdr_service,
        mock_cdr_storage,
        mock_error_cdr_storage,
        mock_request,
    )


class TestCDR:
    def test_real_time_usage_documents(self, mock_cdr):
        (
            mock_cdr_service,
            mock_cdr_storage,
            mock_error_cdr_storage,
            mock_request,
        ) = mock_cdr
        mock_cdr_service.is_valid_xml_cdr.return_value = True
        mock_cdr_service.is_duplicate_cdr.return_value = False
        mock_cdr_service.add_cdr.return_value = UUID(
            "8ed3c484-18ad-47e5-9dfb-eb5923be6731"
        )

        result = real_time_usage_documents(
            request=mock_request,
            xml_string=CDR_DATA_XML,
            cdr_service=mock_cdr_service,
            cdr_storage=mock_cdr_storage,
            error_cdr_storage=mock_error_cdr_storage,
        )
        assert isinstance(result["uuid"], UUID)

    def test_real_time_usage_documents_invalid_xml(self, mock_cdr):
        (
            mock_cdr_service,
            mock_cdr_storage,
            mock_error_cdr_storage,
            mock_request,
        ) = mock_cdr
        mock_cdr_service.is_valid_xml_cdr.return_value = True
        mock_cdr_service.is_duplicate_cdr.return_value = True
        real_time_usage_documents.side_effect = ValueError

        with pytest.raises(HTTPException) as e:
            real_time_usage_documents(
                request=mock_request,
                xml_string="<ID>10674668629</ID>",
                cdr_service=mock_cdr_service,
                cdr_storage=mock_cdr_storage,
                error_cdr_storage=mock_error_cdr_storage,
            )
        assert e.value.status_code == 422
        assert e.value.detail == "Invalid xml body."

    def test_real_time_usage_documents_successful(self, mock_cdr):
        (
            mock_cdr_service,
            mock_cdr_storage,
            mock_error_cdr_storage,
            mock_request,
        ) = mock_cdr
        mock_cdr_service.is_valid_xml_cdr.return_value = True
        mock_cdr_service.is_duplicate_cdr.return_value = False
        mock_cdr_service.add_cdr_to_analytics.return_value = model.AnalyticsCDRObject(
            cdr_object_id="abcd1234"
        )
        mock_cdr_service.add_cdr.return_value = UUID(
            "8ed3c484-18ad-47e5-9dfb-eb5923be6731"
        )

        mock_error_cdr_storage.error_cdr_file_storage.return_value = "file.xml"

        result = real_time_usage_documents(
            request=mock_request,
            xml_string=CDR_DATA_XML,
            cdr_service=mock_cdr_service,
            cdr_storage=mock_cdr_storage,
            error_cdr_storage=mock_error_cdr_storage,
        )
        assert isinstance(result["uuid"], UUID)

    def test_real_time_usage_documents_analytics_api_error(self, mock_cdr):
        (
            mock_cdr_service,
            mock_cdr_storage,
            mock_error_cdr_storage,
            mock_request,
        ) = mock_cdr
        mock_cdr_service.is_valid_xml_cdr.return_value = True
        mock_cdr_service.is_duplicate_cdr.return_value = False
        mock_cdr_service.add_cdr_to_analytics.side_effect = AnalyticsAPIError
        mock_cdr_service.add_cdr.return_value = UUID(
            "8ed3c484-18ad-47e5-9dfb-eb5923be6731"
        )

        mock_error_cdr_storage.error_cdr_file_storage.return_value = "file.xml"

        with pytest.raises(HTTPException) as e:
            real_time_usage_documents(
                request=mock_request,
                xml_string=CDR_DATA_XML,
                cdr_service=mock_cdr_service,
                cdr_storage=mock_cdr_storage,
                error_cdr_storage=mock_error_cdr_storage,
            )
        assert e.value.status_code == 403
        assert e.value.detail == "Analytics api error"

    def test_real_time_usage_documents_general_error(self, mock_cdr):
        (
            mock_cdr_service,
            mock_cdr_storage,
            mock_error_cdr_storage,
            mock_request,
        ) = mock_cdr
        mock_cdr_service.is_valid_xml_cdr.return_value = True
        mock_cdr_service.is_duplicate_cdr.return_value = False
        mock_cdr_service.add_cdr_to_analytics.side_effect = Exception
        mock_cdr_service.add_cdr.return_value = UUID(
            "8ed3c484-18ad-47e5-9dfb-eb5923be6731"
        )

        mock_error_cdr_storage.error_cdr_file_storage.return_value = "file.xml"

        with pytest.raises(HTTPException) as e:
            real_time_usage_documents(
                request=mock_request,
                xml_string=CDR_DATA_XML,
                cdr_service=mock_cdr_service,
                cdr_storage=mock_cdr_storage,
                error_cdr_storage=mock_error_cdr_storage,
            )
        assert e.value.status_code == 400
        assert e.value.detail == "We couldn't process your request."

    def test_get_duplicate_cdr_successful(self, mock_cdr):
        (
            mock_cdr_service,
            mock_cdr_storage,
            mock_error_cdr_storage,
            mock_request,
        ) = mock_cdr
        mock_cdr_service.get_duplicate_cdr.return_value = [
            model.DuplicateCdr(
                imsi="12341234567891",
                session_starttime="2022-04-03T18:47:05",
                session_endtime="2022-04-03T18:47:05",
                duration=40,
                data_volume=1024,
                num_duplicates=2,
                file_key=["file1.xml", "file2.xml"],
            )
        ]
        result = get_duplicate_cdr(
            month=Month(2024, 4, 1),
            cdr_service=mock_cdr_service,
        )
        assert isinstance(result, list)
        assert result == [
            model.DuplicateCdr(
                imsi="12341234567891",
                session_starttime="2022-04-03T18:47:05",
                session_endtime="2022-04-03T18:47:05",
                duration=40,
                data_volume=1024,
                num_duplicates=2,
                file_key=["file1.xml", "file2.xml"],
            )
        ]

    def test_get_duplicate_cdr_general_error(self, mock_cdr):
        (
            mock_cdr_service,
            mock_cdr_storage,
            mock_error_cdr_storage,
            mock_request,
        ) = mock_cdr
        mock_cdr_service.get_duplicate_cdr.side_effect = Exception
        with pytest.raises(HTTPException) as e:
            get_duplicate_cdr(
                month=Month(2024, 4, 1),
                cdr_service=mock_cdr_service,
            )
        print(e.value.detail)
        assert e.value.status_code == 400
        assert e.value.detail == "We couldn't process your request."

    def test_download_file_from_s3_successful(self, mock_cdr):
        (
            mock_cdr_service,
            mock_cdr_storage,
            mock_error_cdr_storage,
            mock_request,
        ) = mock_cdr
        path = os.path.abspath("file.xml")
        file_path = f"{os.path.dirname(path)}/file.xml"
        with open(file_path, "w") as file:
            file.write("content")
        mock_cdr_storage.download_file_to_local.return_value = file_path
        result = download_file_from_s3(
            file_key="file.xml", cdr_storage=mock_cdr_storage
        )
        assert result.status_code == 200
        assert result.headers["Content-Disposition"] == "attachment; filename=file.xml"
        assert not os.path.exists("/path/to/file.xml")

    def test_download_file_from_s3_file_notfound(self, mock_cdr):
        (
            mock_cdr_service,
            mock_cdr_storage,
            mock_error_cdr_storage,
            mock_request,
        ) = mock_cdr
        path = os.path.abspath("file.xml")
        file_path = f"{os.path.dirname(path)}/file.xml"
        mock_cdr_storage.download_file_to_local.return_value = file_path
        with pytest.raises(HTTPException) as e:
            download_file_from_s3(file_key="file.xml", cdr_storage=mock_cdr_storage)
        assert e.value.status_code == 404
        assert e.value.detail == "File not found"

    def test_download_file_from_s3_invalid_file(self, mock_cdr):
        (
            mock_cdr_service,
            mock_cdr_storage,
            mock_error_cdr_storage,
            mock_request,
        ) = mock_cdr
        mock_cdr_storage.download_file_to_local.return_value = None
        with pytest.raises(HTTPException) as e:
            download_file_from_s3(file_key="file.xml", cdr_storage=mock_cdr_storage)
        assert e.value.status_code == 404
        assert e.value.detail == "File not found"

    def test_download_file_from_s3_client_error(self, mock_cdr):
        (
            mock_cdr_service,
            mock_cdr_storage,
            mock_error_cdr_storage,
            mock_request,
        ) = mock_cdr
        mock_cdr_storage.download_file_to_local.side_effect = ClientError(
            error_response={}, operation_name="Get S3 file"
        )
        with pytest.raises(HTTPException) as e:
            download_file_from_s3(file_key="file.xml", cdr_storage=mock_cdr_storage)
        assert e.value.status_code == 404
        assert (
            e.value.detail == "An error occurred (Unknown) when calling the"
            " Get S3 file operation: Unknown"
        )

    def test_download_file_from_s3_validation_error(self, mock_cdr):
        (
            mock_cdr_service,
            mock_cdr_storage,
            mock_error_cdr_storage,
            mock_request,
        ) = mock_cdr
        mock_cdr_storage.download_file_to_local.side_effect = ValidationError
        with pytest.raises(HTTPException) as e:
            download_file_from_s3(file_key="file.xml", cdr_storage=mock_cdr_storage)
        assert e.value.status_code == 400
        assert e.value.detail == "'value'"

    def test_download_file_from_s3_param_validation(self, mock_cdr):
        (
            mock_cdr_service,
            mock_cdr_storage,
            mock_error_cdr_storage,
            mock_request,
        ) = mock_cdr
        mock_cdr_storage.download_file_to_local.side_effect = ParamValidationError
        with pytest.raises(HTTPException) as e:
            download_file_from_s3(file_key="file.xml", cdr_storage=mock_cdr_storage)
        assert e.value.status_code == 400
        assert e.value.detail == "'report'"
