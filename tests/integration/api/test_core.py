from unittest.mock import Magic<PERSON>ock

import pytest
from fastapi import HTT<PERSON>Ex<PERSON>

from api.core.endpoints import create_partitions
from api.core.schemas import Partitions, PartitionsList
from core.domain.model import CreatePartitionsResponse


@pytest.fixture
def valid_partitions_fixture():
    return PartitionsList(
        month=None, partitions=[Partitions.cdr, Partitions.cdr_data]  # Use enum values
    )


@pytest.fixture
def mock_dependencies():
    core_service_mock = MagicMock()
    request_mock = MagicMock()
    return {
        "core_service_mock": core_service_mock,
        "request_mock": request_mock,
        "valid_partitions_fixture": CreatePartitionsResponse(
            created=["partition1", "partition2"], skipped=[]
        ),
    }


class TestCreatePartitions:
    def test_create_partitions_success(
        self, mock_dependencies, valid_partitions_fixture
    ):
        core_service_mock = mock_dependencies["core_service_mock"]

        # Mock the return value to be a CreatePartitionsResponse object
        core_service_mock.create_partitions.return_value = CreatePartitionsResponse(
            created=["partition1", "partition2"], skipped=[]
        )

        response = create_partitions(
            partitions=valid_partitions_fixture,  # ✅ Use PartitionsList
            core_service=core_service_mock,
            trace_id="test-trace-id",
        )

        assert response.created == ["partition1", "partition2"]
        assert response.skipped == []

    def test_create_partitions_value_error(
        self, mock_dependencies, valid_partitions_fixture
    ):
        error_exception = ValueError("Invalid partition data")
        mock_dependencies[
            "core_service_mock"
        ].create_partitions.side_effect = error_exception

        with pytest.raises(HTTPException) as exc_info:
            create_partitions(
                partitions=valid_partitions_fixture,
                core_service=mock_dependencies["core_service_mock"],
                trace_id="test-trace-id",
            )

        assert exc_info.value.status_code == 400
        assert str(exc_info.value.detail) == "Invalid partition data"

    def test_create_partitions_unexpected_error(
        self, mock_dependencies, valid_partitions_fixture
    ):
        error_exception = Exception("Unexpected error")
        mock_dependencies[
            "core_service_mock"
        ].create_partitions.side_effect = error_exception

        with pytest.raises(HTTPException) as exc_info:
            create_partitions(
                partitions=valid_partitions_fixture,
                core_service=mock_dependencies["core_service_mock"],
                trace_id="test-trace-id",
            )

        assert exc_info.value.status_code == 500
        assert str(exc_info.value.detail) == "Unexpected error"
