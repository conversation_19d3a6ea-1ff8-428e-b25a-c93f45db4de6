{"info": {"_postman_id": "bf0cf5e0-79f3-4ab7-9523-78b6addfd72b", "name": "MarketShareAnalyticsService_CA", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "29298941", "_collection_link": "https://nextgenclearing.postman.co/workspace/QA_SIMplify_Team~82bc3228-fd80-437d-bfc3-fd6585e93620/collection/29298941-bf0cf5e0-79f3-4ab7-9523-78b6addfd72b?action=share&source=collection_link&creator=29298941"}, "item": [{"name": "Push CDR", "item": [{"name": "Push CDR(GBRME) - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const firstdate = moment().startOf('month').format('YYYY-MM-DD');\r", "pm.environment.set('fromDate', firstdate);\r", "\r", "var enddate = moment().add('months', 1).date(-0);\r", "pm.environment.set('toDate', enddate.format((\"YYYY-MM-DD\")));\r", "\r", "function getRandomTime() {\r", "    const randomSeconds = Math.floor(Math.random() * 86400);\r", "\r", "    const dateObj = new Date(randomSeconds * 1000);\r", "\r", "    const hours = dateObj.getUTCHours().toString().padStart(2, '0');\r", "    const minutes = dateObj.getUTCMinutes().toString().padStart(2, '0');\r", "    const seconds = dateObj.getUTCSeconds().toString().padStart(2, '0');\r", "\r", "    return `${hours}:${minutes}:${seconds}`;\r", "}\r", "\r", "const randomTime = getRandomTime();\r", "\r", "console.log(\"Generated Random Time:\", randomTime);\r", "pm.environment.set(\"randomTime\", randomTime);\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "<cdr>\r\n    <ID>10674668629</ID>\r\n    <Distributor>British Telecommunications</Distributor>\r\n    <DistributorID>15772316</DistributorID>\r\n    <CustomerGroup moniker=\"BT_Default\">British Telecommunications Default</CustomerGroup>\r\n    <Customer>883200000110320</Customer>\r\n    <CustomerID>15943419</CustomerID>\r\n    <CustomerReference>883200000110320</CustomerReference>\r\n    <Subscriber>883200000110320 {{IMSI_1}}</Subscriber>\r\n    <SubscriberID>15943420</SubscriberID>\r\n    <SubscriberReference>8944538532046590109</SubscriberReference>\r\n    <CreateTime>{{fromDate}} 18:47:05</CreateTime>\r\n    <ConnectTime>{{fromDate}} 18:47:05</ConnectTime>\r\n    <CallDate>{{fromDate}} 18:47:05</CallDate>\r\n    <EndTime>{{toDate}} {{randomTime}}</EndTime>\r\n    <Type>gsm-data</Type>\r\n    <Leg1>Data service</Leg1>\r\n    <Leg1TADIG>{{Carrier_Type1}}</Leg1TADIG>\r\n    <Leg1Country>United Kingdom</Leg1Country>\r\n    <Leg1Type>mobile</Leg1Type>\r\n    <Leg2></Leg2>\r\n    <Leg2Country></Leg2Country>\r\n    <Leg2Type></Leg2Type>\r\n    <Duration>20</Duration>\r\n    <Bytes>10</Bytes>\r\n    <Outcome>successful</Outcome>\r\n    <Narrative>Data Session (0.009 Mb consumed)</Narrative>\r\n    <BuyCurrency bottom-up-level=\"1\" top-down-level=\"2\" supplying-distributor-id=\"187715\" consuming-distributor-id=\"15772316\">GBP</BuyCurrency>\r\n    <BuyAmount bottom-up-level=\"1\" top-down-level=\"2\" supplying-distributor-id=\"187715\" consuming-distributor-id=\"15772316\">0.0000000000000</BuyAmount>\r\n    <RetailCurrency bottom-up-level=\"0\" top-down-level=\"3\" supplying-distributor-id=\"15772316\">GBP</RetailCurrency>\r\n    <RetailAmount bottom-up-level=\"0\" top-down-level=\"3\" supplying-distributor-id=\"15772316\">0.0000000000000</RetailAmount>\r\n    <Charges>\r\n        <Charge top-down-level=\"3\" bottom-up-level=\"0\" supplying-distributor-id=\"15772316\">\r\n            <Currency>GBP</Currency>\r\n            <Amount>0.0000000000000</Amount>\r\n            <Balance>0.0000</Balance>\r\n        </Charge>\r\n    </Charges>\r\n    <Legs>\r\n        <Leg>\r\n            <LegType>data</LegType>\r\n            <Country ISOCode=\"GBR\">United Kingdom</Country>\r\n            <ZoneType>mobile</ZoneType>\r\n            <Number></Number>\r\n            <RouteNumber>447953711126</RouteNumber>\r\n            <RateNumber>447953711126</RateNumber>\r\n            <Identity mcc=\"234\" mnc=\"58\"/>\r\n        </Leg>\r\n    </Legs>\r\n</cdr>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{BaseURL}}/cdr/documents", "host": ["{{BaseURL}}"], "path": ["cdr", "documents"]}}, "response": []}, {"name": "Push CDR(GBRVF)[0 Bytes] - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const firstdate = moment().startOf('month').format('YYYY-MM-DD');\r", "pm.environment.set('fromDate', firstdate);\r", "\r", "var enddate = moment().add('months', 1).date(-0);\r", "pm.environment.set('toDate', enddate.format((\"YYYY-MM-DD\")));\r", "\r", "function getRandomTime() {\r", "    const randomSeconds = Math.floor(Math.random() * 86400);\r", "\r", "    const dateObj = new Date(randomSeconds * 1000);\r", "\r", "    const hours = dateObj.getUTCHours().toString().padStart(2, '0');\r", "    const minutes = dateObj.getUTCMinutes().toString().padStart(2, '0');\r", "    const seconds = dateObj.getUTCSeconds().toString().padStart(2, '0');\r", "\r", "    return `${hours}:${minutes}:${seconds}`;\r", "}\r", "\r", "const randomTime = getRandomTime();\r", "\r", "console.log(\"Generated Random Time:\", randomTime);\r", "pm.environment.set(\"randomTime\", randomTime);\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "<cdr>\r\n    <ID>10674668629</ID>\r\n    <Distributor>British Telecommunications</Distributor>\r\n    <DistributorID>15772316</DistributorID>\r\n    <CustomerGroup moniker=\"BT_Default\">British Telecommunications Default</CustomerGroup>\r\n    <Customer>883200000110320</Customer>\r\n    <CustomerID>15943419</CustomerID>\r\n    <CustomerReference>883200000110320</CustomerReference>\r\n    <Subscriber>883200000110320 {{IMSI_1}}</Subscriber>\r\n    <SubscriberID>15943420</SubscriberID>\r\n    <SubscriberReference>8944538532046590109</SubscriberReference>\r\n    <CreateTime>{{fromDate}} 18:47:05</CreateTime>\r\n    <ConnectTime>{{fromDate}} 18:47:05</ConnectTime>\r\n    <CallDate>{{fromDate}} 18:47:05</CallDate>\r\n    <EndTime>{{toDate}} {{randomTime}}</EndTime>\r\n    <Type>gsm-data</Type>\r\n    <Leg1>Data service</Leg1>\r\n    <Leg1TADIG>{{Carrier_Type2}}</Leg1TADIG>\r\n    <Leg1Country>United Kingdom</Leg1Country>\r\n    <Leg1Type>mobile</Leg1Type>\r\n    <Leg2></Leg2>\r\n    <Leg2Country></Leg2Country>\r\n    <Leg2Type></Leg2Type>\r\n    <Duration>20</Duration>\r\n    <Bytes>0</Bytes>\r\n    <Outcome>successful</Outcome>\r\n    <Narrative>Data Session (0.009 Mb consumed)</Narrative>\r\n    <BuyCurrency bottom-up-level=\"1\" top-down-level=\"2\" supplying-distributor-id=\"187715\" consuming-distributor-id=\"15772316\">GBP</BuyCurrency>\r\n    <BuyAmount bottom-up-level=\"1\" top-down-level=\"2\" supplying-distributor-id=\"187715\" consuming-distributor-id=\"15772316\">0.0000000000000</BuyAmount>\r\n    <RetailCurrency bottom-up-level=\"0\" top-down-level=\"3\" supplying-distributor-id=\"15772316\">GBP</RetailCurrency>\r\n    <RetailAmount bottom-up-level=\"0\" top-down-level=\"3\" supplying-distributor-id=\"15772316\">0.0000000000000</RetailAmount>\r\n    <Charges>\r\n        <Charge top-down-level=\"3\" bottom-up-level=\"0\" supplying-distributor-id=\"15772316\">\r\n            <Currency>GBP</Currency>\r\n            <Amount>0.0000000000000</Amount>\r\n            <Balance>0.0000</Balance>\r\n        </Charge>\r\n    </Charges>\r\n    <Legs>\r\n        <Leg>\r\n            <LegType>data</LegType>\r\n            <Country ISOCode=\"GBR\">United Kingdom</Country>\r\n            <ZoneType>mobile</ZoneType>\r\n            <Number></Number>\r\n            <RouteNumber>447953711126</RouteNumber>\r\n            <RateNumber>447953711126</RateNumber>\r\n            <Identity mcc=\"234\" mnc=\"58\"/>\r\n        </Leg>\r\n    </Legs>\r\n</cdr>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{BaseURL}}/cdr/documents", "host": ["{{BaseURL}}"], "path": ["cdr", "documents"]}}, "response": []}, {"name": "Push CDR Market Share - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "var jsonData = pm.response.json();\r", "pm.environment.set(\"CDR_ID\", jsonData.cdrObjectId);\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const firstdate = moment().startOf('month').format('YYYY-MM-DD');\r", "pm.environment.set('fromDate', firstdate);\r", "\r", "var enddate = moment().add('months', 1).date(-0);\r", "pm.environment.set('toDate', enddate.format((\"YYYY-MM-DD\")));\r", "\r", "function getRandomTime() {\r", "    const randomSeconds = Math.floor(Math.random() * 86400);\r", "\r", "    const dateObj = new Date(randomSeconds * 1000);\r", "\r", "    const hours = dateObj.getUTCHours().toString().padStart(2, '0');\r", "    const minutes = dateObj.getUTCMinutes().toString().padStart(2, '0');\r", "    const seconds = dateObj.getUTCSeconds().toString().padStart(2, '0');\r", "\r", "    return `${hours}:${minutes}:${seconds}`;\r", "}\r", "\r", "const randomTime = getRandomTime();\r", "\r", "console.log(\"Generated Random Time:\", randomTime);\r", "pm.environment.set(\"randomTime\", randomTime);\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"s3_key\": \"2024_12_23_cdr.xml\",\r\n  \"cdr\": {\r\n    \"Subscriber\": \"{{MSISDN}} {{IMSI}}\",\r\n    \"SubscriberReference\": \"{{ICCID}}\",\r\n    \"CreateTime\": \"{{fromDate}} 18:47:05\",\r\n    \"EndTime\": \"{{toDate}} {{randomTime}}\",\r\n    \"Type\": \"gsm-data\",\r\n    \"Leg1TADIG\": \"{{Carrier_Type3}}\",\r\n    \"Duration\": \"40\",\r\n    \"Bytes\": 1\r\n  }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/pushcdr", "host": ["{{BaseURL}}"], "path": ["pushcdr"]}}, "response": []}, {"name": "Push CDR Market Share [0 Bytes] - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "var jsonData = pm.response.json();\r", "pm.environment.set(\"CDR_ID\", jsonData.cdrObjectId);\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const firstdate = moment().startOf('month').format('YYYY-MM-DD');\r", "pm.environment.set('fromDate', firstdate);\r", "\r", "var enddate = moment().add('months', 1).date(-0);\r", "pm.environment.set('toDate', enddate.format((\"YYYY-MM-DD\")));\r", "\r", "function getRandomTime() {\r", "    const randomSeconds = Math.floor(Math.random() * 86400);\r", "\r", "    const dateObj = new Date(randomSeconds * 1000);\r", "\r", "    const hours = dateObj.getUTCHours().toString().padStart(2, '0');\r", "    const minutes = dateObj.getUTCMinutes().toString().padStart(2, '0');\r", "    const seconds = dateObj.getUTCSeconds().toString().padStart(2, '0');\r", "\r", "    return `${hours}:${minutes}:${seconds}`;\r", "}\r", "\r", "const randomTime = getRandomTime();\r", "\r", "console.log(\"Generated Random Time:\", randomTime);\r", "pm.environment.set(\"randomTime\", randomTime);\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"s3_key\": \"2024_12_23_cdr.xml\",\r\n  \"cdr\": {\r\n    \"Subscriber\": \"{{MSISDN}} {{IMSI}}\",\r\n    \"SubscriberReference\": \"{{ICCID}}\",\r\n    \"CreateTime\": \"{{fromDate}} 18:47:05\",\r\n    \"EndTime\": \"{{toDate}} {{randomTime}}\",\r\n    \"Type\": \"gsm-data\",\r\n    \"Leg1TADIG\": \"{{Carrier_Type3}}\",\r\n    \"Duration\": \"40\",\r\n    \"Bytes\": 0\r\n  }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/pushcdr", "host": ["{{BaseURL}}"], "path": ["pushcdr"]}}, "response": []}]}, {"name": "Market Share IMSIS", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Market Share IMSIS - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", " pm.test(\"Response body has 'totalUsage' key\", function () {\r", "     const responseBody = pm.response.json();\r", "     pm.expect(responseBody).to.be.hasOwnProperty('totalUsage')\r", " });\r", "\r", " pm.test(\"Response body has 'summary' key\", function () {\r", "     const responseBody = pm.response.json();\r", "     pm.expect(responseBody).to.be.hasOwnProperty('summary')\r", " });"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}, {"name": "Market Share IMSIS - 404 Not Found", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/ims", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "ims"]}}, "response": []}, {"name": "Market Share IMSIS - Method Not Allowed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}, {"name": "Market Share IMSIS - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}, {"name": "Market Share IMSIS - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}, {"name": "Market Share IMSIS - Single IMSI", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\"\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}, {"name": "Market Share IMSI's - Invalid IMSI", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"2345885yuur70fdfd01\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}, {"name": "Market Share IMSI's - Blank IMSI", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"2023-04-01\",\r\n  \"toDate\": \"2023-04-30\",\r\n  \"imsis\": [\r\n    \"\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}, {"name": "Market Share IMSI's - Max IMSI Length", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"234588570010027452\",\r\n    \"{{IMSI}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}, {"name": "Market Share IMSI's - Min IMSI Length", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"2023-04-01\",\r\n  \"toDate\": \"2023-04-30\",\r\n  \"imsis\": [\r\n    \"234588\",\r\n    \"234588560667870\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}, {"name": "Market Share IMSIS - Blank FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}, {"name": "Market Share IMSIS - Invalid days FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"2023-04-011\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}, {"name": "Market Share IMSIS - Blank days FROM  date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"2023-04-\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}, {"name": "Market Share IMSIS - Invalid month FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"2023-13-01\",\r\n  \"toDate\": \"{{fromDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}, {"name": "Market Share IMSIS - Blank month FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"2023--01\",\r\n  \"toDate\": \"{{fromDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}, {"name": "Market Share IMSIS - Invalid year FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"202-04-01\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}, {"name": "Market Share IMSIS - Blank year FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"-04-01\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}, {"name": "Market Share IMSIS - Blank TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}, {"name": "Market Share IMSIS - Invalid days TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"2023-04-32\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}, {"name": "Market Share IMSIS - Blank days TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"2023-04-\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}, {"name": "Market Share IMSIS - Invalid month TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"2023-13-30\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}, {"name": "Market Share IMSIS - Blank month TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"2023--30\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}, {"name": "Market Share IMSIS - Invalid year TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"202-04-30\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}, {"name": "Market Share IMSIS - Blank year TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"-04-30\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/imsis", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "imsis"]}}, "response": []}]}, {"name": "Market Share Carrier Summary", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Market Share IMSIS - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", " pm.test(\"Response body has 'totalUsage' key\", function () {\r", "     const responseBody = pm.response.json();\r", "     pm.expect(responseBody).to.be.hasOwnProperty('totalUsage')\r", " });\r", "\r", " pm.test(\"Response body has 'summary' key\", function () {\r", "     const responseBody = pm.response.json();\r", "     pm.expect(responseBody).to.be.hasOwnProperty('summary')\r", " });\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSIS - 404 Not Found", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}    \"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/mark/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "mark", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSIS - Method Not Allowed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSIS - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSIS - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSIS - Single IMSI", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSI's - Invalid IMSI", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"23458857001\",\r\n    \"{{IMSI}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSI's - Blank IMSI", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSI's - Max IMSI Length", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"234588570010027452\",\r\n    \"{{IMSI}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSI's - Min IMSI Length", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"234588\",\r\n    \"{{IMSI}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSIS - Blank FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}    \"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSIS - Invalid days FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"2023-04-011\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}    \"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSIS - Blank days FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"2023-04-\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}    \"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSIS - Invalid month FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"2023-13-01\",\r\n  \"toDate\": \"2023-04-30\",\r\n  \"imsis\": [\r\n    \"234588570010027\",\r\n    \"234588560667870\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSIS - Blank month FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"2023--01\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}    \"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSIS - Invalid year FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"202-04-01\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n      \"{{IMSI}}\",\r\n    \"{{IMSI_1}}    \"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSIS - Blank year FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"-04-01\",\r\n  \"toDate\": \"{{toDate}}\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}    \"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSIS - Blank TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}    \"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSIS - Invalid days TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"2023-04-32\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}    \"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSIS - Blank days TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"2023-04-\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}    \"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSIS - Invalid month TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"2023-13-30\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}    \"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSIS - Blank month TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"2023--30\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}    \"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSIS - Invalid year TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"{{fromDate}}\",\r\n  \"toDate\": \"202-04-30\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}    \"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}, {"name": "Market Share IMSIS - Blank year TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"fromDate\": \"2023-04-01\",\r\n  \"toDate\": \"-04-30\",\r\n  \"imsis\": [\r\n    \"{{IMSI}}\",\r\n    \"{{IMSI_1}}    \"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/analytics/market/carrier/summary", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "carrier", "summary"]}}, "response": []}]}, {"name": "IMSI Usage Monthly", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "IMSI Usage Monthly - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/analytics/market/usage/{{IMSI}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "usage", "{{IMSI}}"]}}, "response": []}, {"name": "IMSI Usage Monthly - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/analytics/market/usage/{{IMSI}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "usage", "{{IMSI}}"]}}, "response": []}, {"name": "IMSI Usage Monthly - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/analytics/market/usage/{{IMSI}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "usage", "{{IMSI}}"]}}, "response": []}, {"name": "IMSI Usage Monthly - Response time <500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/analytics/market/usage/{{IMSI}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "market", "usage", "{{IMSI}}"]}}, "response": []}]}, {"name": "Add Account Rule (Redis)", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Rate Plans by Accounts using ID - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "var jsonData = pm.response.json();\r", "pm.environment.set(\"Source_RatePlan\", jsonData.ratePlans[0].id);\r", "\r", "var jsonData = pm.response.json();\r", "pm.environment.set(\"Target_RatePlan\", jsonData.ratePlans[1].id);\r", "\r", "var jsonData = pm.response.json();\r", "pm.environment.set(\"rateplan_id\", jsonData.ratePlans[1].id);\r", "\r", "\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/by-accounts/{{account_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "by-accounts", "{{account_id}}"]}}, "response": []}, {"name": "Create Rule (Monoglass) - 201 OK", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData = pm.response.json();\r", "pm.environment.set(\"rule_uuid\", jsonData.uuids[0].uuid);\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"lock\": false,\r\n  \"action\": {\r\n    \"reactivateSim\": {\r\n      \"value\": \"Next billing cycle\"\r\n    },\r\n    \"ratePlan\": {\r\n      \"source\": {{Source_RatePlan}},\r\n      \"target\": {{Target_RatePlan}},\r\n      \"value\": \"Return to the previous rate plan\"\r\n    }\r\n  },\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Add Account Rule - 201 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"IMSIs\": [\r\n    \"{{IMSI}}\"\r\n  ],\r\n  \"accountId\": {{account_id}},\r\n  \"accountName\": \"{{Rule_Name}}\",\r\n  \"rules\": [\r\n    {\r\n      \"status\": true,\r\n      \"rulesUuidParam\": \"{{rule_uuid}}\",\r\n      \"simUsageLimit\": {{Data_Volume}},\r\n      \"unit\": \"KB\",\r\n      \"sizeInBytes\": 1526784,\r\n      \"actions\": [\r\n        {\r\n          \"name\": \"Deactivate SIM\",\r\n          \"action\": \"Reactivate Sim\",\r\n          \"actionValue\": \"Next billing cycle\"\r\n        }\r\n      ],\r\n      \"notifications\": [\r\n        {\r\n          \"name\": \"Send email\",\r\n          \"notification\": true,\r\n          \"email\": [\r\n            \"<EMAIL>\"\r\n          ]\r\n        }\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rule", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rule"]}}, "response": []}, {"name": "Add Account Rule - Blank IMSIs", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"IMSIs\": [\r\n    \"\"\r\n  ],\r\n  \"accountId\": {{account_id}},\r\n  \"accountName\": \"{{Rule_Name}}\",\r\n  \"rules\": [\r\n    {\r\n      \"status\": true,\r\n      \"rulesUuidParam\": \"{{rule_uuid}}\",\r\n      \"simUsageLimit\": {{Data_Volume}},\r\n      \"unit\": \"KB\",\r\n      \"sizeInBytes\": 1526784,\r\n      \"actions\": [\r\n        {\r\n          \"name\": \"Deactivate SIM\",\r\n          \"action\": \"Reactivate Sim\",\r\n          \"actionValue\": \"Next billing cycle\"\r\n        }\r\n      ],\r\n      \"notifications\": [\r\n        {\r\n          \"name\": \"Send email\",\r\n          \"notification\": true,\r\n          \"email\": [\r\n            \"<EMAIL>\"\r\n          ]\r\n        }\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rule", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rule"]}}, "response": []}, {"name": "Add Account Rule - Without IMSIs", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{account_id}},\r\n  \"accountName\": \"{{Rule_Name}}\",\r\n  \"rules\": [\r\n    {\r\n      \"status\": true,\r\n      \"rulesUuidParam\": \"{{rule_uuid}}\",\r\n      \"simUsageLimit\": {{Data_Volume}},\r\n      \"unit\": \"KB\",\r\n      \"sizeInBytes\": 1526784,\r\n      \"actions\": [\r\n        {\r\n          \"name\": \"Deactivate SIM\",\r\n          \"action\": \"Reactivate Sim\",\r\n          \"actionValue\": \"Next billing cycle\"\r\n        }\r\n      ],\r\n      \"notifications\": [\r\n        {\r\n          \"name\": \"Send email\",\r\n          \"notification\": true,\r\n          \"email\": [\r\n            \"<EMAIL>\"\r\n          ]\r\n        }\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rule", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rule"]}}, "response": []}, {"name": "Add Account Rule -  Invalid Account ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"IMSIs\": [\r\n    \"{{IMSI}}\"\r\n  ],\r\n  \"accountId\": \"dsgfb\",\r\n  \"accountName\": \"{{Rule_Name}}\",\r\n  \"rules\": [\r\n    {\r\n      \"status\": true,\r\n      \"rulesUuidParam\": \"{{rule_uuid}}\",\r\n      \"simUsageLimit\": {{Data_Volume}},\r\n      \"unit\": \"KB\",\r\n      \"sizeInBytes\": 1526784,\r\n      \"actions\": [\r\n        {\r\n          \"name\": \"Deactivate SIM\",\r\n          \"action\": \"Reactivate Sim\",\r\n          \"actionValue\": \"Next billing cycle\"\r\n        }\r\n      ],\r\n      \"notifications\": [\r\n        {\r\n          \"name\": \"Send email\",\r\n          \"notification\": true,\r\n          \"email\": [\r\n            \"<EMAIL>\"\r\n          ]\r\n        }\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rule", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rule"]}}, "response": []}, {"name": "Add Account Rule -  Without Account ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"IMSIs\": [\r\n    \"{{IMSI}}\"\r\n  ],\r\n  \"accountName\": \"{{Rule_Name}}\",\r\n  \"rules\": [\r\n    {\r\n      \"status\": true,\r\n      \"rulesUuidParam\": \"{{rule_uuid}}\",\r\n      \"simUsageLimit\": {{Data_Volume}},\r\n      \"unit\": \"KB\",\r\n      \"sizeInBytes\": 1526784,\r\n      \"actions\": [\r\n        {\r\n          \"name\": \"Deactivate SIM\",\r\n          \"action\": \"Reactivate Sim\",\r\n          \"actionValue\": \"Next billing cycle\"\r\n        }\r\n      ],\r\n      \"notifications\": [\r\n        {\r\n          \"name\": \"Send email\",\r\n          \"notification\": true,\r\n          \"email\": [\r\n            \"<EMAIL>\"\r\n          ]\r\n        }\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rule", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rule"]}}, "response": []}, {"name": "Add Account Rule - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"IMSIs\": [\r\n    \"{{IMSI}}\"\r\n  ],\r\n  \"accountId\": {{account_id}},\r\n  \"accountName\": \"{{Rule_Name}}\",\r\n  \"rules\": [\r\n    {\r\n      \"status\": true,\r\n      \"rulesUuidParam\": \"{{rule_uuid}}\",\r\n      \"simUsageLimit\": {{Data_Volume}},\r\n      \"unit\": \"KB\",\r\n      \"sizeInBytes\": 1526784,\r\n      \"actions\": [\r\n        {\r\n          \"name\": \"Deactivate SIM\",\r\n          \"action\": \"Reactivate Sim\",\r\n          \"actionValue\": \"Next billing cycle\"\r\n        }\r\n      ],\r\n      \"notifications\": [\r\n        {\r\n          \"name\": \"Send email\",\r\n          \"notification\": true,\r\n          \"email\": [\r\n            \"<EMAIL>\"\r\n          ]\r\n        }\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rul", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rul"]}}, "response": []}, {"name": "Add Account Rule - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"IMSIs\": [\r\n    \"{{IMSI}}\"\r\n  ],\r\n  \"accountId\": {{account_id}},\r\n  \"accountName\": \"{{Rule_Name}}\",\r\n  \"rules\": [\r\n    {\r\n      \"status\": true,\r\n      \"rulesUuidParam\": \"{{rule_uuid}}\",\r\n      \"simUsageLimit\": {{Data_Volume}},\r\n      \"unit\": \"KB\",\r\n      \"sizeInBytes\": 1526784,\r\n      \"actions\": [\r\n        {\r\n          \"name\": \"Deactivate SIM\",\r\n          \"action\": \"Reactivate Sim\",\r\n          \"actionValue\": \"Next billing cycle\"\r\n        }\r\n      ],\r\n      \"notifications\": [\r\n        {\r\n          \"name\": \"Send email\",\r\n          \"notification\": true,\r\n          \"email\": [\r\n            \"<EMAIL>\"\r\n          ]\r\n        }\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytic/rule", "host": ["{{BaseURL}}"], "path": ["v1", "analytic", "rule"]}}, "response": []}, {"name": "Add Account Rule - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"IMSIs\": [\r\n    \"{{IMSI}}\"\r\n  ],\r\n  \"accountId\": {{account_id}},\r\n  \"accountName\": \"{{Rule_Name}}\",\r\n  \"rules\": [\r\n    {\r\n      \"status\": true,\r\n      \"rulesUuidParam\": \"{{rule_uuid}}\",\r\n      \"simUsageLimit\": {{Data_Volume}},\r\n      \"unit\": \"KB\",\r\n      \"sizeInBytes\": 1526784,\r\n      \"actions\": [\r\n        {\r\n          \"name\": \"Deactivate SIM\",\r\n          \"action\": \"Reactivate Sim\",\r\n          \"actionValue\": \"Next billing cycle\"\r\n        }\r\n      ],\r\n      \"notifications\": [\r\n        {\r\n          \"name\": \"Send email\",\r\n          \"notification\": true,\r\n          \"email\": [\r\n            \"<EMAIL>\"\r\n          ]\r\n        }\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rule", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rule"]}}, "response": []}, {"name": "Add Account Rule - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", "\r", "pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"IMSIs\": [\r\n    \"{{IMSI}}\"\r\n  ],\r\n  \"accountId\": {{account_id}},\r\n  \"accountName\": \"{{Rule_Name}}\",\r\n  \"rules\": [\r\n    {\r\n      \"status\": true,\r\n      \"rulesUuidParam\": \"{{rule_uuid}}\",\r\n      \"simUsageLimit\": {{Data_Volume}},\r\n      \"unit\": \"KB\",\r\n      \"sizeInBytes\": 1526784,\r\n      \"actions\": [\r\n        {\r\n          \"name\": \"Deactivate SIM\",\r\n          \"action\": \"Reactivate Sim\",\r\n          \"actionValue\": \"Next billing cycle\"\r\n        }\r\n      ],\r\n      \"notifications\": [\r\n        {\r\n          \"name\": \"Send email\",\r\n          \"notification\": true,\r\n          \"email\": [\r\n            \"<EMAIL>\"\r\n          ]\r\n        }\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rule", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rule"]}}, "response": []}]}, {"name": "Get Rule By IMSI (Redis)", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Rule By IMSI - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/rules/{{IMSI}}/{{ICCID}}/{{CDR_ID}}", "host": ["{{BaseURL}}"], "path": ["rules", "{{IMSI}}", "{{ICCID}}", "{{CDR_ID}}"]}}, "response": []}, {"name": "Get Rule By IMSI - Blank IMSI", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/rules/{{}}/{{ICCID}}/{{CDR_ID}}", "host": ["{{BaseURL}}"], "path": ["rules", "{{}}", "{{ICCID}}", "{{CDR_ID}}"]}}, "response": []}, {"name": "Get Rule By IMSI - Invalid IMSI", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/rules/64544/{{ICCID}}/{{CDR_ID}}", "host": ["{{BaseURL}}"], "path": ["rules", "64544", "{{ICCID}}", "{{CDR_ID}}"]}}, "response": []}, {"name": "Get Rule By IMSI - Blank ICCID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/rules/{{IMSI}}/{{}}/{{CDR_ID}}", "host": ["{{BaseURL}}"], "path": ["rules", "{{IMSI}}", "{{}}", "{{CDR_ID}}"]}}, "response": []}, {"name": "Get Rule By IMSI - Invalid ICCID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/rules/{{IMSI}}/5656548445/{{CDR_ID}}", "host": ["{{BaseURL}}"], "path": ["rules", "{{IMSI}}", "5656548445", "{{CDR_ID}}"]}}, "response": []}, {"name": "Get Rule By IMSI - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/rules/{{IMSI}}/{{ICCID}}/{{CDR_ID}}", "host": ["{{BaseURL}}"], "path": ["rules", "{{IMSI}}", "{{ICCID}}", "{{CDR_ID}}"]}}, "response": []}, {"name": "Get Rule By IMSI - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/rules/{{IMSI}}/{{ICCID}}/{{CDR_ID}}", "host": ["{{BaseURL}}"], "path": ["rules", "{{IMSI}}", "{{ICCID}}", "{{CDR_ID}}"]}}, "response": []}, {"name": "Get Rule By IMSI - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", "\r", "pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/rules/{{IMSI}}/{{ICCID}}/{{CDR_ID}}", "host": ["{{BaseURL}}"], "path": ["rules", "{{IMSI}}", "{{ICCID}}", "{{CDR_ID}}"]}}, "response": []}]}, {"name": "Update IMSI List (Redis)", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Update IMSI List - 202 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 202\", function () {\r", "    pm.response.to.have.status(202);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"IMSIs\": [\n    \"{{IMSI}}\"\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/imsis/account/{{account_id}}/rateplan/{{rateplan_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "imsis", "account", "{{account_id}}", "rateplan", "{{rateplan_id}}"]}}, "response": []}, {"name": "Update IMSI List - Blank IMSIS", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"IMSIs\": [\r\n    \"{{}}\"\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/imsis/account/{{account_id}}/rateplan/{{rateplan_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "imsis", "account", "{{account_id}}", "rateplan", "{{rateplan_id}}"]}}, "response": []}, {"name": "Update IMSI List - Invalid IMSIS", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"IMSIs\": [\r\n    \"0045455\"\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/imsis/account/{{account_id}}/rateplan/{{rateplan_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "imsis", "account", "{{account_id}}", "rateplan", "{{rateplan_id}}"]}}, "response": []}, {"name": "Update IMSI List - Blank Account ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"IMSIs\": [\r\n    \"{{IMSI}}\"\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/imsis/account/{{}}/rateplan/{{rateplan_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "imsis", "account", "{{}}", "rateplan", "{{rateplan_id}}"]}}, "response": []}, {"name": "Update IMSI List - Invalid Account ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"IMSIs\": [\r\n    \"{{IMSI}}\"\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/imsis/account/hjdsbjbhf/rateplan/{{rateplan_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "imsis", "account", "hjdsbjbhf", "rateplan", "{{rateplan_id}}"]}}, "response": []}, {"name": "Update IMSI List - Without Account ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"IMSIs\": [\r\n    \"{{IMSI}}\"\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/imsis/account/rateplan/{{rateplan_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "imsis", "account", "rateplan", "{{rateplan_id}}"]}}, "response": []}, {"name": "Update IMSI List - Blank Rate Plan ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"IMSIs\": [\n    \"{{IMSI}}\"\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/imsis/account/{{account_id}}/rateplan/{{}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "imsis", "account", "{{account_id}}", "rateplan", "{{}}"]}}, "response": []}, {"name": "Update IMSI List - Invalid Rate Plan ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"IMSIs\": [\n    \"{{IMSI}}\"\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/imsis/account/{{account_id}}/rateplan/abcd", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "imsis", "account", "{{account_id}}", "rateplan", "abcd"]}}, "response": []}, {"name": "Update IMSI List - Without Rate Plan ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"IMSIs\": [\n    \"{{IMSI}}\"\n  ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/imsis/account/{{account_id}}/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "imsis", "account", "{{account_id}}", "rateplan"]}}, "response": []}, {"name": "Update IMSI List - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"IMSIs\": [\r\n    \"{{IMSI}}\"\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/imsi/account/{{account_id}}/rateplan/{{rateplan_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "imsi", "account", "{{account_id}}", "rateplan", "{{rateplan_id}}"]}}, "response": []}, {"name": "Update IMSI List - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"IMSIs\": [\r\n    \"{{IMSI}}\"\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/imsis/account/{{account_id}}/rateplan/{{rateplan_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "imsis", "account", "{{account_id}}", "rateplan", "{{rateplan_id}}"]}}, "response": []}, {"name": "Update IMSI List - Unauthoized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"IMSIs\": [\r\n    \"\"\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/imsis/account/{{account_id}}/rateplan/{{rateplan_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "imsis", "account", "{{account_id}}", "rateplan", "{{rateplan_id}}"]}}, "response": []}, {"name": "Update IMSI List - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", "\r", "pm.test(\"Status code is 202\", function () {\r", "    pm.response.to.have.status(202);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"IMSIs\": [\r\n    \"{{IMSI}}\"\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/imsis/account/{{account_id}}/rateplan/{{rateplan_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "imsis", "account", "{{account_id}}", "rateplan", "{{rateplan_id}}"]}}, "response": []}]}, {"name": "Update Rule (Redis)", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Update Rule - 202 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 202\", function () {\r", "    pm.response.to.have.status(202);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"status\": true,\r\n  \"simUsageLimit\": 1,\r\n  \"unit\": \"KB\",\r\n  \"sizeInBytes\": 1024,\r\n  \"actions\": [],\r\n  \"notifications\": []\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rule/{{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rule", "{{rule_uuid}}"]}}, "response": []}, {"name": "Update Rule - Blank UUID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"status\": true,\r\n  \"simUsageLimit\": 1,\r\n  \"unit\": \"KB\",\r\n  \"sizeInBytes\": 1024,\r\n  \"actions\": [],\r\n  \"notifications\": []\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rule/{{}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rule", "{{}}"]}}, "response": []}, {"name": "Update Rule - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"status\": true,\r\n  \"simUsageLimit\": 1,\r\n  \"unit\": \"KB\",\r\n  \"sizeInBytes\": 1024,\r\n  \"actions\": [],\r\n  \"notifications\": []\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rule/a4679a39-4a95-4ced-8751-611d975693bc", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rule", "a4679a39-4a95-4ced-8751-611d975693bc"]}}, "response": []}, {"name": "Update Rule - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"status\": true,\r\n  \"simUsageLimit\": 1,\r\n  \"unit\": \"KB\",\r\n  \"sizeInBytes\": 1024,\r\n  \"actions\": [],\r\n  \"notifications\": []\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rule/a4679a39-4a95-4ced-8751-611d975693bc", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rule", "a4679a39-4a95-4ced-8751-611d975693bc"]}}, "response": []}, {"name": "Update Rule - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"status\": true,\r\n  \"simUsageLimit\": 1,\r\n  \"unit\": \"KB\",\r\n  \"sizeInBytes\": 1024,\r\n  \"actions\": [],\r\n  \"notifications\": []\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rule/a4679a39-4a95-4ced-8751-611d975693bc", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rule", "a4679a39-4a95-4ced-8751-611d975693bc"]}}, "response": []}, {"name": "Update Rule - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", "\r", "pm.test(\"Status code is 202\", function () {\r", "    pm.response.to.have.status(202);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"status\": true,\r\n  \"simUsageLimit\": 1,\r\n  \"unit\": \"KB\",\r\n  \"sizeInBytes\": 1024,\r\n  \"actions\": [],\r\n  \"notifications\": []\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rule/{{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rule", "{{rule_uuid}}"]}}, "response": []}]}, {"name": "Delete Rule (Redis)", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Delete Rule - 202 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", "\r", "pm.test(\"Status code is 202\", function () {\r", "    pm.response.to.have.status(202);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/analytics/rule/{{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rule", "{{rule_uuid}}"]}}, "response": []}, {"name": "Delete Rule - Blank UUID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/analytics/rule/{{}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rule", "{{}}"]}}, "response": []}, {"name": "Delete Rule - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/analytics/rule/{{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rule", "{{rule_uuid}}"]}}, "response": []}, {"name": "Delete Rule - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "OPTIONS", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/analytics/rule/{{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rule", "{{rule_uuid}}"]}}, "response": []}, {"name": "Delete Rule - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/analytics/rule/{{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rule", "{{rule_uuid}}"]}}, "response": []}]}, {"name": "Add Rate Plan (Redis)", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Add Rate Plan - 201 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Blank Account ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": \"\",\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Invalid Account ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": \"abc\",\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Without Account ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Without Name", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Blank Access Fee", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": \"\",\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Invalid Access Fee", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": \"abc\",\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Without Access Fee", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Without Currency", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": \"\",\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Invalid IsDefault", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": falsee,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Blank SIM Limit", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": \"\",\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Invalid SIM Limit", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": \"abc\",\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Blank AllowanceUsed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": \"\",\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Invalid AllowanceUsed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": \"abc\",\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Without AllowanceUsed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Blank Origination Zones", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Without Origination Zones", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Blank Data RateModel", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": \"\",\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Invalid Data RateModel", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": \"xyz\",\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Without Data RateModel", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Blank Voicemo RateModel", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": \"\",\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Invalid Voicemo RateModel", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": \"xyz\",\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Without Voicemo RateModel", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Blank Voicemt RateModel", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": \"\",\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Invalid Voicemt RateModel", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": \"xyz\",\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Without Voicemt RateModel", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Blank SMS RateModel", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": \"\",\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Invalid SMS RateModel", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": \"xyz\",\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Without SMS RateModel", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Blank Rate Plan ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Invalid Rate Plan ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": \"xyz\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Without Rate Plan ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/ratepln", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "ratepln"]}}, "response": []}, {"name": "Add Rate Plan - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "OPTIONS", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}, {"name": "Add Rate Plan - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", "\r", "pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ],\r\n  \"ratePlanId\": 0\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan"]}}, "response": []}]}, {"name": "Update Rate Plan (Redis)", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Update Rate Plan - 202 & Response time", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", "\r", "pm.test(\"Status code is 202\", function () {\r", "    pm.response.to.have.status(202);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan/{{rateplan_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan", "{{rateplan_id}}"]}}, "response": []}, {"name": "Update Rate Plan - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/ratepln/{{rateplan_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "ratepln", "{{rateplan_id}}"]}}, "response": []}, {"name": "Update Rate Plan - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan/{{rateplan_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan", "{{rateplan_id}}"]}}, "response": []}, {"name": "Update Rate Plan - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "OPTIONS", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan/{{rateplan_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan", "{{rateplan_id}}"]}}, "response": []}, {"name": "Update Rate Plan - Blank Rate Plan ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan/{{}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan", "{{}}"]}}, "response": []}, {"name": "Update Rate Plan - Invalid Rate Plan ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan/abcd", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan", "abcd"]}}, "response": []}, {"name": "Update Rate Plan - Without Rate Plan ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": 0,\r\n  \"name\": \"string\",\r\n  \"accessFee\": 0,\r\n  \"currency\": \"string\",\r\n  \"isDefault\": false,\r\n  \"simLimit\": 0,\r\n  \"allowanceUsed\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"origination_zones\": [\r\n        \"string\",\r\n        \"string\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mo\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"voice_mt\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 0,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 0,\r\n            \"value\": 0,\r\n            \"rangeUnit\": \"string\",\r\n            \"priceUnit\": \"string\",\r\n            \"overageFee\": 0,\r\n            \"overageUnit\": \"string\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 0\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan/", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan", ""]}}, "response": []}]}, {"name": "Delete Rate Plan (Redis)", "item": [{"name": "Delete Rate Plan - 202 & Response time", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", "\r", "pm.test(\"Status code is 202\", function () {\r", "    pm.response.to.have.status(202);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan/{{rateplan_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan", "{{rateplan_id}}"]}}, "response": []}, {"name": "Delete Rate Plan - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/analytics/ratepln/{{rateplan_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "ratepln", "{{rateplan_id}}"]}}, "response": []}, {"name": "Delete Rate Plan - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "OPTIONS", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan/{{rateplan_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan", "{{rateplan_id}}"]}}, "response": []}, {"name": "Delete Rate Plan - Blank Rate Plan ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan/{{}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan", "{{}}"]}}, "response": []}, {"name": "Delete Rate Plan - Invalid Rate Plan ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan/{{}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan", "{{}}"]}}, "response": []}, {"name": "Delete Rate Plan - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/analytics/rateplan/{{rateplan_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "analytics", "rateplan", "{{rateplan_id}}"]}}, "response": []}]}]}