{"info": {"_postman_id": "85dbca16-bb7a-4149-8bbd-10ee7d1bb9c8", "name": "Billing_CA", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "29298941", "_collection_link": "https://nextgenclearing.postman.co/workspace/QA_SIMplify_Team~82bc3228-fd80-437d-bfc3-fd6585e93620/collection/29298941-85dbca16-bb7a-4149-8bbd-10ee7d1bb9c8?action=share&source=collection_link&creator=29298941"}, "item": [{"name": "Get Invoices New", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get invoices - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test('Received id', function() {\r", "    const data = pm.response.json();\r", "    const m1 = pm.environment.get('AccountId');\r", "  for (let i = 0; i < data.length; i++) {\r", "        pm.environment.set('invoice_id', data[i]['id']);\r", "    }\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const previousMonth = moment().subtract(1, 'months').startOf('month').format('YYYY-MM');\r", "pm.environment.set('Billing_cycle_date', previousMonth);\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices?month={{Billing_cycle_date}}&page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices"], "query": [{"key": "month", "value": "{{Billing_cycle_date}}"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get invoices - Page Out Of Boundary", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const previousMonth = moment().subtract(1, 'months').startOf('month').format('YYYY-MM');\r", "pm.environment.set('Billing_cycle_date', previousMonth);\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices?month={{Billing_cycle_date}}&page=10000&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices"], "query": [{"key": "month", "value": "{{Billing_cycle_date}}"}, {"key": "page", "value": "10000"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get invoices - Blank Page", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const previousMonth = moment().subtract(1, 'months').startOf('month').format('YYYY-MM');\r", "pm.environment.set('Billing_cycle_date', previousMonth);\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices?month={{Billing_cycle_date}}&page=&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices"], "query": [{"key": "month", "value": "{{Billing_cycle_date}}"}, {"key": "page", "value": ""}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get invoices - Blank <PERSON> Si<PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const previousMonth = moment().subtract(1, 'months').startOf('month').format('YYYY-MM');\r", "pm.environment.set('Billing_cycle_date', previousMonth);\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices?month={{Billing_cycle_date}}&page=1&page_size=", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices"], "query": [{"key": "month", "value": "{{Billing_cycle_date}}"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": ""}]}}, "response": []}, {"name": "Get invoices - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoics?month={{Billing_cycle_date}}&page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoics"], "query": [{"key": "month", "value": "{{Billing_cycle_date}}"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get invoices - Method Not Allowed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices?month={{Billing_cycle_date}}&page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices"], "query": [{"key": "month", "value": "{{Billing_cycle_date}}"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get invoices - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices?month={{Billing_cycle_date}}&page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices"], "query": [{"key": "month", "value": "{{Billing_cycle_date}}"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get invoices - Invalid Year Format", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices?month=20255-12&page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices"], "query": [{"key": "month", "value": "20255-12"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get invoices - Blank Month", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices?month=&page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices"], "query": [{"key": "month", "value": ""}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get invoices - Invalid Month", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices?month=2025-122&page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices"], "query": [{"key": "month", "value": "2025-122"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get invoices - Without Month", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices"]}}, "response": []}, {"name": "Get invoices - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices?month={{Billing_cycle_date}}&page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices"], "query": [{"key": "month", "value": "{{Billing_cycle_date}}"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}]}, {"name": "Invoice Details", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Invoice Details - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}"]}}, "response": []}, {"name": "Invoice Details - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoics/{{invoice_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoics", "{{invoice_id}}"]}}, "response": []}, {"name": "Invoice Details - Method Not Allowed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}"]}}, "response": []}, {"name": "Invoice Details - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}"]}}, "response": []}, {"name": "Invoice Details - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}"]}}, "response": []}, {"name": "Invoice Details - Blank Invoice ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/ '", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", " '"]}}, "response": []}, {"name": "Invoice Details - Invalid Invoice ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/123b '", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "123b '"]}}, "response": []}, {"name": "invoice details - Invoice ID (Out Of Boundary)", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/3235654551", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "3235654551"]}}, "response": []}]}, {"name": "Get Adjustment Type", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Adjustment Types - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", " pm.test(\"Response body has 'ref' key\", function () {\r", "     const responseBody = pm.response.json();\r", "     pm.expect(responseBody).to.be.hasOwnProperty('ref')\r", " });\r", "\r", "  pm.test(\"Response body has 'name' key\", function () {\r", "     const responseBody = pm.response.json();\r", "     pm.expect(responseBody).to.be.hasOwnProperty('name')\r", " });\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/adjustment-types", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "adjustment-types"]}}, "response": []}, {"name": "Adjustment Types - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/adjustment-ty", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "adjustment-ty"]}}, "response": []}, {"name": "Adjustment Types - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/adjustment-types", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "adjustment-types"]}}, "response": []}, {"name": "Adjustment Types - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/adjustment-types", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "adjustment-types"]}}, "response": []}, {"name": "Adjustment Types - Content Type", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "pm.test(\"Content-Type is present and is of type JSON\", function () {\r", "    // Check if the \"Content-Type\" header is present\r", "    pm.response.to.have.header(\"Content-Type\");\r", " \r", "    // Get the value of the \"Content-Type\" header\r", "    var contentType = pm.response.headers.get(\"Content-Type\");\r", " \r", "    // Check if the content type is JSON\r", "    if (contentType.includes(\"application/json\")) {\r", "        // Additional assertions for JSON content\r", "        pm.test(\"Response body is valid JSON\", function () {\r", "            pm.response.to.be.json;\r", "        })\r", " \r", "    } else {\r", "        // Handle other content types if needed\r", "        console.log(\"Content-Type is not JSON\");\r", "    }\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/adjustment-types", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "adjustment-types"]}}, "response": []}, {"name": "Adjustment Types - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/adjustment-types", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "adjustment-types"]}}, "response": []}]}, {"name": "Get Invoice Uses", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Invoice Usage - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage-Invalid method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage-Invalid  URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoiceswqdsx/{{invoice_id}}/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoiceswqdsx", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage-Invalid Token", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_dist9685ributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage-Invoice id blank", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/ /usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", " ", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage Invoice id Invalid", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/ *12/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", " *12", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage -Invoice id alphanum", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/5a2b3c/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "5a2b3c", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage -Invoice id decimal", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/1.25/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "1.25", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage -Invoice id negative value", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/-{{invoice_id}}/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "-{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage-page - valid num", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage- page alphanum", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1bc&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1bc"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage- page negative value", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=-1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "-1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage-page decimal value", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1.50&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1.50"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage -page blank", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page= &page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": " "}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage- Page remove", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage- page size valid", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage- page size blank", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size= &ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": " "}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage-page size remove", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage page size alphanum", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=2fdc&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "2fdc"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage- page size symbol", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=***&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "***"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage-page size decimal", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=2.50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "2.50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage -Ordering valid", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}, {"name": "Invoice Usage -Ordering blank", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=50&ordering= ", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": " "}]}}, "response": []}, {"name": "Invoice Usage -Ordering valid", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=50&ordering=rate_plan_name", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "rate_plan_name"}]}}, "response": []}, {"name": "Invoice Usage -Ordering Invalid", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=50&ordering=abc123", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "abc123"}]}}, "response": []}, {"name": "Invoice Usage -Ordering Remove", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Invoice Usage -  Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage?page=1&page_size=50&ordering=iccid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iccid"}]}}, "response": []}]}, {"name": "Export Invoice Usage to CSV", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Export invoice Usage to csv - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage/export?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoces/{{invoice_id}}/usage/export?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoces", "{{invoice_id}}", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Method Not Allowed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage/export?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Blank Invoice ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/ /usage/export?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", " ", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Invalid Invoice ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/fd644h/usage/export?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "fd644h", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Invalid Page No", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage/export?page=1ac&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage", "export"], "query": [{"key": "page", "value": "1ac"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Blank Page No", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage/export?page=&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage", "export"], "query": [{"key": "page", "value": ""}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Without Page No", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage/export?page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage", "export"], "query": [{"key": "page", "value": "1.242", "disabled": true}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Blank Page Size", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage/export?page=1&page_size=&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": ""}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Invalid Page Size", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage/export?page=1&page_size=retrt121&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "retrt121"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Without <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});0\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage/export?page=1&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Valid Ordering", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage/export?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Export invoice Usage to csv -  Invalid ordering", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage/export?page=1&page_size=50&ordering=jkd", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "jkd"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Without Ordering", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage/export?page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage/export?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Export invoice Usage to csv - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "text/csv"}], "url": {"raw": "{{BaseURL}}/v1/glass/billing/invoices/{{invoice_id}}/usage/export?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "billing", "invoices", "{{invoice_id}}", "usage", "export"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}]}]}