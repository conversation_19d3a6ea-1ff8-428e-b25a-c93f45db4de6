{"info": {"_postman_id": "dbbcd354-01dd-4653-9499-5258fee82556", "name": "MarketShareReport_CA", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "29298941", "_collection_link": "https://nextgenclearing.postman.co/workspace/QA_SIMplify_Team~82bc3228-fd80-437d-bfc3-fd6585e93620/collection/29298941-dbbcd354-01dd-4653-9499-5258fee82556?action=share&source=collection_link&creator=29298941"}, "item": [{"name": "Push CDR", "item": [{"name": "Push CDR(GBRME) - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const firstdate = moment().startOf('month').format('YYYY-MM-DD');\r", "pm.environment.set('fromDate', firstdate);\r", "\r", "var enddate = moment().add('months', 1).date(-0);\r", "pm.environment.set('toDate', enddate.format((\"YYYY-MM-DD\")));\r", "\r", "function getRandomTime() {\r", "    const randomSeconds = Math.floor(Math.random() * 86400);\r", "\r", "    const dateObj = new Date(randomSeconds * 1000);\r", "\r", "    const hours = dateObj.getUTCHours().toString().padStart(2, '0');\r", "    const minutes = dateObj.getUTCMinutes().toString().padStart(2, '0');\r", "    const seconds = dateObj.getUTCSeconds().toString().padStart(2, '0');\r", "\r", "    return `${hours}:${minutes}:${seconds}`;\r", "}\r", "\r", "const randomTime = getRandomTime();\r", "\r", "console.log(\"Generated Random Time:\", randomTime);\r", "pm.environment.set(\"randomTime\", randomTime);\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "<cdr>\r\n    <ID>10674668629</ID>\r\n    <Distributor>British Telecommunications</Distributor>\r\n    <DistributorID>15772316</DistributorID>\r\n    <CustomerGroup moniker=\"BT_Default\">British Telecommunications Default</CustomerGroup>\r\n    <Customer>883200000110320</Customer>\r\n    <CustomerID>15943419</CustomerID>\r\n    <CustomerReference>883200000110320</CustomerReference>\r\n    <Subscriber>883200000110320 {{IMSI_1}}</Subscriber>\r\n    <SubscriberID>15943420</SubscriberID>\r\n    <SubscriberReference>8944538532046590109</SubscriberReference>\r\n    <CreateTime>{{fromDate}} 18:47:05</CreateTime>\r\n    <ConnectTime>{{fromDate}} 18:47:05</ConnectTime>\r\n    <CallDate>{{fromDate}} 18:47:05</CallDate>\r\n    <EndTime>{{toDate}} {{randomTime}}</EndTime>\r\n    <Type>gsm-data</Type>\r\n    <Leg1>Data service</Leg1>\r\n    <Leg1TADIG>{{Carrier_Type1}}</Leg1TADIG>\r\n    <Leg1Country>United Kingdom</Leg1Country>\r\n    <Leg1Type>mobile</Leg1Type>\r\n    <Leg2></Leg2>\r\n    <Leg2Country></Leg2Country>\r\n    <Leg2Type></Leg2Type>\r\n    <Duration>20</Duration>\r\n    <Bytes>83886080</Bytes>\r\n    <Outcome>successful</Outcome>\r\n    <Narrative>Data Session (0.009 Mb consumed)</Narrative>\r\n    <BuyCurrency bottom-up-level=\"1\" top-down-level=\"2\" supplying-distributor-id=\"187715\" consuming-distributor-id=\"15772316\">GBP</BuyCurrency>\r\n    <BuyAmount bottom-up-level=\"1\" top-down-level=\"2\" supplying-distributor-id=\"187715\" consuming-distributor-id=\"15772316\">0.0000000000000</BuyAmount>\r\n    <RetailCurrency bottom-up-level=\"0\" top-down-level=\"3\" supplying-distributor-id=\"15772316\">GBP</RetailCurrency>\r\n    <RetailAmount bottom-up-level=\"0\" top-down-level=\"3\" supplying-distributor-id=\"15772316\">0.0000000000000</RetailAmount>\r\n    <Charges>\r\n        <Charge top-down-level=\"3\" bottom-up-level=\"0\" supplying-distributor-id=\"15772316\">\r\n            <Currency>GBP</Currency>\r\n            <Amount>0.0000000000000</Amount>\r\n            <Balance>0.0000</Balance>\r\n        </Charge>\r\n    </Charges>\r\n    <Legs>\r\n        <Leg>\r\n            <LegType>data</LegType>\r\n            <Country ISOCode=\"GBR\">United Kingdom</Country>\r\n            <ZoneType>mobile</ZoneType>\r\n            <Number></Number>\r\n            <RouteNumber>************</RouteNumber>\r\n            <RateNumber>************</RateNumber>\r\n            <Identity mcc=\"234\" mnc=\"58\"/>\r\n        </Leg>\r\n    </Legs>\r\n</cdr>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{BaseURL}}/cdr/documents", "host": ["{{BaseURL}}"], "path": ["cdr", "documents"]}}, "response": []}, {"name": "Push CDR(GBRVF)[0 Bytes] - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["var moment = require('moment');\r", "const firstdate = moment().startOf('month').format('YYYY-MM-DD');\r", "pm.environment.set('fromDate', firstdate);\r", "\r", "var enddate = moment().add('months', 1).date(-0);\r", "pm.environment.set('toDate', enddate.format((\"YYYY-MM-DD\")));\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "<cdr>\r\n    <ID>10674668629</ID>\r\n    <Distributor>British Telecommunications</Distributor>\r\n    <DistributorID>15772316</DistributorID>\r\n    <CustomerGroup moniker=\"BT_Default\">British Telecommunications Default</CustomerGroup>\r\n    <Customer>883200000110320</Customer>\r\n    <CustomerID>15943419</CustomerID>\r\n    <CustomerReference>883200000110320</CustomerReference>\r\n    <Subscriber>883200000110320{{IMSI}}</Subscriber>\r\n    <SubscriberID>15943420</SubscriberID>\r\n    <SubscriberReference>8944538532046590109</SubscriberReference>\r\n    <CreateTime>{{fromDate}} 18:47:05</CreateTime>\r\n    <ConnectTime>{{fromDate}} 18:47:05</ConnectTime>\r\n    <CallDate>{{fromDate}} 18:47:05</CallDate>\r\n    <EndTime>{{toDate}} 18:47:44</EndTime>\r\n    <Type>gsm-data</Type>\r\n    <Leg1>Data service</Leg1>\r\n    <Leg1TADIG>{{Carrier_Type2}}</Leg1TADIG>\r\n    <Leg1Country>United Kingdom</Leg1Country>\r\n    <Leg1Type>mobile</Leg1Type>\r\n    <Leg2></Leg2>\r\n    <Leg2Country></Leg2Country>\r\n    <Leg2Type></Leg2Type>\r\n    <Duration>20</Duration>\r\n    <Bytes>0</Bytes>\r\n    <Outcome>successful</Outcome>\r\n    <Narrative>Data Session (0.009 Mb consumed)</Narrative>\r\n    <BuyCurrency bottom-up-level=\"1\" top-down-level=\"2\" supplying-distributor-id=\"187715\" consuming-distributor-id=\"15772316\">GBP</BuyCurrency>\r\n    <BuyAmount bottom-up-level=\"1\" top-down-level=\"2\" supplying-distributor-id=\"187715\" consuming-distributor-id=\"15772316\">0.0000000000000</BuyAmount>\r\n    <RetailCurrency bottom-up-level=\"0\" top-down-level=\"3\" supplying-distributor-id=\"15772316\">GBP</RetailCurrency>\r\n    <RetailAmount bottom-up-level=\"0\" top-down-level=\"3\" supplying-distributor-id=\"15772316\">0.0000000000000</RetailAmount>\r\n    <Charges>\r\n        <Charge top-down-level=\"3\" bottom-up-level=\"0\" supplying-distributor-id=\"15772316\">\r\n            <Currency>GBP</Currency>\r\n            <Amount>0.0000000000000</Amount>\r\n            <Balance>0.0000</Balance>\r\n        </Charge>\r\n    </Charges>\r\n    <Legs>\r\n        <Leg>\r\n            <LegType>data</LegType>\r\n            <Country ISOCode=\"GBR\">United Kingdom</Country>\r\n            <ZoneType>mobile</ZoneType>\r\n            <Number></Number>\r\n            <RouteNumber>************</RouteNumber>\r\n            <RateNumber>************</RateNumber>\r\n            <Identity mcc=\"234\" mnc=\"58\"/>\r\n        </Leg>\r\n    </Legs>\r\n</cdr>", "options": {"raw": {"language": "xml"}}}, "url": {"raw": "{{BaseURL}}/cdr/documents", "host": ["{{BaseURL}}"], "path": ["cdr", "documents"]}}, "response": []}]}, {"name": "Market share by account with account id", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Market share by account with account id - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}} ", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/{{account_id}}?from_date={{fromDate}}&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "{{account_id}}"], "query": [{"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market share by account with account id - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/acco/{{account_id}}?from_date={{fromDate}}&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "acco", "{{account_id}}"], "query": [{"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market share by account with account id - Method Not Allowed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}} ", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/{{account_id}}?from_date={{fromDate}}&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "{{account_id}}"], "query": [{"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market share by account with account id - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/{{account_id}}?from_date={{fromDate}}&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "{{account_id}}"], "query": [{"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market share by account with account id - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}} ", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/{{account_id}}?from_date={{fromDate}}&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "{{account_id}}"], "query": [{"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market share by account with account id -  Invalid Account ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/1052?from_date={{fromDate}}&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "1052"], "query": [{"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market share by account with account id - Without Account ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}} ", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account?from_date={{fromDate}}&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account"], "query": [{"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market share by account with account id - Blank Account ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}} ", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/{{}}?from_date={{fromDate}}&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "{{}}"], "query": [{"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market share by account with account id - without Date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/{{account_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "{{account_id}}"], "query": [{"key": "from_date", "value": "20-09-01", "disabled": true}, {"key": "to_date", "value": "2023-09-30", "disabled": true}]}}, "response": []}, {"name": "Market share by account with account id - without From Date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/{{account_id}}?to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "{{account_id}}"], "query": [{"key": "from_date", "value": "{{fromDate}}", "disabled": true}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market share by account with account id - without To Date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/{{account_id}}?from_date={{fromDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "{{account_id}}"], "query": [{"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "{{toDate}}", "disabled": true}]}}, "response": []}, {"name": "Market share by account with account id - Invalid Format To Date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/{{account_id}}?from_date={{fromDate}}&to_date=fdf4545", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "{{account_id}}"], "query": [{"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "fdf4545"}]}}, "response": []}, {"name": "Market share by account with account id - Invalid Format From Date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/{{account_id}}?from_date=146df121&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "{{account_id}}"], "query": [{"key": "from_date", "value": "146df121"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market share by account with account id - Invalid Year TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/{{account_id}}?from_date={{fromDate}}&to_date=20-09-30", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "{{account_id}}"], "query": [{"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "20-09-30"}]}}, "response": []}, {"name": "Market share by account with account id - Blank Year TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/{{account_id}}?from_date=2023-09-01&to_date=-09-30", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "{{account_id}}"], "query": [{"key": "from_date", "value": "2023-09-01"}, {"key": "to_date", "value": "-09-30"}]}}, "response": []}, {"name": "Market share by account with account id - Invalid Month TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/{{account_id}}?from_date={{fromDate}}&to_date=2023-13-30", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "{{account_id}}"], "query": [{"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "2023-13-30"}]}}, "response": []}, {"name": "Market share by account with account id - Blank Month TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/{{account_id}}?from_date={{fromDate}}&to_date=2023--30", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "{{account_id}}"], "query": [{"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "2023--30"}]}}, "response": []}, {"name": "Market share by account with account id - Invalid Days TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/{{account_id}}?from_date={{fromDate}}&to_date=2023-09-32", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "{{account_id}}"], "query": [{"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "2023-09-32"}]}}, "response": []}, {"name": "Market share by account with account id - Blank Days TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/{{account_id}}?from_date={{fromDate}}&to_date=2023-09-", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "{{account_id}}"], "query": [{"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "2023-09-"}]}}, "response": []}, {"name": "Market share by account with account id - Invalid Year From date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/{{account_id}}?from_date=20-09-01&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "{{account_id}}"], "query": [{"key": "from_date", "value": "20-09-01"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market share by account with account id - Blank Year FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/{{account_id}}?from_date=-09-01&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "{{account_id}}"], "query": [{"key": "from_date", "value": "-09-01"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market share by account with account id - Invalid Month FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/{{account_id}}?from_date=2023-13-01&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "{{account_id}}"], "query": [{"key": "from_date", "value": "2023-13-01"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market share by account with account id - Blank Month FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/{{account_id}}?from_date=2023--01&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "{{account_id}}"], "query": [{"key": "from_date", "value": "2023--01"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market share by account with account id - Invalid Days FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/{{account_id}}?from_date={{fromDate}}&to_date=2023-13-30", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "{{account_id}}"], "query": [{"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "2023-13-30"}]}}, "response": []}, {"name": "Market share by account with account id - Bank Days FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/{{account_id}}?from_date=2023-09-&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "{{account_id}}"], "query": [{"key": "from_date", "value": "2023-09-"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}]}, {"name": "Market share by accounts with IMSI", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Market accounts IMSI - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}} ", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}]}}, "response": []}, {"name": "Market accounts IMSI - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accou/imsi?imsi={{IMSI}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accou", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}]}}, "response": []}, {"name": "Market accounts IMSI - Method Not Allowed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}} ", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}]}}, "response": []}, {"name": "Market accounts IMSI - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}]}}, "response": []}, {"name": "Market accounts IMSI - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}} ", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}]}}, "response": []}, {"name": "Market accounts IMSI - Invalid IMSI", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/account/imsi?imsi=8944538532046590117", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "account", "imsi"], "query": [{"key": "imsi", "value": "8944538532046590117"}]}}, "response": []}, {"name": "Market accounts IMSI - Blank IMSI", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi=", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": ""}]}}, "response": []}, {"name": "Market accounts IMSI - Without IMSI", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "", "disabled": true}]}}, "response": []}, {"name": "Market accounts IMSI - Max IMSI Length", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi=234588570010011253", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "234588570010011253"}]}}, "response": []}, {"name": "Market accounts IMSI - Min IMSI Length", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi=2000000", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "2000000"}]}}, "response": []}, {"name": "Market account IMSI - with To and From Date - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}&from_date={{fromDate}}&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}, {"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market account IMSI - without any fields", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}", "disabled": true}, {"key": "from_date", "value": "{{fromDate}}", "disabled": true}, {"key": "to_date", "value": "{{toDate}}", "disabled": true}]}}, "response": []}, {"name": "Market account IMSI - Invalid To Date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}&from_date={{fromDate}}&to_date=202-10-30", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}, {"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "202-10-30"}]}}, "response": []}, {"name": "Market account IMSI - Invalid From Date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}&from_date=2023-10-0123&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}, {"key": "from_date", "value": "2023-10-0123"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market account IMSI - without From Date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}, {"key": "from_date", "value": "{{fromDate}}", "disabled": true}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market account IMSI  - without To Date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}&from_date={{fromDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}, {"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "{{toDate}}", "disabled": true}]}}, "response": []}, {"name": "Market account IMSI  - Invalid Format To Date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}&from_date={{fromDate}}&to_date=2023-125-3021", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}, {"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "2023-125-3021"}]}}, "response": []}, {"name": "Market account IMSI - Invalid Format From Date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}&from_date=2023-125-3021&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}, {"key": "from_date", "value": "2023-125-3021"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market account IMSI  - Invalid Year TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}&from_date={{fromDate}}&to_date=20233-12-30", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}, {"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "20233-12-30"}]}}, "response": []}, {"name": "Market account IMSI  - Blank Year TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}&from_date={{fromDate}}&to_date=-12-30", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}, {"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "-12-30"}]}}, "response": []}, {"name": "Market account IMSI  - Invalid Month TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}&from_date={{fromDate}}&to_date=2023-122-30", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}, {"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "2023-122-30"}]}}, "response": []}, {"name": "Market account IMSI  - Blank Month TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}&from_date={{fromDate}}&to_date=2023--30", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}, {"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "2023--30"}]}}, "response": []}, {"name": "Market account IMSI  - Invalid Days TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}&from_date={{fromDate}}&to_date=2023-12-303", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}, {"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "2023-12-303"}]}}, "response": []}, {"name": "Market account IMSI  - Blank Days TO date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}&from_date={{fromDate}}&to_date=20233-12-", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}, {"key": "from_date", "value": "{{fromDate}}"}, {"key": "to_date", "value": "20233-12-"}]}}, "response": []}, {"name": "Market account IMSI  - Invalid Year From date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}&from_date=20233-12-01&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}, {"key": "from_date", "value": "20233-12-01"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market account IMSI  - Blank Year FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}&from_date=-12-01&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}, {"key": "from_date", "value": "-12-01"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market account IMSI - Invalid Month FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}&from_date=2023-122-01&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}, {"key": "from_date", "value": "2023-122-01"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market account IMSI  - Blank Month FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}&from_date=2023--01&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}, {"key": "from_date", "value": "2023--01"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market account IMSI  - Invalid Days FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}&from_date=2023-12-011&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}, {"key": "from_date", "value": "2023-12-011"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}, {"name": "Market account IMSI  - Bank Days FROM date", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/market/accounts/imsi?imsi={{IMSI}}&from_date=2023-12-&to_date={{toDate}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "market", "accounts", "imsi"], "query": [{"key": "imsi", "value": "{{IMSI}}"}, {"key": "from_date", "value": "2023-12-"}, {"key": "to_date", "value": "{{toDate}}"}]}}, "response": []}]}]}