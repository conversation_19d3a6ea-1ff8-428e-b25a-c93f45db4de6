{"id": "04b5fd5a-73dd-4e2e-bfe4-88d32ddf68e6", "name": "Test_Environment_SPOG", "values": [{"key": "TokenUrl", "value": "https://sso.test.spogconnected.com/auth/realms/test-connected-platform/protocol/openid-connect/token", "type": "default", "enabled": true}, {"key": "BaseURL", "value": "https://test.spogconnected.com", "type": "default", "enabled": true}, {"key": "IMSI", "value": "***************", "type": "default", "enabled": true}, {"key": "IMSI_1", "value": "***************", "type": "default", "enabled": true}, {"key": "month", "value": "2024-11", "type": "default", "enabled": true}, {"key": "account_id", "value": "12", "type": "default", "enabled": true}, {"key": "invoice_id", "value": 5530, "type": "default", "enabled": true}, {"key": "rateplanid", "value": 2121, "type": "any", "enabled": true}, {"key": "AccountId", "value": 7549, "type": "any", "enabled": true}, {"key": "Name", "value": "lrcxkydmox", "type": "default", "enabled": true}, {"key": "Client_id", "value": "organization-management", "type": "default", "enabled": true}, {"key": "Client_secret", "value": "d3df5208-c809-4063-bfba-c2254ebefd2f", "type": "default", "enabled": true}, {"key": "Grant_type", "value": "password", "type": "default", "enabled": true}, {"key": "Username", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "Password", "value": "P@ssw0rd1", "type": "default", "enabled": true}, {"key": "fromDate", "value": "2024-11-01", "type": "any", "enabled": true}, {"key": "toDate", "value": "2024-11-29", "type": "any", "enabled": true}, {"key": "Adjustment_id", "value": 271, "type": "default", "enabled": true}, {"key": "Billing_cycle_date", "value": "2024-11", "type": "default", "enabled": true}, {"key": "Organisation_id", "value": "", "type": "default", "enabled": true}, {"key": "Billing_cycle_date_Future", "value": "2024-09", "type": "default", "enabled": true}, {"key": "Adjustment date", "value": "2024-11-01", "type": "any", "enabled": true}, {"key": "random_number", "value": "350889138733758", "type": "any", "enabled": true}, {"key": "Random_number", "value": "", "type": "any", "enabled": true}, {"key": "contractEndDate", "value": "2024-11-30", "type": "any", "enabled": true}, {"key": "Token_distributor", "value": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJ5S01pTXNYTl9qX2ZuNW1KWjJ6S09IVDZFODBBd0l4RUdzMHhJem1hMUtRIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MVO4qMTTBdW7LKdAcuX7G_6H5J08iZWaSsB3FVXUQ4jIrGuLCGjtHlinZCp2rfuX9Kfh5i-AMQloz5vQGU7lt0JG8QlbfRojdEQwKPYTwET4SXHnPyf5SKhod5h-hJO6STVWLCrPY5n-0eJ2VkBDOIHQ0WYMidIQh_3597oCHJr1Ek3Dg6Ey9TjcSA1-gocP_vY_9KKd13lePDNaeLf93qCtcAKp4u2t-qNFS6A8RMI_z2RcgSQejk1M3T201ggtl1qXmC4Jm4qCPzszYxWaBskDKjeqqYA4dS51i-jbo4gGTd6BJXqQgsxqPvLcde0bAEXDaJHohtC5pmgoK4rHCA", "type": "any", "enabled": true}, {"key": "payment_term", "value": "257", "type": "any", "enabled": true}, {"key": "sim_charge", "value": "98332730469446207151", "type": "any", "enabled": true}, {"key": "group_path", "value": "Nextgen Clearing", "type": "default", "enabled": true}, {"key": "resource_name", "value": "xsleo4g2d4", "type": "any", "enabled": true}, {"key": "resource_id", "value": "a3cbf37d-7211-4234-b8ca-4b5e6f55ee0c", "type": "any", "enabled": true}, {"key": "role_id", "value": "1fd6d80e-4ad6-46b2-80d6-07a5ba3f7375", "type": "any", "enabled": true}, {"key": "discription", "value": "k7bzt99b6v9c1bvroe3s", "type": "any", "enabled": true}, {"key": "permission_id", "value": "72b302d4-da87-4e9b-ad02-c930e0af6808", "type": "default", "enabled": true}, {"key": "search_value", "value": "DistributorAdmin", "type": "default", "enabled": true}, {"key": "role_name", "value": "l20cs0m5rw", "type": "default", "enabled": true}, {"key": "resource_scopes_name", "value": "6cl8rwe", "type": "default", "enabled": true}, {"key": "resource_scopes_display_name", "value": "wj9gcyr", "type": "any", "enabled": true}, {"key": "resource_scopes", "value": "blf0a3n", "type": "any", "enabled": true}, {"key": "permission_name", "value": "wjcnv50yt839", "type": "default", "enabled": true}, {"key": "scope_id", "value": "19fb646f-7a95-4df9-af6d-b5f507c4de73", "type": "default", "enabled": true}, {"key": "group_id", "value": "0d1503f1-381b-4da1-bf38-bdd9fcc4cf57", "type": "any", "enabled": true}, {"key": "owner", "value": "3wlxt", "type": "any", "enabled": true}, {"key": "account_id_reallocate", "value": "4610", "type": "default", "enabled": true}, {"key": "rateplan_id_reallocate", "value": "1427", "type": "default", "enabled": true}, {"key": "ICCID", "value": "8944538532046590133", "type": "default", "enabled": true}, {"key": "imsi_reallocate", "value": "**************", "type": "default", "enabled": true}, {"key": "ActiveSIM_ID", "value": "12", "type": "default", "enabled": true}, {"key": "ActiveSIM_Month", "value": "2024-11", "type": "default", "enabled": true}, {"key": "account_id_get_imsi", "value": "5542", "type": "default", "enabled": true}, {"key": "Carrier_Type1", "value": "GBRME", "type": "default", "enabled": true}, {"key": "Carrier_Type2", "value": "GBRVF", "type": "default", "enabled": true}, {"key": "Carrier_Type3", "value": "GBRCN", "type": "default", "enabled": true}, {"key": "resource_scopes1", "value": "66watb9w5q", "type": "any", "enabled": true}, {"key": "MSISDN", "value": "***************", "type": "default", "enabled": true}, {"key": "file_key", "value": ["temp_file_key", "temp_file_key"], "type": "any", "enabled": true}, {"key": "Rule_Name", "value": "7brhdg6fsq", "type": "any", "enabled": true}, {"key": "Data_Volume", "value": 7196, "type": "any", "enabled": true}, {"key": "rule_uuid", "value": null, "type": "any", "enabled": true}, {"key": "currentHour", "value": "18", "type": "any", "enabled": true}, {"key": "currentMinute", "value": "45", "type": "any", "enabled": true}, {"key": "currentSecond", "value": "12", "type": "any", "enabled": true}, {"key": "ValueIncrease", "value": null, "type": "any", "enabled": true}, {"key": "notification_id", "value": "66e010f8ae477bd22f474a4b", "type": "any", "enabled": true}, {"key": "work_id", "value": "66a76c48549e7f158048fa27", "type": "any", "enabled": true}, {"key": "month_adt", "value": "2024-08", "type": "any", "enabled": true}, {"key": "Random_Email", "value": "<EMAIL>", "type": "any", "enabled": true}, {"key": "dataVolume", "value": null, "type": "any", "enabled": true}, {"key": "cdr_ID", "value": "123458542", "type": "any", "enabled": true}, {"key": "rule_ID", "value": "c3d0f796-18cf-4d51-bef3-1e51a6d24780", "type": "any", "enabled": true}, {"key": "IMSI_notification", "value": "***************", "type": "any", "enabled": true}, {"key": "usage_limit_notification", "value": 504, "type": "any", "enabled": true}, {"key": "month_notification", "value": "2024-09", "type": "any", "enabled": true}, {"key": "ICCID_notification", "value": "8944538532046590133", "type": "default", "enabled": true}, {"key": "MSISDN_notification", "value": "***************", "type": "default", "enabled": true}, {"key": "ModelID", "value": 12, "type": "any", "enabled": true}, {"key": "rateplan_id", "value": 2143, "type": "any", "enabled": true}, {"key": "Token_distributor ", "value": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJ5S01pTXNYTl9qX2ZuNW1KWjJ6S09IVDZFODBBd0l4RUdzMHhJem1hMUtRIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.c2Yj_nfde7Lrtk7Npo8DThD3eyV2Oloi8eywf3vPhmPtHwpC57Gh2LbI006moeDMzEhLLsK0StDan34MBPHwtJULQtXIhlkV5iiOx-l1x5qMQJrjiVjHiQznxwD3bwLRhCChPYv17Uf8XidEAMLSDF3369wNqotPYAHNKq_dW980u3VTZCtV1amhshq-X3hmJQF3lsmKG6VDd2tfMzvHi6-LoUkzAbFJu9lsrL_pmFOiKrc1_GFvcYxlE6WlJYfRHGAKdTdfTLtghx66JYv_HPMOkzCql8coBqscgn6sbXtaWfU-n785dXtkuVhvKbY_4FI0nqN_luYdaDOZadT-pA", "type": "any", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-11-21T07:57:00.730Z", "_postman_exported_using": "Postman/11.20.0"}