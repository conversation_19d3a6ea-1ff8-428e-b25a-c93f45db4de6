{"info": {"_postman_id": "f3731f35-4190-4e87-845c-5d3beaa1e301", "name": "AuditLogs_CA", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "29298941", "_collection_link": "https://nextgenclearing.postman.co/workspace/QA-_Team_SPOG~66755c34-5aed-4dcf-af75-9956e2650e16/collection/29298941-f3731f35-4190-4e87-845c-5d3beaa1e301?action=share&source=collection_link&creator=29298941"}, "item": [{"name": "System Audit Logs", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "System Audit Logs - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/audit/{{account_id}}?page=1&page_size=50&ordering=-createdDate", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "audit", "{{account_id}}"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "-createdDate"}]}, "description": "Generated from cURL: curl -X 'GET' \\\r\n  'https://test.spogconnected.com/v1/glass/audit/12?page=1&page_size=50&ordering=-createdDate' \\\r\n  -H 'accept: application/json'"}, "response": []}, {"name": "System Audit Logs - Blank Account ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/audit/?page=1&page_size=50&ordering=-createdDate", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "audit", ""], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "-createdDate"}]}, "description": "Generated from cURL: curl -X 'GET' \\\r\n  'https://test.spogconnected.com/v1/glass/audit/12?page=1&page_size=50&ordering=-createdDate' \\\r\n  -H 'accept: application/json'"}, "response": []}, {"name": "System Audit Logs - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/audt/{{account_id}}?page=1&page_size=50&ordering=-createdDate", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "audt", "{{account_id}}"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "-createdDate"}]}, "description": "Generated from cURL: curl -X 'GET' \\\r\n  'https://test.spogconnected.com/v1/glass/audit/12?page=1&page_size=50&ordering=-createdDate' \\\r\n  -H 'accept: application/json'"}, "response": []}, {"name": "System Audit Logs - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/audit/{{account_id}}?page=1&page_size=50&ordering=-createdDate", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "audit", "{{account_id}}"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "-createdDate"}]}, "description": "Generated from cURL: curl -X 'GET' \\\r\n  'https://test.spogconnected.com/v1/glass/audit/12?page=1&page_size=50&ordering=-createdDate' \\\r\n  -H 'accept: application/json'"}, "response": []}, {"name": "System Audit Logs - Invalid Account ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/audit/545121?page=1&page_size=50&ordering=-createdDate", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "audit", "545121"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "-createdDate"}]}, "description": "Generated from cURL: curl -X 'GET' \\\r\n  'https://test.spogconnected.com/v1/glass/audit/12?page=1&page_size=50&ordering=-createdDate' \\\r\n  -H 'accept: application/json'"}, "response": []}, {"name": "System Audit Logs - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/audit/{{account_id}}?page=1&page_size=50&ordering=-createdDate", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "audit", "{{account_id}}"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "-createdDate"}]}, "description": "Generated from cURL: curl -X 'GET' \\\r\n  'https://test.spogconnected.com/v1/glass/audit/12?page=1&page_size=50&ordering=-createdDate' \\\r\n  -H 'accept: application/json'"}, "response": []}]}]}