{"id": "5c36358f-6e85-4be0-ab8a-8844b7962d81", "name": "CA_Test_Environment_SPOG", "values": [{"key": "TokenUrl", "value": "https://sso.test.spogconnected.com/auth/realms/test-connected-platform/protocol/openid-connect/token", "type": "default", "enabled": true}, {"key": "BaseURL", "value": "https://test.spogconnected.com", "type": "default", "enabled": true}, {"key": "IMSI", "value": "***************", "type": "default", "enabled": true}, {"key": "IMSI_1", "value": "***************", "type": "default", "enabled": true}, {"key": "IMSI_2", "value": "***************", "type": "default", "enabled": true}, {"key": "month", "value": "2024-11", "type": "default", "enabled": true}, {"key": "account_id", "value": "12", "type": "default", "enabled": true}, {"key": "invoice_id", "value": 220, "type": "default", "enabled": true}, {"key": "rateplanid", "value": "", "type": "any", "enabled": true}, {"key": "Name", "value": "k24bmqawzj", "type": "default", "enabled": true}, {"key": "Client_id", "value": "organization-management", "type": "default", "enabled": true}, {"key": "Client_secret", "value": "d3df5208-c809-4063-bfba-c2254ebefd2f", "type": "default", "enabled": true}, {"key": "Grant_type", "value": "password", "type": "default", "enabled": true}, {"key": "Username", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "Password", "value": "Manish@Test123", "type": "default", "enabled": true}, {"key": "Adjustment_id", "value": "", "type": "default", "enabled": true}, {"key": "Billing_cycle_date", "value": "2023-07", "type": "default", "enabled": true}, {"key": "Organisation_id", "value": "", "type": "default", "enabled": true}, {"key": "Billing_cycle_date_Future", "value": "2024-07", "type": "default", "enabled": true}, {"key": "Adjustment date", "value": "2024-07-01", "type": "any", "enabled": true}, {"key": "random_number", "value": "055667218405816", "type": "any", "enabled": true}, {"key": "Random_number", "value": "", "type": "any", "enabled": true}, {"key": "contractEndDate", "value": "2024-06-14", "type": "any", "enabled": true}, {"key": "payment_term", "value": "289", "type": "any", "enabled": true}, {"key": "sim_charge", "value": "98781544148857864903", "type": "any", "enabled": true}, {"key": "group_path", "value": "Nextgen Clearing", "type": "default", "enabled": true}, {"key": "resource_name", "value": "", "type": "any", "enabled": true}, {"key": "resource_id", "value": "", "type": "any", "enabled": true}, {"key": "role_id", "value": "72c4f17f-b1dc-4590-89b9-d0d580edcb9a", "type": "any", "enabled": true}, {"key": "discription", "value": "eub42jzzlhwx5ote90xz", "type": "any", "enabled": true}, {"key": "permission_id", "value": "44816875-77f4-4ae0-b951-6aa32e6d7a0c", "type": "default", "enabled": true}, {"key": "search_value", "value": "ClientAdmin", "type": "default", "enabled": true}, {"key": "role_name", "value": "i10e5z3pop", "type": "default", "enabled": true}, {"key": "resource_scopes_name", "value": "", "type": "default", "enabled": true}, {"key": "resource_scopes_display_name", "value": "", "type": "any", "enabled": true}, {"key": "resource_scopes", "value": "", "type": "any", "enabled": true}, {"key": "permission_name", "value": "", "type": "default", "enabled": true}, {"key": "scope_id", "value": "", "type": "default", "enabled": true}, {"key": "group_id", "value": "", "type": "any", "enabled": true}, {"key": "owner", "value": "", "type": "any", "enabled": true}, {"key": "account_id_reallocate", "value": "4610", "type": "default", "enabled": true}, {"key": "rateplan_id_reallocate", "value": "1427", "type": "default", "enabled": true}, {"key": "ICCID", "value": "8944538532046590133", "type": "default", "enabled": true}, {"key": "imsi_reallocate", "value": "***************", "type": "default", "enabled": true}, {"key": "ActiveSIM_ID", "value": "12", "type": "default", "enabled": true}, {"key": "ActiveSIM_Month", "value": "2024-10", "type": "default", "enabled": true}, {"key": "account_id_get_imsi", "value": "14", "type": "default", "enabled": true}, {"key": "Carrier_Type1", "value": "GBRME", "type": "default", "enabled": true}, {"key": "Carrier_Type2", "value": "GBRVF", "type": "default", "enabled": true}, {"key": "Carrier_Type3", "value": "GBRCN", "type": "default", "enabled": true}, {"key": "Rule_Name", "value": "nhiaiw9ogr", "type": "any", "enabled": true}, {"key": "Data_Volume", "value": 7293, "type": "any", "enabled": true}, {"key": "rule_uuid", "value": null, "type": "any", "enabled": true}, {"key": "Random_Email", "value": "<EMAIL>", "type": "any", "enabled": true}, {"key": "Token_distributor", "value": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJ5S01pTXNYTl9qX2ZuNW1KWjJ6S09IVDZFODBBd0l4RUdzMHhJem1hMUtRIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.J38Vpj3wfqY7pk_kLheJ3xCD7MbXmmf9W5M-33Q3evGO_oDEB5yIEjdfCScFzP1LNaNRoIxUypZN0T_5mjfooGGh9_X26ABZRUpU3C_etPPZnnbN7GJ1UkqYQV3Z9exZV3eOe6Q8WEuQXs4gJxdaczMVixlkwezwbydA0vSEtTHba71Wvdr-JcibiDEK0aCTgGmuYPhKFIx2XDF37dbsr6Ii286AjBPGqrlNyV_QJSvWxf7ExPDV-38iPkGI-qGLbiejd1KLPHX5MAQRvbEPVGzYm4FAXy5cXA_C8ab70F6ETvlOMxgwptomRaNqz8yfmIRutO4347zAy3IsT1XVgw", "type": "any", "enabled": true}, {"key": "AccountId", "value": "", "type": "any", "enabled": true}, {"key": "notification_id", "value": "66ea838709566a6a2e7c6af0", "type": "any", "enabled": true}, {"key": "cdr_ID", "value": "*********", "type": "any", "enabled": true}, {"key": "rule_ID", "value": "6afda784-d763-45aa-a4f5-dcd382b7e865", "type": "any", "enabled": true}, {"key": "IMSI_notification", "value": "***************", "type": "any", "enabled": true}, {"key": "month_notification", "value": "2024-09", "type": "any", "enabled": true}, {"key": "usage_limit_notification", "value": 504, "type": "any", "enabled": true}, {"key": "fromDate", "value": "2024-11-01", "type": "any", "enabled": true}, {"key": "toDate", "value": "2024-11-29", "type": "any", "enabled": true}, {"key": "file_key", "value": ["2024-07-10/2024-07-10_4dc7b3bb-7e80-45b4-9cd6-2a748840655b.xml", "2024-07-19/2024-07-19_0e7daa68-c83e-4c19-8e43-43f0e00443a7.xml"], "type": "any", "enabled": true}, {"key": "currentHour", "value": "12", "type": "any", "enabled": true}, {"key": "currentMinute", "value": "08", "type": "any", "enabled": true}, {"key": "currentSecond", "value": "56", "type": "any", "enabled": true}, {"key": "ValueIncrease", "value": 7, "type": "any", "enabled": true}, {"key": "MSISDN", "value": "883200000110323", "type": "any", "enabled": true}, {"key": "dataVolume", "value": 7, "type": "any", "enabled": true}, {"key": "MSISDN_notification", "value": "883200000110323", "type": "default", "enabled": true}, {"key": "ICCID_notification", "value": "8944538532046590133", "type": "default", "enabled": true}, {"key": "Token_distributor ", "value": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJ5S01pTXNYTl9qX2ZuNW1KWjJ6S09IVDZFODBBd0l4RUdzMHhJem1hMUtRIn0.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.aQ7bBEfszoxPU4a2p0DxE7H36DA8AJFnn_RFjzrtVR0tOdXvericyCXEsH-uhzHHmSW7tly9fdGG-xVeiWBNs4_4Ipiqzmc52ncps7OviVDncnjhm-Ceu9-1wdtkqQ7ql_TRMf6Rdl4j96Tvslxtb065rqhny9K1pcVg6P9ZdihAwKF1zFfb6PifpE5z2whmTFhZSbQtOW8suAogCyGNsD7t13y-H-v6G0UYtj7m79mmaP0igMSKk_b621tZW23yK_du3FWo_MI_RPkUGcTkjTByKHQx-oe4GHEpyOjFLv2bhZycb9t40O-BQIoOXq6YVDs7ljD1BrKUaduBn4uZPw", "type": "any", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-11-21T07:56:56.064Z", "_postman_exported_using": "Postman/11.20.0"}