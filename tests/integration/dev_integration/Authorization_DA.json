{"info": {"_postman_id": "d3c8c412-6778-4e77-bdda-fe9c669e9da1", "name": "Authorization_DA", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "29298941", "_collection_link": "https://nextgenclearing.postman.co/workspace/QA-_Team_SPOG~66755c34-5aed-4dcf-af75-9956e2650e16/collection/29298941-d3c8c412-6778-4e77-bdda-fe9c669e9da1?action=share&source=collection_link&creator=29298941"}, "item": [{"name": "Get Roles", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Roles - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "var jsonData = pm.response.json();\r", "pm.environment.set(\"role_id\", jsonData.results[0].id);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"]}}, "response": []}, {"name": "Get Roles - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/re", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "re"]}}, "response": []}, {"name": "Get Roles - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"]}}, "response": []}, {"name": "Get Roles - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"]}}, "response": []}, {"name": "Get Roles - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"]}}, "response": []}, {"name": "Get Roles - with <PERSON> and <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role?page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get Roles - <PERSON><PERSON> Page No", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role?page=&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"], "query": [{"key": "page", "value": ""}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get Roles - Without Page No", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role?page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"], "query": [{"key": "page", "value": "", "disabled": true}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get Roles - <PERSON><PERSON>id Page No Copy", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role?page=545454&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"], "query": [{"key": "page", "value": "545454"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get Roles - <PERSON><PERSON> <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role?page=1&page_size=", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": ""}]}}, "response": []}, {"name": "Get Roles - Without <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role?page=1", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "", "disabled": true}]}}, "response": []}, {"name": "Get Roles - <PERSON><PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role?page=1&page_size=65454", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "65454"}]}}, "response": []}, {"name": "Get Roles - with search", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role?search=DistributorAdmin&page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"], "query": [{"key": "search", "value": "DistributorAdmin"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get Roles - with Invalid search", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["pm.test(\"Results is null\", () => {\r", "  const jsonData = pm.response.json();\r", "  pm.expect(jsonData.results).to.eql([]);\r", "});\r", "\r", "pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role?search=dfndjgfjghfdgdf&page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"], "query": [{"key": "search", "value": "dfndjgfjghfdgdf"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}]}, {"name": "Create Role", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Permissions Role Id - 200", "event": [{"listen": "test", "script": {"exec": ["// Fixed the status code test\r", "pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "var jsonData = pm.response.json();\r", "pm.environment.set(\"permission_id\", jsonData.permission[0]);\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/{{role_id}}/permission", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "{{role_id}}", "permission"]}}, "response": []}, {"name": "Create Role - 201 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData = JSON.parse(responseBody);\r", "pm.environment.set(\"role_id\", jsonData.id);"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["function randomString(length=1) {\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('role_name', randomString(10));\r", "pm.environment.set('discription', randomString(20));"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\r\n    \"{{permission_id}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"]}}, "response": []}, {"name": "Create Role - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\r\n    \"{{permission_id}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/authorization/roleeee", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "<PERSON>eee"]}}, "response": []}, {"name": "Create Role - Create Role Again", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 409\", function () {\r", "    pm.response.to.have.status(409);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\r\n    \"{{permission_id}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"]}}, "response": []}, {"name": "Delete Role - 204", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {\r", "    pm.response.to.have.status(204);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role?role_uuid={{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"], "query": [{"key": "role_uuid", "value": "{{role_id}}"}]}}, "response": []}, {"name": "Create Role -Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\r\n    \"{{permission_id}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"]}}, "response": []}, {"name": "Create Role - Blank Role Name", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\r\n    \"{{permission_id}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"]}}, "response": []}, {"name": "Create Role - Without Role Name", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\r\n    \"{{permission_id}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"]}}, "response": []}, {"name": "Create Role - Blank Description", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData = JSON.parse(responseBody);\r", "pm.environment.set(\"role_id\", jsonData.id);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\r\n    \"{{permission_id}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"]}}, "response": []}, {"name": "Delete Role - 204", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {\r", "    pm.response.to.have.status(204);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role?role_uuid={{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"], "query": [{"key": "role_uuid", "value": "{{role_id}}"}]}}, "response": []}, {"name": "Create Role - Without Description", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData = JSON.parse(responseBody);\r", "pm.environment.set(\"role_id\", jsonData.id);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\r\n    \"{{permission_id}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"]}}, "response": []}, {"name": "Delete Role - 204", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {\r", "    pm.response.to.have.status(204);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role?role_uuid={{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"], "query": [{"key": "role_uuid", "value": "{{role_id}}"}]}}, "response": []}, {"name": "Create Role - Without attributes", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData = JSON.parse(responseBody);\r", "pm.environment.set(\"role_id\", jsonData.id);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\r\n    \"{{permission_id}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"]}}, "response": []}, {"name": "Create Role - Blank RoleGroup", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"\",\r\n  \"permission\": [\r\n    \"{{permission_id}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"]}}, "response": []}, {"name": "Create Role - Blank Permission ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\r\n    \"{{}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"]}}, "response": []}, {"name": "Create Role - Without Permission ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"]}}, "response": []}, {"name": "Create Role - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", "\r", "pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData = JSON.parse(responseBody);\r", "pm.environment.set(\"role_id\", jsonData.id);\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\r\n    \"{{permission_id}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"]}}, "response": []}]}, {"name": "Delete Role", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Delete Role - 204 No Content", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {\r", "    pm.response.to.have.status(204);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role?role_uuid={{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"], "query": [{"key": "role_uuid", "value": "{{role_id}}"}]}}, "response": []}, {"name": "Delete Role - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role?role_uuid={{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"], "query": [{"key": "role_uuid", "value": "{{role_id}}"}]}}, "response": []}, {"name": "Delete Role - Delete Same Role Again", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role?role_uuid={{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"], "query": [{"key": "role_uuid", "value": "{{role_id}}"}]}}, "response": []}, {"name": "Delete Role - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role?role_ud={{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"], "query": [{"key": "role_ud", "value": "{{role_id}}"}]}}, "response": []}, {"name": "Delete Role - Invalid Role ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role?role_uuid={{ff44gfg5f44g5}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"], "query": [{"key": "role_uuid", "value": "{{ff44gfg5f44g5}}"}]}}, "response": []}, {"name": "Delete Role - 204 Blank Role ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role?role_uuid={{}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"], "query": [{"key": "role_uuid", "value": "{{}}"}]}}, "response": []}, {"name": "Delete Role - Without Role ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role?role_uuid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"], "query": [{"key": "role_uuid", "value": null}]}}, "response": []}, {"name": "Create Role - 201 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData = JSON.parse(responseBody);\r", "pm.environment.set(\"role_id\", jsonData.id);"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["function randomString(length=1) {\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('role_name', randomString(10));\r", "pm.environment.set('discription', randomString(20));"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\r\n    \"{{permission_id}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"]}}, "response": []}, {"name": "Delete Role - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {\r", "    pm.response.to.have.status(204);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role?role_uuid={{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"], "query": [{"key": "role_uuid", "value": "{{role_id}}"}]}}, "response": []}]}, {"name": "Get Permissions Role Id", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Create Role - 201 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData = JSON.parse(responseBody);\r", "pm.environment.set(\"role_id\", jsonData.id);"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["function randomString(length=1) {\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('role_name', randomString(10));\r", "pm.environment.set('discription', randomString(20));"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{role_name}}\",\r\n  \"description\": \"{{discription}}\",\r\n  \"attributes\": {},\r\n  \"roleGroup\": \"My Organization\",\r\n  \"permission\": [\r\n    \"{{permission_id}}\"\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"]}}, "response": []}, {"name": "Get Permissions Role Id - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/{{role_id}}/permission", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "{{role_id}}", "permission"]}}, "response": []}, {"name": "Get Permissions Role Id - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/{{role_id}}/permission", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "{{role_id}}", "permission"]}}, "response": []}, {"name": "Get Permissions Role Id - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authoriztion/{{role_id}}/perssion", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authoriztion", "{{role_id}}", "perssion"]}}, "response": []}, {"name": "Get Permissions Role Id - Blank Role ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/{{}}/permission", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "{{}}", "permission"]}}, "response": []}, {"name": "Get Permissions Role Id - Without Role ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/permission", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "permission"]}}, "response": []}, {"name": "Get Permissions Role Id - Invalid Role ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/{{gf4545gf}}/permission", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "{{gf4545gf}}", "permission"]}}, "response": []}, {"name": "Get Permissions Role Id - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/{{role_id}}/permission", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "{{role_id}}", "permission"]}}, "response": []}, {"name": "Delete Role - 204", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {\r", "    pm.response.to.have.status(204);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/authorization/role?role_uuid={{role_id}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "authorization", "role"], "query": [{"key": "role_uuid", "value": "{{role_id}}"}]}}, "response": []}]}, {"name": "Generate Access Token", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Generate Access Token - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response body has 'Access Token' key\", function () {\r", "    const responseBody = pm.response.json();\r", "    pm.expect(responseBody).to.have.property('access_token');\r", "});\r", "\r", "pm.test(\"Response body has 'Expire Token' key\", function () {\r", "    const responseBody = pm.response.json();\r", "    pm.expect(responseBody).to.have.property('expires_in');\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/api/token", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "api", "token"]}}, "response": []}, {"name": "Generate Access Token - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 404', function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/api/toen", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "api", "toen"]}}, "response": []}, {"name": "Generate Access Token - Invalid Method", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/api/token", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "api", "token"]}}, "response": []}, {"name": "Generate Access Token - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 401', function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/api/token", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "api", "token"]}}, "response": []}, {"name": "Generate Access Token - Response < 500 ms", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/api/token", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "api", "token"]}}, "response": []}]}]}