{"info": {"_postman_id": "3c6621aa-e262-43cc-8fe1-30afd359be74", "name": "AutomationRule_DA", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "29298941", "_collection_link": "https://nextgenclearing.postman.co/workspace/QA-_Team_SPOG~66755c34-5aed-4dcf-af75-9956e2650e16/collection/29298941-3c6621aa-e262-43cc-8fe1-30afd359be74?action=share&source=collection_link&creator=29298941"}, "item": [{"name": "Get Rule Unit Enum", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Rule Unit Enum - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/rule-unit", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "rule-unit"]}}, "response": []}, {"name": "Get Rule Unit Enum - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/rue-unit", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "rue-unit"]}}, "response": []}, {"name": "Get Rule Unit Enum - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/rule-unit", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "rule-unit"]}}, "response": []}, {"name": "Get Rule Unit Enum - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/rule-unit", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "rule-unit"]}}, "response": []}, {"name": "Get Rule Unit Enum - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/rule-unit", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "rule-unit"]}}, "response": []}]}, {"name": "Get Rule Action Enum", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Rule Action Enum - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/rule-action", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "rule-action"]}}, "response": []}, {"name": "Get Rule Action Enum - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/rue-action", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "rue-action"]}}, "response": []}, {"name": "Get Rule Action Enum - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/rule-action", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "rule-action"]}}, "response": []}, {"name": "Get Rule Action Enum - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/rule-action", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "rule-action"]}}, "response": []}, {"name": "Get Rule Action Enum - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/rule-action", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "rule-action"]}}, "response": []}]}, {"name": "Create Rule", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Create Rule - 201 OK", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData = pm.response.json();\r", "pm.environment.set(\"rule_uuid\", jsonData.uuids[0].uuid);\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Rule Already Exist", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 409\", function () {\r", "    pm.response.to.have.status(409);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Negative Value", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": -45455,\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Invalid URL", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n     \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"Rule3\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": 40015,\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rue", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rue"]}}, "response": []}, {"name": "Create Rule - Invalid Method", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n     \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"Rule3\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": 40015,\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Blank Action", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n     \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Without Action", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Invalid Action", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivat\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Delete Rule - 204", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {\r", "    pm.response.to.have.status(204);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?rules_uuid={{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "rules_uuid", "value": "{{rule_uuid}}"}]}}, "response": []}, {"name": "Create Rule - Blank Value", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData = pm.response.json();\r", "pm.environment.set(\"rule_uuid\", jsonData.uuids[0].uuid);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Delete Rule - 204", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {\r", "    pm.response.to.have.status(204);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?rules_uuid={{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "rules_uuid", "value": "{{rule_uuid}}"}]}}, "response": []}, {"name": "Create Rule - Invalid Value", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData = pm.response.json();\r", "pm.environment.set(\"rule_uuid\", jsonData.uuids[0].uuid);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billi\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Delete Rule - 204", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {\r", "    pm.response.to.have.status(204);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?rules_uuid={{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "rules_uuid", "value": "{{rule_uuid}}"}]}}, "response": []}, {"name": "Create Rule - Without Value", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Blank ID", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": ,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Invalid ID", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n   {\r\n      \"id\": 14485,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Without ID", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Blank ID Notification", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": ,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Invalid ID Notification", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 13533,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Without ID Notification", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Blank Notification", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": ,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Invalid Notification", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": truee,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Without Notification", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Blank ID Notification", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": ,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Invalid ID Notification", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1693,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Without ID Notification", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Blank Notification Value", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Invalid Notification Value", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"vm@exmaplfdgecom\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Without Notification Value", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Blank RuleTypeID", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": ,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Invalid RuleTypeID", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1639552,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Without RuleTypeID", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Blank RuleCategoryId", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": ,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Invalid RuleCategoryId", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 162314,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Without RuleCategoryId", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Blank Rule Name", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Invalid Rule Name", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"frdsfnrdjg drg dfjgndj gmdnfg fdkg fdmg fdkg fdmgfdkgjndgdmfdignd gnsifg dnfmg digndfng djgndfj gdmfghdgdg\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Without Rule Name", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Blank RuleDefinitionId", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": \"\",\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Invalid RuleDefinitionId", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n     \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 165641,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Without RuleDefinitionId", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Blank DataVolume", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": \"\",\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Invalid DataVolume", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": 59949845gfgf1548415,\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Without DataVolume", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Blank Unit", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": \"{{Data_Volume}}\",\r\n  \"unit\": \"\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Invalid Unit", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KBBB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Without Unit", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Blank Status", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": \"{{Data_Volume}}\",\r\n  \"unit\": \"MB\",\r\n  \"status\": \"\",\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Invalid Status", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": trueed,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Without Status", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Unauthorized", "event": [{"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"Rule3\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": 40015,\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Create Rule - Response time < 500ms", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", "\r", "var jsonData = pm.response.json();\r", "pm.environment.set(\"rule_uuid\", jsonData.uuids[0].uuid);\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"I don't want to reactivate\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Delete Rule - 204", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {\r", "    pm.response.to.have.status(204);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?rules_uuid={{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "rules_uuid", "value": "{{rule_uuid}}"}]}}, "response": []}]}, {"name": "Get Rule", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Rule - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "var jsonData = pm.response.json();\r", "pm.environment.set(\"rule_uuid\", jsonData.results[0].uuid);\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?ordering=rule_type&page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "ordering", "value": "rule_type"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get Rule - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?ordering=rule_type&page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "ordering", "value": "rule_type"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get Rule - Check Response Json", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response structure is correct\", function () {\r", "    pm.expect(pm.response.json()).to.have.property(\"page\");\r", "    pm.expect(pm.response.json()).to.have.property(\"pageSize\");\r", "    pm.expect(pm.response.json()).to.have.property(\"lastPage\");\r", "    pm.expect(pm.response.json()).to.have.property(\"totalCount\");\r", "    pm.expect(pm.response.json()).to.have.property(\"summary\");\r", "    pm.expect(pm.response.json()).to.have.property(\"results\");\r", "\r", "    pm.expect(pm.response.json().results).to.be.an(\"array\").that.is.not.empty;\r", "\r", "    var result = pm.response.json().results[0];\r", "    pm.expect(result).to.have.property(\"uuid\");\r", "    pm.expect(result).to.have.property(\"accountName\");\r", "    pm.expect(result).to.have.property(\"ruleName\");\r", "    pm.expect(result).to.have.property(\"dataVolume\");\r", "    pm.expect(result).to.have.property(\"unit\");\r", "    pm.expect(result).to.have.property(\"status\");\r", "    pm.expect(result).to.have.property(\"action\");\r", "    pm.expect(result).to.have.property(\"notification\");\r", "    pm.expect(result).to.have.property(\"logoKey\");\r", "    pm.expect(result).to.have.property(\"logoUrl\");\r", "    pm.expect(result).to.have.property(\"ruleType\");\r", "    pm.expect(result).to.have.property(\"ruleCategory\");\r", "    pm.expect(result).to.have.property(\"ruleDefinition\");\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?ordering=rule_type&page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "ordering", "value": "rule_type"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get Rule - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rle?ordering=rule_type&page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rle"], "query": [{"key": "ordering", "value": "rule_type"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get Rule - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?ordering=rule_type&page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "ordering", "value": "rule_type"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get Rule - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?ordering=rule_type&page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "ordering", "value": "rule_type"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get Rule - Blank Ordering", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?ordering=&page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "ordering", "value": ""}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get Rule - Without Ordering", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "ordering", "value": "", "disabled": true}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get Rule - Invalid Ordering", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?ordering=ruletype&page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "ordering", "value": "ruletype"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get Rule - <PERSON><PERSON> Page", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?ordering=rule_type&page=&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "ordering", "value": "rule_type"}, {"key": "page", "value": ""}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get Rule - <PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?ordering=rule_type&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "ordering", "value": "rule_type"}, {"key": "page", "value": "", "disabled": true}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get Rule - <PERSON><PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?ordering=rule_type&page=123564&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "ordering", "value": "rule_type"}, {"key": "page", "value": "123564"}, {"key": "page_size", "value": "50"}]}}, "response": []}]}, {"name": "Get Rule Category", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Rule Category - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/category?type_id=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "category"], "query": [{"key": "type_id", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Rule Category - Check Response Json", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response structure is correct\", function () {\r", "    pm.expect(pm.response.json()).to.have.property(\"page\");\r", "    pm.expect(pm.response.json()).to.have.property(\"pageSize\");\r", "    pm.expect(pm.response.json()).to.have.property(\"lastPage\");\r", "    pm.expect(pm.response.json()).to.have.property(\"totalCount\");\r", "    pm.expect(pm.response.json()).to.have.property(\"summary\");\r", "    pm.expect(pm.response.json()).to.have.property(\"results\");\r", "\r", "    pm.expect(pm.response.json().results).to.be.an(\"array\").that.is.not.empty;\r", "\r", "    var result = pm.response.json().results[0];\r", "    pm.expect(result).to.have.property(\"id\");\r", "    pm.expect(result).to.have.property(\"ruleTypeId\");\r", "    pm.expect(result).to.have.property(\"ruleType\");\r", "    pm.expect(result).to.have.property(\"category\");\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/category?type_id=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "category"], "query": [{"key": "type_id", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Rule Category - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/category?type_id=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "category"], "query": [{"key": "type_id", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Rule Category - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/cateory?type_id=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "cateory"], "query": [{"key": "type_id", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Rule Category - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/category?type_id=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "category"], "query": [{"key": "type_id", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Rule Category - Without Ordering", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/category?type_id=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "category"], "query": [{"key": "type_id", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id", "disabled": true}]}}, "response": []}, {"name": "Get Rule Category - Blank Ordering", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/category?type_id=1&page_size=50&ordering=i", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "category"], "query": [{"key": "type_id", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "i"}]}}, "response": []}, {"name": "Get Rule Category - Invalid Ordering", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/category?type_id=1&page_size=50&ordering=dsfdsg4545sdfdg", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "category"], "query": [{"key": "type_id", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "dsfdsg4545sdfdg"}]}}, "response": []}, {"name": "Get Rule Category - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/category?type_id=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "category"], "query": [{"key": "type_id", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}]}, {"name": "Get Action", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Action - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/action?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "action"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Action - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/action?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "action"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Action - Check Response Json", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response structure is correct\", function () {\r", "    pm.expect(pm.response.json()).to.have.property(\"page\");\r", "    pm.expect(pm.response.json()).to.have.property(\"pageSize\");\r", "    pm.expect(pm.response.json()).to.have.property(\"lastPage\");\r", "    pm.expect(pm.response.json()).to.have.property(\"totalCount\");\r", "    pm.expect(pm.response.json()).to.have.property(\"summary\");\r", "    pm.expect(pm.response.json()).to.have.property(\"results\");\r", "\r", "    pm.expect(pm.response.json().results).to.be.an(\"array\").that.is.not.empty;\r", "\r", "    var result = pm.response.json().results[0];\r", "    pm.expect(result).to.have.property(\"id\");\r", "    pm.expect(result).to.have.property(\"action\");\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/action?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "action"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Action - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rue/acton?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rue", "acton"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Action - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/action?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "action"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Action - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/action?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "action"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Action - Blank Ordering", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/action?page=1&page_size=50&ordering=", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "action"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": ""}]}}, "response": []}, {"name": "Get Action - Without Ordering", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/action?page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "action"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "", "disabled": true}]}}, "response": []}, {"name": "Get Action - Invalid Ordering", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/action?page=1&page_size=50&ordering=fgfdgfdg", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "action"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "fgfdgfdg"}]}}, "response": []}]}, {"name": "Update Rule", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Create Rule - 201 OK", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData = pm.response.json();\r", "pm.environment.set(\"rule_uuid\", jsonData.uuids[0].uuid);\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"I don't want to reactivate\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Update Rule - 201 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);\r", "\r", "const randomEmail = `${randomString(10)}@example.com`;\r", "pm.environment.set('Random_Email', randomEmail);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n     \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"{{Random_Email}}\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule?rules_uuid={{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "rules_uuid", "value": "{{rule_uuid}}"}]}}, "response": []}, {"name": "Update Rule - Rule Not Found", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);\r", "\r", "const randomEmail = `${randomString(10)}@example.com`;\r", "pm.environment.set('Random_Email', randomEmail);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n     \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"{{Random_Email}}\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule?rules_uuid=9f2d7ca7-d376-466c-98cc-9316dbff666e", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "rules_uuid", "value": "9f2d7ca7-d376-466c-98cc-9316dbff666e"}]}}, "response": []}, {"name": "Update Rule - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n     \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"Chocha1\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": 40005,\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule?rules_uuid={{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "rules_uuid", "value": "{{rule_uuid}}"}]}}, "response": []}, {"name": "Update Rule - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n     \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"Chocha1\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": 40005,\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rue?rules_uuid={{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rue"], "query": [{"key": "rules_uuid", "value": "{{rule_uuid}}"}]}}, "response": []}, {"name": "Update Rule - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n     \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"Chocha1\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": 40005,\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule?rules_uuid={{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "rules_uuid", "value": "{{rule_uuid}}"}]}}, "response": []}, {"name": "Update Rule - Without UUID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n     \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"Chocha1\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": 40005,\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule?rules_uuid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "rules_uuid", "value": null}]}}, "response": []}, {"name": "Update Rule - Invalid UUID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n     \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"Chocha1\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": 40005,\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule?rules_uuid=7c4b7-60c1-481d-a689-34fbb2523", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "rules_uuid", "value": "7c4b7-60c1-481d-a689-34fbb2523"}]}}, "response": []}, {"name": "Update Rule - Blank UUID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n     \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"Chocha1\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": 40005,\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule?rules_uuid={{}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "rules_uuid", "value": "{{}}"}]}}, "response": []}, {"name": "Update Rule - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);\r", "\r", "const randomEmail = `${randomString(10)}@example.com`;\r", "pm.environment.set('Random_Email', randomEmail);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n     \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"{{Random_Email}}\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule?rules_uuid={{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "rules_uuid", "value": "{{rule_uuid}}"}]}}, "response": []}]}, {"name": "Get Notification", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Notification - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/notification?notification_id=1&page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "notification"], "query": [{"key": "notification_id", "value": "1"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Notification - Check Response", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response structure is correct\", function () {\r", "    pm.expect(pm.response.json()).to.have.property(\"page\");\r", "    pm.expect(pm.response.json()).to.have.property(\"pageSize\");\r", "    pm.expect(pm.response.json()).to.have.property(\"lastPage\");\r", "    pm.expect(pm.response.json()).to.have.property(\"totalCount\");\r", "    pm.expect(pm.response.json()).to.have.property(\"summary\");\r", "    pm.expect(pm.response.json()).to.have.property(\"results\");\r", "\r", "    pm.expect(pm.response.json().results).to.be.an(\"array\").that.is.not.empty;\r", "\r", "    var result = pm.response.json().results[0];\r", "    pm.expect(result).to.have.property(\"id\");\r", "    pm.expect(result).to.have.property(\"notification\").to.eql(\"Send email\");\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/notification?notification_id=1&page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "notification"], "query": [{"key": "notification_id", "value": "1"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Notification - Without Notification ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/notification?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "notification"], "query": [{"key": "notification_id", "value": "1", "disabled": true}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Notification - Blank Notification ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/notification?notification_id=&page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "notification"], "query": [{"key": "notification_id", "value": ""}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Notification - Invalid Notification ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/notification?notification_id=5446464&page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "notification"], "query": [{"key": "notification_id", "value": "5446464"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Notification - Method Not Allowed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/notification?notification_id=1&page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "notification"], "query": [{"key": "notification_id", "value": "1"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Notification - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rue/notifction?notification_id=1&page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rue", "notifction"], "query": [{"key": "notification_id", "value": "1"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Notification - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/notification?notification_id=1&page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "notification"], "query": [{"key": "notification_id", "value": "1"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Notification - Response time <500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/notification?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "notification"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}]}, {"name": "Get Rule Type", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Rule Type - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/type?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "type"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Rule Type - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/type?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "type"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Rule Type - Check Response Json", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response structure is correct\", function () {\r", "    pm.expect(pm.response.json()).to.have.property(\"page\");\r", "    pm.expect(pm.response.json()).to.have.property(\"pageSize\");\r", "    pm.expect(pm.response.json()).to.have.property(\"lastPage\");\r", "    pm.expect(pm.response.json()).to.have.property(\"totalCount\");\r", "    pm.expect(pm.response.json()).to.have.property(\"summary\");\r", "    pm.expect(pm.response.json()).to.have.property(\"results\");\r", "\r", "    pm.expect(pm.response.json().results).to.be.an(\"array\").that.is.not.empty;\r", "\r", "    var result = pm.response.json().results[0];\r", "    pm.expect(result).to.have.property(\"id\");\r", "    pm.expect(result).to.have.property(\"ruleType\");\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/type?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "type"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Rule Type - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rue/tpe?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rue", "tpe"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Rule Type - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/type?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "type"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Rule Type - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/type?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "type"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Rule Type - Blank Ordering", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/type?page=1&page_size=50&ordering=", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "type"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": ""}]}}, "response": []}, {"name": "Get Rule Type - Without Ordering", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/type?page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "type"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id", "disabled": true}]}}, "response": []}, {"name": "Get Rule Type - Invalid Ordering", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/type?page=1&page_size=50&ordering=iddf5f44", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "type"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "iddf5f44"}]}}, "response": []}]}, {"name": "Get Rule by UUID", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Rule - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "var jsonData = pm.response.json();\r", "pm.environment.set(\"rule_uuid\", jsonData.results[0].uuid);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?ordering=rule_type&page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "ordering", "value": "rule_type"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Get Rule by UUID - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/{{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "{{rule_uuid}}"]}}, "response": []}, {"name": "Get Rule by UUID - 404 Not found", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/9f967ca7-d375-455c-98cc-931625ff655e", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "9f967ca7-d375-455c-98cc-931625ff655e"]}}, "response": []}, {"name": "Get Rule by UUID - Check Response", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/{{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "{{rule_uuid}}"]}}, "response": []}, {"name": "Get Rule by UUID - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rle/{{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rle", "{{rule_uuid}}"]}}, "response": []}, {"name": "Get Rule by UUID - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/{{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "{{rule_uuid}}"]}}, "response": []}, {"name": "Get Rule by UUID - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/{{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "{{rule_uuid}}"]}}, "response": []}, {"name": "Get Rule by UUID - Invalid UUID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/c9195e-1108-4797-bc1a-bd0d576e72", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "c9195e-1108-4797-bc1a-bd0d576e72"]}}, "response": []}, {"name": "Get Rule by UUID - Blank UUID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/{{}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "{{}}"]}}, "response": []}, {"name": "Get Rule by UUID - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/{{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "{{rule_uuid}}"]}}, "response": []}]}, {"name": "Get Rule Definition", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Rule Definition - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/1/definition?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "1", "definition"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Rule Definition - Check Response", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response structure is correct\", function () {\r", "    pm.expect(pm.response.json()).to.have.property(\"page\");\r", "    pm.expect(pm.response.json()).to.have.property(\"pageSize\");\r", "    pm.expect(pm.response.json()).to.have.property(\"lastPage\");\r", "    pm.expect(pm.response.json()).to.have.property(\"totalCount\");\r", "    pm.expect(pm.response.json()).to.have.property(\"summary\");\r", "    pm.expect(pm.response.json()).to.have.property(\"results\");\r", "\r", "    pm.expect(pm.response.json().results).to.be.an(\"array\").that.is.not.empty;\r", "\r", "    var result = pm.response.json().results[0];\r", "    pm.expect(result).to.have.property(\"id\");\r", "    pm.expect(result).to.have.property(\"definition\");\r", "    pm.expect(result).to.have.property(\"category\");\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/1/definition?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "1", "definition"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Rule Definition - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rue/1/defnton", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rue", "1", "defnton"]}}, "response": []}, {"name": "Get Rule Definition - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/1/definition?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "1", "definition"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Rule Definition - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/1/definition?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "1", "definition"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}, {"name": "Get Rule Definition - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/1/definition?page=1&page_size=50&ordering=id", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "1", "definition"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}, {"key": "ordering", "value": "id"}]}}, "response": []}]}, {"name": "Patch Rule Status by Rule UUID", "item": [{"name": "Get Rule - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "var jsonData = pm.response.json();\r", "pm.environment.set(\"rule_uuid\", jsonData.results[0].uuid);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?ordering=rule_type&page=1&page_size=50", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "ordering", "value": "rule_type"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "50"}]}}, "response": []}, {"name": "Patch Rule Status by Rule UUID - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/{{rule_uuid}}/true", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "{{rule_uuid}}", "true"]}}, "response": []}, {"name": "Patch Rule Status by Rule UUID - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rue/{{rule_uuid}}/true", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rue", "{{rule_uuid}}", "true"]}}, "response": []}, {"name": "Patch Rule Status by Rule UUID - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/{{rule_uuid}}/true", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "{{rule_uuid}}", "true"]}}, "response": []}, {"name": "Patch Rule Status by Rule UUID - Without Rule UUID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule//true", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "", "true"]}}, "response": []}, {"name": "Patch Rule Status by Rule UUID - Invalid Rule UUID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/f79982-1b29-4d6e-a4f8-11f61890c/true", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "f79982-1b29-4d6e-a4f8-11f61890c", "true"]}}, "response": []}, {"name": "Patch Rule Status by Rule UUID - Blank Rule UUID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/{{}}/true", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "{{}}", "true"]}}, "response": []}, {"name": "Patch Rule Status by Rule UUID - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/{{rule_uuid}}/true", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "{{rule_uuid}}", "true"]}}, "response": []}, {"name": "Patch Rule Status by Rule UUID - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule/{{rule_uuid}}/true", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule", "{{rule_uuid}}", "true"]}}, "response": []}]}, {"name": "Delete Rule", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Delete Rule - 204 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {\r", "    pm.response.to.have.status(204);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?rules_uuid={{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "rules_uuid", "value": "{{rule_uuid}}"}]}}, "response": []}, {"name": "Delete Rule - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?ruls_uid={{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "ruls_uid", "value": "{{rule_uuid}}"}]}}, "response": []}, {"name": "Delete Rule - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?rules_uuid={{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "rules_uuid", "value": "{{rule_uuid}}"}]}}, "response": []}, {"name": "Delete Rule - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?rules_uuid={{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "rules_uuid", "value": "{{rule_uuid}}"}]}}, "response": []}, {"name": "Delete Rule - Without UUID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?rules_uuid", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "rules_uuid", "value": null}]}}, "response": []}, {"name": "Delete Rule - Blank UUID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?rules_uuid={{}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "rules_uuid", "value": "{{}}"}]}}, "response": []}, {"name": "Delete Rule - Invalid UUID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?rules_uuid=7c4c17-60c1-41d-a689-34dfbb2523", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "rules_uuid", "value": "7c4c17-60c1-41d-a689-34dfbb2523"}]}}, "response": []}, {"name": "Create Rule - 201 OK", "event": [{"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Rule_Name', randomString(10));\r", "\r", "\r", "function generateRandomNumber(startValue) {\r", "    const randomNumber = startValue + Math.floor(Math.random() * 9000);\r", "    return randomNumber;\r", "}\r", "const randomNum = generateRandomNumber(1000);\r", "\r", "pm.environment.set(\"Data_Volume\", randomNum);"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData = pm.response.json();\r", "pm.environment.set(\"rule_uuid\", jsonData.uuid);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n         \"accountId\": [\r\n    {{account_id}}\r\n  ],\r\n  \"ruleTypeId\": 1,\r\n  \"ruleCategoryId\": 1,\r\n  \"ruleName\": \"{{Rule_Name}}\",\r\n  \"ruleDefinitionId\": 1,\r\n  \"dataVolume\": {{Data_Volume}},\r\n  \"unit\": \"KB\",\r\n  \"status\": true,\r\n  \"action\": [\r\n    {\r\n      \"id\": 1,\r\n      \"action\": \"Reactivate Sim\",\r\n      \"value\": \"Next billing cycle\"\r\n    }\r\n  ],\r\n  \"notification\": [\r\n    {\r\n      \"id\": 1,\r\n      \"notification\": true,\r\n      \"notificationValue\": [\r\n        \"<EMAIL>\"\r\n      ]\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rule", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"]}}, "response": []}, {"name": "Delete Rule - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {\r", "    pm.response.to.have.status(204);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rule?rules_uuid={{rule_uuid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rule"], "query": [{"key": "rules_uuid", "value": "{{rule_uuid}}"}]}}, "response": []}]}]}