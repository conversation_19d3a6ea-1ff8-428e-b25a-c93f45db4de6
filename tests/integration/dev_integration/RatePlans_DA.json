{"info": {"_postman_id": "b716e8a5-d196-423e-90d6-c922c9634e24", "name": "Rate Plans_DA", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "29298941", "_collection_link": "https://nextgenclearing.postman.co/workspace/QA-_Team_SPOG~66755c34-5aed-4dcf-af75-9956e2650e16/collection/29298941-b716e8a5-d196-423e-90d6-c922c9634e24?action=share&source=collection_link&creator=29298941"}, "item": [{"name": "Rate Plans", "item": [{"name": "Create Rate Plan", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Create Account - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "var jsonData= JSON.parse(responseBody);\r", " pm.environment.set(\"AccountId\", jsonData.id);"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Name', randomString(10));\r", "\r", "var moment = require('moment');\r", "\r", "var enddate = moment().add('months', 1).date(-0);\r", "pm.environment.set('contractEndDate', enddate.format((\"YYYY-MM-DD\")));"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{Name}}\",\r\n  \"agreementNumber\": \"************\",\r\n  \"logoKey\": null,\r\n  \"status\": \"ACTIVE\",\r\n  \"salesChannel\": \"WHOLESALE\",\r\n  \"salesPerson\": \"QA_Test\",\r\n  \"industryVertical\": \"AEROSPACE\",\r\n  \"contractEndDate\": \"{{contractEndDate}}\",\r\n  \"isBillable\": true,\r\n  \"contactName\": \"uma\",\r\n  \"email\": \"{{Username}}\",\r\n  \"phone\": \"*************\",\r\n  \"jobTitle\": \"QA\",\r\n  \"country\": \"UK\",\r\n  \"stateRegion\": \"aaaaa\",\r\n  \"city\": \"bbbbb\",\r\n  \"address1\": \"abc123\",\r\n  \"address2\": \"xyzaas\",\r\n  \"postcode\": \"wsdddd\",\r\n  \"thresholdCharge\": 0,\r\n  \"warningThreshold\": 0,\r\n  \"simCharge\": 0,\r\n  \"paymentTerms\": 99,\r\n  \"currency\": \"GBR\",\r\n  \"productTypes\": [\r\n    \"NATIONAL_ROAMING\"\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/accounts", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "accounts"]}}, "response": []}, {"name": "Create Rate Plan - 201 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData= JSON.parse(responseBody);\r", " pm.environment.set( \"rateplanid\", jsonData.id);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Delete Rate Plan - 202", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 202\", function () {\r", "    pm.response.to.have.status(202);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Create Rate Plan - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", "\r", "var jsonData= JSON.parse(responseBody);\r", " pm.environment.set( \"rateplanid\", jsonData.id);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Pay as You Go plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Pay as You Go plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-pas", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-pas"]}}, "response": []}, {"name": "Create Rate Plan - Method Not Allowed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Pay as You Go plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Delete Rate Plan - 202", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 202\", function () {\r", "    pm.response.to.have.status(202);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Create Rate Plan  - Blank Account ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": \"\",\r\n  \"name\": \"Pay as You Go plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Negative Account ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": -20,\r\n  \"name\": \"Pay as You Go plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - With Account ID Alpha", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": \"abc\",\r\n  \"name\": \"Pay as You Go plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan  - Without Account ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"Pay as You Go plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Blank Name", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Without Name", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Zero AccessFee", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Pay as You Go plan\",\r\n  \"accessFee\": 0.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Without AccessFee", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Decimal AccessFee", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.05,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Max Length AccessFee", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": **********,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Symbol AccessFee", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": \"***\",\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Alphanum AccessFee", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": \"test123\",\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Alpha AccessFee", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": \"test\",\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is \", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "var jsonData= JSON.parse(responseBody);\r", " pm.environment.set( \"rateplanid\", jsonData.id);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Delete Rate Plan - 202", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 202\", function () {\r", "    pm.response.to.have.status(202);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Create Rate Plan - Blank Currency", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Number Currency", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "var jsonData= JSON.parse(responseBody);\r", " pm.environment.set( \"rateplanid\", jsonData.id);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"123\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Symbol Currency", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Test111\",\r\n  \"accessFee\":12,\r\n  \"currency\": \"***\",\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": \"PAYG\",\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1000,\r\n            \"value\": 2\r\n          },\r\n          {\r\n            \"rangeFrom\": 1000,\r\n            \"value\": 1\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": \"PAYG\",\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 100,\r\n            \"value\":2\r\n          },\r\n          {\r\n            \"rangeFrom\": 100,\r\n            \"value\": 1\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": \"PAYG\",\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"value\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": \"PAYG\",\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 200,\r\n            \"value\": 3\r\n          },\r\n          {\r\n            \"rangeFrom\": 200,\r\n            \"value\": 1\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Delete Rate Plan - 202", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 202\", function () {\r", "    pm.response.to.have.status(202);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Create Rate Plan - Max Length Currency", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBPST\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Max Length Currency", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GB\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Remove Currency Field", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Blank Origination Zones", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GB\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Invalid Org Zone", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK25%\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Min Length Org Zone", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"U\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Max Length Org Zone", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UKIFKNJGBJ\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Number Org Zone", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"454516\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Without Org Zone", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - PAYG - Alpha - Data Rate Range", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": \"abc\",\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - PAYG - Negative - Data Rate Range", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": -20,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - PAYG - Invalid Range Unit", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MBB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Individual - Invalid Range Unit", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Minn\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Fixed - Invalid Range Unit", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Minn\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Flexible - Invalid Range Unit", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMSS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - PAYG -  Invalid Price Unit", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GBB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Individual -  Invalid Price Unit", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GBB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Fixed -  Invalid Price Unit", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Minn\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Flexible - Invalid Price Unit", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMSS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Individual - Invalid Overage Unit", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Minn\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Fixed - Invalid Overage Unit", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Minn\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Mins\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Flexible - Invalid Overage Unit", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {\r", "    pm.response.to.have.status(400);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMSS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMSs\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - PAYG -  Invalid IsOverage", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": \"fals\"\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Individual -  Invalid IsOverage", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": \"fals\",\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Fixed - Invalid IsOverage", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": \"fals\",\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Flexible - Invalid IsOverage", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": \"fals\",\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Individual -  Invalid Overage Fee", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": \"test123\",\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": \"fals\",\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Fixed - Invalid Overage Fee", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": \"test123\",\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Flexible - Invalid Overage Fee", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": \"test123\",\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Individual -  Invalid OveragePer", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": \"cd123\"\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Fixed - Invalid OveragePer", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": \"cd123\"\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Create Rate Plan - Flexible - Invalid OveragePer", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": [""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": \"cd123\"\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Delete_Account - 202", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 202\", function () {\r", "    pm.response.to.have.status(202);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{BaseURL}}/v1/glass/accounts/{{AccountId}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "accounts", "{{AccountId}}"]}}, "response": []}, {"name": "Create Rate Plan  - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\":{{AccountId}},\r\n  \"name\": \"Pay as You Go plan \",\r\n  \"accessFee\": 25,\r\n  \"currency\": \"GBP\",\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": \"PAYG\",\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1000,\r\n            \"value\": 2\r\n          },\r\n          {\r\n            \"rangeFrom\": 1000,\r\n            \"value\": 1\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": \"PAYG\",\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 100,\r\n            \"value\":2\r\n          },\r\n          {\r\n            \"rangeFrom\": 100,\r\n            \"value\": 1\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": \"PAYG\",\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"value\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": \"PAYG\",\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 200,\r\n            \"value\": 3\r\n          },\r\n          {\r\n            \"rangeFrom\": 200,\r\n            \"value\": 1\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}]}, {"name": "Rate Plans by Accounts", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Rate Plans by Accounts Valid Account ID - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/by-accounts", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "by-accounts"]}}, "response": []}, {"name": "Rate Plans by Accounts Valid Account ID - Invalid uRL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plns/by-aounts", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plns", "by-aounts"]}}, "response": []}, {"name": "Rate Plans by Accounts Valid Account ID - Method Not Allowed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/by-accounts", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "by-accounts"]}}, "response": []}, {"name": "Rate Plans by Accounts Valid Account ID - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/by-accounts", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "by-accounts"]}}, "response": []}, {"name": "Rate Plans by Accounts Valid Account ID - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/by-accounts", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "by-accounts"]}}, "response": []}]}, {"name": "Rate Plans Model by ID", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Create Account - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "\r", "var jsonData= JSON.parse(responseBody);\r", " pm.environment.set(\"AccountId\", jsonData.id);"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Name', randomString(10));\r", "\r", "var moment = require('moment');\r", "\r", "var enddate = moment().add('months', 1).date(-0);\r", "pm.environment.set('contractEndDate', enddate.format((\"YYYY-MM-DD\")));"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{Name}}\",\r\n  \"agreementNumber\": \"************\",\r\n  \"logoKey\": null,\r\n  \"status\": \"ACTIVE\",\r\n  \"salesChannel\": \"WHOLESALE\",\r\n  \"salesPerson\": \"viraj\",\r\n  \"industryVertical\": \"AEROSPACE\",\r\n  \"contractEndDate\": \"{{contractEndDate}}\",\r\n  \"isBillable\": true,\r\n  \"contactName\": \"uma\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"phone\": \"*************\",\r\n  \"jobTitle\": \"QA\",\r\n  \"country\": \"UK\",\r\n  \"stateRegion\": \"aaaaa\",\r\n  \"city\": \"bbbbb\",\r\n  \"address1\": \"abc123\",\r\n  \"address2\": \"xyzaas\",\r\n  \"postcode\": \"wsdddd\",\r\n  \"thresholdCharge\": 0,\r\n  \"warningThreshold\": 0,\r\n  \"simCharge\": 0,\r\n  \"paymentTerms\": 99,\r\n  \"currency\": \"GBR\",\r\n  \"productTypes\": [\r\n    \"NATIONAL_ROAMING\"\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/accounts", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "accounts"]}}, "response": []}, {"name": "Create Rate Plan - 201 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData= JSON.parse(responseBody);\r", " pm.environment.set( \"rateplanid\", jsonData.id);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Get Rate Plans by Account ID - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/by-accounts/{{AccountId}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "by-accounts", "{{AccountId}}"]}}, "response": []}, {"name": "Get Rate Plans by Account ID - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/by-acunts/{{AccountId}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "by-acunts", "{{AccountId}}"]}}, "response": []}, {"name": "Get Rate Plans by Account ID - Method Not Allowed", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/by-accounts/{{AccountId}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "by-accounts", "{{AccountId}}"]}}, "response": []}, {"name": "Get Rate Plans by Account ID - Invalid Account ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/by-accounts/0101045", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "by-accounts", "0101045"]}}, "response": []}, {"name": "Get Rate Plans by Account ID -  Alphanumeric", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/by-accounts/b2", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "by-accounts", "b2"]}}, "response": []}, {"name": "Get Rate Plans by Account ID -  Symbol", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/by-accounts/*", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "by-accounts", "*"]}}, "response": []}, {"name": "Get Rate Plans by Account ID - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/by-accounts/{{AccountId}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "by-accounts", "{{AccountId}}"]}}, "response": []}, {"name": "Get Rate Plans by Account ID - Response time <500 ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/by-accounts/{{AccountId}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "by-accounts", "{{AccountId}}"]}}, "response": []}, {"name": "Delete Rate Plan - 202", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 202\", function () {\r", "    pm.response.to.have.status(202);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Delete_Account - 202 No Content", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 202\", function () {\r", "    pm.response.to.have.status(202);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{BaseURL}}/v1/glass/accounts/{{AccountId}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "accounts", "{{AccountId}}"]}}, "response": []}]}, {"name": "Rate Plan Details by ID", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Create Account - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "\r", "var jsonData= JSON.parse(responseBody);\r", " pm.environment.set(\"AccountId\", jsonData.id);"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Name', randomString(10));\r", "\r", "var moment = require('moment');\r", "\r", "var enddate = moment().add('months', 1).date(-0);\r", "pm.environment.set('contractEndDate', enddate.format((\"YYYY-MM-DD\")));"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{Name}}\",\r\n  \"agreementNumber\": \"************\",\r\n  \"logoKey\": null,\r\n  \"status\": \"ACTIVE\",\r\n  \"salesChannel\": \"WHOLESALE\",\r\n  \"salesPerson\": \"viraj\",\r\n  \"industryVertical\": \"AEROSPACE\",\r\n  \"contractEndDate\": \"{{contractEndDate}}\",\r\n  \"isBillable\": true,\r\n  \"contactName\": \"uma\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"phone\": \"*************\",\r\n  \"jobTitle\": \"QA\",\r\n  \"country\": \"UK\",\r\n  \"stateRegion\": \"aaaaa\",\r\n  \"city\": \"bbbbb\",\r\n  \"address1\": \"abc123\",\r\n  \"address2\": \"xyzaas\",\r\n  \"postcode\": \"wsdddd\",\r\n  \"thresholdCharge\": 0,\r\n  \"warningThreshold\": 0,\r\n  \"simCharge\": 0,\r\n  \"paymentTerms\": 99,\r\n  \"currency\": \"GBR\",\r\n  \"productTypes\": [\r\n    \"NATIONAL_ROAMING\"\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/accounts", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "accounts"]}}, "response": []}, {"name": "Create Rate Plan - 201 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData= JSON.parse(responseBody);\r", " pm.environment.set( \"rateplanid\", jsonData.id);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Rate plan - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\":{{AccountId}},\r\n  \"name\": \"Pay as You Go plan \",\r\n  \"accessFee\": 25,\r\n  \"currency\": \"GBP\",\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": \"PAYG\",\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1000,\r\n            \"value\": 2\r\n          },\r\n          {\r\n            \"rangeFrom\": 1000,\r\n            \"value\": 1\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": \"PAYG\",\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 100,\r\n            \"value\":2\r\n          },\r\n          {\r\n            \"rangeFrom\": 100,\r\n            \"value\": 1\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": \"PAYG\",\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"value\": 0\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": \"PAYG\",\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 200,\r\n            \"value\": 3\r\n          },\r\n          {\r\n            \"rangeFrom\": 200,\r\n            \"value\": 1\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Rate Plan - With Account ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Rate Plan - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-pls/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-pls", "{{rateplanid}}"]}}, "response": []}, {"name": "Rate Plan - Blank Rate Plan ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{}}"]}}, "response": []}, {"name": "Rate Plan - Invalid Rate Plan", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/gdfugi54456", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "gdfugi54456"]}}, "response": []}, {"name": "Rate Plan - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Rate Plan - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Rate Plan - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Delete Rate Plan - 202", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 202\", function () {\r", "    pm.response.to.have.status(202);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Delete_Account - 202 No Content", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 202\", function () {\r", "    pm.response.to.have.status(202);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{BaseURL}}/v1/glass/accounts/{{AccountId}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "accounts", "{{AccountId}}"]}}, "response": []}]}, {"name": "Get Rate Plan", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Rate Plans - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Get Rate Plans - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Get Rate Plans  - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-pln", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-pln"]}}, "response": []}, {"name": "Get Rate Plans - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Get Rate Plans - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}]}, {"name": "Delete Rate Plan", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Create Account - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "\r", "var jsonData= JSON.parse(responseBody);\r", " pm.environment.set(\"AccountId\", jsonData.id);"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Name', randomString(10));\r", "\r", "var moment = require('moment');\r", "\r", "var enddate = moment().add('months', 1).date(-0);\r", "pm.environment.set('contractEndDate', enddate.format((\"YYYY-MM-DD\")));"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{Name}}\",\r\n  \"agreementNumber\": \"************\",\r\n  \"logoKey\": null,\r\n  \"status\": \"ACTIVE\",\r\n  \"salesChannel\": \"WHOLESALE\",\r\n  \"salesPerson\": \"viraj\",\r\n  \"industryVertical\": \"AEROSPACE\",\r\n  \"contractEndDate\": \"{{contractEndDate}}\",\r\n  \"isBillable\": true,\r\n  \"contactName\": \"uma\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"phone\": \"*************\",\r\n  \"jobTitle\": \"QA\",\r\n  \"country\": \"UK\",\r\n  \"stateRegion\": \"aaaaa\",\r\n  \"city\": \"bbbbb\",\r\n  \"address1\": \"abc123\",\r\n  \"address2\": \"xyzaas\",\r\n  \"postcode\": \"wsdddd\",\r\n  \"thresholdCharge\": 0,\r\n  \"warningThreshold\": 0,\r\n  \"simCharge\": 0,\r\n  \"paymentTerms\": 99,\r\n  \"currency\": \"GBR\",\r\n  \"productTypes\": [\r\n    \"NATIONAL_ROAMING\"\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/accounts", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "accounts"]}}, "response": []}, {"name": "Create Rate Plan - 201 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData= JSON.parse(responseBody);\r", " pm.environment.set( \"rateplanid\", jsonData.id);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Delete Rate Plan - 202 No Content", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 202\", function () {\r", "    pm.response.to.have.status(202);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Delete Rate Plan - Invalid Rate Plan ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/595651515", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "595651515"]}}, "response": []}, {"name": "Delete Rate Plan - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rae-plns/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rae-plns", "{{rateplanid}}"]}}, "response": []}, {"name": "Delete Rate Plan - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Delete Rate Plan - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Delete_Account - 202 No Content", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 202\", function () {\r", "    pm.response.to.have.status(202);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{BaseURL}}/v1/glass/accounts/{{AccountId}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "accounts", "{{AccountId}}"]}}, "response": []}]}, {"name": "Update Rate Plan", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Create Account - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "\r", "var jsonData= JSON.parse(responseBody);\r", " pm.environment.set(\"AccountId\", jsonData.id);"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"exec": ["function randomString(length=1){\r", "    let randomString = \"\";\r", "        for (let i = 0; i < length; i++){\r", "        randomString += pm.variables.replaceIn(\"{{$randomAlphaNumeric}}\");\r", "    }\r", "    return randomString;\r", "}\r", "pm.environment.set('Name', randomString(10));\r", "\r", "var moment = require('moment');\r", "\r", "var enddate = moment().add('months', 1).date(-0);\r", "pm.environment.set('contractEndDate', enddate.format((\"YYYY-MM-DD\")));"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"{{Name}}\",\r\n  \"agreementNumber\": \"************\",\r\n  \"logoKey\": null,\r\n  \"status\": \"ACTIVE\",\r\n  \"salesChannel\": \"WHOLESALE\",\r\n  \"salesPerson\": \"viraj\",\r\n  \"industryVertical\": \"AEROSPACE\",\r\n  \"contractEndDate\": \"{{contractEndDate}}\",\r\n  \"isBillable\": true,\r\n  \"contactName\": \"uma\",\r\n  \"email\": \"<EMAIL>\",\r\n  \"phone\": \"*************\",\r\n  \"jobTitle\": \"QA\",\r\n  \"country\": \"UK\",\r\n  \"stateRegion\": \"aaaaa\",\r\n  \"city\": \"bbbbb\",\r\n  \"address1\": \"abc123\",\r\n  \"address2\": \"xyzaas\",\r\n  \"postcode\": \"wsdddd\",\r\n  \"thresholdCharge\": 0,\r\n  \"warningThreshold\": 0,\r\n  \"simCharge\": 0,\r\n  \"paymentTerms\": 99,\r\n  \"currency\": \"GBR\",\r\n  \"productTypes\": [\r\n    \"NATIONAL_ROAMING\"\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/accounts", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "accounts"]}}, "response": []}, {"name": "Create Rate Plan - 201 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData= JSON.parse(responseBody);\r", " pm.environment.set( \"rateplanid\", jsonData.id);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Mixed Rate Plan\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 0,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans"]}}, "response": []}, {"name": "Update Rate Plan - Name", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"id\": {{rateplanid}},\r\n  \"isDefault\": true,\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Update Name\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 100,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 0.21,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 1.22,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Update Rate Plan - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"id\": {{rateplanid}},\r\n  \"isDefault\": true,\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Update Name\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 100,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 0.21,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 1.22,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Update Rate Plan - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"id\": {{rateplanid}},\r\n  \"isDefault\": true,\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Update Name\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 100,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 0.21,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 1.22,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rte-plas/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rte-plas", "{{rateplanid}}"]}}, "response": []}, {"name": "Update Rate Plan - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"id\": {{rateplanid}},\r\n  \"isDefault\": true,\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Update Name\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 100,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 0.21,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 1.22,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Update Rate Plan - Access Fee", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"id\": {{rateplanid}},\r\n  \"isDefault\": true,\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Update Name\",\r\n  \"accessFee\": 2.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 100,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1024,\r\n            \"value\": 0.21,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1024,\r\n            \"value\": 1.22,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 3.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 2.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Update Rate Plan - PAYG - Data Range", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"id\": {{rateplanid}},\r\n  \"isDefault\": true,\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Update Name\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 100,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1030,\r\n            \"value\": 0.21,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1030,\r\n            \"value\": 1.22,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Update Rate Plan - Individual - Data Range", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"id\": {{rateplanid}},\r\n  \"isDefault\": true,\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Update Name\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 100,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1030,\r\n            \"value\": 0.21,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1030,\r\n            \"value\": 1.22,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10250,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Update Rate Plan - Fixed - Data Range", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"id\": {{rateplanid}},\r\n  \"isDefault\": true,\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Update Name\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 100,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1030,\r\n            \"value\": 0.21,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1030,\r\n            \"value\": 1.22,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Update Rate Plan - Flexible - Data Range", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"id\": {{rateplanid}},\r\n  \"isDefault\": true,\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Update Name\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 100,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1030,\r\n            \"value\": 0.21,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1030,\r\n            \"value\": 1.22,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10243,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Update Rate Plan - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});\r", "\r", "pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"id\": {{rateplanid}},\r\n  \"isDefault\": true,\r\n  \"accountId\": {{AccountId}},\r\n  \"name\": \"Update Name\",\r\n  \"accessFee\": 1.00,\r\n  \"currency\": \"GBP\",\r\n  \"simLimit\": 100,\r\n  \"originationGroups\": [\r\n    {\r\n      \"originationZones\": [\r\n        \"UK\"\r\n      ],\r\n      \"data\": {\r\n        \"rateModel\": 1,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 1030,\r\n            \"value\": 0.21,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          },\r\n          {\r\n            \"rangeFrom\": 1030,\r\n            \"value\": 1.22,\r\n            \"rangeUnit\": \"MB\",\r\n            \"priceUnit\": \"GB\",\r\n            \"isoverage\": false\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMo\": {\r\n        \"rateModel\": 2,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10240,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 5.26,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"voiceMt\": {\r\n        \"rateModel\": 3,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10241,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"Min\",\r\n            \"priceUnit\": \"Min\",\r\n            \"overageFee\": 6.27,\r\n            \"overageUnit\": \"Min\",\r\n            \"isoverage\": false,\r\n            \"overagePer\": 60\r\n          }\r\n        ]\r\n      },\r\n      \"sms\": {\r\n        \"rateModel\": 4,\r\n        \"rates\": [\r\n          {\r\n            \"rangeFrom\": 0,\r\n            \"rangeTo\": 10242,\r\n            \"value\": 1.00,\r\n            \"rangeUnit\": \"SMS\",\r\n            \"priceUnit\": \"SMS\",\r\n            \"overageFee\": 7.28,\r\n            \"overageUnit\": \"SMS\",\r\n            \"isoverage\": true,\r\n            \"overagePer\": 100\r\n          }\r\n        ]\r\n      }\r\n    }\r\n  ]\r\n}"}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Delete Rate Plan - 202", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 202\", function () {\r", "    pm.response.to.have.status(202);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/{{rateplanid}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "{{rateplanid}}"]}}, "response": []}, {"name": "Delete Account - 202", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 202\", function () {\r", "    pm.response.to.have.status(202);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{BaseURL}}/v1/glass/accounts/{{AccountId}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "accounts", "{{AccountId}}"]}}, "response": []}]}, {"name": "Rate Plans Models", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Rate Plans Models - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/models", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "models"]}}, "response": []}, {"name": "Get Rate Plans Models - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/model", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "model"]}}, "response": []}, {"name": "Get Rate Plans Models - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/models", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "models"]}}, "response": []}, {"name": "Get Rate Plans Models - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/models", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "models"]}}, "response": []}, {"name": "Get Rate Plans Models - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/models", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "models"]}}, "response": []}]}, {"name": "Rate Plans Model by ID", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Get Rate Plans Model by ID - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/models/3", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "models", "3"]}}, "response": []}, {"name": "Get Rate Plans Model by ID - Invalid ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 422\", function () {\r", "    pm.response.to.have.status(422);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/models/3.5", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "models", "3.5"]}}, "response": []}, {"name": "Get Rate Plans Model by ID - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/model/3", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "model", "3"]}}, "response": []}, {"name": "Get Rate Plans Model by ID - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/models/3", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "models", "3"]}}, "response": []}, {"name": "Get Rate Plans Model by ID - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/models/3", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "models", "3"]}}, "response": []}, {"name": "Get Rate Plans Model by ID - Response time < 500ms", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Response time is less than 500ms\", function () {\r", "    pm.expect(pm.response.responseTime).to.be.below(500);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "GET", "header": [{"key": "accept", "value": "application/json"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/models/3", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "models", "3"]}}, "response": []}]}, {"name": "Create Rate Plan Model", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Create Rate Plan Model - 201 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData= JSON.parse(responseBody);\r", " pm.environment.set( \"ModelID\", jsonData.value);"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"packages": {}, "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/models", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "models"]}}, "response": []}, {"name": "Create Rate Plan Model - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plan/models", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plan", "models"]}}, "response": []}, {"name": "Create Rate Plan Model - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/models", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "models"]}}, "response": []}, {"name": "Create Rate Plan Model - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/models", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "models"]}}, "response": []}, {"name": "Delete Rate Plan Model - 202 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {\r", "    pm.response.to.have.status(204);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/models/{{ModelID}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "models", "{{ModelID}}"]}}, "response": []}]}, {"name": "Update Rate Plans Model by ID", "item": [{"name": "Token Distributor", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () {", "    pm.response.to.have.status(200);", "});", " pm.test('Received access_token', function(){", "    const data = pm.response.json();", "    pm.expect(data).to.haveOwnProperty('access_token');", "   pm.environment.set('Token_distributor', data['access_token']);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "client_id", "value": "{{Client_id}}", "type": "text"}, {"key": "client_secret", "value": "{{Client_secret}}", "type": "text"}, {"key": "grant_type", "value": "{{Grant_type}}", "type": "text"}, {"key": "username", "value": "{{<PERSON><PERSON><PERSON>}}", "type": "text"}, {"key": "password", "value": "{{Password}}", "type": "text"}]}, "url": {"raw": "{{TokenUrl}}", "host": ["{{TokenUrl}}"]}}, "response": []}, {"name": "Create Rate Plan Model - 201 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 201\", function () {\r", "    pm.response.to.have.status(201);\r", "});\r", "\r", "var jsonData= JSON.parse(responseBody);\r", " pm.environment.set( \"ModelID\", jsonData.value);"], "type": "text/javascript", "packages": {}}}, {"listen": "prerequest", "script": {"packages": {}, "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "POST", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/models", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "models"]}}, "response": []}, {"name": "Update Rate Plans Model by ID - 200 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/models/{{ModelID}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "models", "{{ModelID}}"]}}, "response": []}, {"name": "Update Rate Plans Model by ID - Invalid ID", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/models/121515115", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "models", "121515115"]}}, "response": []}, {"name": "Update Rate Plans Model by ID - Invalid URL", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 404\", function () {\r", "    pm.response.to.have.status(404);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plan/models/{{ModelID}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plan", "models", "{{ModelID}}"]}}, "response": []}, {"name": "Update Rate Plans Model by ID - Invalid Method", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {\r", "    pm.response.to.have.status(405);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/models/{{ModelID}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "models", "{{ModelID}}"]}}, "response": []}, {"name": "Update Rate Plans Model by ID - Unauthorized", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 401\", function () {\r", "    pm.response.to.have.status(401);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "", "type": "string"}]}, "method": "PUT", "header": [{"key": "accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"test\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/models/{{ModelID}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "models", "{{ModelID}}"]}}, "response": []}, {"name": "Delete Rate Plan Model - 202 OK", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 204\", function () {\r", "    pm.response.to.have.status(204);\r", "});\r", "\r", ""], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{Token_distributor}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "accept", "value": "*/*"}], "url": {"raw": "{{BaseURL}}/v1/glass/rate-plans/models/{{ModelID}}", "host": ["{{BaseURL}}"], "path": ["v1", "glass", "rate-plans", "models", "{{ModelID}}"]}}, "response": []}]}]}]}