from datetime import date
from decimal import Decimal

import pytest

from accounts.adapters.repository import DatabaseAccountRepository
from accounts.domain.model import (
    Account,
    AccountStatus,
    PaymentTerms,
    SalesChannel,
    SimProfile,
)
from cdrdata.adapters.repository import DatabaseCdrRepository
from rate_plans.adapters.rate_plan_repository import DatabaseRatePlanRepository
from sim.adapters.externalapi import (
    AbstractMarketShareAPI,
    FakeMarketShareAPI,
    FakeSIMProvisioningAPI,
)
from sim.adapters.repository import DatabaseSimRepository
from tests.unit.sims.test_service import SimContract


class TestSimService(SimContract):
    @pytest.fixture
    def sim_repository(self, session) -> DatabaseSimRepository:
        return DatabaseSimRepository(session)

    @pytest.fixture
    def rate_plan_repository(self, session) -> DatabaseRatePlanRepository:
        return DatabaseRatePlanRepository(session)

    @pytest.fixture
    def provisioning(self) -> FakeSIMProvisioningAPI:
        return FakeSIMProvisioningAPI()

    @pytest.fixture
    def cdr_repository(self, session) -> DatabaseCdrRepository:
        return DatabaseCdrRepository(session)

    @pytest.fixture
    def account_repository(self, session) -> DatabaseAccountRepository:
        return DatabaseAccountRepository(session)

    @pytest.fixture
    def market_share_api(self, *args, **kwargs) -> AbstractMarketShareAPI:
        return FakeMarketShareAPI()

    @pytest.fixture(autouse=True)
    def create_account(self, account_repository):
        account_repository.add(
            Account(
                name="Vodafone",
                status=AccountStatus.ACTIVE,
                agreement_number="Test",
                currency="UAH",
                product_types=["NATIONAL_ROAMING"],
                industry_vertical="TELECOMMUNICATIONS",
                sales_person="John Doe",
                is_billable=True,
                contract_end_date=date.today(),
                sales_channel=SalesChannel.WHOLESALE,
                payment_terms=PaymentTerms(30),
                sim_charge=Decimal(0),
                organization_id=1,
                sim_profile=SimProfile.DATA_ONLY,
            )
        )
