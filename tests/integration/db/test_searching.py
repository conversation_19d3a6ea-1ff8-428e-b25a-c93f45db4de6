import pytest
from sqlalchemy import (
    Big<PERSON><PERSON><PERSON>,
    <PERSON>umn,
    Foreign<PERSON><PERSON>,
    Identity,
    Integer,
    Numeric,
    String,
    select,
)
from sqlalchemy.orm import Session, relationship

from accounts.domain.model import PhoneNumber
from common.db import Base
from common.searching import Searching, apply_search_to_sql


class TestSearching:
    class FakeAccount(Base):
        __tablename__ = "test_account"
        id: int = Column(Integer, Identity(), primary_key=True)
        name: str = Column(String(255), nullable=False)
        phone: str = Column(String(length=PhoneNumber.max_length), nullable=True)

        sim_usage = relationship(  # type: ignore
            "FakeSimUsage", back_populates="account"
        )

    class FakeSimUsage(Base):
        __tablename__ = "test_sim_usage"
        id: int = Column(Integer, Identity(), primary_key=True)
        account_id = Column(Integer, ForeignKey("test_account.id"))

        charge: float = Column(Numeric, nullable=False)
        volume: int = Column(BigInteger, nullable=False)

        account = relationship(  # type: ignore
            "FakeAccount", back_populates="sim_usage"
        )

        info = relationship(  # type: ignore
            "FakeSimUsageInfo", back_populates="sim_usage", uselist=False
        )

    class FakeSimUsageInfo(Base):
        __tablename__ = "test_sim_usage_info"
        id: int = Column(Integer, Identity(), primary_key=True)
        sim_usage_id: int = Column(Integer, ForeignKey("test_sim_usage.id"))
        note: str = Column(String(255), nullable=False)

        sim_usage = relationship("FakeSimUsage", back_populates="info")  # type: ignore

    @pytest.fixture
    def create_test_account(self, session: Session):
        def factory(name, phone, usage):
            account = self.FakeAccount(name=name, phone=phone)
            account.sim_usage = usage
            session.add(account)
            session.commit()
            return account

        return factory

    @pytest.mark.parametrize(
        "fields, search_input, account_count",
        [
            ({"name"}, "Bill", 1),
            ({"name"}, "Bill John", 2),
            ({"name", "phone"}, "Bill +380", 2),
            ({"name", "phone"}, "B +380", 2),
            ({"name", "phone"}, "B +380", 2),
            ({"sim_usage.charge", "sim_usage.volume"}, "2", 1),
            (
                {"sim_usage.charge", "sim_usage.volume", "sim_usage.info.note"},
                "1.1 3.3 g",
                3,
            ),
        ],
    )
    def test_successfully_built_search_criteria(
        self,
        create_test_account,
        session: Session,
        fields,
        search_input,
        account_count,
    ):
        usage_1 = self.FakeSimUsage(charge=1.1, volume=2)
        usage_2 = self.FakeSimUsage(charge=3.3, volume=4)
        usage_3 = self.FakeSimUsage(charge=6.6, volume=9)

        usage_1.info = self.FakeSimUsageInfo(note="a")  # type: ignore
        usage_2.info = self.FakeSimUsageInfo(note="e")  # type: ignore
        usage_3.info = self.FakeSimUsageInfo(note="g")  # type: ignore

        account_1_usage = [usage_1]
        account_2_usage = [usage_2]
        account_3_usage = [usage_3]

        create_test_account(name="Bill", phone="+************", usage=account_1_usage)
        create_test_account(name="John", phone="+************", usage=account_2_usage)
        create_test_account(name="Mike", phone="+************", usage=account_3_usage)

        searching = Searching(search=search_input, fields=fields)
        query = select(self.FakeAccount).filter(
            self.FakeAccount.id == self.FakeSimUsage.account_id
        )
        query = apply_search_to_sql(searching, self.FakeAccount, query)
        query = query.distinct(self.FakeAccount.id)
        result = session.execute(query).all()
        assert len(result) == account_count
