import re
from pathlib import Path

import pytest

from app.constants import APP_DIR

ALEMBIC_DIR = Path(APP_DIR.parent / "alembic" / "versions").expanduser().absolute()
COMMENT_REVISION_RE = re.compile(r"^Revision ID: (?P<revision>\w+)$", re.MULTILINE)
VAR_REVISION_RE = re.compile(r'^revision = "(?P<revision>\w+)"$', re.MULTILINE)
COMMENT_DOWN_REVISION_RE = re.compile(r"^Revises:\s?(?P<revision>\w*)$", re.MULTILINE)
VAR_DOWN_REVISION_RE = re.compile(
    r'^down_revision = ("(?P<revision>\w*)"|None)$', re.MULTILINE
)


def version_file_names() -> list[str]:
    version_files = sorted(i for i in ALEMBIC_DIR.iterdir() if i.is_file())
    return list(map(lambda i: i.name, version_files))


@pytest.mark.parametrize("filename", version_file_names())
class TestVersionFiles:
    def test_filename(self, filename):
        version_file_path = ALEMBIC_DIR / filename
        content = version_file_path.read_text()

        match = VAR_REVISION_RE.search(content)
        assert match
        var_rev = match.group("revision")

        filename_rev = filename[16:28]
        assert var_rev == filename_rev

    def test_revision_comment(self, filename):
        version_file_path = ALEMBIC_DIR / filename
        content = version_file_path.read_text()

        match = VAR_REVISION_RE.search(content)
        assert match
        var_rev = match.group("revision")

        match = COMMENT_REVISION_RE.search(content)
        assert match
        comment_rev = match["revision"]

        assert var_rev == comment_rev

    def test_down_revision_comment(self, filename):
        version_file_path = ALEMBIC_DIR / filename
        content = version_file_path.read_text()

        match = VAR_DOWN_REVISION_RE.search(content)
        assert match
        var_down_rev = match.group("revision") or ""  # None -> ""

        match = COMMENT_DOWN_REVISION_RE.search(content)
        assert match
        comment_down_rev = match.group("revision")

        assert var_down_rev == comment_down_rev
