from datetime import date
from decimal import Decimal

from accounts.adapters.repository import (
    DatabaseAccountRepository,
    DatabaseUserRepository,
)
from accounts.domain import model
from accounts.domain.model import Account
from accounts.domain.ports import AbstractAccountRepository, AbstractUserRepository
from tests.unit.accounts.test_repository import UserRepositoryContract

import pytest  # isort: skip


class TestAccountDatabaseRepository:
    @pytest.fixture
    def repository(self, session) -> AbstractAccountRepository:
        return DatabaseAccountRepository(session)

    def make_account(self, **kwargs) -> model.Account:
        defaults = dict(
            name="Kyivstar",
            agreement_number="asdf",
            status=model.AccountStatus.ACTIVE,
            currency="UAH",
            sales_channel=model.SalesChannel.WHOLESALE,
            industry_vertical=model.IndustryVertical.TELECOMMUNICATIONS,
            product_types=[model.ProductType.NATIONAL_ROAMING],
            sales_person="<PERSON>",
            organization_id=42,
            contract_end_date=date.today(),
            is_billable=False,
            contact_name="Bill Person",
            email="<EMAIL>",
            phone="**********",
            job_title=None,
            country="UA",
            city="Manchester",
            postcode="398591",
            sim_charge=12.35,
            payment_terms=30,
            threshold_charge=0.006,
            carrier_name="EE",
            warning_threshold=97,
            sim_profile=model.SimProfile.DATA_ONLY,
        )
        defaults.update(kwargs)
        return model.Account(**defaults)  # type: ignore[arg-type]

    def test_added_account_retreived_by_id(self, repository):
        account = self.make_account()
        account_id = repository.add(account)
        assert account_id is not None
        saved_account = repository.get(account_id)
        assert isinstance(saved_account.id, int)
        assert saved_account.name == account.name
        assert saved_account.status == account.status
        assert saved_account.product_types == account.product_types
        assert saved_account.sales_channel == account.sales_channel
        assert saved_account.sales_person == account.sales_person

    def test_account_updated(self, repository):
        account_id = repository.add(self.make_account())
        account = repository.get(account_id)
        account.name += "_test"
        account.sales_person += "_test"
        account.warning_threshold = 99
        account.threshold_charge = 0.009
        account.status = model.AccountStatus.SUSPENDED
        account.sim_charge = Decimal(23.32)
        account.payment_terms = 28
        account.sim_profile = model.SimProfile.VOICE_SMS_DATA

        repository.add(account)

        assert account.name.endswith("_test")
        assert account.sales_person.endswith("_test")
        assert account.status == model.AccountStatus.SUSPENDED
        assert account.sim_profile == model.SimProfile.VOICE_SMS_DATA
        assert account.sim_charge == Decimal(23.32)
        assert account.payment_terms == 28
        assert account.warning_threshold == 99
        assert account.threshold_charge == 0.009


class TestDatabaseUserRepository(UserRepositoryContract):
    @pytest.fixture
    def account(self, session) -> Account:
        account = self.make_account()
        DatabaseAccountRepository(session).add(account)
        return account

    @pytest.fixture
    def repository(self, session) -> AbstractUserRepository:
        return DatabaseUserRepository(session)
