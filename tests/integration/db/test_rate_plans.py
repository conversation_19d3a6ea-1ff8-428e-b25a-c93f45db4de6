from datetime import date, datetime
from decimal import Decimal

import pytest

from accounts.adapters.repository import DatabaseAccountRepository
from accounts.domain.model import (
    Account,
    AccountStatus,
    CurrencyCode,
    IndustryVertical,
    OrganizationName,
    PaymentTerms,
    ProductType,
    SalesChannel,
    SimProfile,
)
from rate_plans.adapters.exceptions import RatePlanDeletionError
from rate_plans.adapters.rate_plan_repository import DatabaseRatePlanRepository
from sim.adapters.repository import DatabaseSimRepository
from sim.domain import model
from tests.unit.rate_plans.test_repository import RatePlanContract


class TestRatePlanDatabaseRepository(RatePlanContract):
    @pytest.fixture
    def rate_plan_repository(self, session) -> DatabaseRatePlanRepository:
        return DatabaseRatePlanRepository(session)

    @pytest.fixture
    def sim_repository(self, session) -> DatabaseSimRepository:
        return DatabaseSimRepository(session)

    @pytest.fixture
    def account_repository(self, session) -> DatabaseAccountRepository:
        return DatabaseAccountRepository(session)

    @pytest.fixture
    def create_allocation(self, sim_repository, account_repository):
        def factory(
            allocation_rate_plan_id,
            sim_rate_plan_id=None,
            msisdn_pool: list[model.MsisdnPool] | None = None,
        ):
            sim_card = model.SIMCard(
                iccid=model.ICCID("75647583748396758496"),
                imsi=model.IMSI("***************"),
                msisdn=model.MSISDN("************"),
                rate_plan_id=sim_rate_plan_id,
            )
            range_ = model.Range(
                title="Reference",
                form_factor=model.FormFactor.MICRO,
                created_at=datetime.now(),
                created_by="John Billing",
                imsi_first=model.IMSI("***************"),
                imsi_last=model.IMSI("***************"),
                remaining=1,
                quantity=1,
            )
            account_repository.add(
                Account(
                    name=OrganizationName("Vodafone"),
                    status=AccountStatus.ACTIVE,
                    agreement_number="Test",
                    currency=CurrencyCode("UAH"),
                    product_types=[ProductType.NATIONAL_ROAMING],
                    industry_vertical=IndustryVertical.TELECOMMUNICATIONS,
                    sales_person="John Doe",
                    is_billable=True,
                    contract_end_date=date.today(),
                    sales_channel=SalesChannel.WHOLESALE,
                    payment_terms=PaymentTerms(30),
                    sim_profile=SimProfile.DATA_ONLY,
                    sim_charge=Decimal(0),
                    organization_id=1,
                )
            )
            range_.sim_cards = [sim_card]
            sim_repository.add_range(range_, msisdn_pool=msisdn_pool)
            allocation = model.Allocation(
                title="Reference",
                account_id=1,
                range_id=range_.id if range_.id else 1,
                quantity=range_.quantity,
                created_at=datetime.now(),
                rate_plan_id=allocation_rate_plan_id,
                imsi_first=model.IMSI("***************"),
                imsi_last=model.IMSI("***************"),
            )
            sim_repository.add_allocation(allocation)
            return allocation

        return factory

    def test_successful_rate_plan_deletion(self, rate_plans, rate_plan_repository):
        rate_plan = rate_plans[0]
        rate_plan_id = rate_plan_repository.add(rate_plan)
        rate_plan = rate_plan_repository.get(rate_plan_id)
        rate_plan_repository.delete(rate_plan)
        rate_plan = rate_plan_repository.get(rate_plan_id)
        assert rate_plan is None

    def test_get_error_when_deleting_rate_plan_with_assigned_allocations(
        self, rate_plans, rate_plan_repository, create_allocation
    ):
        rate_plan = rate_plans[0]
        rate_plan_id = rate_plan_repository.add(rate_plan)
        create_allocation(rate_plan_id)
        with pytest.raises(RatePlanDeletionError):
            rate_plan = rate_plan_repository.get(rate_plan_id)
            rate_plan_repository.delete(rate_plan)

    def test_get_error_when_deleting_rate_plan_with_assigned_sim_card(
        self, rate_plans, rate_plan_repository, create_allocation
    ):
        allocation_rate_plan, sim_rate_plan = rate_plans[0], rate_plans[1]
        allocation_rate_plan_id = rate_plan_repository.add(allocation_rate_plan)
        sim_rate_plan_id = rate_plan_repository.add(sim_rate_plan)
        create_allocation(allocation_rate_plan_id, sim_rate_plan_id=sim_rate_plan_id)
        with pytest.raises(RatePlanDeletionError):
            sim_rate_plan = rate_plan_repository.get(sim_rate_plan_id)
            rate_plan_repository.delete(sim_rate_plan)
