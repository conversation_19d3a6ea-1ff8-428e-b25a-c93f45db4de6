import unittest
from unittest.mock import MagicMock
from uuid import UUID, uuid4

from common.funcs import convert_to_bytes
from common.types import Unit
from sync_redis.adapters.externalapi import AbstractRedisAPI
from sync_redis.adapters.repository import AbstractRepository
from sync_redis.domain import model
from sync_redis.service import RedisService


class TestRedisService(unittest.TestCase):
    def setUp(self):
        self.mock_repository = MagicMock(spec=AbstractRepository)
        self.mock_redis_api = MagicMock(spec=AbstractRedisAPI)

        self.redis_service = RedisService(
            repository=self.mock_repository, redis_api=self.mock_redis_api
        )

    def test_convert_to_bytes_valid_unit(self):
        result = convert_to_bytes(5, "MB")
        self.assertEqual(result, 5 * 1024 * 1024)

    def test_convert_to_bytes_invalid_unit(self):
        with self.assertRaises(ValueError) as context:
            convert_to_bytes(10, "INVALID_UNIT")
        self.assertEqual(str(context.exception), "Unknown unit: INVALID_UNIT")

    def test_create_redis_rule(self):
        account_id = [123]
        rule_uuid = uuid4()

        mock_rule_info = model.RuleInfo(
            IMSIs=["***************"],
            accountId=123,
            accountName="account 1",
            rules=[
                model.Rules(
                    status=True,
                    definitionCode="rule_code",
                    rulesUuidParam=UUID("1f3a7ed0-5a7e-4eab-a0ab-8c59a34de3f5"),
                    simUsageLimit=1,
                    unit=Unit.BYTES,
                )
            ],
        )
        self.mock_repository.get_rules.return_value = mock_rule_info

        self.redis_service.create_redis_rule(account_id, rule_uuid)

        self.mock_repository.get_rules.assert_called_once_with(
            account_id[0], rule_uuid=rule_uuid
        )
        self.mock_redis_api.create_redis_rule.assert_called_once_with(mock_rule_info)

    def test_update_redis_rule(self):
        account_id = [123]
        rule_uuid = UUID("1f3a7ed0-5a7e-4eab-a0ab-8c59a34de3f5")

        mock_rule = model.Rules(
            status=True,
            definitionCode="rule_code",
            rulesUuidParam=UUID("1f3a7ed0-5a7e-4eab-a0ab-8c59a34de3f5"),
            simUsageLimit=1,
            unit=Unit.BYTES,
        )
        self.mock_repository.get_rules.return_value = mock_rule

        self.redis_service.update_redis_rule(account_id, rule_uuid)

        self.mock_repository.get_rules.assert_called_once_with(
            account_id[0], rule_uuid=rule_uuid
        )
        self.mock_redis_api.update_redis_rule.assert_called_once_with(
            mock_rule, str(rule_uuid)
        )

    def test_update_rule_status_with_existing_rule_activate(self):
        account_id = [123]
        rule_uuid = uuid4()
        mock_rule_info = model.RuleInfo(
            IMSIs=["***************"],
            accountId=123,
            accountName="account 1",
            rules=[
                model.Rules(
                    status=True,
                    definitionCode="rule_code",
                    rulesUuidParam=UUID("1f3a7ed0-5a7e-4eab-a0ab-8c59a34de3f5"),
                    simUsageLimit=1,
                    unit=Unit.BYTES,
                )
            ],
        )
        self.mock_repository.get_rules.return_value = mock_rule_info
        self.mock_repository.get_get_rule_by_uuid.return_value = 1

        result = self.redis_service.update_rule_status(True, account_id[0], rule_uuid)

        self.mock_redis_api.create_redis_rule.assert_called_once_with(mock_rule_info)
        assert result is None

    def test_update_rule_status_with_existing_rule_deactivate(self):
        account_id = 123
        rule_uuid = uuid4()
        result = self.redis_service.update_rule_status(False, account_id, rule_uuid)

        self.mock_redis_api.update_rule_status.assert_called_once_with(str(rule_uuid))
        self.assertTrue(result)

    def test_delete_redis_rule_with_deleted_rule(self):
        account_id = 123
        rule_uuid = uuid4()

        result = self.redis_service.delete_redis_rule(account_id, rule_uuid)

        self.mock_redis_api.delete_redis_rule.assert_called_once_with(str(rule_uuid))
        self.assertIsNotNone(result)

    def test_allocate_sims(self):
        account_id = 123
        rate_plan_id = 1
        imsis = ["imsi1", "imsi2", "imsi3"]
        self.mock_repository.get_imsis.return_value = imsis

        self.redis_service.allocate_sims(account_id, rate_plan_id)

        self.mock_repository.get_imsis.assert_called_once_with(
            account_id, rate_plan_id=rate_plan_id
        )
        self.mock_redis_api.allocate_sims.assert_called_once_with(
            account_id, rate_plan_id, imsis
        )


if __name__ == "__main__":
    unittest.main()
