import unittest
import uuid
from unittest.mock import MagicMock, patch

from fastapi.encoders import jsonable_encoder
from platform_api_client import PlatformAPIClient, PlatformAPIError
from starlette import status

from auth.exceptions import ForbiddenError, Unauthorized
from common.types import Unit
from sync_redis.adapters.externalapi import HTTPRed<PERSON><PERSON>I
from sync_redis.domain.model import RuleInfo, Rules
from sync_redis.exception import RedisAPIError

RULE = RuleInfo(
    IMSIs=["***************"],
    accountId=123,
    accountName="Test Account",
    rules=[
        Rules(
            status=True,
            definitionCode="Test Rule",
            rulesUuidParam=uuid.uuid4(),
            simUsageLimit=10,
            unit=Unit.BYTES,
            sizeInBytes=10,
            actions=[],
            notifications=[],
        )
    ],
)


class TestHTTPRedisAPI(unittest.TestCase):
    def setUp(self):
        self.mock_api_client = MagicMock(spec=PlatformAPIClient)
        self.redis_api = HTTPRedisAPI(api_client=self.mock_api_client)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_create_redis_rule_success(self):
        mock_response = MagicMock()
        mock_response.status_code = 201
        mock_response.json.return_value = {"success": True}
        self.mock_api_client.post.return_value = mock_response

        result = self.redis_api.create_redis_rule(RULE)

        self.mock_api_client.post.assert_called_once_with(
            "http://mock-api/v1/analytics/rule",
            headers={"Content-Type": "application/json"},
            json=jsonable_encoder(RULE),
        )
        self.assertEqual(result, {"success": True})

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_create_redis_rule_api_error(self):
        mock_response = MagicMock()
        mock_response.status_code = 404

        with self.assertRaises(RedisAPIError):
            self.redis_api.create_redis_rule(RULE)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_create_redis_rule_unauthorized(self):
        self.mock_api_client.post.side_effect = PlatformAPIError(
            status_code=status.HTTP_401_UNAUTHORIZED, message="Unauthorized"
        )

        with self.assertRaises(Unauthorized):
            self.redis_api.create_redis_rule(RULE)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_create_redis_rule_forbidden(self):
        self.mock_api_client.post.side_effect = PlatformAPIError(
            status_code=status.HTTP_403_FORBIDDEN, message="Forbidden"
        )

        with self.assertRaises(ForbiddenError):
            self.redis_api.create_redis_rule(RULE)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_create_redis_rule_bad_request(self):
        self.mock_api_client.post.side_effect = PlatformAPIError(
            status_code=status.HTTP_400_BAD_REQUEST, message="Bad request"
        )
        self.redis_api.create_redis_rule(RULE)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_create_redis_rule_connection_error(self):
        self.mock_api_client.post.side_effect = ConnectionError(
            "Failed to connect with the Redis API"
        )

        with self.assertRaises(ConnectionError):
            self.redis_api.create_redis_rule(RULE)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_update_redis_rule_success(self):
        mock_response = MagicMock()
        mock_response.status_code = 202
        mock_response.json.return_value = {"updated": True}
        self.mock_api_client.put.return_value = mock_response

        rule_uuid = "123-uuid"
        result = self.redis_api.update_redis_rule(RULE.rules[0], rule_uuid)

        self.mock_api_client.put.assert_called_once_with(
            f"http://mock-api/v1/analytics/rule/{rule_uuid}",
            headers={"Content-Type": "application/json"},
            json=jsonable_encoder(RULE.rules[0]),
        )
        self.assertEqual(result, {"updated": True})

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_update_redis_api_error(self):
        mock_response = MagicMock()
        mock_response.status_code = 404
        rule_uuid = "123-uuid"

        with self.assertRaises(RedisAPIError):
            self.redis_api.update_redis_rule(RULE, rule_uuid)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_update_redis_rule_unauthorized(self):
        rule_uuid = "123-uuid"
        self.mock_api_client.put.side_effect = PlatformAPIError(
            status_code=status.HTTP_401_UNAUTHORIZED, message="Unauthorized"
        )

        with self.assertRaises(Unauthorized):
            self.redis_api.update_redis_rule(RULE, rule_uuid)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_update_redis_rule_forbidden(self):
        rule_uuid = "123-uuid"
        self.mock_api_client.put.side_effect = PlatformAPIError(
            status_code=status.HTTP_403_FORBIDDEN, message="Forbidden"
        )

        with self.assertRaises(ForbiddenError):
            self.redis_api.update_redis_rule(RULE, rule_uuid)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_update_redis_rule_bad_request(self):
        rule_uuid = "123-uuid"
        self.mock_api_client.put.side_effect = PlatformAPIError(
            status_code=status.HTTP_400_BAD_REQUEST, message="Bad request"
        )

        self.redis_api.update_redis_rule(RULE, rule_uuid)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_update_redis_rule_connection_error(self):
        rule_uuid = "123-uuid"
        self.mock_api_client.put.side_effect = ConnectionError(
            "Failed to connect with the Redis API"
        )

        with self.assertRaises(ConnectionError):
            self.redis_api.update_redis_rule(RULE, rule_uuid)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_update_rule_status_success(self):
        mock_response = MagicMock()
        mock_response.status_code = 202
        mock_response.json.return_value = {"status_updated": True}
        self.mock_api_client.delete.return_value = mock_response

        rule_uuid = "123-uuid"
        result = self.redis_api.update_rule_status(rule_uuid)

        self.mock_api_client.delete.assert_called_once_with(
            f"http://mock-api/v1/analytics/rule/{rule_uuid}",
            headers={"Content-Type": "application/json"},
        )
        self.assertEqual(result, {"status_updated": True})

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_update_rule_status_api_error(self):
        mock_response = MagicMock()
        mock_response.status_code = 404
        rule_uuid = "123-uuid"

        with self.assertRaises(RedisAPIError):
            self.redis_api.update_rule_status(rule_uuid)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_update_rule_status_unauthorized(self):
        rule_uuid = "123-uuid"
        self.mock_api_client.delete.side_effect = PlatformAPIError(
            status_code=status.HTTP_401_UNAUTHORIZED, message="Unauthorized"
        )

        with self.assertRaises(Unauthorized):
            self.redis_api.update_rule_status(rule_uuid)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_update_rule_status_forbidden(self):
        rule_uuid = "123-uuid"
        self.mock_api_client.delete.side_effect = PlatformAPIError(
            status_code=status.HTTP_403_FORBIDDEN, message="Forbidden"
        )

        with self.assertRaises(ForbiddenError):
            self.redis_api.update_rule_status(rule_uuid)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_update_rule_status_connection_error(self):
        rule_uuid = "123-uuid"
        self.mock_api_client.delete.side_effect = ConnectionError(
            "Failed to connect with the Redis API"
        )

        with self.assertRaises(ConnectionError):
            self.redis_api.update_rule_status(rule_uuid)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_update_rule_status_bad_request(self):
        rule_uuid = "123-uuid"
        self.mock_api_client.delete.side_effect = PlatformAPIError(
            status_code=status.HTTP_400_BAD_REQUEST, message="Bad request"
        )

        self.redis_api.update_rule_status(rule_uuid)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_delete_redis_rule_success(self):
        mock_response = MagicMock()
        mock_response.status_code = 202
        mock_response.json.return_value = {"deleted": True}
        self.mock_api_client.delete.return_value = mock_response

        rule_uuid = "123-uuid"
        result = self.redis_api.delete_redis_rule(rule_uuid)

        self.mock_api_client.delete.assert_called_once_with(
            f"http://mock-api/v1/analytics/rule/{rule_uuid}",
            headers={"Content-Type": "application/json"},
        )
        self.assertEqual(result, {"deleted": True})

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_delete_redis_api_error(self):
        mock_response = MagicMock()
        mock_response.status_code = 404
        rule_uuid = "123-uuid"
        with self.assertRaises(RedisAPIError):
            self.redis_api.delete_redis_rule(rule_uuid)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_delete_redis_rule_unauthorized(self):
        self.mock_api_client.delete.side_effect = PlatformAPIError(
            status_code=status.HTTP_401_UNAUTHORIZED, message="Unauthorized"
        )

        rule_uuid = "123-uuid"
        with self.assertRaises(Unauthorized):
            self.redis_api.delete_redis_rule(rule_uuid)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_delete_redis_rule_forbidden(self):
        self.mock_api_client.delete.side_effect = PlatformAPIError(
            status_code=status.HTTP_403_FORBIDDEN, message="Forbidden"
        )

        rule_uuid = "123-uuid"
        with self.assertRaises(ForbiddenError):
            self.redis_api.delete_redis_rule(rule_uuid)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_delete_redis_rule_bad_request(self):
        self.mock_api_client.delete.side_effect = PlatformAPIError(
            status_code=status.HTTP_400_BAD_REQUEST, message="Bad request"
        )

        rule_uuid = "123-uuid"
        self.redis_api.delete_redis_rule(rule_uuid)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_delete_redis_rule_connection_error(self):
        self.mock_api_client.delete.side_effect = ConnectionError

        rule_uuid = "123-uuid"
        with self.assertRaises(ConnectionError):
            self.redis_api.delete_redis_rule(rule_uuid)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_allocate_sims_success(self):
        mock_response = MagicMock()
        mock_response.status_code = 202
        mock_response.json.return_value = {"allocated": True}
        self.mock_api_client.put.return_value = mock_response

        account_id = 456
        rate_plan_id = 1
        imsis = ["***************", "***************"]
        result = self.redis_api.allocate_sims(account_id, rate_plan_id, imsis)

        self.mock_api_client.put.assert_called_once_with(
            f"http://mock-api/v1/analytics/imsis/account/{account_id}/rateplan/{rate_plan_id}",  # noqa
            headers={"Content-Type": "application/json"},
            json={"IMSIs": imsis},
        )
        self.assertEqual(result, {"allocated": True})

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_allocate_sims_api_error(self):
        mock_response = MagicMock()
        mock_response.status_code = 404

        account_id = 456
        rate_plan_id = 1
        imsis = ["***************", "***************"]

        with self.assertRaises(RedisAPIError):
            self.redis_api.allocate_sims(account_id, rate_plan_id, imsis)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_allocate_sims_unauthorized(self):
        self.mock_api_client.put.side_effect = PlatformAPIError(
            status_code=status.HTTP_401_UNAUTHORIZED, message="Unauthorized"
        )

        account_id = 456
        rate_plan_id = 1
        imsis = ["***************", "***************"]

        with self.assertRaises(Unauthorized):
            self.redis_api.allocate_sims(account_id, rate_plan_id, imsis)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_allocate_sims_forbidden(self):
        self.mock_api_client.put.side_effect = PlatformAPIError(
            status_code=status.HTTP_403_FORBIDDEN, message="Forbidden"
        )

        account_id = 456
        rate_plan_id = 1
        imsis = ["***************", "***************"]

        with self.assertRaises(ForbiddenError):
            self.redis_api.allocate_sims(account_id, rate_plan_id, imsis)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_allocate_sims_bad_request(self):
        self.mock_api_client.put.side_effect = PlatformAPIError(
            status_code=status.HTTP_400_BAD_REQUEST, message="Bad request"
        )

        account_id = 456
        rate_plan_id = 1
        imsis = ["***************", "***************"]

        self.redis_api.allocate_sims(account_id, rate_plan_id, imsis)

    @patch("app.config.settings.APP_BASE_URL", "http://mock-api")
    def test_allocate_sims_connection_error(self):
        self.mock_api_client.put.side_effect = ConnectionError

        account_id = 456
        rate_plan_id = 1
        imsis = ["***************", "***************"]

        with self.assertRaises(ConnectionError):
            self.redis_api.allocate_sims(account_id, rate_plan_id, imsis)


if __name__ == "__main__":
    unittest.main()
