from unittest.mock import MagicMock

from api.sim.examples import SIM_AUDIT_TRAIL_RESPONSE
from audit.services import AuditService
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from common.types import IMSI
from sim.adapters.repository import AbstractSimRepository
from sim.domain.ports import AbstractAuditService


class TestAuditService:
    def setup_method(self):
        self.sim_repository = MagicMock(spec=AbstractSimRepository)
        self.audit_service = MagicMock(spec=AbstractAuditService)
        self.audit_service_instance = AuditService(
            sim_repository=self.sim_repository,
            audit_service=self.audit_service,
        )

    def test_system_audit(self):
        account_id = 123
        pagination = MagicMock(spec=Pagination)
        ordering = MagicMock(spec=Ordering)
        searching = MagicMock(spec=Searching)
        is_client = False

        sim_card_mock = MagicMock()
        sim_card_mock.imsi = "***************"
        self.sim_repository.get_sim_cards.return_value = iter([sim_card_mock])

        audit_logs_mock = MagicMock()
        audit_logs_mock.results = iter([SIM_AUDIT_TRAIL_RESPONSE])
        audit_logs_mock.totalCount = 2
        self.audit_service.get_account_audit_api.return_value = audit_logs_mock

        results, total_count = self.audit_service_instance.system_audit(
            account_id=account_id,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )

        self.sim_repository.get_sim_cards.assert_called_once_with(account_id=account_id)
        self.audit_service.get_account_audit_api.assert_called_once_with(
            imsi=[IMSI(sim_card_mock.imsi)],
            is_client=is_client,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        assert list(results) == [SIM_AUDIT_TRAIL_RESPONSE]
        assert total_count == 2
