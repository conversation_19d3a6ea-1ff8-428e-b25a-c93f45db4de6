import uuid
from abc import ABC, abstractmethod
from collections import namedtuple
from datetime import date
from decimal import Decimal
from random import randint
from typing import Iterable
from unittest.mock import MagicMock
from uuid import UUID

from platform_api_client import PlatformAPIClient, PlatformAPIError
from pydantic import EmailStr
from sqlalchemy.orm.session import Session
from starlette import status

from accounts.adapters.repository import (
    DatabaseAccountRepository,
    HTTPOrganizationRepository,
    InMemoryOrganizationRepository,
    InMemoryUserRepository,
    OrganizationUser,
)
from accounts.domain.dto import AccountUser, UserInvite
from accounts.domain.exceptions import (
    OrganizationAlreadyExists,
    OrganizationDoesNotExist,
    SimAccountDataNotFound,
    UserNotFound,
)
from accounts.domain.model import (
    Account,
    AccountStatus,
    ContactName,
    IndustryVertical,
    Organization,
    OrganizationName,
    PaymentTerms,
    PhoneNumber,
    Postcode,
    ProductType,
    SAFInvite,
    SalesChannel,
    SimAccountInfoDetails,
    <PERSON>mP<PERSON><PERSON><PERSON>,
    User,
)
from accounts.domain.ports import AbstractOrganizationRepository, AbstractUserRepository
from auth.exceptions import ConflictException, ForbiddenError
from common.types import CountryCode, CurrencyCode, SimStatus
from openapi.domain.model import TokenResponse

import pytest  # isort: skip


@pytest.fixture
def database_accounts_repository_mock():
    def auth_factory(session_mock):
        mock_database_accounts_repository = DatabaseAccountRepository(
            session=session_mock
        )

        return mock_database_accounts_repository

    return auth_factory


@pytest.fixture
def db_session():
    session_mock = MagicMock()
    mock_repository = DatabaseAccountRepository(session_mock)
    return session_mock, mock_repository


@pytest.fixture
def session_mock():
    mock_session = MagicMock(spec=Session)

    return mock_session


class TestOrganizationRepository:
    @pytest.fixture
    def make_organization_repository(self):
        def _factory():
            return InMemoryOrganizationRepository()

        return _factory

    @pytest.fixture
    def make_organization_records(self, make_organization_repository):
        def _factory(
            count: Iterable[int] = range(10),
        ) -> AbstractOrganizationRepository:
            organization_repository = make_organization_repository()

            for i in count:
                org = Organization(name=OrganizationName(f"Organization {i}"))
                organization_repository.add_client(randint(1, 10), org)
            return organization_repository

        return _factory

    def test_adding_organization(self, make_organization_repository):
        organization_repository = make_organization_repository()
        new_organization = Organization(name=OrganizationName("Organization for test"))
        organization_id = organization_repository.add_client(
            parent_id=101, client=new_organization
        )
        assert organization_id

        exists_organization = organization_repository.get(organization_id)
        assert exists_organization

        exists_organization_by_name = organization_repository.get_by_name(
            new_organization.name
        )
        assert exists_organization_by_name

    def test_adding_organization_with_exists_name(self, make_organization_records):
        organization_repository = make_organization_records()
        new_organization = Organization(name=OrganizationName("Organization 1"))
        with pytest.raises(OrganizationAlreadyExists):
            _ = organization_repository.add_client(
                parent_id=102, client=new_organization
            )

    def test_getting_non_existent_organization(self, make_organization_records):
        organization_repository = make_organization_records()
        organization_1 = organization_repository.get(2000)
        assert not organization_1

        organization_2 = organization_repository.get_by_name(
            "Non-existent organization"
        )
        assert not organization_2

    def test_updating_organization(self, make_organization_records):
        organization_repository = make_organization_records()
        organization = organization_repository.get(1)

        new_name = OrganizationName("Fake name")
        organization.name = new_name
        updated_organization = organization_repository.update(organization)

        assert updated_organization
        assert updated_organization.name == new_name
        assert updated_organization.id == organization.id

    def test_updating_organization_with_exists_name(self, make_organization_records):
        organization_repository = make_organization_records()
        organization_1 = organization_repository.get(1)
        organization_2 = organization_repository.get(2)

        organization_2.name = organization_1.name
        with pytest.raises(OrganizationAlreadyExists):
            _ = organization_repository.update(organization_2)

    def test_deleting_wrong_organization(self, make_organization_repository):
        organization_repository = make_organization_repository()
        with pytest.raises(OrganizationDoesNotExist):
            organization_repository.delete(1001)

    def test_deleting_organization(self, make_organization_records):
        organization_repository = make_organization_records()
        organization_repository.delete(1)


class TestHTTPOrganizationRepository:
    @pytest.fixture
    def mock_api_client(self):
        api_client = MagicMock(spec=PlatformAPIClient)
        http_organization = HTTPOrganizationRepository(api_client=api_client)
        return api_client, http_organization

    def test_add_client_success(self, mock_api_client):
        api_client, http_organization = mock_api_client
        api_client.post.return_value = MagicMock()
        api_client.parse_payload.return_value = Organization(name="TestOrg", id=123)
        client_to_add = Organization(name="TestOrg")
        result = http_organization.add_client(parent_id=1, client=client_to_add)
        assert result == 123
        api_client.post.assert_called_once()
        api_client.parse_payload.assert_called_once()

    def test_add_client_conflict(self, mock_api_client):
        api_client, http_organization = mock_api_client
        api_client.post.side_effect = PlatformAPIError(
            status_code=status.HTTP_409_CONFLICT, message=""
        )
        client_to_add = Organization(name="TestOrg")
        with pytest.raises(OrganizationAlreadyExists):
            http_organization.add_client(parent_id=1, client=client_to_add)
        api_client.post.assert_called_once()

    def test_add_client_forbidden(self, mock_api_client):
        api_client, http_organization = mock_api_client
        api_client.post.side_effect = PlatformAPIError(
            status_code=status.HTTP_403_FORBIDDEN, message=""
        )
        client_to_add = Organization(name="TestOrg")
        with pytest.raises(ForbiddenError):
            http_organization.add_client(parent_id=1, client=client_to_add)
        api_client.post.assert_called_once()

    def test_add_client_api_error(self, mock_api_client):
        api_client, http_organization = mock_api_client
        api_client.post.side_effect = PlatformAPIError(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, message=""
        )
        client_to_add = Organization(name="TestOrg")
        with pytest.raises(PlatformAPIError):
            http_organization.add_client(parent_id=1, client=client_to_add)
        api_client.post.assert_called_once()

    def test_get_organization_success(self, mock_api_client):
        api_client, http_organization = mock_api_client
        api_client.get.return_value = MagicMock()
        api_client.parse_payload.return_value = Organization(name="TestOrg", id=123)
        result = http_organization.get(organization_id=123)
        assert result == Organization(name="TestOrg", id=123)
        api_client.get.assert_called_once_with("/v1/organizations/123")
        api_client.parse_payload.assert_called_once()

    def test_get_organization_not_found(self, mock_api_client):
        api_client, http_organization = mock_api_client
        api_client.get.side_effect = PlatformAPIError(
            status_code=status.HTTP_404_NOT_FOUND, message=""
        )
        result = http_organization.get(organization_id=123)
        assert result is None
        api_client.get.assert_called_once_with("/v1/organizations/123")

    def test_get_organization_forbidden(self, mock_api_client):
        api_client, http_organization = mock_api_client
        api_client.get.side_effect = PlatformAPIError(
            status_code=status.HTTP_403_FORBIDDEN, message=""
        )
        with pytest.raises(ForbiddenError):
            http_organization.get(organization_id=123)
        api_client.get.assert_called_once_with("/v1/organizations/123")

    def test_get_organization_api_error(self, mock_api_client):
        api_client, http_organization = mock_api_client
        api_client.get.side_effect = PlatformAPIError(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, message=""
        )
        with pytest.raises(PlatformAPIError):
            http_organization.get(organization_id=123)
        api_client.get.assert_called_once_with("/v1/organizations/123")

    def test_get_organization_by_name_success(self, mock_api_client):
        api_client, http_organization = mock_api_client
        api_client.get.return_value = MagicMock()
        organizations = [
            Organization(name="TestOrg1", id=1),
            Organization(name="TestOrg2", id=2),
            Organization(name="TestOrg3", id=3),
        ]
        api_client.parse_payload.return_value = organizations
        result = http_organization.get_by_name(name="TestOrg2")
        assert result == Organization(name="TestOrg2", id=2)
        api_client.get.assert_called_once_with("/v1/organizations/")
        api_client.parse_payload.assert_called_once()

    def test_get_organization_by_name_forbidden(self, mock_api_client):
        api_client, http_organization = mock_api_client
        api_client.get.side_effect = PlatformAPIError(
            status_code=status.HTTP_403_FORBIDDEN, message=""
        )
        with pytest.raises(ForbiddenError):
            http_organization.get_by_name(name="TestOrg2")
        api_client.get.assert_called_once_with("/v1/organizations/")

    def test_get_organization_by_name_api_error(self, mock_api_client):
        api_client, http_organization = mock_api_client
        api_client.get.side_effect = PlatformAPIError(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, message=""
        )
        with pytest.raises(PlatformAPIError):
            http_organization.get_by_name(name="TestOrg2")
        api_client.get.assert_called_once_with("/v1/organizations/")

    def test_update_client_success(self, mock_api_client):
        api_client, http_organization = mock_api_client
        client_to_update = Organization(id=123, name="UpdatedOrg")
        api_client.put.return_value = MagicMock()
        api_client.parse_payload.return_value = client_to_update
        result = http_organization.update(client=client_to_update)
        assert result == client_to_update
        api_client.put.assert_called_once_with(
            "/v1/organizations/clients/123", json={"name": "UpdatedOrg"}
        )
        api_client.parse_payload.assert_called_once()

    def test_update_client_conflict(self, mock_api_client):
        api_client, http_organization = mock_api_client
        client_to_update = Organization(id=123, name="UpdatedOrg")
        api_client.put.side_effect = PlatformAPIError(
            status_code=status.HTTP_409_CONFLICT, message=""
        )
        with pytest.raises(OrganizationAlreadyExists):
            http_organization.update(client=client_to_update)
        api_client.put.assert_called_once_with(
            "/v1/organizations/clients/123", json={"name": "UpdatedOrg"}
        )

    def test_update_client_not_found(self, mock_api_client):
        api_client, http_organization = mock_api_client
        client_to_update = Organization(id=123, name="UpdatedOrg")
        api_client.put.side_effect = PlatformAPIError(
            status_code=status.HTTP_404_NOT_FOUND, message=""
        )
        with pytest.raises(OrganizationDoesNotExist):
            http_organization.update(client=client_to_update)
        api_client.put.assert_called_once_with(
            "/v1/organizations/clients/123", json={"name": "UpdatedOrg"}
        )

    def test_update_client_forbidden(self, mock_api_client):
        api_client, http_organization = mock_api_client
        client_to_update = Organization(id=123, name="UpdatedOrg")
        api_client.put.side_effect = PlatformAPIError(
            status_code=status.HTTP_403_FORBIDDEN, message=""
        )
        with pytest.raises(ForbiddenError):
            http_organization.update(client=client_to_update)
        api_client.put.assert_called_once_with(
            "/v1/organizations/clients/123", json={"name": "UpdatedOrg"}
        )

    def test_update_client_api_error(self, mock_api_client):
        api_client, http_organization = mock_api_client
        client_to_update = Organization(id=123, name="UpdatedOrg")
        api_client.put.side_effect = PlatformAPIError(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, message=""
        )
        with pytest.raises(PlatformAPIError):
            http_organization.update(client=client_to_update)
        api_client.put.assert_called_once_with(
            "/v1/organizations/clients/123", json={"name": "UpdatedOrg"}
        )

    def test_update_client_invalid_id(self, mock_api_client):
        api_client, http_organization = mock_api_client
        client_to_update = Organization(id=None, name="UpdatedOrg")
        with pytest.raises(AssertionError, match="Expected ID to update the client"):
            http_organization.update(client=client_to_update)
        api_client.put.assert_not_called()

    def test_delete_organization_success(self, mock_api_client):
        api_client, http_organization = mock_api_client
        api_client.delete.return_value = None
        http_organization.delete(organization_id=123)
        api_client.delete.assert_called_once_with("/v1/organizations/123")

    def test_delete_organization_forbidden(self, mock_api_client):
        api_client, http_organization = mock_api_client
        api_client.delete.side_effect = PlatformAPIError(
            status_code=status.HTTP_403_FORBIDDEN, message=""
        )
        with pytest.raises(ForbiddenError):
            http_organization.delete(organization_id=123)
        api_client.delete.assert_called_once_with("/v1/organizations/123")

    def test_delete_organization_not_found(self, mock_api_client):
        api_client, http_organization = mock_api_client
        api_client.delete.side_effect = PlatformAPIError(
            status_code=status.HTTP_404_NOT_FOUND, message=""
        )
        with pytest.raises(OrganizationDoesNotExist):
            http_organization.delete(organization_id=123)
        api_client.delete.assert_called_once_with("/v1/organizations/123")

    def test_delete_organization_api_error(self, mock_api_client):
        api_client, http_organization = mock_api_client
        api_client.delete.side_effect = PlatformAPIError(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, message=""
        )
        with pytest.raises(PlatformAPIError):
            http_organization.delete(organization_id=123)
        api_client.delete.assert_called_once_with("/v1/organizations/123")

    def test_create_organization_member_success(self, mock_api_client):
        api_client, http_organization = mock_api_client
        user_invite = UserInvite(
            email="<EMAIL>", first_name="John", last_name="Doe", role="Admin"
        )
        created_user_id = uuid.UUID("a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11")
        api_client.post.return_value = MagicMock()
        api_client.parse_payload.return_value = {"id": created_user_id}
        result = http_organization.create_organization_member(
            organization_id=123, user_invite=user_invite
        )
        assert result == created_user_id
        api_client.post.assert_called_once_with(
            "/v1/organizations/123/users",
            json={
                "email": "<EMAIL>",
                "firstName": "John",
                "lastName": "Doe",
                "role": "ClientAdmin",
                "enabled": True,
                "emailVerified": True,
            },
        )
        api_client.parse_payload.assert_called_once()

    def test_create_organization_member_conflict(self, mock_api_client):
        api_client, http_organization = mock_api_client
        user_invite = UserInvite(
            email="<EMAIL>", first_name="John", last_name="Doe", role="Admin"
        )
        api_client.post.side_effect = PlatformAPIError(
            status_code=status.HTTP_409_CONFLICT, message=""
        )
        with pytest.raises(ConflictException):
            http_organization.create_organization_member(
                organization_id=123, user_invite=user_invite
            )
        api_client.post.assert_called_once()

    def test_create_organization_member_forbidden(self, mock_api_client):
        api_client, http_organization = mock_api_client
        user_invite = UserInvite(
            email="<EMAIL>", first_name="John", last_name="Doe", role="Admin"
        )
        api_client.post.side_effect = PlatformAPIError(
            status_code=status.HTTP_403_FORBIDDEN, message=""
        )
        with pytest.raises(ForbiddenError):
            http_organization.create_organization_member(
                organization_id=123, user_invite=user_invite
            )
        api_client.post.assert_called_once()

    def test_create_organization_member_api_error(self, mock_api_client):
        api_client, http_organization = mock_api_client
        user_invite = UserInvite(
            email="<EMAIL>", first_name="John", last_name="Doe", role="Admin"
        )
        api_client.post.side_effect = PlatformAPIError(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, message=""
        )
        with pytest.raises(PlatformAPIError):
            http_organization.create_organization_member(
                organization_id=123, user_invite=user_invite
            )
        api_client.post.assert_called_once()

    def test_get_organization_member_success(self, mock_api_client):
        api_client, http_organization = mock_api_client
        user_id = uuid.UUID("a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11")
        api_client.get.return_value = MagicMock()
        org_user = OrganizationUser(
            id=user_id,
            email="<EMAIL>",
            username="testuser",
            firstName="John",
            lastName="Doe",
            roles=["ClientAdmin"],
            enabled=True,
            emailVerified=True,
        )
        api_client.parse_payload.return_value = org_user
        result = http_organization.get_organization_member(
            organization_id=123, user_id=user_id
        )
        assert result == AccountUser(
            id=user_id,
            username="testuser",
            email="<EMAIL>",
            first_name="John",
            last_name="Doe",
            role="Admin",
        )
        api_client.get.assert_called_once_with(
            "/v1/organizations/123/users/a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11"
        )
        api_client.parse_payload.assert_called_once()

    def test_get_organization_member_role_not_supported(self, mock_api_client):
        api_client, http_organization = mock_api_client
        user_id = uuid.UUID("a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11")
        api_client.get.return_value = MagicMock()
        org_user = OrganizationUser(
            id=user_id,
            email="<EMAIL>",
            username="testuser",
            firstName="John",
            lastName="Doe",
            roles=["Admin"],
            enabled=True,
            emailVerified=True,
        )
        api_client.parse_payload.return_value = org_user
        result = http_organization.get_organization_member(
            organization_id=123, user_id=user_id
        )
        assert result is None
        api_client.get.assert_called_once_with(
            "/v1/organizations/123/users/a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11"
        )
        api_client.parse_payload.assert_called_once()

    def test_get_organization_member_forbidden(self, mock_api_client):
        api_client, http_organization = mock_api_client
        user_id = uuid.UUID("a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11")
        api_client.get.side_effect = PlatformAPIError(
            status_code=status.HTTP_403_FORBIDDEN, message=""
        )
        with pytest.raises(ForbiddenError):
            http_organization.get_organization_member(
                organization_id=123, user_id=user_id
            )
        api_client.get.assert_called_once_with(
            "/v1/organizations/123/users/a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11"
        )

    def test_get_organization_member_not_found(self, mock_api_client):
        api_client, http_organization = mock_api_client
        user_id = uuid.UUID("a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11")
        api_client.get.side_effect = PlatformAPIError(
            status_code=status.HTTP_404_NOT_FOUND, message=""
        )
        result = http_organization.get_organization_member(
            organization_id=123, user_id=user_id
        )
        assert result is None
        api_client.get.assert_called_once_with(
            "/v1/organizations/123/users/a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11"
        )

    def test_get_organization_member_api_error(self, mock_api_client):
        api_client, http_organization = mock_api_client
        user_id = uuid.UUID("a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11")
        api_client.get.side_effect = PlatformAPIError(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, message=""
        )
        with pytest.raises(PlatformAPIError):
            http_organization.get_organization_member(
                organization_id=123, user_id=user_id
            )
        api_client.get.assert_called_once_with(
            "/v1/organizations/123/users/a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11"
        )

    def test_update_organization_member_username_success(self, mock_api_client):
        api_client, http_organization = mock_api_client
        user_id = uuid.UUID("a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11")
        username = "newusername"
        http_organization.update_organization_member_username(
            organization_id=123, user_id=user_id, username=username
        )
        api_client.patch.assert_called_once_with(
            "/v1/organizations/123/users/a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11",
            json={"username": "newusername"},
        )

    def test_update_organization_member_username_not_found(self, mock_api_client):
        api_client, http_organization = mock_api_client
        user_id = uuid.UUID("a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11")
        username = "newusername"
        api_client.patch.side_effect = PlatformAPIError(
            status_code=status.HTTP_404_NOT_FOUND, message=""
        )
        with pytest.raises(UserNotFound):
            http_organization.update_organization_member_username(
                organization_id=123, user_id=user_id, username=username
            )
        api_client.patch.assert_called_once_with(
            "/v1/organizations/123/users/a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11",
            json={"username": "newusername"},
        )

    def test_update_organization_member_username_forbidden(self, mock_api_client):
        api_client, http_organization = mock_api_client
        user_id = uuid.UUID("a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11")
        username = "newusername"
        api_client.patch.side_effect = PlatformAPIError(
            status_code=status.HTTP_403_FORBIDDEN, message=""
        )
        with pytest.raises(ForbiddenError):
            http_organization.update_organization_member_username(
                organization_id=123, user_id=user_id, username=username
            )
        api_client.patch.assert_called_once_with(
            "/v1/organizations/123/users/a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11",
            json={"username": "newusername"},
        )

    def test_update_organization_member_username_api_error(self, mock_api_client):
        api_client, http_organization = mock_api_client
        user_id = uuid.UUID("a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11")
        username = "newusername"
        api_client.patch.side_effect = PlatformAPIError(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, message=""
        )
        with pytest.raises(PlatformAPIError):
            http_organization.update_organization_member_username(
                organization_id=123, user_id=user_id, username=username
            )
        api_client.patch.assert_called_once_with(
            "/v1/organizations/123/users/a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11",
            json={"username": "newusername"},
        )

    def test_get_access_token_success(self, mock_api_client):
        api_client, http_organization = mock_api_client
        expected_response = {
            "access_token": "eyJhbGciOiJSUzI1NiIsInR5cC",
            "expires_in": 3600,
        }
        response_mock = MagicMock()
        response_mock.json.return_value = expected_response
        api_client.get.return_value = response_mock

        result = http_organization.get_access_token()

        assert result == TokenResponse(**expected_response)
        api_client.get.assert_called_once_with("/v1/organizations/api/token")

    def test_get_access_token_platform_api_error(self, mock_api_client):
        api_client, http_organization = mock_api_client
        api_client.get.side_effect = PlatformAPIError(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, message=""
        )

        with pytest.raises(PlatformAPIError):
            http_organization.get_access_token()

        api_client.get.assert_called_once_with("/v1/organizations/api/token")


class UserRepositoryContract(ABC):
    @classmethod
    def make_account(cls) -> Account:
        return Account(
            name=OrganizationName("Kyivstar"),
            agreement_number="asdf",
            status=AccountStatus.ACTIVE,
            currency=CurrencyCode("UAH"),
            sales_channel=SalesChannel.WHOLESALE,
            industry_vertical=IndustryVertical.TELECOMMUNICATIONS,
            product_types=[ProductType.NATIONAL_ROAMING],
            sales_person="John Doe",
            organization_id=42,
            contract_end_date=date.today(),
            is_billable=False,
            contact_name=ContactName("Bill Person"),
            email=EmailStr("<EMAIL>"),
            phone=PhoneNumber("**********"),
            job_title=None,
            country=CountryCode("UA"),
            city="Manchester",
            postcode=Postcode("398591"),
            sim_charge=Decimal(12.35),
            payment_terms=PaymentTerms(30),
            threshold_charge=None,
            carrier_name="EE",
            warning_threshold=97,
            sim_profile=SimProfile.DATA_ONLY,
        )

    @pytest.fixture
    def account(self, *args, **kwargs) -> Account:
        account = self.make_account()
        account.id = 15
        return account

    @abstractmethod
    @pytest.fixture
    def repository(self, *args, **kwargs) -> AbstractUserRepository:
        ...

    def test_created_user_can_be_retreived_by_email(self, account, repository):
        user = User(
            id=uuid.uuid4(),
            account_id=account.id,
            email=EmailStr("<EMAIL>"),
            role="Admin",
        )
        repository.add(user)
        saved_user = repository.get_by_email(user.email)
        assert saved_user is not None
        assert saved_user.email == user.email

    def test_existing_user_updated(self, account, repository):
        user = User(
            id=uuid.uuid4(),
            account_id=account.id,
            email=EmailStr("<EMAIL>"),
            role="Admin",
        )
        repository.add(user)
        reference_id = user.create_invite()
        user.saf_id = str(uuid.uuid4())
        repository.add(user)

        saved_user = repository.get_by_email(user.email)
        assert saved_user.saf_id == user.saf_id
        assert isinstance(saved_user.saf_invites.get(reference_id), SAFInvite)


class TestInMemoryUserRepository(UserRepositoryContract):
    @pytest.fixture
    def repository(self) -> AbstractUserRepository:
        return InMemoryUserRepository()


class TestAccountRepository:
    def test_get_sim_account_info(
        self, database_accounts_repository_mock, session_mock
    ):

        Row = namedtuple(
            "Row",
            [
                "imsi",
                "iccid",
                "msisdn",
                "sim_status",
                "account_id",
                "account_name",
                "account_logo_key",
                "status",
                "rules_uuid_param",
                "simusagelimit",
                "unit",
                "actions",
                "notifications",
            ],
        )

        rules = Row(
            imsi="***************",
            iccid="8944538531005850000",
            msisdn="************",
            sim_status="ACTIVE",
            account_id=1,
            account_name="name 1",
            account_logo_key="/home/<USER>/tmp/monoglass/images/bt-logo.svg",
            status=True,
            rules_uuid_param=UUID("123e4567-e89b-12d3-a456-************"),
            simusagelimit=40005,
            unit="KB",
            actions=[],
            notifications=[],
        )

        mock_result = MagicMock()
        mock_result.all.return_value = [rules]
        session_mock.execute.return_value = mock_result

        mock_database_sim_repository = database_accounts_repository_mock(session_mock)

        result = mock_database_sim_repository.get_sim_account_info("***************")

        expected_response = SimAccountInfoDetails(
            imsi="***************",
            iccid="8944538531005850000",
            msisdn="************",
            sim_status="ACTIVE",
            account_id=1,
            account_name="name 1",
            account_logo_key="/home/<USER>/tmp/monoglass/images/bt-logo.svg",
        )

        assert result.imsi == expected_response.imsi
        assert result.iccid == expected_response.iccid
        assert result.msisdn == expected_response.msisdn
        assert result.sim_status == SimStatus[expected_response.sim_status]
        assert result.account_id == expected_response.account_id
        assert result.account_name == expected_response.account_name
        assert result.account_logo_key == expected_response.account_logo_key


if __name__ == "__main__":
    import pytest

    pytest.main()

    def test_get_sim_expected_response_error(
        self, database_accounts_repository_mock, session_mock
    ):

        mock_result = MagicMock()
        mock_result.first.return_value = None

        session_mock.execute.return_value = mock_result

        mock_database_sim_repository = database_accounts_repository_mock(
            session_mock=session_mock
        )

        with pytest.raises(SimAccountDataNotFound):
            mock_database_sim_repository.get_sim_account_info("***************")
