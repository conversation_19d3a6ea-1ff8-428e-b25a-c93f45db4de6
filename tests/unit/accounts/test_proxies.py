import uuid
from dataclasses import asdict
from datetime import date, datetime
from itertools import repeat, starmap
from operator import attrgetter
from typing import Any, Callable, Iterable

from accounts.domain import dto
from accounts.domain.model import (
    Account,
    AccountNames,
    AccountStatus,
    AccountSummary,
    GetAccount,
    IndustryVertical,
    OrganizationName,
    ProductType,
    SalesChannel,
    SimAccountInfoDetails,
    SimProfile,
)
from accounts.proxies import AccountServiceAuthProxy, UserServiceAuthProxy
from accounts.services import AbstractAccountService, AbstractUserService
from auth.dto import Account as AuthAccount
from auth.dto import AuthenticatedUser, Client, Distributor, Service
from auth.exceptions import ForbiddenError, NotFound
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from common.types import CurrencyCode

import pytest  # isort: skip


@pytest.fixture
def create_account():
    def factory(account_id: int, **kwargs):
        common_data = dict(
            status=AccountStatus.ACTIVE,
            product_types=[ProductType.NATIONAL_ROAMING],
            industry_vertical=IndustryVertical.DEFENCE,
            sales_channel=SalesChannel.WHOLESALE,
            contract_end_date=date.today(),
            is_billable=True,
            sim_profile=SimProfile.DATA_ONLY,
        )
        common_data.update(kwargs)
        return Account(
            _id=account_id,
            name=OrganizationName(f"account {account_id}"),
            agreement_number=f"{account_id}agreement",
            currency=CurrencyCode("GBP"),
            sales_person=f"sales-account-{account_id}",
            organization_id=account_id + 100,
            **common_data,  # type:ignore[arg-type]
        )

    return factory


@pytest.fixture
def create_account_proxy():
    def factory(account_id: int, **kwargs):
        common_data = dict(
            status=AccountStatus.ACTIVE.name,
            product_types=[],
            industry_vertical=IndustryVertical.DEFENCE.name,
            sales_channel=SalesChannel.WHOLESALE.name,
            contract_end_date=date.today(),
            is_billable=True,
        )
        common_data.update(kwargs)
        return Account(
            _id=account_id,
            name=OrganizationName(f"account {account_id}"),
            agreement_number=f"{account_id}agreement",
            currency=CurrencyCode("GBP"),
            sales_person=f"sales-account-{account_id}",
            organization_id=account_id + 100,
            sim_profile=SimProfile.DATA_ONLY,
            **common_data,  # type:ignore[arg-type]
        )

    return factory


@pytest.fixture
def get_account():
    def factory(account_id: int, **kwargs):
        common_data = dict(
            status=AccountStatus.ACTIVE,
            product_types=[ProductType.NATIONAL_ROAMING],
            industry_vertical=IndustryVertical.DEFENCE,
            sales_channel=SalesChannel.WHOLESALE,
            contract_end_date=date.today(),
            is_billable=True,
        )
        common_data.update(kwargs)
        return dto.GetAccount(
            _id=account_id,
            name=OrganizationName(f"account {account_id}"),
            agreement_number=f"{account_id}agreement",
            currency=CurrencyCode("GBP"),
            sales_person=f"sales-account-{account_id}",
            organization_id=account_id + 100,
            sims_total=10,
            active_sims_total=5,
            plans_total=2,
            users_total=3,
            default_rate_plan="rate-plan-1",
            **common_data,  # type:ignore[arg-type]
        )

    return factory


class FakeAccountService(AbstractAccountService):
    def __init__(
        self,
        accounts: list[Account],
        account_names: list[AccountNames],
        get_account: GetAccount,
    ):
        self.accounts = accounts
        self.account_name = account_names
        self.get_account = get_account

    def get(self, account_id: int) -> dto.GetAccount:
        return dto.GetAccount.from_model_get_acc(self.get_account, None)

    def account_count(
        self, account_ids: list[int] | None = None, searching: Searching | None = None
    ) -> int:
        return len(
            list(starmap(dto.Account.from_model, zip(self.accounts, repeat(None))))
        )

    def list(
        self,
        account_ids: list[int] | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> Iterable[dto.GetAccount]:
        return starmap(
            dto.GetAccount.from_model_get_acc, zip(self.accounts, repeat(None))
        )

    def get_billing_settings(self, account_id: int) -> dto.BillingSettings:
        return dto.BillingSettings.from_model(self.accounts[0])

    def set_billing_settings(
        self, account_id: int, billing_settings: dto.BillingSettings
    ) -> dto.BillingSettings:
        return self.get_billing_settings(account_id)

    def create_account(self, account: dto.CreateAccount) -> int:
        return 0

    def update(self, account_id: int, account: dto.UpdateAccount) -> dto.Account:
        return dto.Account.from_model(self.accounts[0], None)

    def delete(self, account_id: int) -> None:
        return None

    def sync_accounts_with_organizations(
        self, root_organization_name: str, log: Callable[[str], None] = print
    ) -> None:
        raise NotImplementedError

    def get_account_names(self) -> Iterable[dto.AccountNames]:
        return [dto.AccountNames.from_model(account) for account in self.account_name]

    def get_account_by_organization_id(self, account_id: int):
        return None

    def get_sim_account_info(self, search: str) -> SimAccountInfoDetails:
        pass

    def get_account_summary(self, account_id: int) -> AccountSummary:
        pass


class FakeUserService(AbstractUserService):
    def invite_user(self, account: dto.Account, user_invite: dto.UserInvite) -> str:
        return str(uuid.uuid4())

    def accept_user_invite(self, event: dto.SAFEvent) -> None:
        return None


@pytest.fixture
def create_distributor_staff_user():
    def factory(organization_id: int = 1):
        return AuthenticatedUser(
            id=uuid.uuid4(),
            organization=Distributor(id=organization_id),
            role="Admin",
            email="<EMAIL>",
        )

    return factory


@pytest.fixture
def create_distributor_client_user():
    def factory(account_id: int):
        return AuthenticatedUser(
            id=uuid.uuid4(),
            organization=Client(
                id=account_id + 100,
                account=AuthAccount(id=account_id),
            ),
            role="Admin",
            email="<EMAIL>",
        )

    return factory


class TestAccountServiceAuthProxy:
    def test_create_account_allowed_for_distributor_staff(
        self,
        create_distributor_staff_user,
        create_account,
        get_account,
    ):
        proxy = AccountServiceAuthProxy(
            FakeAccountService([], [], get_account),
            create_distributor_staff_user(),
        )
        account = create_account(1)
        cmd = dto.CreateAccount.parse_obj(account_to_dto_dict(account))
        assert proxy.create_account(cmd) is not None

    def test_create_account_forbidden_for_distributor_client(
        self,
        create_distributor_client_user,
        create_account,
        get_account,
    ):
        proxy = AccountServiceAuthProxy(
            FakeAccountService([], [], get_account),
            create_distributor_client_user(1),
        )
        account = create_account(1)
        cmd = dto.CreateAccount.parse_obj(account_to_dto_dict(account))
        with pytest.raises(ForbiddenError):
            proxy.create_account(cmd)

    def test_update_account_allowed_for_distributor_staff(
        self,
        create_distributor_staff_user,
        create_account,
        get_account,
    ):
        account = create_account(1)
        proxy = AccountServiceAuthProxy(
            FakeAccountService([account], [], get_account),
            create_distributor_staff_user(),
        )
        cmd = dto.UpdateAccount.parse_obj(account_to_dto_dict(account))
        assert proxy.update(account.id, cmd) is not None

    def test_update_account_forbidden_for_distributor_client(
        self,
        create_distributor_client_user,
        create_account,
        get_account,
    ):
        account_1 = create_account(1)
        account_2 = create_account(2)
        proxy = AccountServiceAuthProxy(
            FakeAccountService([account_1, account_2], [], get_account),
            create_distributor_client_user(account_1.id),
        )
        cmd = dto.UpdateAccount.parse_obj(account_to_dto_dict(account_1))
        with pytest.raises(ForbiddenError):
            proxy.update(account_1.id, cmd)

        cmd = dto.UpdateAccount.parse_obj(account_to_dto_dict(account_2))
        with pytest.raises(ForbiddenError):
            proxy.update(account_2.id, cmd)

    def test_set_billing_settings_allowed_for_distributor_staff(
        self,
        create_distributor_staff_user,
        create_account,
        get_account,
    ):
        account = create_account(1)
        proxy = AccountServiceAuthProxy(
            FakeAccountService([account], [], get_account),
            create_distributor_staff_user(),
        )
        cmd = dto.BillingSettings.parse_obj(account_to_dto_dict(account))
        assert proxy.set_billing_settings(account.id, cmd) is not None

    def test_set_billing_settings_forbidden_for_distributor_client(
        self,
        create_distributor_client_user,
        create_account,
        get_account,
    ):
        account = create_account(1)
        proxy = AccountServiceAuthProxy(
            FakeAccountService([account], [], get_account),
            create_distributor_client_user(account.id),
        )
        cmd = dto.BillingSettings.parse_obj(account_to_dto_dict(account))
        with pytest.raises(ForbiddenError):
            proxy.set_billing_settings(account.id, cmd)

    def test_delete_account_allowed_for_distributor_staff(
        self,
        create_distributor_staff_user,
        create_account,
        get_account,
    ):
        account = create_account(1)
        proxy = AccountServiceAuthProxy(
            FakeAccountService([account], [], get_account),
            create_distributor_staff_user(),
        )
        assert proxy.delete(account.id) is None

    def test_delete_account_forbidden_for_distributor_client(
        self,
        create_distributor_client_user,
        create_account,
        get_account,
    ):
        account = create_account(3)
        proxy = AccountServiceAuthProxy(
            FakeAccountService([account], [], get_account),
            create_distributor_client_user(account.id),
        )
        with pytest.raises(ForbiddenError):
            proxy.delete(1)

    def test_distributor_staff_allowed_to_list_all_accounts(
        self,
        create_distributor_staff_user,
        create_account_proxy,
        get_account,
    ):
        account1 = create_account_proxy(1)
        account2 = create_account_proxy(2)
        AccountServiceAuthProxy(
            FakeAccountService([account1, account2], [], get_account),
            create_distributor_staff_user(),
        )
        # assert ilen(proxy.list()) == 2

    def test_distributor_client_forbidden_to_list_accounts(
        self,
        create_distributor_client_user,
        create_account,
        get_account,
    ):
        account1 = create_account(1)
        account2 = create_account(2)
        proxy = AccountServiceAuthProxy(
            FakeAccountService([account1, account2], [], get_account),
            create_distributor_client_user(account1.id),
        )
        with pytest.raises(ForbiddenError):
            proxy.list()

    def test_distributor_staff_allowed_to_get_any_account(
        self,
        create_distributor_staff_user,
        create_account_proxy,
        get_account,
    ):
        account1 = create_account_proxy(1)
        account2 = create_account_proxy(2)
        AccountServiceAuthProxy(
            FakeAccountService([account1, account2], [], get_account),
            create_distributor_staff_user(),
        )
        # assert proxy.get(account1.id)
        # assert proxy.get(account2.id)

    def test_distributor_client_allowed_to_get_own_account(
        self,
        create_distributor_client_user,
        create_account,
        get_account,
    ):
        account1 = create_account(1)
        account2 = create_account(2)
        AccountServiceAuthProxy(
            FakeAccountService([account1, account2], [], get_account),
            create_distributor_client_user(account1.id),
        )
        # assert proxy.get(account1.id)

    def test_distributor_client_forbidden_to_get_account_other_than_his_own(
        self,
        create_distributor_client_user,
        create_account,
        get_account,
    ):
        account1 = create_account(1)
        account2 = create_account(2)
        proxy = AccountServiceAuthProxy(
            FakeAccountService([account1, account2], [], get_account),
            create_distributor_client_user(account1.id),
        )
        with pytest.raises(NotFound):
            assert proxy.get(account2.id)

    def test_distributor_staff_allowed_to_get_billing_settings_of_any_account(
        self,
        create_distributor_staff_user,
        create_account,
        get_account,
    ):
        account1 = create_account(1)
        account2 = create_account(2)
        proxy = AccountServiceAuthProxy(
            FakeAccountService([account1, account2], [], get_account),
            create_distributor_staff_user(),
        )
        assert proxy.get_billing_settings(account1.id)
        assert proxy.get_billing_settings(account2.id)

    def test_distributor_client_forbidden_to_get_billing_settings_of_any_account(
        self,
        create_distributor_client_user,
        create_account,
        get_account,
    ):
        account1 = create_account(1)
        account2 = create_account(2)
        proxy = AccountServiceAuthProxy(
            FakeAccountService([account1, account2], [], get_account),
            create_distributor_client_user(account1.id),
        )
        with pytest.raises(ForbiddenError):
            assert proxy.get_billing_settings(account1.id)
        with pytest.raises(ForbiddenError):
            assert proxy.get_billing_settings(account2.id)


def account_to_dto_dict(account: Account) -> dict[str, Any]:
    data = asdict(account)
    enum_key = attrgetter("name")
    data.update(
        status=enum_key(account.status),
        product_types=list(map(enum_key, account.product_types)),
        industry_vertical=enum_key(account.industry_vertical),
        sales_channel=enum_key(account.sales_channel),
        sim_profile=enum_key(account.sim_profile),
    )
    return data


class TestUserAuthProxy:
    @pytest.fixture
    def create_invite(self):
        def factory(**kwargs):
            defaults = dict(
                email="<EMAIL>",
                role="Admin",
                first_name="Franklin",
                last_name="Benjamin",
            )
            defaults.update(kwargs)
            return dto.UserInvite(**defaults)

        return factory

    @pytest.fixture
    def create_saf_event(self):
        def factory(**kwargs) -> dto.SAFEvent:
            defaults = dict(
                email="<EMAIL>",
                user_id="user-id-123",
                registration_ts=datetime.utcnow().timestamp(),
                reference_id="reference-id-123",
            )
            defaults.update(kwargs)
            return dto.SAFEvent(**defaults)

        return factory

    @pytest.fixture
    def create_proxy_with_invite(
        self, create_invite, create_distributor_staff_user, create_account
    ):
        def factory() -> tuple[UserServiceAuthProxy, dto.UserInvite]:
            proxy = UserServiceAuthProxy(
                FakeUserService(), create_distributor_staff_user(1)
            )
            account = create_account(1)
            invite = create_invite(role="User")
            proxy.invite_user(account, invite)
            return proxy, invite

        return factory

    @pytest.mark.parametrize("role", ["Admin", "User"])
    def test_distributor_staff_allowed_to_invite_account_member(
        self, role, create_distributor_staff_user, create_account, create_invite
    ):
        proxy = UserServiceAuthProxy(
            FakeUserService(), create_distributor_staff_user(1)
        )
        account = create_account(1)
        invite = create_invite(role=role)
        assert proxy.invite_user(account, invite)

    def test_account_admin_allowed_to_invite_account_user(
        self, create_distributor_client_user, create_account, create_invite
    ):
        proxy = UserServiceAuthProxy(
            FakeUserService(), create_distributor_client_user(1)
        )
        account = create_account(1)
        invite = create_invite(role="User")
        assert proxy.invite_user(account, invite)

    def test_account_admin_forbidden_to_invite_account_admin(
        self, create_distributor_client_user, create_account, create_invite
    ):
        proxy = UserServiceAuthProxy(
            FakeUserService(), create_distributor_client_user(1)
        )
        account = create_account(1)
        invite = create_invite(role="Admin")
        with pytest.raises(ForbiddenError):
            proxy.invite_user(account, invite)

    def test_account_admin_forbidden_to_invite_account_user_for_another_account(
        self, create_distributor_client_user, create_account, create_invite
    ):
        proxy = UserServiceAuthProxy(
            FakeUserService(), create_distributor_client_user(1)
        )
        account = create_account(2)
        invite = create_invite(role="User")
        with pytest.raises(ForbiddenError):
            proxy.invite_user(account, invite)

    @pytest.mark.parametrize(
        "actor",
        [
            (
                AuthenticatedUser(
                    id=uuid.uuid4(),
                    organization=Client(id=1),
                    role="User",
                    email="<EMAIL>",
                ),
            ),
            (
                AuthenticatedUser(
                    id=uuid.uuid4(),
                    organization=Client(id=1),
                    role="Admin",
                    email="<EMAIL>",
                ),
            ),
            (
                AuthenticatedUser(
                    id=uuid.uuid4(),
                    organization=Distributor(id=1),
                    role="User",
                    email="<EMAIL>",
                ),
            ),
        ],
    )
    def test_user_invite_accepting_with_wrong_access_of_actor(
        self, create_proxy_with_invite, create_saf_event, actor
    ):
        proxy, invite = create_proxy_with_invite()
        event = create_saf_event(email=invite.email)
        proxy.actor = actor

        with pytest.raises(ForbiddenError):
            proxy.accept_user_invite(event)

    @pytest.mark.parametrize(
        "actor",
        [
            (
                AuthenticatedUser(
                    id=uuid.uuid4(),
                    organization=Distributor(id=1),
                    role="Admin",
                    email="<EMAIL>",
                )
            ),
            (Service(client_id=str(uuid.uuid4()))),
        ],
    )
    def test_user_invite_accepting_with_correct_access_of_actor(
        self, create_proxy_with_invite, create_saf_event, actor
    ):
        proxy, invite = create_proxy_with_invite()
        event = create_saf_event(email=invite.email)
        proxy.actor = actor

        assert proxy.accept_user_invite(event) is None
