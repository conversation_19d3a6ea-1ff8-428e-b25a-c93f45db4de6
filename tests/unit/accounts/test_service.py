import uuid
from dataclasses import dataclass
from datetime import date
from decimal import Decimal
from unittest.mock import MagicMock

import pytest

from accounts.adapters.exceptions import DigitalIdentityUserExist
from accounts.adapters.repository import (
    InMemoryAccountRepository,
    InMemoryDigitalIdentity,
    InMemoryOrganizationRepository,
    InMemoryUserRepository,
)
from accounts.domain import dto
from accounts.domain.exceptions import (
    AccountAlreadyExists,
    AccountDoesNotExist,
    MediaError,
)
from accounts.domain.model import (
    AccountStatus,
    IndustryVertical,
    Organization,
    OrganizationName,
    SalesChannel,
    SimAccountInfoDetails,
    SimProfile,
)
from accounts.domain.ports import (
    AbstractAccountRepository,
    AbstractDigitalIdentity,
    AbstractOrganizationRepository,
    AbstractUserRepository,
)
from accounts.exceptions import (
    UserAlreadyRegistered,
    UserInvitedToDifferentAccount,
    UserInvitedWithAnotherRole,
)
from accounts.services import AccountService, MediaService, UserService
from api.accounts.examples import SIM_ACCOUNT_INFO_DETAILS, SIM_INFO_LOGO_KEY_DETAILS
from auth.dto import AuthenticatedUser
from auth.services import AbstractAuthService, FakeAuthService
from automation.adapters.repository import AbstractAutomationRepository
from common.file_storage import LocalFileStorage
from common.types import Enumeration


@pytest.fixture
def accounts_service_mock():
    user_mock = MagicMock(spec=AuthenticatedUser)
    account_repository_mock = MagicMock(spec=AbstractAccountRepository)
    organization_repository_mock = MagicMock(spec=AbstractOrganizationRepository)
    media_service_mock = MagicMock(spec=MediaService)

    account_repository_mock.get_sim_account_info.return_value = SimAccountInfoDetails(
        **SIM_INFO_LOGO_KEY_DETAILS[0]
    )
    media_service_mock.get_file_url.return_value = "http://localhost:8000//home/<USER>/tmp"

    mock_accounts_service = AccountService(
        user=user_mock,
        account_repository=account_repository_mock,
        organization_repository=organization_repository_mock,
        media_service=media_service_mock,
    )

    return mock_accounts_service


@pytest.fixture
def accounts_service_mock_error():
    def auth_factory(error_type, message):
        user_mock = MagicMock(spec=AuthenticatedUser)
        account_repository_mock = MagicMock(spec=AbstractAccountRepository)
        organization_repository_mock = MagicMock(spec=AbstractOrganizationRepository)
        media_service_mock = MagicMock(spec=MediaService)

        account_repository_mock.get_sim_account_info.return_value = None

        mock_accounts_service = AccountService(
            user=user_mock,
            account_repository=account_repository_mock,
            organization_repository=organization_repository_mock,
            media_service=media_service_mock,
        )

        return mock_accounts_service

    return auth_factory


class TestAccountService:
    @pytest.fixture
    def auth_service(self) -> AbstractAuthService:
        return FakeAuthService()

    @pytest.fixture
    def account_repository(self) -> AbstractAccountRepository:
        return InMemoryAccountRepository()

    @pytest.fixture
    def organization_repository(self) -> InMemoryOrganizationRepository:
        return InMemoryOrganizationRepository()

    @pytest.fixture
    def media_service(self) -> MediaService:
        file_storage = LocalFileStorage("")
        return MediaService(file_storage)

    @pytest.fixture
    def automation_repository(self) -> AbstractAutomationRepository:
        return MagicMock(spec=AbstractAutomationRepository)

    @pytest.fixture
    def account_service(
        self,
        account_repository,
        organization_repository,
        auth_service,
        media_service,
        automation_repository,
    ):
        return AccountService(
            auth_service.authenticated_user,
            account_repository,
            organization_repository,
            media_service,
            automation_repository,
        )

    @classmethod
    def create_account_command(cls, **kwargs) -> dto.CreateAccount:
        defaults = dict(
            name="Kyivstar",
            agreement_number="asdf",
            status="ACTIVE",
            country="UA",
            currency="UAH",
            sales_channel="WHOLESALE",
            industry_vertical="TELECOMMUNICATIONS",
            product_types=["NATIONAL_ROAMING"],
            sales_person="John Doe",
            contract_end_date=date.today(),
            is_billable=False,
            sim_charge=12.35,
            payment_terms=30,
            warning_threshold=90,
            sim_profile="DATA_ONLY",
        )
        defaults.update(kwargs)
        return dto.CreateAccount.parse_obj({**defaults, **kwargs})

    @classmethod
    def update_account_command(cls, **kwargs) -> dto.UpdateAccount:
        defaults = dict(
            name="Vodafone",
            agreement_number="Test",
            sales_channel="TRIAL",
            sales_person="Jimmy Park",
            industry_vertical="DEFENCE",
            contract_end_date=date.today(),
            is_billable=False,
            sim_charge=12.35,
            payment_terms=30,
            warning_threshold=93,
            sim_profile="DATA_ONLY",
        )
        defaults.update(kwargs)
        return dto.UpdateAccount.parse_obj({**defaults, **kwargs})

    def test_account_preserved(self, account_service, account_repository):
        command = self.create_account_command()
        account_id = account_service.create_account(command)
        account = account_repository.get(account_id)
        assert account.id == account_id
        assert account.name == "Kyivstar"
        assert account.status == AccountStatus.ACTIVE
        assert account.agreement_number == "asdf"
        assert account.country == "UA"
        assert account.currency == "UAH"
        assert account.sales_channel == SalesChannel.WHOLESALE
        assert account.industry_vertical == IndustryVertical.TELECOMMUNICATIONS
        assert account.sales_person == "John Doe"
        assert account.warning_threshold == 90
        assert account.sim_profile == SimProfile.DATA_ONLY

    def test_create_two_accounts_with_same_name(self, account_service):
        create_command = self.create_account_command()
        _ = account_service.create_account(create_command)

        # Test create account with exists name
        with pytest.raises(AccountAlreadyExists):
            _ = account_service.create_account(create_command)

    def test_organization_created(
        self, organization_repository, account_service, auth_service, account_repository
    ):
        command = self.create_account_command()
        account_id = account_service.create_account(command)
        account = account_repository.get(account_id)
        org = organization_repository.get(account.organization_id)
        assert org is not None
        assert org.id == account.organization_id

    def test_sync_accounts_with_organizations_happy_path_is_ok(
        self, organization_repository, account_service, account_repository
    ):
        root_org = Organization(id=1, name=OrganizationName("Nextgen Clearing"))
        organization_repository.organizations[root_org.id] = root_org

        account = self.create_account_command().to_model(organization_id=0)
        account_id = account_repository.add(account)

        account_service.sync_accounts_with_organizations(root_org.name)

        updated_account = account_repository.get(account_id)
        organization = organization_repository.get_by_name(updated_account.name)
        assert organization
        assert organization.name == account.name
        assert updated_account.organization_id == organization.id

    def test_sync_account_with_existing_organization_is_ok(
        self, organization_repository, account_service, account_repository
    ):
        root_org = Organization(id=1, name=OrganizationName("Nextgen Clearing"))
        organization_repository.organizations[root_org.id] = root_org

        account = self.create_account_command().to_model(organization_id=0)
        client_org = Organization(id=2, name=account.name)

        organization_repository.organizations[client_org.id] = client_org
        account_id = account_repository.add(account)

        account_service.sync_accounts_with_organizations(root_org.name)

        updated_account = account_repository.get(account_id)
        assert updated_account.organization_id == client_org.id

    def test_sync_account_when_root_organization_does_not_exist(self, account_service):
        with pytest.raises(RuntimeError):
            account_service.sync_accounts_with_organizations("No Org")

    def test_delete_account_with_organization(
        self, organization_repository, account_service, account_repository
    ):
        command = self.create_account_command()
        account_id = account_service.create_account(command)
        account_service.automation_repository.count_rules_by_account = MagicMock(
            return_value=0
        )
        account = account_repository.get(account_id)
        org = organization_repository.get(account.organization_id)
        assert account is not None
        assert org is not None

        account_service.delete(account_id)
        org = organization_repository.get(account.organization_id)
        account = account_repository.get(account_id)
        assert org is None
        assert account is None

    def test_update_account(self, account_service):
        create_command = self.create_account_command()
        account_id = account_service.create_account(create_command)

        update_command = self.update_account_command()
        updated_account = account_service.update(account_id, update_command)

        assert updated_account.id == account_id
        assert updated_account.name == "Vodafone"
        assert updated_account.agreement_number == "Test"
        assert updated_account.sales_channel == Enumeration.from_enum_value(
            SalesChannel.TRIAL
        )
        assert updated_account.industry_vertical == Enumeration.from_enum_value(
            IndustryVertical.DEFENCE
        )
        assert updated_account.sales_person == "Jimmy Park"
        assert updated_account.warning_threshold == 93
        assert updated_account.sim_profile == Enumeration.from_enum_value(
            SimProfile.DATA_ONLY
        )

    def test_update_non_exists_account(self, account_service):
        command = self.update_account_command()
        with pytest.raises(AccountDoesNotExist):
            _ = account_service.update(100, command)

    def test_update_account_with_duplicated_organization(
        self, account_service, account_repository
    ):
        create_command = self.create_account_command()
        account_id = account_service.create_account(create_command)
        account = account_repository.get(account_id)
        second_account_id = account_service.create_account(
            self.create_account_command(name="Test")
        )

        # Test update account with exists name
        update_command = self.update_account_command(name=account.name)
        with pytest.raises(AccountAlreadyExists):
            _ = account_service.update(second_account_id, update_command)

    def test_create_account_with_bad_logo_key_raises_error(self, account_service):
        command = self.create_account_command(
            logo_key=f"non-existing-key{uuid.uuid4().hex}"
        )
        with pytest.raises(MediaError):
            account_service.create_account(command)

    def test_update_account_with_bad_logo_key(
        self, account_service, account_repository
    ):
        create_command = self.create_account_command()
        account_id = account_service.create_account(create_command)
        command = self.update_account_command(
            logo_key=f"non-existing-key{uuid.uuid4().hex}"
        )
        with pytest.raises(MediaError):
            account_service.update(account_id, command)

    def test_list_account(self, account_service):
        account_service.create_account(
            self.create_account_command(
                name="Test1", warning_threshold=90, threshold_charge=None
            )
        )
        account_service.create_account(
            self.create_account_command(
                name="Test2", warning_threshold=91, threshold_charge=Decimal(0.00003)
            )
        )

    def test_account_by_organization_id(self, account_service, account_repository):
        create_command = self.create_account_command()
        account_id = account_service.create_account(create_command)
        account = account_repository.get_by_organization_id(account_id)
        assert account is not None


class TestUserService:
    @pytest.fixture
    def auth_service(self) -> AbstractAuthService:
        return FakeAuthService()

    @pytest.fixture
    def account_repository(self) -> AbstractAccountRepository:
        return InMemoryAccountRepository()

    @pytest.fixture
    def organization_repository(self) -> InMemoryOrganizationRepository:
        return InMemoryOrganizationRepository()

    @pytest.fixture
    def user_repository(self) -> AbstractUserRepository:
        return InMemoryUserRepository()

    @pytest.fixture
    def digital_identity(self) -> AbstractDigitalIdentity:
        return InMemoryDigitalIdentity()

    @pytest.fixture
    def user_service(
        self,
        account_repository: AbstractAccountRepository,
        organization_repository: InMemoryOrganizationRepository,
        user_repository: AbstractUserRepository,
        digital_identity: AbstractDigitalIdentity,
    ) -> UserService:
        return UserService(
            account_repository,
            organization_repository,
            user_repository,
            digital_identity,
        )

    @pytest.fixture
    def create_account(self, user_service):
        @dataclass
        class FakeAccount:
            id: int
            organization_id: int
            name: str

        def factory(**kwargs):
            defaults = dict(id=1, organization_id=1, name="John")
            defaults.update(kwargs)
            account = FakeAccount(**defaults)
            user_service.account_repository.accounts[account.id] = account
            return account

        return factory

    @pytest.fixture
    def create_user_invite(self):
        def factory(**kwargs):
            defaults = dict(
                email="<EMAIL>",
                role="Admin",
                first_name="Admin",
                last_name="Admin",
            )
            defaults.update(kwargs)
            return dto.UserInvite(**defaults)

        return factory

    @pytest.fixture
    def create_saf_event(self):
        def factory(**kwargs):
            defaults = dict(
                email="<EMAIL>",
                user_id="us-**************",
                registration_ts="**********",
                reference_id="0bc786c6-1579-4160-91d0-85a63ff80318",
            )
            defaults.update(kwargs)
            return dto.SAFEvent(**defaults)

        return factory

    @pytest.fixture
    def create_organization(self, organization_repository):
        def factory(**kwargs):
            defaults = dict(
                id=1,
                name="johndoe",
            )
            defaults.update(kwargs)
            org = Organization(**defaults)
            organization_repository.organizations[defaults["id"]] = org
            return org

        return factory

    def test_user_successfully_invited(
        self, user_service, create_account, create_user_invite
    ):
        account = create_account()
        invite = create_user_invite()
        user_service.invite_user(account, invite)
        user = user_service.user_repository.get_by_email(invite.email)
        invites = list(user.saf_invites.values())
        assert invites
        invite = invites[0]
        assert invite.sent

    def test_error_when_user_already_invited_by_different_account(
        self, user_service, create_account, create_user_invite
    ):
        account = create_account()
        invite = create_user_invite()
        user_service.invite_user(account, invite)
        account = create_account(id=2, organization_id=2)

        with pytest.raises(UserInvitedToDifferentAccount):
            user_service.invite_user(account, invite)

    def test_error_when_user_already_registered(
        self, user_service, create_account, create_user_invite
    ):
        account = create_account()
        invite = create_user_invite()
        user_service.invite_user(account, invite)
        user = user_service.user_repository.get_by_email(invite.email)
        user.saf_id = "1"

        with pytest.raises(UserAlreadyRegistered):
            user_service.invite_user(account, invite)

    def test_error_when_user_invited_with_different_role(
        self, user_service, create_account, create_user_invite
    ):
        account = create_account()
        invite = create_user_invite()
        user_service.invite_user(account, invite)
        invite = create_user_invite(role="User")

        with pytest.raises(UserInvitedWithAnotherRole):
            user_service.invite_user(account, invite)

    def test_handling_existing_digital_identity_user(
        self, monkeypatch, user_service, create_account, create_user_invite
    ):
        def _send_invite_mock(*args, **kwargs):
            user_id = uuid.uuid4()
            raise DigitalIdentityUserExist(user_id)

        monkeypatch.setattr(
            user_service.digital_identity, "send_invite", _send_invite_mock
        )

        account = create_account()
        invite = create_user_invite()

        with pytest.raises(UserAlreadyRegistered):
            user_service.invite_user(account, invite)

        user = user_service.user_repository.get_by_email(invite.email)
        assert user.registered

    def test_user_successfully_accepted_invite(
        self,
        user_service,
        create_account,
        create_user_invite,
        create_saf_event,
        create_organization,
    ):
        account = create_account()
        invite = create_user_invite()
        invite_id = user_service.invite_user(account, invite)
        saf_event = create_saf_event(reference_id=invite_id, email=invite.email)
        org = create_organization(name=account.name)
        user_service.accept_user_invite(saf_event)

        user = user_service.user_repository.get_by_email(invite.email)
        org_user = user_service.organization_repository.get_organization_member(
            org.id, user.id
        )
        assert org_user.username == user.saf_id


class TestSimInfo:
    @pytest.mark.skip("Need to fix")
    def test_get_sim_account_info_success(self, accounts_service_mock):

        expected_response = SimAccountInfoDetails(**SIM_ACCOUNT_INFO_DETAILS)

        search = "***************"
        result = accounts_service_mock.get_sim_account_info(search=search)

        assert isinstance(result, SimAccountInfoDetails)
        assert result == expected_response
