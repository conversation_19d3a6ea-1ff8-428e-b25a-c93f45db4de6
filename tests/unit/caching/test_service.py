from unittest.mock import MagicMock

import pytest

from caching.service import RedisService


@pytest.fixture
def repository_mock():
    return MagicMock()


@pytest.fixture
def mock_service(repository_mock):
    return RedisService(repository=repository_mock)


@pytest.mark.parametrize(
    "redis_data, expected",
    [
        (True, True),
        (<PERSON>als<PERSON>, False),
    ],
)
def test_is_duplicate_cdr_redis(repository_mock, mock_service, redis_data, expected):
    cdr_id = "test-cdr-123"
    repository_mock.is_duplicate_cdr.return_value = redis_data

    result = mock_service.is_duplicate_cdr(cdr_id)

    repository_mock.is_duplicate_cdr.assert_called_once_with(cdr_id)
    assert result == expected
