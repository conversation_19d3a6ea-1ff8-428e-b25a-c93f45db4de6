from unittest.mock import MagicMock

import pytest

from caching.adapters.repository import RedisRepository


@pytest.fixture
def redis_repo():
    return MagicMock()


@pytest.fixture
def mock_redis_repository(redis_repo):
    return RedisRepository(redis_client=redis_repo)


def test_is_duplicate_cdr(redis_repo, mock_redis_repository):
    redis_repo.set.return_value = True
    cdr_id = "12345"

    result = mock_redis_repository.is_duplicate_cdr(cdr_id)

    redis_repo.set.return_value = True
    assert result is True


def test_is_duplicate_cdr_duplicate(redis_repo, mock_redis_repository):
    redis_repo.set.return_value = None
    cdr_id = "12345"

    result = mock_redis_repository.is_duplicate_cdr(cdr_id)

    redis_repo.set.return_value = False
    assert result is False
