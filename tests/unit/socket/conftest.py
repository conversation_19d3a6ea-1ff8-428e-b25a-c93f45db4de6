from unittest.mock import AsyncMock, MagicMock

import pytest
import socketio  # type: ignore


@pytest.fixture
def mock_sio():
    mock = AsyncMock(spec=socketio.AsyncServer)
    mock.emit = AsyncMock()
    mock.on = MagicMock()
    return mock


@pytest.fixture
def mock_redis_manager():
    return MagicMock(spec=socketio.AsyncRedisManager)


@pytest.fixture
def mock_settings():
    mock = MagicMock()
    mock.APP_LANDING_PAGE_URL = "http://localhost:3000"
    mock.APP_BASE_URL = "http://localhost:8000"
    mock.REDIS_DB_HOST = "localhost"
    mock.REDIS_DB_PORT = 6379
    return mock
