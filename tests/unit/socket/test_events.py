from unittest.mock import MagicMock, patch

import pytest

from socket_events.sim.events import SimSocketEvents, get_sim_socket_events


class TestSimSocketEvents:
    def test_init(self, mock_sio):
        with patch.object(SimSocketEvents, "register_events") as mock_register:
            events = SimSocketEvents(mock_sio)

            assert events.sio == mock_sio
            mock_register.assert_called_once()

    def test_register_events(self, mock_sio):
        events = SimSocketEvents(mock_sio)

        events.register_events()

    @pytest.mark.asyncio
    async def test_sim_details_update_success(self, mock_sio):
        with patch("socket_events.sim.events.logger") as mock_logger:
            events = SimSocketEvents(mock_sio)
            imsi_list = ["123456789012345", "987654321098765"]
            status = "ACTIVE"

            await events.sim_details_update(imsi_list, status)

            expected_data = {"imsi": imsi_list, "simStatus": status}
            mock_sio.emit.assert_called_once_with("sim_details_update", expected_data)
            mock_logger.info.assert_called_once_with(
                "[Socket] Emitted 'sim_details_update' event"
            )
            mock_logger.debug.assert_called_once_with(
                f"[Socket] Payload: {expected_data}"
            )

    @pytest.mark.asyncio
    async def test_sim_details_update_exception(self, mock_sio):
        with patch("socket_events.sim.events.logger") as mock_logger:
            mock_sio.emit.side_effect = Exception("Emit failed")
            events = SimSocketEvents(mock_sio)
            imsi_list = ["123456789012345"]
            status = "ACTIVE"

            await events.sim_details_update(imsi_list, status)

            expected_data = {"imsi": imsi_list, "simStatus": status}
            mock_logger.error.assert_called_once_with(
                "[Socket] Failed to emit 'sim_details_update' event: Emit failed"
            )
            mock_logger.debug.assert_called_once_with(
                f"[Socket] Event data: {expected_data}"
            )

    @pytest.mark.asyncio
    async def test_sim_details_update_empty_imsi_list(self, mock_sio):
        with patch("socket_events.sim.events.logger") as mock_logger:
            events = SimSocketEvents(mock_sio)
            imsi_list = []
            status = "DEACTIVATED"

            await events.sim_details_update(imsi_list, status)

            expected_data = {"imsi": imsi_list, "simStatus": status}
            mock_sio.emit.assert_called_once_with("sim_details_update", expected_data)
            mock_logger.info.assert_called_once_with(
                "[Socket] Emitted 'sim_details_update' event"
            )

    @pytest.mark.asyncio
    async def test_sim_details_update_single_imsi(self, mock_sio):
        with patch("socket_events.sim.events.logger") as mock_logger:
            events = SimSocketEvents(mock_sio)
            imsi_list = ["123456789012345"]
            status = "SUSPENDED"

            await events.sim_details_update(imsi_list, status)

            expected_data = {"imsi": imsi_list, "simStatus": status}
            mock_sio.emit.assert_called_once_with("sim_details_update", expected_data)
            mock_logger.info.assert_called_once_with(
                "[Socket] Emitted 'sim_details_update' event"
            )
            mock_logger.debug.assert_called_once_with(
                f"[Socket] Payload: {expected_data}"
            )


class TestGetSimSocketEvents:
    def test_get_sim_socket_events(self, mock_sio):
        with patch("socket_events.sim.events.SimSocketEvents") as mock_sim_events_class:
            mock_instance = MagicMock()
            mock_sim_events_class.return_value = mock_instance

            result = get_sim_socket_events(mock_sio)

            mock_sim_events_class.assert_called_once_with(mock_sio)
            assert result == mock_instance

    def test_get_sim_socket_events_returns_correct_type(self, mock_sio):
        result = get_sim_socket_events(mock_sio)

        assert isinstance(result, SimSocketEvents)
        assert result.sio == mock_sio
