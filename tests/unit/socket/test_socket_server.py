from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from app.socket_server import SocketServer, get_redis_manager


class TestGetRedisManager:
    def test_get_redis_manager_success(self):
        with patch("app.socket_server.AsyncRedisManager") as mock_manager:
            mock_instance = MagicMock()
            mock_manager.return_value = mock_instance

            result = get_redis_manager("localhost", 6379)

            mock_manager.assert_called_once_with("redis://localhost:6379")
            assert result == mock_instance

    def test_get_redis_manager_exception(self):
        with patch("app.socket_server.AsyncRedisManager") as mock_manager:
            mock_manager.side_effect = Exception("Connection failed")

            with patch("app.socket_server.logger") as mock_logger:
                result = get_redis_manager("localhost", 6379)

                assert result is None
                mock_logger.error.assert_called_once()


class TestSocketServer:
    def test_init_with_redis_manager(self, mock_redis_manager, mock_settings):
        with patch(
            "app.socket_server.get_redis_manager", return_value=mock_redis_manager
        ):
            with patch("app.socket_server.socketio.AsyncServer") as mock_async_server:
                with patch(
                    "app.socket_server.get_sim_socket_events"
                ) as mock_get_sim_events:
                    mock_sio = MagicMock()
                    mock_async_server.return_value = mock_sio
                    mock_sim_events = MagicMock()
                    mock_get_sim_events.return_value = mock_sim_events

                    server = SocketServer(
                        cors_allowed_origins=["http://localhost:3000"],
                        async_mode="asgi",
                        mgr=mock_redis_manager,
                        ping_timout=10,
                        ping_interval=30,
                    )

                    mock_async_server.assert_called_once_with(
                        cors_allowed_origins=["http://localhost:3000"],
                        async_mode="asgi",
                        client_manager=mock_redis_manager,
                        ping_timeout=10,
                        ping_interval=30,
                    )
                    mock_sio.on.assert_any_call("connect", server.handle_connect)
                    mock_sio.on.assert_any_call("disconnect", server.handle_disconnect)
                    mock_get_sim_events.assert_called_once_with(mock_sio)
                    assert server.sim_events == mock_sim_events

    @pytest.mark.asyncio
    async def test_handle_connect(self, mock_settings):
        with patch("app.socket_server.socketio.AsyncServer") as mock_async_server:
            with patch("app.socket_server.get_sim_socket_events"):
                with patch("app.socket_server.settings", mock_settings):
                    with patch("app.socket_server.logger") as mock_logger:
                        mock_sio = AsyncMock()
                        mock_async_server.return_value = mock_sio

                        server = SocketServer()

                        await server.handle_connect("test_sid", {}, None)

                        mock_logger.info.assert_any_call(
                            "[Socket] Client connected: sid=test_sid"
                        )
                        mock_logger.info.assert_any_call(
                            f"[Socket] connected with url - "
                            f"{mock_settings.APP_BASE_URL}"
                        )
                        mock_sio.emit.assert_called_once_with(
                            "server_message", {"msg": "Connected!"}, to="test_sid"
                        )

    @pytest.mark.asyncio
    async def test_handle_disconnect(self):
        with patch("app.socket_server.socketio.AsyncServer") as mock_async_server:
            with patch("app.socket_server.get_sim_socket_events"):
                with patch("app.socket_server.logger") as mock_logger:
                    mock_sio = AsyncMock()
                    mock_async_server.return_value = mock_sio

                    server = SocketServer()

                    await server.handle_disconnect("test_sid")

                    mock_logger.info.assert_called_once_with(
                        "[Socket] Client disconnected: sid=test_sid"
                    )

    def test_register_core_event_handlers(self):
        with patch("app.socket_server.socketio.AsyncServer") as mock_async_server:
            with patch("app.socket_server.get_sim_socket_events"):
                mock_sio = MagicMock()
                mock_async_server.return_value = mock_sio

                server = SocketServer()

                mock_sio.on.assert_any_call("connect", server.handle_connect)
                mock_sio.on.assert_any_call("disconnect", server.handle_disconnect)

    def test_register_domain_event_handlers(self):
        with patch("app.socket_server.socketio.AsyncServer") as mock_async_server:
            with patch(
                "app.socket_server.get_sim_socket_events"
            ) as mock_get_sim_events:
                mock_sio = MagicMock()
                mock_async_server.return_value = mock_sio
                mock_sim_events = MagicMock()
                mock_get_sim_events.return_value = mock_sim_events

                server = SocketServer()

                mock_get_sim_events.assert_called_once_with(mock_sio)
                assert server.sim_events == mock_sim_events
