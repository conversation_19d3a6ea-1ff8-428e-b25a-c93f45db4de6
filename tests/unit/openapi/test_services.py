from unittest.mock import MagicMock, patch
from uuid import uuid4

import pytest

from accounts.domain.ports import AbstractOrganizationRepository
from auth.exceptions import ForbiddenError
from authorization.domain.model import Permission, UserScope, UserScopeList
from authorization.domain.ports import AbstractAuthorizationAPI
from openapi.domain import model
from openapi.services import OpenApiAuthService


class TestOpenAPIAuth:
    @patch("openapi.services.OpenApiAuthService.validate_api_access", return_value=True)
    def test_generate_access_token(self, validate_api_access_mock):
        mock_authorization_service = MagicMock(spec=AbstractAuthorizationAPI)
        mock_organization_repository = MagicMock(spec=AbstractOrganizationRepository)

        mock_organization_repository.get_access_token.return_value = (
            model.TokenResponse(
                access_token="eyJhbGciOiJSUzI1NiIsInR5cC", expires_in=3600
            )
        )

        openapi_service_mock = OpenApiAuthService(
            authorization=mock_authorization_service,
            organization_repository=mock_organization_repository,
        )

        result = openapi_service_mock.generate_access_token()
        assert isinstance(result, model.TokenResponse)

    @patch(
        "openapi.services.OpenApiAuthService.validate_api_access", return_value=False
    )
    def test_generate_access_token_forbidden(self, validate_api_access_mock):
        mock_authorization_service = MagicMock(spec=AbstractAuthorizationAPI)
        mock_organization_repository = MagicMock(spec=AbstractOrganizationRepository)

        openapi_service_mock = OpenApiAuthService(
            authorization=mock_authorization_service,
            organization_repository=mock_organization_repository,
        )
        with pytest.raises(ForbiddenError):
            openapi_service_mock.generate_access_token()

    def test_validate_api_access(self):
        mock_authorization_service = MagicMock(spec=AbstractAuthorizationAPI)
        mock_organization_repository = MagicMock(spec=AbstractOrganizationRepository)

        mock_authorization_service.get_user_scope.return_value = UserScopeList(
            result=[
                UserScope(
                    name="Sim Details",
                    permission=[
                        Permission(
                            id=uuid4(),
                            name="Get SIM Status API",
                            title="Get SIM Status",
                        )
                    ],
                )
            ]
        )

        openapi_service_mock = OpenApiAuthService(
            authorization=mock_authorization_service,
            organization_repository=mock_organization_repository,
        )
        result = openapi_service_mock.validate_api_access()
        assert result is True

    def test_validate_api_access_no_permission(self):
        mock_authorization_service = MagicMock(spec=AbstractAuthorizationAPI)
        mock_organization_repository = MagicMock(spec=AbstractOrganizationRepository)

        mock_authorization_service.get_user_scope.return_value = UserScopeList(
            result=[]
        )

        openapi_service_mock = OpenApiAuthService(
            authorization=mock_authorization_service,
            organization_repository=mock_organization_repository,
        )
        result = openapi_service_mock.validate_api_access()
        assert result is False
