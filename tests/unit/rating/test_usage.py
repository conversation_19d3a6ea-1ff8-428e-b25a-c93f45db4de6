from unittest.mock import MagicMock

import pytest

from common.pagination import Pagination
from rating.adapters.usage_repository import (
    AbstractUsageRepository,
    InMemoryUsageRepository,
)
from rating.exceptions import NoData
from rating.usage import UsageService


@pytest.fixture
def usage_repository() -> AbstractUsageRepository:
    return InMemoryUsageRepository()


@pytest.fixture
def usage_service(usage_repository):
    return UsageService(usage_repository=usage_repository)


class TestServiceUsage:
    def setup_method(self):
        self.usage = UsageService(AbstractUsageRepository)

    def test_get_monthly_usage_success(
        self, usage_service, make_usage_model, make_operator_model
    ):
        pagination = Pagination(page_size=50, page=1)
        usage_service.usage_repository.get_monthly_usage = MagicMock(
            return_value=make_usage_model(0, 1)
        )
        usage = usage_service.get_monthly_usage("2020-01", pagination)
        result = make_operator_model(0, 1)
        assert len(usage) == 2
        assert usage[0][0].IMSI == result[0].IMSI
        assert usage[0][0].Carrier == result[0].Carrier
        assert usage[0][0].SMSMO == result[0].SMSMO
        assert usage[0][0].SMSMT == result[0].SMSMT
        assert usage[0][0].VoiceMOSeconds == result[0].VoiceMOSeconds
        assert usage[0][0].VoiceMTSeconds == result[0].VoiceMTSeconds

    def test_get_monthly_usage_with_pagination(
        self, usage_service, make_usage_model, make_operator_model
    ):
        pagination = Pagination(page_size=1, page=1)
        usage_service.usage_repository.get_monthly_usage = MagicMock(
            return_value=make_usage_model(0, 5)
        )
        usage = usage_service.get_monthly_usage("2020-01", pagination)
        result = make_operator_model(0, 1)
        assert len(usage) == 2
        assert usage[0][0].IMSI == result[0].IMSI
        assert usage[0][0].Carrier == result[0].Carrier
        assert usage[0][0].SMSMO == result[0].SMSMO
        assert usage[0][0].SMSMT == result[0].SMSMT
        assert usage[0][0].VoiceMOSeconds == result[0].VoiceMOSeconds
        assert usage[0][0].VoiceMTSeconds == result[0].VoiceMTSeconds

    def test_get_monthly_usage_no_data(self, usage_service):
        pagination = Pagination(page_size=1, page=1)
        usage_service.usage_repository.get_monthly_usage = MagicMock(return_value=[])
        with pytest.raises(NoData):
            usage_service.get_monthly_usage("2020-01", pagination)
