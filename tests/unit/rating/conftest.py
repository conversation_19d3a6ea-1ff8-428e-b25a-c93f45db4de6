import pytest

from common.types import Service
from rating.domain import model


@pytest.fixture
def make_usage_model():
    def factory(start_with, end_with):
        return [
            model.UsageModel(
                imsi=f"758496758392{str(i).zfill(3)}",
                service=Service.DATA.name,
                operator="test",
                volume=100 + i,
                month="2020-01",
            )
            for i in range(start_with, end_with)
        ]

    return factory


@pytest.fixture
def make_operator_model():
    def factory(start_with, end_with):
        return [
            model.OperatorUsage(
                IMSI=f"758496758392{str(i).zfill(3)}",
                Carrier="test",
                Month="2020-01",
                SMS_MO=None,
                SMS_MT=None,
                DATA=100 + i,
                VOICE_MO=None,
                VOICE_MT=None,
                volume=100 + i,
            )
            for i in range(start_with, end_with)
        ]

    return factory
