import csv
import random
from datetime import date
from decimal import Decimal
from typing import Callable, Iterable

import pytest

from common.constants import GB, MB
from common.minions import rangeofuse
from common.types import IMSI, Month, Service
from rating.adapters.usage_repository import (  # InMemoryUsageRepository,
    AbstractAppRepository,
    InMemoryAppRepository,
)
from rating.calculation import AppService, RatingCalculator  # , RatingService

# from rating.domain.command import CalculateMonthlyRates
from rating.domain.dto import MonthlyUsageAggregate
from rating.domain.model import (
    AppVersion,
    MonthlyUsageRecords,
    Rate,
    RateGroup,
    RatePlan,
)
from tests.constants import TESTS_DIR


@pytest.fixture(scope="session")
def data_group():
    return RateGroup(
        services={Service.DATA},
        rates=[
            Rate(0, 1, Decimal("143.75")),
            Rate(1, 512, Decimal("4.3125")),
            Rate(512, 10240, Decimal("3.025")),
            Rate(10240, 51200, Decimal("1.4375")),
            Rate(51200, None, Decimal("1.15")),
        ],
    )


@pytest.fixture(scope="session")
def sms_group():
    return RateGroup(
        services={Service.SMS_MO, Service.SMS_MT},
        rates=[
            Rate(0, 100, Decimal("0.24")),
            Rate(100, None, Decimal("0.00003")),
        ],
    )


@pytest.fixture(scope="session")
def voice_mo_group():
    return RateGroup(
        services={Service.VOICE_MO},
        rates=[
            Rate(0, 50, Decimal("0.75")),
            Rate(50, 100, Decimal("0.59")),
            Rate(100, None, Decimal(".025")),
        ],
    )


@pytest.fixture(scope="session")
def voice_mt_group():
    return RateGroup(
        services={Service.VOICE_MT},
        rates=[Rate(0, None, Decimal("0.00001"))],
    )


def volume_charge_pairs():
    path = TESTS_DIR / "data" / "payg.tiers-rating.csv"
    with path.open("r") as f:
        reader = csv.DictReader(f)
        return [(int(row["volume"]) * MB, Decimal(row["charge"])) for row in reader]


class TestRatingCalculator:
    @pytest.fixture
    def make_usage_aggregate(self):
        def _factory(
            imsi=to_imsi(1),
            service=Service.DATA,
            volume: int | None = 1,
            month=date(2022, 1, 1),
        ) -> MonthlyUsageAggregate:
            return MonthlyUsageAggregate(
                imsi=imsi,
                service=service,
                volume=volume,
                month=month,
            )

        return _factory

    @pytest.mark.parametrize("volume,charge", volume_charge_pairs())
    def test_volume_charge(self, data_group, make_usage_aggregate, volume, charge):
        rates = rangeofuse.adjust_units(data_group.rates, MB)
        calculator = RatingCalculator(rates, GB, GB, rounding_precision=2)
        assert calculator.apply_rates(volume, 1, Service.DATA) == charge.quantize(
            calculator.exp
        )

    def test_charge_of_none_volume_is_zero(self, make_usage_aggregate):
        rate = Rate(0, None, Decimal("22"))
        calculator = RatingCalculator([rate], GB, GB)
        assert calculator.apply_rates(None, 1, Service.DATA) == Decimal("0")


class TestRatingService:
    # @pytest.fixture
    # def make_rating_service(self):
    #     def _factory(usage_records: list[MonthlyUsageRecords] = None):
    #         return RatingService(
    #             usage_repo=InMemoryUsageRepository(usage_records),
    #         )

    #     return _factory

    @pytest.fixture
    def make_rate_plan(
        self,
        data_group,
        sms_group,
        voice_mo_group,
        voice_mt_group,
    ):
        def _factory(
            access_fee=Decimal("0.23"),
            rate_groups=(
                data_group,
                sms_group,
                voice_mo_group,
                voice_mt_group,
            ),
        ):
            return RatePlan(
                access_fee=access_fee,
                rate_groups=rate_groups,
            )

        return _factory

    @pytest.fixture
    def make_usage_records(self):
        def _factory(
            imsi_list: Iterable[int] = range(3),
            services: Iterable[Service] = tuple(Service),
            months: Iterable[Month] = (
                Month(2022, 11, 1),
                Month(2022, 12, 1),
                Month(2023, 1, 1),
            ),
            volume_function: Callable[
                [IMSI, Service, date], int
            ] = lambda i, s, d: random.choice(range(100)),
        ):
            return [
                MonthlyUsageRecords(
                    imsi=to_imsi(i),
                    service=service,
                    volume=volume_function(to_imsi(i), service, month),
                    month=month,
                    operator="DUMMY",
                )
                for service in services
                for i in imsi_list
                for month in months
            ]

        return _factory


def to_imsi(i: int) -> IMSI:
    return IMSI(f"{i:0>15}")


class AppContract:
    @pytest.fixture
    def app_repository(self, *args, **kwargs) -> AbstractAppRepository:
        raise NotImplementedError()

    @pytest.fixture
    def app_service(
        self,
        app_repository: AbstractAppRepository,
    ) -> AppService:
        return AppService(
            app_repository=app_repository,
        )

    @pytest.fixture
    def make_app_version(self):
        def factory(start_with=0, end_with=3):
            return [
                AppVersion(name="SPOG UI", version=f"1.0.{i}")
                for i in range(start_with, end_with)
            ]

        return factory

    def test_create(self, app_service: AppService, make_app_version):
        app_versions = make_app_version()
        response = app_service.app_repository.create(app_versions)
        assert response is True

    def test_get(self, app_service: AppService, make_app_version):
        app_versions = make_app_version()
        for version in app_versions:
            app_service.app_repository.create(version)
            response = app_service.app_repository.get()
        if response:
            assert response.id == 1
            assert response.name == "SPOG UI"
            assert response.version == "1.0.2"

    def test_get_no_data(self, app_service: AppService):
        response = app_service.app_repository.get()
        assert response is None


class TestAppService(AppContract):
    @pytest.fixture
    def app_repository(self) -> InMemoryAppRepository:
        return InMemoryAppRepository()
