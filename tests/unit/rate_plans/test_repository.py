import random
import uuid
from collections import namedtuple
from decimal import Decimal
from unittest.mock import MagicMock

from sqlalchemy import literal_column, select
from sqlalchemy.sql import func

from common.constants import FIXED, FLEXI
from common.utils import set_trace_id
from rate_plans.adapters.rate_plan_repository import (
    AbstractRatePlanRepository,
    DatabaseRatePlanRepository,
    InMemoryRatePlanRepository,
)
from rate_plans.domain.model import RatePlan
from rate_plans.exceptions import RatePlanError

import pytest  # isort: skip


set_trace_id(uuid.uuid4())


class RatePlanContract:
    @pytest.fixture
    def accounts_id(self):
        return list(range(1, 10))

    @pytest.fixture
    def rate_plans(self, accounts_id):
        return [
            RatePlan(
                account_id=account_id,
                name=f"Test Rate Plan {account_id}/{i}",
                access_fee=Decimal(random.randint(1, 100) / 100).quantize(
                    Decimal("0.00")
                ),
                is_default=False,
                sim_limit=0,
            )
            for i in range(10)
            for account_id in accounts_id
        ]

    @pytest.fixture
    def rate_plan_repository(self, *args, **kwargs) -> AbstractRatePlanRepository:
        raise NotImplementedError()

    def test_query_rate_plans_by_account_ids(
        self,
        rate_plan_repository: AbstractRatePlanRepository,
        rate_plans: list[RatePlan],
        accounts_id: list[int],
    ):
        for rate_plan in rate_plans:
            rate_plan_repository.add(rate_plan)

        # Mock response for `func.get_rate_plans_detail`
        mock_get_rate_plans_detail = MagicMock()
        func.get_rate_plans_detail = mock_get_rate_plans_detail  # type: ignore

        # Mock the SQLAlchemy subquery response
        mock_subquery = select(
            [
                literal_column("1").label("account_id"),
                literal_column("'test_account'").label("account_name"),
                literal_column(
                    "'/home/<USER>/tmp/monoglass/src/images/bt-logo.svg'"
                ).label("logo_key"),
                literal_column("1").label("id"),
                literal_column("'Pay as You Go plan'").label("name"),
                literal_column("0.03").label("access_fee"),
                literal_column("'STR'").label("currency"),
                literal_column("1").label("rate_model_id"),
                literal_column("NULL").label("rate_plan_model"),
                literal_column("NULL").label("sim_limit"),
                literal_column("0").label("range_from"),
                literal_column("100").label("range_to"),
                literal_column("0.73").label("value"),
                literal_column("'Min'").label("range_unit"),
                literal_column("'Min'").label("price_unit"),
                literal_column("NULL").label("overage_fee"),
                literal_column("NULL").label("overage_unit"),
                literal_column("false").label("isoverage"),
                literal_column("NULL").label("overage_per"),
                literal_column("'VOICE_MO'").label("service"),
                literal_column("'True'").label("is_default"),
                literal_column("0").label("allowance_used"),
            ]
        )
        mock_get_rate_plans_detail.return_value = mock_subquery

        # Mock session behavior
        mock_session = MagicMock()
        mock_execute_result = MagicMock()
        mock_execute_result.all.return_value = [
            {
                "account_id": 1,
                "account_name": "test_account",
                "logo_key": "/home/<USER>/tmp/monoglass/src/images/bt-logo.svg",
                "id": 1,
                "name": "Pay as You Go plan",
                "access_fee": Decimal("0.03"),
                "currency": "STR",
                "rate_model_id": 1,
                "rate_plan_model": None,
                "sim_limit": None,
                "range_from": 0,
                "range_to": Decimal("100"),
                "value": Decimal("0.73"),
                "range_unit": "Min",
                "price_unit": "Min",
                "overage_fee": None,
                "overage_unit": None,
                "isoverage": False,
                "overage_per": None,
                "service": "VOICE_MO",
                "is_default": "True",
                "allowance_used": Decimal("0"),
            }
        ]
        mock_session.execute.return_value = mock_execute_result

        # Call the method
        list(rate_plan_repository.query(accounts_id))

    def test_getting_rate_plans_count_for_accounts(
        self,
        rate_plan_repository: AbstractRatePlanRepository,
        rate_plans: list[RatePlan],
        accounts_id: list[int],
    ):
        for rate_plan in rate_plans:
            rate_plan_repository.add(rate_plan)

        account_id = accounts_id[0]
        rate_plans_count = rate_plan_repository.count_for_account(account_id)
        account_rate_plans = tuple(
            filter(lambda rate_plan: rate_plan.account_id == account_id, rate_plans)
        )
        assert len(account_rate_plans) == rate_plans_count


class TestRatePlanRepository(RatePlanContract):
    @pytest.fixture
    def rate_plan_repository(self) -> InMemoryRatePlanRepository:
        return InMemoryRatePlanRepository(
            rate_plans=[],
        )

    @pytest.fixture
    def mock_session(self):
        return MagicMock()

    @pytest.fixture
    def rate_plan_service(self, mock_session):
        return DatabaseRatePlanRepository(session=mock_session)

    def test_validate_fixed_flexi_plan_success(self, rate_plan_service, mock_session):
        """Test when all rate plans have FIXED or FLEXI rate models"""
        rate_plan_id = [1, 2, 3]
        service = ["DATA"]
        MockRow = namedtuple(
            "MockRow",
            ["rate_plan_id", "rate_model_id", "model", "rate_model_code", "service"],
        )
        mock_session.execute.return_value.all.return_value = [
            MockRow(
                rate_plan_id=1,
                rate_model_id=3,
                model="Fixed Plan",
                rate_model_code=FIXED,
                service="DATA",
            ),
            MockRow(
                rate_plan_id=2,
                rate_model_id=4,
                model="Flexible Plan",
                rate_model_code=FLEXI,
                service="DATA",
            ),
            MockRow(
                rate_plan_id=3,
                rate_model_id=3,
                model="Fixed Plan",
                rate_model_code=FIXED,
                service="DATA",
            ),
        ]

        response = rate_plan_service.validate_fixed_flexi_plan(rate_plan_id, service)
        assert response is True

    def test_validate_fixed_flexi_plan_some_invalid(
        self, rate_plan_service, mock_session
    ):
        """Test when some rate plans are not FIXED or FLEXI"""
        rate_plan_id = [1, 2, 3]
        service = ["DATA"]
        MockRow = namedtuple(
            "MockRow",
            ["rate_plan_id", "rate_model_id", "model", "rate_model_code", "service"],
        )
        mock_session.execute.return_value.all.return_value = [
            MockRow(
                rate_plan_id=1,
                rate_model_id=3,
                model="Fixed Plan",
                rate_model_code=FIXED,
                service="DATA",
            ),
        ]  # Only one valid rate plan

        with pytest.raises(
            RatePlanError, match="Requested rate plans are not FIXED or FLEXI"
        ):
            rate_plan_service.validate_fixed_flexi_plan(rate_plan_id, service)

    def test_validate_fixed_flexi_plan_none_exist(
        self, rate_plan_service, mock_session
    ):
        """Test when no rate plans exist"""
        rate_plan_id = [1, 2, 3]
        service = ["DATA"]
        mock_session.execute.return_value.all.return_value = []

        with pytest.raises(
            RatePlanError, match="Requested rate plans are not FIXED or FLEXI"
        ):
            rate_plan_service.validate_fixed_flexi_plan(rate_plan_id, service)

    def test_validate_fixed_flexi_plan_no_data_service(
        self, rate_plan_service, mock_session
    ):
        """Test when rate plans exist but do not have DATA service"""
        rate_plan_id = [1, 2, 3]
        service = ["DATA"]
        MockRow = namedtuple(
            "MockRow",
            ["rate_plan_id", "rate_model_id", "model", "rate_model_code", "service"],
        )
        mock_session.execute.return_value.all.return_value = [
            MockRow(
                rate_plan_id=1,
                rate_model_id=3,
                model="Fixed Plan",
                rate_model_code=FIXED,
                service="VOICE",
            ),
            MockRow(
                rate_plan_id=2,
                rate_model_id=3,
                model="Fixed Plan",
                rate_model_code=FLEXI,
                service="SMS",
            ),
        ]

        with pytest.raises(
            RatePlanError, match="Requested rate plans are not FIXED or FLEXI"
        ):
            rate_plan_service.validate_fixed_flexi_plan(rate_plan_id, service)
