import uuid

import pytest
from pydantic import UUID4, ValidationError, parse_obj_as

from auth.dto import Account, Anonymous, AuthenticatedUser, Client, Distributor, User

USER_ID = uuid.uuid4()


@pytest.mark.parametrize(
    "payload,user",
    [
        ({"active": False}, Anonymous()),
        (
            {
                "active": True,
                "sub": str(USER_ID),
                "organization": {"id": 1, "type": "DISTRIBUTOR"},
                "realm_access": {"roles": ["DistributorUser"]},
                "email": "<EMAIL>",
            },
            AuthenticatedUser(
                id=USER_ID,
                organization=Distributor(id=1),
                role="User",
                email="<EMAIL>",
            ),
        ),
        (
            {
                "active": True,
                "sub": str(USER_ID),
                "organization": {"id": 2, "type": "CLIENT"},
                "realm_access": {"roles": ["ClientAdmin"]},
                "email": "<EMAIL>",
            },
            AuthenticatedUser(
                id=USER_ID,
                organization=Client(id=2),
                role="Admin",
                email="<EMAIL>",
            ),
        ),
    ],
)
def test_user_from_token_payload(payload, user):
    assert parse_obj_as(User, payload) == user


@pytest.mark.parametrize(
    "realm_access, role, email",
    [
        ({"roles": ["DistributorUser"]}, "User", "<EMAIL>"),
        (
            {"roles": ["offline_access", "uma_authorization", "DistributorAdmin"]},
            "Admin",
            "<EMAIL>",
        ),
        (
            {"roles": ["DistributorUser", "DistributorAdmin"]},
            "Admin",
            "<EMAIL>",
        ),
        (
            {"roles": ["DistributorUser", "ClientAdmin", "ClientUser"]},
            "Admin",
            "<EMAIL>",
        ),
        ({"roles": ["ClientUser", "some-fake-access"]}, "User", "<EMAIL>"),
    ],
)
def test_user_from_token_payload_with_correct_roles(realm_access, role, email):
    payload = {
        "active": True,
        "sub": str(USER_ID),
        "organization": {"id": 2, "type": "CLIENT"},
        "realm_access": realm_access,
        "email": email,
    }

    user = parse_obj_as(User, payload)
    assert user.role == role


@pytest.mark.parametrize(
    "realm_access",
    [
        ({"roles": ["Distribut1rUseR"]}),
        ({"roles": ["offline_access", "uma_authorization"]}),
    ],
)
def test_user_from_token_payload_with_wrong_roles(realm_access):
    payload = {
        "active": True,
        "sub": str(USER_ID),
        "organization": {"id": 2, "type": "CLIENT"},
        "realm_access": realm_access,
    }
    with pytest.raises(ValidationError):
        parse_obj_as(User, payload)


def users() -> list[tuple[User, UUID4 | None]]:
    anon = Anonymous()
    distributor_user = AuthenticatedUser(
        id=uuid.uuid4(),
        organization=Distributor(id=1),
        role="Admin",
        email="<EMAIL>",
    )
    client_user = AuthenticatedUser(
        id=uuid.uuid4(),
        organization=Client(id=2),
        role="User",
        email="<EMAIL>",
    )
    client_user_with_account = AuthenticatedUser(
        id=uuid.uuid4(),
        organization=Client(id=3, account=Account(id=5)),
        role="Admin",
        email="<EMAIL>",
    )
    return [
        (anon, None),
        (distributor_user, distributor_user.id),
        (client_user, client_user.id),
        (client_user_with_account, client_user_with_account.id),
    ]


@pytest.mark.parametrize("user, user_id", users())
def test_pattern_matching_support(user, user_id):
    match user:
        case Anonymous():
            actual_user_id = None
        case AuthenticatedUser(id=uid, organization=Distributor()):
            actual_user_id = uid
        case AuthenticatedUser(id=uid, organization=Client(account=None)):
            actual_user_id = uid
        case AuthenticatedUser(id=uid, organization=Client(account=Account())):
            actual_user_id = uid
        case _:
            assert False, "unexpected user"
    assert actual_user_id == user_id, f"doesn't match: {user}"
