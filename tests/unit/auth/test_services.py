import uuid

import pytest

from auth.dto import Account, Anonymous, AuthenticatedUser, Client, Distributor, Service
from auth.services import (
    AccountAuthService,
    FakeAuthService,
    TokenIntrospectionAuthService,
)


@pytest.mark.parametrize("account", [None, Account(id=42)])
def test_account_auth_service_populates_account_for_client_user(account):
    user = AuthenticatedUser(
        id=uuid.uuid4(),
        organization=Client(id=3),
        role="User",
        email="<EMAIL>",
    )
    _auth_service = FakeAuthService(user=user)
    account_auth_service = AccountAuthService(_auth_service, lambda _: account)
    assert account_auth_service.authenticated_user == user.copy(
        update=dict(
            organization=user.organization.copy(
                update=dict(account=account),
            ),
        )
    )


def test_account_auth_service_ignores_non_client_users():
    user = AuthenticatedUser(
        id=uuid.uuid4(),
        organization=Distributor(id=3),
        role="User",
        email="<EMAIL>",
    )
    _auth_service = FakeAuthService(user=user)
    account_auth_service = AccountAuthService(_auth_service, lambda _: None)
    assert not hasattr(account_auth_service.authenticated_user.organization, "account")


@pytest.mark.parametrize(
    "payload, result",
    [
        (
            {
                "typ": "Bearer",
                "clientHost": "**************",
                "clientId": "organization-management",
                "client_id": "organization-management-ui",
                "username": "service-account-organization-management",
                "active": True,
            },
            Service(client_id="organization-management"),
        ),
        (
            {
                "typ": "ID",
            },
            Anonymous(),
        ),
    ],
)
def test_parse_token_payload(payload, result):
    assert TokenIntrospectionAuthService.parse_payload(payload) == result
