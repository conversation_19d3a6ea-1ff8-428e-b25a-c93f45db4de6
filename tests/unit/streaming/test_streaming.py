import json
import unittest
from unittest.mock import MagicMock, patch
from uuid import uuid4

from requests import HTTPError

from app.config import settings
from common.types import MSISDN
from sim.domain import model
from streaming.streaming import HTTPKafkaAPI


class TestHTTPKafkaAPI(unittest.TestCase):
    def setUp(self):
        self.kafka_api = HTTPKafkaAPI()

    def test_construct_kafka_message(self):
        key = "test_key"
        msisdn = "1234567890"
        audit_details = [
            model.MsisdnPoolAudit(
                uuid=uuid4(),
                msisdn=MSISDN(msisdn),
                request_type="Upload MSISDN",
                prior_value="Warehouse",
                new_value=MSISDN(msisdn),
                field="MSISDN",
                action="Uploaded",
                client_ip="127.0.0.1",
                created_by="<EMAIL>",
            )
        ]

        result = self.kafka_api.construct_kafka_message(key, audit_details)
        message = {
            "records": [
                {
                    "key": key,
                    "value": {
                        "audits": [
                            {
                                "uuid": str(audit_details[0].uuid),
                                "msisdn": msisdn,
                                "request_type": "Upload MSISDN",
                                "prior_value": "Warehouse",
                                "new_value": msisdn,
                                "field": "MSISDN",
                                "action": "Uploaded",
                                "client_ip": "127.0.0.1",
                                "created_by": "<EMAIL>",
                            }
                        ]
                    },
                }
            ]
        }
        assert result == message

    @patch("streaming.streaming.get_kafka_service_access_token")
    def test_get_access_kafka_token(self, mock_token):
        mock_token.return_value = "test_token"
        max_retries = 3
        retry_delay = 1
        settings.KAFKA_MAX_RETRIES = max_retries
        settings.KAFKA_RETRY_DELAY = retry_delay
        token = self.kafka_api.get_access_kafka_token()
        assert token == "Bearer test_token"

    @patch("streaming.streaming.get_kafka_service_access_token")
    def test_get_access_kafka_token_with_exception(self, mock_token):
        mock_token.side_effect = Exception("Test exception")
        with self.assertRaises(Exception):
            self.kafka_api.get_access_kafka_token()

    @patch("requests.post")
    def test_send_to_kafka_success(self, mock_post):
        topic = "upload-topic"
        key = "test_key"
        settings.KAFKA_BASE_URL = "http://mock-kafka-url"

        mock_get_token = "Bearer mocked_token"

        message = {
            "records": [{"key": key, "value": {"audits": [{"test": "success"}]}}]
        }

        result = self.kafka_api.send_to_kafka(
            topic=topic, message=message, token=mock_get_token
        )

        self.assertTrue(result)
        mock_post.assert_called_once_with(
            url="http://mock-kafka-url/topics/upload-topic",
            headers={
                "Content-Type": "application/vnd.kafka.json.v2+json",
                "Authorization": "Bearer mocked_token",
            },
            data=json.dumps(message),
            timeout=10,
        )

    @patch("requests.post")
    def test_send_to_kafka_failure(self, mock_post):
        topic = "upload-topic"
        key = "test_key"
        settings.KAFKA_BASE_URL = "http://mock-kafka-url"

        mock_get_token = "Bearer mocked_token"
        mock_post.return_value.status_code = 400

        message = {
            "records": [{"key": key, "value": {"audits": [{"test": "success"}]}}]
        }

        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.text = "Bad Request"
        mock_response.raise_for_status.side_effect = HTTPError(
            "400 Client Error: Bad Request"
        )
        mock_post.return_value = mock_response

        with self.assertRaises(HTTPError):
            self.kafka_api.send_to_kafka(
                topic=topic, message=message, token=mock_get_token
            )

    @patch("requests.post")
    @patch("streaming.streaming.HTTPKafkaAPI.get_access_kafka_token")
    def test_submit_to_kafka(self, mock_token, mock_post):
        key = "test_key"
        topic = "upload-topic"
        audit_details = [
            model.MsisdnPoolAudit(
                uuid=uuid4(),
                msisdn=MSISDN("1234567890"),
                request_type="Upload MSISDN",
                prior_value="Warehouse",
                new_value="1234567890",
                field="MSISDN",
                action="Uploaded",
                client_ip="127.0.0.1",
                created_by="<EMAIL>",
            )
        ]
        mock_token.return_value = "test_token"
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"status": "success"}
        mock_post.return_value = mock_response
        result = self.kafka_api.submit_to_kafka(
            key=key, topic=topic, audit_details=audit_details
        )
        assert result is True
