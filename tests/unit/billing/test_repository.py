from datetime import date, datetime
from decimal import Decimal
from random import choice, randint
from typing import Iterable

import pytest

from accounts.domain.model import PaymentTerms
from billing.adapters.repository import InMemoryBillingCycleRepository
from billing.domain import model
from billing.domain.model import AdjustmentType
from common.types import IMSI, Month, Service


class TestBillingRepository:
    @pytest.fixture
    def repository(self):
        return InMemoryBillingCycleRepository()

    @pytest.fixture
    def make_billing_cycle(self):
        def _factory(
            month_string: str = "2023-01", account_ids: Iterable[int] = range(10)
        ) -> model.BillingCycle:
            billing_cycle = model.BillingCycle(Month.from_str(month_string))
            for i in account_ids:
                invoice = billing_cycle.make_invoice(i, PaymentTerms(30))
                invoice.rating_state = model.InvoiceRatingState.GENERATED

            return billing_cycle

        return _factory

    def test_getting_billing_cycle(self, repository, make_billing_cycle):
        month_string = "2023-02"
        account_ids = list(range(10, 20))
        bc = make_billing_cycle(month_string, account_ids)
        repository.add(bc)

        bc = repository.get(Month.from_str(month_string))
        assert bc is not None
        assert list(bc._invoices.keys()) == account_ids

    def test_getting_invoices_by_billing_cycle(self, repository, make_billing_cycle):
        month_string = "2023-02"
        account_ids = list(range(10, 20))
        billing_cycle = make_billing_cycle(month_string, account_ids)
        repository.add(billing_cycle)

        # generate new billing cycle with invoices
        new_billing_cycle = model.BillingCycle(Month.from_str("2023-03"))
        for i in account_ids:
            invoice = new_billing_cycle.make_invoice(i, 30)
            invoice.rating_state = model.InvoiceRatingState.GENERATED
        repository.add(new_billing_cycle)

        # test getting invoices
        old_invoices = list(repository.get_invoices(billing_cycle))
        new_invoices = list(repository.get_invoices(new_billing_cycle))
        all_invoices = list(repository.get_invoices())
        assert len(all_invoices) == len(old_invoices + new_invoices)

    def test_adding_adjustments_for_invoice(self, repository, make_billing_cycle):
        account_ids = list(range(10, 20))
        billing_cycle = make_billing_cycle(account_ids=account_ids)
        repository.add(billing_cycle)

        # test add adjustments
        invoice = billing_cycle.get_invoice(account_id=account_ids[0])
        adjustments = [
            model.Adjustment(
                id=None,
                date=date.fromisoformat(f"2023-02-0{i}"),
                type=AdjustmentType.TRAINING,
                amount=Decimal(randint(1, 100) / 1000),
            )
            for i in range(1, 6)
        ]
        for a in adjustments:
            invoice.add_adjustment(a)
        repository.update_invoice(invoice)

        stored_invoice = repository.get_invoice_by_id(invoice.id)

        assert len(stored_invoice.adjustments) == len(adjustments)
        assert stored_invoice.adjustments[0] == adjustments[0]

    def test_insert_and_calculate_sim_usage(self, repository, make_billing_cycle):
        billing_cycle = make_billing_cycle(month_string="2023-05")
        repository.add(billing_cycle)

        imsi_list = list(map(IMSI.from_int, range(200, 500)))
        usage_records = [
            model.SimUsage(
                imsi=choice(imsi_list),
                service=choice(list(Service)),
                volume=randint(200, 10000),
                charge=Decimal(randint(5, 400) / 1000),
            )
            for _ in range(10)
        ]

        billing_cycle.insert_sim_usage(usage_records)
        repository.add(billing_cycle)
        invoice_id = 1
        assert len(list(billing_cycle.sim_usage)) == len(usage_records)

        service_usages = list(
            repository.get_service_usages(billing_cycle, imsi_list, invoice_id)
        )
        assert len(service_usages) == len(Service)
        assert sorted([s.service for s in service_usages]) == sorted(list(Service))
        for usage in service_usages:
            service_sim_usages = [
                s for s in usage_records if s.service == usage.service
            ]
            assert usage.volume == sum([s.volume for s in service_sim_usages], start=0)
            assert usage.charge == sum([s.charge for s in service_sim_usages], start=0)

    def test_get_invoice_reconciliation(self, repository, make_billing_cycle):
        billing_cycle = make_billing_cycle(month_string="2023-05")
        repository.add(billing_cycle)

        from_date = datetime.date(
            datetime(
                2023,
                5,
                1,
            )
        )
        to_date = datetime.date(datetime(2023, 6, 1))
        billing_cycle_id = 1

        invoice_reconciliation = list(
            repository.get_invoice_reconciliation(from_date, to_date, billing_cycle_id)
        )

        assert isinstance(invoice_reconciliation[0], model.ReconciliationAgg)
        assert isinstance(invoice_reconciliation[1], model.ReconciliationAgg)

    def test_get_monthly_reconciliation(self, repository, make_billing_cycle):
        billing_cycle = make_billing_cycle(month_string="2023-05")
        repository.add(billing_cycle)

        from_date = datetime.date(
            datetime(
                2023,
                5,
                1,
            )
        )
        to_date = datetime.date(datetime(2023, 6, 1))

        invoice_reconciliation = list(
            repository.get_monthly_reconciliation(from_date, to_date)
        )

        assert isinstance(invoice_reconciliation[0], model.ReconciliationAgg)
        assert isinstance(invoice_reconciliation[1], model.ReconciliationAgg)
