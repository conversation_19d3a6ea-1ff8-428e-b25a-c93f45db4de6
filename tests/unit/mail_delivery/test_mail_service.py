from unittest.mock import Magic<PERSON>ock

import pytest
from platform_api_client import PlatformAPIClient, PlatformAPIError

from auth.exceptions import ForbiddenError, Unauthorized
from mail_delivery.exception import MailAPIError
from mail_delivery.service import MailServiceAPI


class TestMailServiceAPI:
    def setup_method(self):
        # Mocking the API client dependency
        self.mock_api_client = MagicMock(spec=PlatformAPIClient)
        self.mail_service = MailServiceAPI(api_client=self.mock_api_client)

    def test_send_mail_success(self):
        # Mock a successful API response
        self.mock_api_client.post.return_value.status_code = 202
        self.mock_api_client.post.return_value.json.return_value = None
        subject = "Test Email"
        name_from = "Tester"
        recipients = ["<EMAIL>"]
        html_body = "<h1>Hello</h1>"

        result = self.mail_service.send_mail(subject, name_from, recipients, html_body)

        self.mock_api_client.post.assert_called_once()
        assert result is None

    def test_send_mail_not_success(self):
        # Mock a successful API response
        self.mock_api_client.post.return_value.status_code = 201
        subject = "Test Email"
        name_from = "Tester"
        recipients = ["<EMAIL>"]
        html_body = "<h1>Hello</h1>"

        with pytest.raises(MailAPIError):
            self.mail_service.send_mail(subject, name_from, recipients, html_body)
            self.mock_api_client.post.assert_called_once()

    def test_send_mail_unauthorized(self):
        self.mock_api_client.post.side_effect = PlatformAPIError(
            "Unauthorized", status_code=401
        )

        with pytest.raises(Unauthorized):
            self.mail_service.send_mail(
                subject="Test",
                name_from="Tester",
                recipients=["<EMAIL>"],
                html_body="<p>Body</p>",
            )

    def test_send_mail_forbidden(self):
        self.mock_api_client.post.side_effect = PlatformAPIError(
            "Forbidden", status_code=403
        )

        with pytest.raises(ForbiddenError):
            self.mail_service.send_mail(
                subject="Test",
                name_from="Tester",
                recipients=["<EMAIL>"],
                html_body="<p>Body</p>",
            )

    def test_send_mail_other_error(self):
        self.mock_api_client.post.side_effect = PlatformAPIError(
            "Server error", status_code=500
        )

        with pytest.raises(PlatformAPIError):
            self.mail_service.send_mail(
                subject="Test",
                name_from="Tester",
                recipients=["<EMAIL>"],
                html_body="<p>Body</p>",
            )

    def test_send_mail_connection_error(self):
        self.mock_api_client.post.side_effect = ConnectionError("No connection")

        with pytest.raises(ConnectionError):
            self.mail_service.send_mail(
                subject="Test",
                name_from="Tester",
                recipients=["<EMAIL>"],
                html_body="<p>Body</p>",
            )
