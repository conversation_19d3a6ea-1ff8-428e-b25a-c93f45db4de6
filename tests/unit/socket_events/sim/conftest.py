from unittest.mock import AsyncMock, MagicMock

import pytest
import socketio  # type: ignore


@pytest.fixture
def mock_sio():
    mock = AsyncMock(spec=socketio.AsyncServer)
    mock.emit = AsyncMock()
    mock.on = MagicMock()
    return mock


@pytest.fixture
def sample_imsi_list():
    return ["123456789012345", "987654321098765", "456789012345678"]


@pytest.fixture
def sample_status():
    return "ACTIVE"
