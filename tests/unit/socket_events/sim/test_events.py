from unittest.mock import AsyncMock, MagicMock, patch

import pytest
import socketio  # type: ignore

from socket_events.sim.events import SimSocket<PERSON>vents, get_sim_socket_events


class TestSimSocketEvents:
    def test_init_calls_register_events(self, mock_sio):
        with patch.object(SimSocketEvents, "register_events") as mock_register:
            events = SimSocketEvents(mock_sio)

            assert events.sio == mock_sio
            mock_register.assert_called_once()

    def test_register_events_method_exists(self, mock_sio):
        events = SimSocketEvents(mock_sio)

        events.register_events()

    @pytest.mark.asyncio
    async def test_sim_details_update_success_with_multiple_imsis(
        self, mock_sio, sample_imsi_list, sample_status
    ):
        with patch("socket_events.sim.events.logger") as mock_logger:
            events = SimSocketEvents(mock_sio)

            await events.sim_details_update(sample_imsi_list, sample_status)

            expected_data = {"imsi": sample_imsi_list, "simStatus": sample_status}
            mock_sio.emit.assert_called_once_with("sim_details_update", expected_data)
            mock_logger.info.assert_called_once_with(
                "[Socket] Emitted 'sim_details_update' event"
            )
            mock_logger.debug.assert_called_once_with(
                f"[Socket] Payload: {expected_data}"
            )

    @pytest.mark.asyncio
    async def test_sim_details_update_success_with_single_imsi(self, mock_sio):
        with patch("socket_events.sim.events.logger") as mock_logger:
            events = SimSocketEvents(mock_sio)
            imsi_list = ["123456789012345"]
            status = "DEACTIVATED"

            await events.sim_details_update(imsi_list, status)

            expected_data = {"imsi": imsi_list, "simStatus": status}
            mock_sio.emit.assert_called_once_with("sim_details_update", expected_data)
            mock_logger.info.assert_called_once_with(
                "[Socket] Emitted 'sim_details_update' event"
            )
            mock_logger.debug.assert_called_once_with(
                f"[Socket] Payload: {expected_data}"
            )

    @pytest.mark.asyncio
    async def test_sim_details_update_success_with_empty_list(self, mock_sio):
        with patch("socket_events.sim.events.logger") as mock_logger:
            events = SimSocketEvents(mock_sio)
            imsi_list = []
            status = "SUSPENDED"

            await events.sim_details_update(imsi_list, status)

            expected_data = {"imsi": imsi_list, "simStatus": status}
            mock_sio.emit.assert_called_once_with("sim_details_update", expected_data)
            mock_logger.info.assert_called_once_with(
                "[Socket] Emitted 'sim_details_update' event"
            )
            mock_logger.debug.assert_called_once_with(
                f"[Socket] Payload: {expected_data}"
            )

    @pytest.mark.asyncio
    async def test_sim_details_update_emit_exception(
        self, mock_sio, sample_imsi_list, sample_status
    ):
        with patch("socket_events.sim.events.logger") as mock_logger:
            mock_sio.emit.side_effect = Exception("Socket emit failed")
            events = SimSocketEvents(mock_sio)

            await events.sim_details_update(sample_imsi_list, sample_status)

            expected_data = {"imsi": sample_imsi_list, "simStatus": sample_status}
            mock_sio.emit.assert_called_once_with("sim_details_update", expected_data)
            mock_logger.error.assert_called_once_with(
                "[Socket] Failed to emit 'sim_details_update' event: Socket emit failed"
            )
            mock_logger.debug.assert_called_once_with(
                f"[Socket] Event data: {expected_data}"
            )

    @pytest.mark.asyncio
    async def test_sim_details_update_different_status_values(self, mock_sio):
        with patch("socket_events.sim.events.logger") as mock_logger:
            events = SimSocketEvents(mock_sio)
            imsi_list = ["123456789012345"]

            status_values = ["ACTIVE", "DEACTIVATED", "SUSPENDED", "TERMINATED"]

            for status in status_values:
                mock_sio.reset_mock()
                mock_logger.reset_mock()

                await events.sim_details_update(imsi_list, status)

                expected_data = {"imsi": imsi_list, "simStatus": status}
                mock_sio.emit.assert_called_once_with(
                    "sim_details_update", expected_data
                )
                mock_logger.info.assert_called_once_with(
                    "[Socket] Emitted 'sim_details_update' event"
                )

    @pytest.mark.asyncio
    async def test_sim_details_update_connection_error(
        self, mock_sio, sample_imsi_list, sample_status
    ):
        with patch("socket_events.sim.events.logger") as mock_logger:
            mock_sio.emit.side_effect = ConnectionError("Connection lost")
            events = SimSocketEvents(mock_sio)

            await events.sim_details_update(sample_imsi_list, sample_status)

            expected_data = {"imsi": sample_imsi_list, "simStatus": sample_status}
            mock_logger.error.assert_called_once_with(
                "[Socket] Failed to emit 'sim_details_update' event: Connection lost"
            )
            mock_logger.debug.assert_called_once_with(
                f"[Socket] Event data: {expected_data}"
            )

    @pytest.mark.asyncio
    async def test_sim_details_update_timeout_error(
        self, mock_sio, sample_imsi_list, sample_status
    ):
        with patch("socket_events.sim.events.logger") as mock_logger:
            mock_sio.emit.side_effect = TimeoutError("Request timeout")
            events = SimSocketEvents(mock_sio)

            await events.sim_details_update(sample_imsi_list, sample_status)

            expected_data = {"imsi": sample_imsi_list, "simStatus": sample_status}
            mock_logger.error.assert_called_once_with(
                "[Socket] Failed to emit 'sim_details_update' event: Request timeout"
            )
            mock_logger.debug.assert_called_once_with(
                f"[Socket] Event data: {expected_data}"
            )


class TestGetSimSocketEvents:
    def test_get_sim_socket_events_returns_instance(self, mock_sio):
        result = get_sim_socket_events(mock_sio)

        assert isinstance(result, SimSocketEvents)
        assert result.sio == mock_sio

    def test_get_sim_socket_events_with_different_sio_instances(self):
        sio1 = AsyncMock(spec=socketio.AsyncServer)
        sio2 = AsyncMock(spec=socketio.AsyncServer)

        result1 = get_sim_socket_events(sio1)
        result2 = get_sim_socket_events(sio2)

        assert result1.sio == sio1
        assert result2.sio == sio2
        assert result1 != result2

    def test_get_sim_socket_events_calls_constructor(self, mock_sio):
        with patch("socket_events.sim.events.SimSocketEvents") as mock_class:
            mock_instance = MagicMock()
            mock_class.return_value = mock_instance

            result = get_sim_socket_events(mock_sio)

            mock_class.assert_called_once_with(mock_sio)
            assert result == mock_instance
