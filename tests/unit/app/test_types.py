import pytest
from pydantic import BaseModel

from app.config import DatabaseUri


@pytest.mark.parametrize(
    "uri,scheme",
    [
        ("sqlite://", "sqlite"),
        ("sqlite:///foo.db", "sqlite"),
        ("sqlite:////absolute/path/to/foo.db", "sqlite"),
        ("postgresql://scott:tiger@localhost/mydatabase", "postgresql"),
        (
            "postgresql+psycopg2://scott:tiger@localhost/mydatabase",
            "postgresql+psycopg2",
        ),
        ("oracle://scott:tiger@127.0.0.1:1521/sidname", "oracle"),
        # Line below produces invalid scheme validation error
        # ("oracle+cx_oracle://scott:tiger@tnsname", "oracle+cx_oracle"),
        ("mssql+pymssql://scott:tiger@hostname:3000/dbname", "mssql+pymssql"),
    ],
)
def test_database_uri_sqlalchemy_urls(uri, scheme):
    class Model(BaseModel):
        u: DatabaseUri

    assert Model(u=uri).u.scheme == scheme
