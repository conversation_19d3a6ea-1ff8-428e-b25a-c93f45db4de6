from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from app.socket_server import (
    SocketServer,
    get_redis_manager,
    sim_events,
    sio,
    socket_server,
)


class TestGetRedisManager:
    def test_get_redis_manager_success(self):
        with patch("app.socket_server.AsyncRedisManager") as mock_manager_class:
            mock_manager_instance = MagicMock()
            mock_manager_class.return_value = mock_manager_instance

            result = get_redis_manager("localhost", 6379)

            mock_manager_class.assert_called_once_with("redis://localhost:6379")
            assert result == mock_manager_instance

    def test_get_redis_manager_with_custom_host_port(self):
        with patch("app.socket_server.AsyncRedisManager") as mock_manager_class:
            mock_manager_instance = MagicMock()
            mock_manager_class.return_value = mock_manager_instance

            result = get_redis_manager("redis.example.com", 6380)

            mock_manager_class.assert_called_once_with("redis://redis.example.com:6380")
            assert result == mock_manager_instance

    def test_get_redis_manager_connection_exception(self):
        with patch("app.socket_server.AsyncRedisManager") as mock_manager_class:
            mock_manager_class.side_effect = ConnectionError("Redis connection failed")

            with patch("app.socket_server.logger") as mock_logger:
                result = get_redis_manager("localhost", 6379)

                assert result is None
                mock_logger.error.assert_called_once_with(
                    "[Redis] Failed to connect to Redis at localhost:6379: "
                    "Redis connection failed"
                )

    def test_get_redis_manager_generic_exception(self):
        with patch("app.socket_server.AsyncRedisManager") as mock_manager_class:
            mock_manager_class.side_effect = Exception("Unexpected error")

            with patch("app.socket_server.logger") as mock_logger:
                result = get_redis_manager("localhost", 6379)

                assert result is None
                mock_logger.error.assert_called_once_with(
                    "[Redis] Failed to connect to Redis at localhost:6379: "
                    "Unexpected error"
                )


class TestSocketServer:
    def test_init_with_default_parameters(self):
        with patch("app.socket_server.socketio.AsyncServer") as mock_async_server:
            with patch(
                "app.socket_server.get_sim_socket_events"
            ) as mock_get_sim_events:
                with patch("app.socket_server.settings") as mock_settings:
                    mock_settings.APP_LANDING_PAGE_URL = "http://localhost:3000"

                    mock_redis_manager = MagicMock()
                    mock_sio = MagicMock()
                    mock_async_server.return_value = mock_sio
                    mock_sim_events = MagicMock()
                    mock_get_sim_events.return_value = mock_sim_events

                    server = SocketServer(
                        cors_allowed_origins=["http://localhost:3000"],
                        mgr=mock_redis_manager,
                    )

                    mock_async_server.assert_called_once_with(
                        cors_allowed_origins=["http://localhost:3000"],
                        async_mode="asgi",
                        client_manager=mock_redis_manager,
                        ping_timeout=10,
                        ping_interval=30,
                    )
                    assert server.sio == mock_sio
                    assert server.sim_events == mock_sim_events

    def test_init_with_custom_parameters(self):
        with patch("app.socket_server.socketio.AsyncServer") as mock_async_server:
            with patch(
                "app.socket_server.get_sim_socket_events"
            ) as mock_get_sim_events:
                mock_redis_manager = MagicMock()
                mock_sio = MagicMock()
                mock_async_server.return_value = mock_sio
                mock_sim_events = MagicMock()
                mock_get_sim_events.return_value = mock_sim_events

                SocketServer(
                    cors_allowed_origins=["http://example.com", "http://test.com"],
                    async_mode="threading",
                    mgr=mock_redis_manager,
                    ping_timout=20,
                    ping_interval=60,
                )

                mock_async_server.assert_called_once_with(
                    cors_allowed_origins=["http://example.com", "http://test.com"],
                    async_mode="threading",
                    client_manager=mock_redis_manager,
                    ping_timeout=20,
                    ping_interval=60,
                )

    def test_register_core_event_handlers(self):
        with patch("app.socket_server.socketio.AsyncServer") as mock_async_server:
            with patch("app.socket_server.get_sim_socket_events"):
                mock_sio = MagicMock()
                mock_async_server.return_value = mock_sio

                server = SocketServer()

                mock_sio.on.assert_any_call("connect", server.handle_connect)
                mock_sio.on.assert_any_call("disconnect", server.handle_disconnect)

    def test_register_domain_event_handlers(self):
        with patch("app.socket_server.socketio.AsyncServer") as mock_async_server:
            with patch(
                "app.socket_server.get_sim_socket_events"
            ) as mock_get_sim_events:
                mock_sio = MagicMock()
                mock_async_server.return_value = mock_sio
                mock_sim_events = MagicMock()
                mock_get_sim_events.return_value = mock_sim_events

                server = SocketServer()

                mock_get_sim_events.assert_called_once_with(mock_sio)
                assert server.sim_events == mock_sim_events

    @pytest.mark.asyncio
    async def test_handle_connect(self):
        with patch("app.socket_server.socketio.AsyncServer") as mock_async_server:
            with patch("app.socket_server.get_sim_socket_events"):
                with patch("app.socket_server.settings") as mock_settings:
                    with patch("app.socket_server.logger") as mock_logger:
                        mock_settings.APP_BASE_URL = "http://localhost:8000"
                        mock_sio = AsyncMock()
                        mock_async_server.return_value = mock_sio

                        server = SocketServer()

                        await server.handle_connect(
                            "test_sid", {"test": "environ"}, {"test": "auth"}
                        )

                        mock_logger.info.assert_any_call(
                            "[Socket] Client connected: sid=test_sid"
                        )
                        mock_logger.info.assert_any_call(
                            "[Socket] connected with url - http://localhost:8000"
                        )
                        mock_sio.emit.assert_called_once_with(
                            "server_message", {"msg": "Connected!"}, to="test_sid"
                        )

    @pytest.mark.asyncio
    async def test_handle_disconnect(self):
        with patch("app.socket_server.socketio.AsyncServer") as mock_async_server:
            with patch("app.socket_server.get_sim_socket_events"):
                with patch("app.socket_server.logger") as mock_logger:
                    mock_sio = AsyncMock()
                    mock_async_server.return_value = mock_sio

                    server = SocketServer()

                    await server.handle_disconnect("test_sid")

                    mock_logger.info.assert_called_once_with(
                        "[Socket] Client disconnected: sid=test_sid"
                    )


class TestSingletonInstances:
    def test_socket_server_singleton_exists(self):
        assert socket_server is not None
        assert hasattr(socket_server, "sio")
        assert hasattr(socket_server, "sim_events")

    def test_sio_reference(self):
        assert hasattr(socket_server, "sio")
        assert sio == socket_server.sio

    def test_sim_events_reference(self):
        assert hasattr(socket_server, "sim_events")
        assert sim_events == socket_server.sim_events
