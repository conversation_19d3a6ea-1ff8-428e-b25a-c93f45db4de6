import pytest
from pydantic import ValidationError

from app.config import Settings


def clear_oidc_config_env(monkeypatch):
    for k in Settings.__fields__:
        if k.startswith("OIDC"):
            monkeypatch.delenv(k, raising=False)


def test_oidc_config_required_on_prod(tmp_path, monkeypatch):
    clear_oidc_config_env(monkeypatch)
    monkeypatch.setenv("APPLICATION_ENV", "prod")
    with pytest.raises(ValidationError) as exc_info:
        Settings()

    assert {
        "loc": ("OIDC_CLIENT_ID",),
        "msg": "OIDC_CLIENT_ID is required for the 'prod' environment.",
        "type": "value_error",
    } in exc_info.value.errors()


def test_oidc_config_not_required_on_local(tmp_path, monkeypatch):
    clear_oidc_config_env(monkeypatch)
    monkeypatch.setenv("APPLICATION_ENV", "local")
    settings = Settings()
    assert settings.APPLICATION_ENV == "local"
    assert settings.OIDC_CLIENT_ID is None
