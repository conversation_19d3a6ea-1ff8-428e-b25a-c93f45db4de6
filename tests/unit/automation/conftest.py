import pytest

from automation.adapters.orm import model
from common.constants import CTDU, DUESL


@pytest.fixture
def make_rule_type_models():
    def factory(
        categories=["Usage Monitoring", "SIM Provisioning", "Network and System"]
    ):
        return [
            model.RuleType(
                id=i + 1,
                rule_type=rule_type,
            )
            for i, rule_type in enumerate(categories)
        ]

    return factory


@pytest.fixture
def make_rule_category_models():
    def factory(
        categories=[
            "Cycle To Date Data Usage",
            "Cycle To Date Voice Usage",
            "Recent SMS Usage",
        ]
    ):
        return [
            model.RuleCategoryDetails(
                id=i + 1,
                category=category,
                rule_type="SIM Provisioning",
                rule_type_id=i + 1,
            )
            for i, category in enumerate(categories)
        ]

    return factory


@pytest.fixture
def make_rule_definition_models():
    def factory(
        definitions=[
            ("Data usage exceeds a specified limit xxx KB", "KB"),
            (" Voice usage threshold is xxx minutes", "mint"),
        ]
    ):
        return [
            model.RuleDefinitionDetails(
                id=i + 1,
                definition=definition,
                category="Cycle To Date Data Usage",
                rule_category_code=CTDU,
                rule_definition_code=DUESL,
            )
            for i, (definition) in enumerate(definitions)
        ]

    return factory


@pytest.fixture
def make_action_models():
    def factory(
        actions=[
            "Send SMS",
            "Send Email",
            "Send Notification",
        ]
    ):
        return [
            model.Actions(id=i + 1, action=action) for i, action in enumerate(actions)
        ]

    return factory


@pytest.fixture
def make_notification_models():
    def factory(
        notifications=[
            "SMS",
            "Email",
            "Notification",
        ]
    ):
        return [
            model.Notifications(id=i + 1, notification=notification)
            for i, notification in enumerate(notifications)
        ]

    return factory
