from unittest.mock import MagicMock

import pytest

from core.domain.model import CreatePartitions, CreatePartitionsResponse
from core.services import CoreService


class TestCoreService:
    def setup_method(self):
        self.repository_mock = MagicMock()
        self.core_service = CoreService(repository=self.repository_mock)

    def test_create_partitions_success(self):
        # Arrange
        create_collection = CreatePartitions(
            month=None, partitions=["partition1", "partition2"]
        )
        expected_response = CreatePartitionsResponse(
            created=["partition1", "partition2"], skipped=[]
        )
        self.repository_mock.create_partitions.return_value = expected_response

        # Act
        response = self.core_service.create_partitions(create_collection)

        # Assert
        assert response == expected_response
        self.repository_mock.create_partitions.assert_called_once_with(
            create_collection
        )

    def test_create_partitions_value_error(self):
        # Arrange
        create_collection = CreatePartitions(
            month=None, partitions=["partition1", "partition2"]
        )
        self.repository_mock.create_partitions.side_effect = ValueError(
            "Invalid partition data"
        )

        # Act & Assert
        with pytest.raises(ValueError, match="Invalid partition data"):
            self.core_service.create_partitions(create_collection)

    def test_create_partitions_unexpected_error(self):
        # Arrange
        create_collection = CreatePartitions(
            month=None, partitions=["partition1", "partition2"]
        )
        self.repository_mock.create_partitions.side_effect = Exception(
            "Unexpected error"
        )

        # Act & Assert
        with pytest.raises(Exception, match="Unexpected error"):
            self.core_service.create_partitions(create_collection)
