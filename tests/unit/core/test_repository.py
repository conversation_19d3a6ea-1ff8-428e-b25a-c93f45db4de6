import uuid
from unittest.mock import Magic<PERSON>ock

import pytest
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from common.utils import set_trace_id
from core.adapters.repository import DataBaseCoreRepository
from core.domain import model

set_trace_id(uuid.uuid4())


@pytest.fixture
def session_mock():
    return MagicMock(spec=Session)


@pytest.fixture
def repository(session_mock):
    return DataBaseCoreRepository(session=session_mock)


def test_create_partitions_success(repository, session_mock):
    partitions_data = model.CreatePartitions(
        month="2025-04", partitions=["cdr_data", "cdr_voice"]
    )

    session_mock.execute.return_value.scalar.side_effect = [
        "Partition created: cdr_data",
        "Partition created: cdr_voice",
    ]

    response = repository.create_partitions(partitions_data)

    assert response.created == [
        "cdr_data_2025-04",
        "cdr_voice_2025-04",
    ]
    assert response.skipped == []


def test_create_partitions_already_exists(repository, session_mock):
    partitions_data = model.CreatePartitions(month="2025-04", partitions=["cdr_data"])

    session_mock.execute.return_value.scalar.return_value = (
        "Partition already exists: cdr_data"
    )

    response = repository.create_partitions(partitions_data)

    assert response.created == []
    assert response.skipped == ["cdr_data_2025-04"]


def test_create_partitions_unexpected_error(repository, session_mock):
    partitions_data = model.CreatePartitions(month="2025-04", partitions=["cdr_data"])

    session_mock.execute.side_effect = SQLAlchemyError("Database error")

    with pytest.raises(SQLAlchemyError, match="Database error"):
        repository.create_partitions(partitions_data)
