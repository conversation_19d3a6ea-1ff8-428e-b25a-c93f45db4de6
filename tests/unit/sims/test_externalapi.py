import json
from unittest.mock import MagicMock, patch

import pytest

from common.types import IMSI, MSISDN
from sim import exceptions
from sim.adapters.externalapi import SimProvisioning
from sim.domain import model


class DummyQueryResponse:
    def __init__(self, call_id):
        self.call_id = call_id


def make_sim_pod_response():
    return model.SIMPODResponse(
        trid="1234",
        message="success",
        imsi=IMSI("123456789012345"),
        msisdn=MSISDN("9876543210"),
    )


def make_xml_response():
    return {"disconnect-data-session-response": {"@trid": "1234", "#text": "success"}}


@patch("sim.adapters.externalapi.requests.post")
@patch("sim.adapters.externalapi.xmltodict.parse")
def test_pod_sim_success(mock_xmltodict_parse, mock_requests_post):
    sim_prov = SimProvisioning()
    imsi = IMSI("123456789012345")
    msisdn = MSISDN("9876543210")

    sim_prov.query_sim_active_call = MagicMock(
        return_value=DummyQueryResponse(call_id="callid123")
    )
    sim_prov._pod_sim_pip = MagicMock(
        return_value=json.dumps(
            {"request": "<xml></xml>", "header": {"Content-Type": "application/xml"}}
        )
    )
    mock_response = MagicMock()
    mock_response.text = "<xml></xml>"
    mock_requests_post.return_value = mock_response
    mock_xmltodict_parse.return_value = make_xml_response()
    expected_response = make_sim_pod_response()
    sim_prov._parse_pod_sim_response = MagicMock(return_value=expected_response)

    result = sim_prov.pod_sim(imsi, msisdn)
    assert result == expected_response
    sim_prov.query_sim_active_call.assert_called_once_with(imsi=imsi, msisdn=msisdn)
    sim_prov._pod_sim_pip.assert_called_once_with(msisdn, "callid123")
    mock_requests_post.assert_called_once()
    mock_xmltodict_parse.assert_called_once()
    sim_prov._parse_pod_sim_response.assert_called_once()


@patch("sim.adapters.externalapi.requests.post")
@patch("sim.adapters.externalapi.xmltodict.parse")
def test_pod_sim_error_response(mock_xmltodict_parse, mock_requests_post):
    sim_prov = SimProvisioning()
    imsi = IMSI("123456789012345")
    msisdn = MSISDN("9876543210")
    sim_prov.query_sim_active_call = MagicMock(
        return_value=DummyQueryResponse(call_id="callid123")
    )
    sim_prov._pod_sim_pip = MagicMock(
        return_value=json.dumps(
            {"request": "<xml></xml>", "header": {"Content-Type": "application/xml"}}
        )
    )
    mock_response = MagicMock()
    mock_response.text = "<xml></xml>"
    mock_requests_post.return_value = mock_response
    mock_xmltodict_parse.return_value = {
        "disconnect-data-session-error": {"@trid": "1234", "#text": "error"}
    }
    # Simulate error in _parse_pod_sim_response
    sim_prov._parse_pod_sim_response = MagicMock(side_effect=Exception("POD error"))
    with pytest.raises(Exception, match="POD error"):
        sim_prov.pod_sim(imsi, msisdn)


@patch("sim.adapters.externalapi.requests.post")
@patch("sim.adapters.externalapi.xmltodict.parse")
def test_flush_sim_success(mock_xmltodict_parse, mock_requests_post):
    sim_prov = SimProvisioning()
    imsi = IMSI("123456789012345")
    msisdn = MSISDN("9876543210")
    sim_prov._flush_sim_state_pip = MagicMock(
        return_value=json.dumps(
            {"request": "<xml></xml>", "header": {"Content-Type": "application/xml"}}
        )
    )
    mock_response = MagicMock()
    mock_response.text = "<xml></xml>"
    mock_requests_post.return_value = mock_response
    mock_xmltodict_parse.return_value = {
        "flush-sim-state-response": {"@trid": "trid1", "#text": "success"}
    }
    result = sim_prov.flush_sim(imsi, msisdn)
    assert result.trid == "trid1"
    assert result.message == "success"
    assert result.imsi == imsi
    assert result.msisdn == msisdn
    sim_prov._flush_sim_state_pip.assert_called_once_with(msisdn)
    mock_requests_post.assert_called_once()
    mock_xmltodict_parse.assert_called_once()


@patch("sim.adapters.externalapi.requests.post")
@patch("sim.adapters.externalapi.xmltodict.parse")
def test_query_sim_active_call_success(mock_xmltodict_parse, mock_requests_post):
    sim_prov = SimProvisioning()
    imsi = IMSI("123456789012345")
    msisdn = MSISDN("9876543210")
    sim_prov._query_sim_active_call_pip = MagicMock(
        return_value=json.dumps(
            {"request": "<xml></xml>", "header": {"Content-Type": "application/xml"}}
        )
    )
    mock_response = MagicMock()
    mock_response.text = "<xml></xml>"
    mock_requests_post.return_value = mock_response
    mock_xmltodict_parse.return_value = {
        "query-active-call-response": {
            "@trid": "trid2",
            "active-call": {"@id": "callid456"},
            "#text": "success",
        }
    }
    result = sim_prov.query_sim_active_call(imsi, msisdn)
    assert result.trid == "trid2"
    assert result.call_id == "callid456"
    assert result.message == "success"
    assert result.imsi == imsi
    assert result.msisdn == msisdn
    sim_prov._query_sim_active_call_pip.assert_called_once_with(msisdn)
    mock_requests_post.assert_called_once()
    mock_xmltodict_parse.assert_called_once()


@patch("sim.adapters.externalapi.requests.post")
@patch("sim.adapters.externalapi.xmltodict.parse")
def test_sms_sim_ppl_success(mock_xmltodict_parse, mock_requests_post):
    sim_prov = SimProvisioning()
    imsi = IMSI("123456789012345")
    msisdn = MSISDN("9876543210")
    message = "Test message"
    sim_prov._send_sim_sms_pip = MagicMock(
        return_value=json.dumps(
            {"request": "<xml></xml>", "header": {"Content-Type": "application/xml"}}
        )
    )
    mock_response = MagicMock()
    mock_response.text = "<xml></xml>"
    mock_requests_post.return_value = mock_response
    mock_xmltodict_parse.return_value = {
        "send-sms-response": {
            "@trid": "trid3",
            "call-id": "callid789",
            "#text": "success",
        }
    }
    result = sim_prov.sms_sim_ppl(imsi, msisdn, message)
    assert result.trid == "trid3"
    assert result.call_id == "callid789"
    assert result.imsi == imsi
    assert result.msisdn == msisdn
    assert result.message == "success"
    sim_prov._send_sim_sms_pip.assert_called_once_with(msisdn, message)
    mock_requests_post.assert_called_once()
    mock_xmltodict_parse.assert_called_once()


class TestDataSessionExternalAPI:
    def setup_method(self):
        self.provisioning_api = SimProvisioning()

    @patch("sim.adapters.externalapi.SimProvisioning.worldov_token")
    @patch("sim.adapters.externalapi.requests.get")
    def test_get_latest_data_session_success(
        self, mock_requests_get, mock_worldov_token
    ):
        # Mock token response
        mock_worldov_token.return_value = model.TokenResponse(
            authentication_token="test_token"
        )

        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "id": "session_123",
            "partner_id": 1,
            "iccid": "8944538532046590001",
            "imsi": "234588560660001",
            "start_time": "2023-03-11T21:13:44Z",
            "last_updated": "2023-03-11T21:13:44Z",
            "end_time": "2023-03-11T21:14:44Z",
            "ip_address": "***********",
            "apn_name": "internet",
            "bytes_total": 9431,
            "bytes_mo": 4715,
            "bytes_mt": 4716,
            "bytes_limit": 1000000,
            "bytes_limit_threshold": 800000,
            "bytes_limit_used": 9431,
            "mobile_country_code": "234",
            "mobile_network_code": "30",
            "lac": "5F2C",
            "cellid": "0A1542",  # Note: lowercase 'cellid' as per API response
        }
        mock_requests_get.return_value = mock_response

        result = self.provisioning_api.get_latest_data_session(
            iccid="8944538532046590001"
        )

        assert result.id == "session_123"
        assert result.imsi == "234588560660001"
        assert result.bytes_total == 9431
        assert result.cellId == "0A1542"  # Verify it's mapped correctly
        mock_requests_get.assert_called_once()

    @patch("sim.adapters.externalapi.SimProvisioning.worldov_token")
    @patch("sim.adapters.externalapi.requests.get")
    def test_get_latest_data_session_404_error(
        self, mock_requests_get, mock_worldov_token
    ):

        # Mock token response
        mock_worldov_token.return_value = model.TokenResponse(
            authentication_token="test_token"
        )

        # Mock 404 response
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_response.json.return_value = {"error": "Not found"}
        mock_requests_get.return_value = mock_response

        with pytest.raises(exceptions.SimCardsNotFound) as exc_info:
            self.provisioning_api.get_latest_data_session(iccid="8944538532046590001")

        assert "No data session found for given SIM" in str(exc_info.value)

    @patch("sim.adapters.externalapi.SimProvisioning.worldov_token")
    @patch("sim.adapters.externalapi.requests.get")
    def test_get_latest_data_session_general_error(
        self, mock_requests_get, mock_worldov_token
    ):
        from sim.exceptions import SimError

        # Mock token response
        mock_worldov_token.return_value = model.TokenResponse(
            authentication_token="test_token"
        )

        # Mock general error response
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        mock_requests_get.return_value = mock_response

        with pytest.raises(SimError) as exc_info:
            self.provisioning_api.get_latest_data_session(iccid="8944538532046590001")

        assert "Failed to get data session: 500" in str(exc_info.value)


class TestLocationExternalAPI:
    def setup_method(self):
        # Use the fake implementation for these tests
        from sim.adapters.externalapi import FakeSIMProvisioningAPI

        self.provisioning_api = FakeSIMProvisioningAPI()

    def test_get_location_success(self):
        # Test the fake implementation
        result = self.provisioning_api.get_location(iccid="8944538532046590001")

        assert result.meta.current_page == 1
        assert result.meta.total_items == 1
        assert len(result.items) == 1
        assert result.items[0].id == "14099673"
        assert result.items[0].imsi == "234588570050716"
        assert result.items[0].country_name == "United Kingdom"

    def test_get_latest_location_success(self):
        # Test the fake implementation
        result = self.provisioning_api.get_latest_location(iccid="8944538532046590001")

        assert result.id == "14099673"
        assert result.imsi == "234588570050716"
        assert result.country_name == "United Kingdom"
        assert result.continent_name == "Europe"

    def test_get_cell_location_success(self):
        # Test the fake implementation
        result = self.provisioning_api.get_cell_location(
            mcc="234", mnc="30", lac="5F2C", cell="0A1542"
        )

        assert result.mcc == "234"
        assert result.mnc == "30"
        assert result.lac == "5F2C"
        assert result.cell == "0A1542"
        assert result.lat == 54.670418333333333333333333333
        assert result.lon == -1.2676033333333333333333333333
        assert result.range == 3867
        assert result.source == "https://opencellid.org/"

    def test_fetch_mcc_mnc_ex_ppl_success(self):
        # Test the fake implementation
        result = self.provisioning_api.fetch_mcc_mnc_ex_ppl(msisdn=MSISDN("1234567890"))

        assert result.trid == "12345646"
        assert result.mcc == "234"
        assert result.mnc == "30"
        assert result.lac == "5F2C"
        assert result.cell_id == "0A1542"
        assert result.age == "12313526"
        assert result.source == "cache"
        assert result.network_domain == "notapplicable"


class TestRealLocationExternalAPI:
    def setup_method(self):
        # Use the real implementation for testing with mocks
        self.provisioning_api = SimProvisioning()

    @patch("sim.adapters.externalapi.SimProvisioning.worldov_token")
    @patch("sim.adapters.externalapi.requests.get")
    def test_get_location_success(self, mock_requests_get, mock_worldov_token):
        # Mock token response
        mock_worldov_token.return_value = model.TokenResponse(
            authentication_token="test_token"
        )

        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "meta": {
                "current_page": 1,
                "total_pages": 1,
                "items_per_page": 10,
                "total_items": 2,
            },
            "items": [
                {
                    "id": "loc_123",
                    "imsi": "234588560660001",
                    "first_location_update": "2023-03-11T21:13:44Z",
                    "last_location_update": "2023-03-11T21:14:44Z",
                    "msc_global_title": "msc_title",
                    "vlr_global_title": "vlr_title",
                    "sgsn_global_title": "sgsn_title",
                    "dra_mobile_country_code": "234",
                    "dra_mobile_network_code": "30",
                    "network_name": "Test Network",
                    "country_name": "United Kingdom",
                    "continent_name": "Europe",
                    "country_flag": "🇬🇧",
                }
            ],
        }
        mock_requests_get.return_value = mock_response

        result = self.provisioning_api.get_location(iccid="8944538532046590001")

        assert result.meta.current_page == 1
        assert result.meta.total_items == 2
        assert len(result.items) == 1
        assert result.items[0].id == "loc_123"
        mock_requests_get.assert_called_once()

    @patch("sim.adapters.externalapi.SimProvisioning.worldov_token")
    @patch("sim.adapters.externalapi.requests.post")
    def test_get_cell_location_404_error(self, mock_requests_post, mock_worldov_token):

        # Mock token response
        mock_worldov_token.return_value = model.TokenResponse(
            authentication_token="test_token"
        )

        # Mock 404 response
        mock_response = MagicMock()
        mock_response.status_code = 404
        mock_requests_post.return_value = mock_response

        with pytest.raises(exceptions.SimCardsNotFound) as exc_info:
            self.provisioning_api.get_cell_location(
                mcc="234", mnc="30", lac="5F2C", cell="0A1542"
            )

        assert "No cell location found for the provided cell tower information" in str(
            exc_info.value
        )
