[build-system]
requires = ["poetry-core>=1.2.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "monoglass"
version = "0.1.0"
description = "Single Pane of Glass API"
authors = ["Flyaps"]
readme = "README.md"
packages = [{ include = "src" }]

[[tool.poetry.source]]
name = "nexus"
url = "https://nexus.ops.connectedplatform.net/repository/pypi/simple/"
default = false
secondary = true

[tool.poetry.dependencies]
python = "^3.10"
fastapi = "0.86.0"
starlette = "0.20.4"
uvicorn = "0.19.0"
uvloop = "^0.17.0"
httptools = "^0.5.0"
gunicorn = "23.0.0"
more-itertools = "^9.0.0"

python-multipart = "0.0.18"
pydantic = { version = ">=1.6.2,!=1.7,!=1.7.1,!=1.7.2,!=1.7.3,!=1.8,!=1.8.1,<2.0.0", extras = ["dotenv", "email"] }
alembic = "1.9.2"
sqlalchemy = { version = "^1.4.44", extras = ["mypy"] }
psycopg2-binary = "^2.9.5"
pyyaml = "6.0"
pydantic-factories = "^1.17.2"
boto3 = "^1.26.62"
boto3-stubs = {extras = ["ec2", "s3"], version = "^1.26.62"}

nv2-pkg-py-authn = { version = "0.0.8", source = "nexus" }
nv2-pkg-py-platform-api-client = { version = "0.0.4", source = "nexus" }
xmltodict = "^0.13.0"
fastapi-xml = "^1.0.0b1"
pillow = "10.3.0"
requests = "^2.28.2"
python-dateutil = "^2.8.2"
svgutils = "^0.3.4"

# Package types
types-python-dateutil = "^*********"
types-requests = "^**********"
types-pillow = "^********"
types-lxml = "^2023.3.28"
prometheus-fastapi-instrumentator = "6.0.0"
pandas = "2.0.3"
openpyxl = "^3.1.2"
bandit = "1.7.5"
requests-mock = "^1.11.0"
pandas-stubs = "^2.1.4.231218"
pymongo = "^4.8.0"
defusedxml = "^0.7.1"
types-defusedxml = "^0.7.0.20240218"
virtualenv = "20.26.6"
setuptools = "70.0.0"
gitpython = "3.1.41"
certifi = "2023.7.22"
python-socketio = {extras = ["redis"], version = "^5.13.0"}
aioredis = "^2.0.1"
redis = "^6.2.0"

[tool.poetry.group.otel.dependencies]
# OpenTelemetry SDK
opentelemetry-api = "1.20.0"
opentelemetry-sdk = "1.20.0"
opentelemetry-proto = "1.20.0"
opentelemetry-distro = "0.41b0"
opentelemetry-util-http = "0.41b0"
opentelemetry-semantic-conventions = "0.41b0"
protobuf = "3.20.2"

# OpenTelemetry instrumentation
opentelemetry-instrumentation = "0.41b0"
opentelemetry-instrumentation-asgi = "0.41b0"
opentelemetry-instrumentation-httpx = "0.41b0"
opentelemetry-instrumentation-fastapi = "0.41b0"
opentelemetry-instrumentation-logging = "0.41b0"


[tool.poetry.group.test]
optional = true

[tool.poetry.group.test.dependencies]
pytest = "7.2.0"
pytest-asyncio = "^0.21.0"
coverage = "6.2"
mypy = "0.982"
lxml = "4.8.0"
types-PyYAML = "^6.0.12"
# in .pre-commit-config.yaml
flake8 = "5.0.4"
black = "22.8.0"
isort = "5.12.0"

[tool.poetry.group.dev]
optional = true

[tool.poetry.group.dev.dependencies]
pre-commit = "^2.20.0"


[tool.mypy]
exclude = "(?x)(alembic/env.py$)"
files = "src, tests"
plugins = "pydantic.mypy,sqlalchemy.ext.mypy.plugin"


[[tool.mypy.overrides]]
module = "uvicorn"
ignore_missing_imports = "True"


[[tool.mypy.overrides]]
module = "fastapi_xml"
ignore_missing_imports = "True"


[[tool.mypy.overrides]]
module = "xmltodict"
ignore_missing_imports = "True"


[[tool.mypy.overrides]]
module = "svgutils.*"
ignore_missing_imports = "True"


[tool.isort]
profile = "black"
known_third_party = ["alembic"]


[tool.pytest.ini_options]
pythonpath = "src"
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"
