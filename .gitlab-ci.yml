stages:
  - build
  - lint
  - trivy
  - test
  - secscan
  - push
  - release
  - deploy

variables:
  # NEXUS_USERNAME: $NEXUS_USERNAME
  # NEXUS_PASSWORD: $NEXUS_PASSWORD
  APP_BASE_URL: http://127.0.0.1:8000
  # set to 'true' if you want continous delivery to DEV
  AUTO_DEPLOY_MASTER: 'false'
  CLUSTER_ENVIRONMENT:
    value: spogdev
    description: Please enter spogdev or spogtest
  # set how long to wait for deployment operations to complete
  # DEPLOYMENT_TIMEOUT: 180s
  # set to 'true' if you want back deploy to OPS button
  # DEPLOY_OPS: 'false'
  # exclude lint job from pipeline when set to 'true'
  # TEST_SKIP_LINT: 'false'
  # exclude mypy job from pipeline when set to 'true'
  # TEST_SKIP_MYPY: 'false'
  # exclude pytest job from pipeline when set to 'true'
  # TEST_SKIP_PYTEST: 'false'
  # set to 'true' if tests require database
  TEST_REQUIRES_POSTGRES: 'true'
  # set to 'true' if tests require Keycloak (implies previous variable)
  # TEST_REQUIRES_KEYCLOAK: 'false'
  # use below variables to overwrite command of the corresponding jobs with your own
  # also variable ${COMMAND} is available and will be evaluated to default command from shared pipeline
  # TEST_COMMAND_LINT:
  # TEST_COMMAND_MYPY:
  # TEST_COMMAND_PYTEST:
  TEST_COMMAND_REQUIREMENTS: make install-test-deps

include:
  - project: devops/build
    file: /pipelines/build-docker-image-api.yaml
  - project: devops/test
    file: /pipelines/sec-scan.yaml
  - project: devops/test
    file: /pipelines/trivy-scan.yaml 
  - project: devops/test
    file: /pipelines/python-tests.yaml
  - project: devops/tools
    file: /pipelines/tag-release-docker-image.yaml
  - project: devops/tools
    file: /pipelines/create-gitlab-release.yaml
  - project: devops/deploy
    file: /pipelines/trigger-deployment.yaml

# docker image:
#   stage: build
#   extends: .common-image-build
#   script:
#     - docker build --build-arg NEXUS_USERNAME=$NEXUS_USERNAME --build-arg NEXUS_PASSWORD=$NEXUS_PASSWORD -t ${DOCKER_IMAGE} .


alembic-check:
  stage: test
  extends: .common-python-test
  needs: ["requirements"]

  variables:
    COMMAND: cd src && python -m alembic upgrade head && python -m alembic check && alembic downgrade 4ae058e615ad
  script:
    - |
      cat > .ci-vars << EOF
      KEYCLOAK_URL=http://keycloak:8080
      KEYCLOAK_REALM=connected-platform
      AUTH_URL_BASE=http://localhost/auth/realms/connected-platform/protocol/openid-connect
      SQLALCHEMY_DATABASE_URI=${DB_SCHEME}://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}
      ORGANIZATIONS_URL=http://localhost/organizations
      EOF

    - docker run ${CONTAINER_OPTIONS} --network ${NETWORK_NAME} --env-file .ci-vars ${TEST_IMAGE} ${RUN_CMD} "${COMMAND}"
  rules:
    - !reference [.python-test-rules, rules]

pytest:
  needs: ["requirements", "alembic-check"]

# securityscan:        
#   stage: test  
#   extends: .common-python-test
#   needs: ["requirements"]
#   variables:
#     COMMAND: bandit -r -f html -o bandit.html -x tests  .
#   script:
#     - docker run ${CONTAINER_OPTIONS} --network ${NETWORK_NAME} ${TEST_IMAGE} ${RUN_CMD} "${COMMAND}"
#   allow_failure: true
#   artifacts:
#     name: monoglass-security-scan
#     expire_in: 1 week
#     paths:
#       - bandit.html
#     when: always
#   interruptible: true
#   rules:
#     - !reference [.python-test-rules, rules]

lint:
  stage: lint
  variables:
    COMMAND: flake8

integration_dev_tests:
   #needs:
   # - lint
  stage: test
  image:
    name: postman/newman:alpine
    entrypoint: [""]
  before_script:
    - newman --version
    - npm install -g newman-reporter-html
    - npm install -g newman-reporter-htmlextra
  script:
    - newman run tests/integration/dev_integration/SPOGAutomation.json -e tests/integration/dev_integration/DevEnvironment.json  --reporters cli,htmlextra --reporter-htmlextra-export ./report-da-dev.html || true
    - newman run tests/integration/dev_integration/SPOGAutomationCA.json -e tests/integration/dev_integration/CADevEnvironment.json --reporters cli,htmlextra --reporter-htmlextra-export ./report-ca-dev.html
  allow_failure: true
  artifacts:
    when: always
    paths:
      - report-da-dev.html
      - report-ca-dev.html
  #when: manual
  rules:
    - if: '$CLUSTER_ENVIRONMENT == "spogdev" && $CI_DEFAULT_BRANCH == "integration-test-mono"'
      when: manual

integration_qa_tests:
  stage: test
  image:
    name: postman/newman:alpine
    entrypoint: [""]
  before_script:
    - newman --version
    - npm install -g newman-reporter-html
    - npm install -g newman-reporter-htmlextra
  script:
    - |
       set -x
       if [ "$simmanagement_qa" == "true" ]; then
         SIM_PREFIX="simmanagement"
         REPORT_DA="report-${SIM_PREFIX}-DA-test.html"
         REPORT_CA="report-${SIM_PREFIX}-CA-test.html"
         newman run tests/integration/qa_integration/SIMManagement_DA.json -e tests/integration/qa_integration/QA_Environment_DA.json --reporters cli,htmlextra --reporter-htmlextra-export ./$REPORT_DA  || true
         newman run tests/integration/qa_integration/SIMManagement_CA.json -e tests/integration/qa_integration/QA_Environment_CA.json --reporters cli,htmlextra --reporter-htmlextra-export ./$REPORT_CA
       fi

       if [ "$accountmanagement_qa" == "true" ]; then
         SIM_PREFIX="accountmanagement"
         REPORT_DA="report-${SIM_PREFIX}-DA-test.html"
         REPORT_CA="report-${SIM_PREFIX}-CA-test.html"
         newman run tests/integration/qa_integration/AccountManagement_DA.json -e tests/integration/qa_integration/QA_Environment_DA.json  --reporters cli,htmlextra --reporter-htmlextra-export ./$REPORT_DA  || true
         newman run tests/integration/qa_integration/AccountManagement_CA.json -e tests/integration/qa_integration/QA_Environment_CA.json  --reporters cli,htmlextra --reporter-htmlextra-export ./$REPORT_CA
       fi

       if [ "$billing_qa" == "true" ]; then
         SIM_PREFIX="billing"
         REPORT_DA="report-${SIM_PREFIX}-DA-test.html"
         REPORT_CA="report-${SIM_PREFIX}-CA-test.html"
         newman run tests/integration/qa_integration/Billing_DA.json -e tests/integration/qa_integration/QA_Environment_DA.json  --reporters cli,htmlextra --reporter-htmlextra-export ./$REPORT_DA  || true
         newman run tests/integration/qa_integration/Billing_CA.json -e tests/integration/qa_integration/QA_Environment_CA.json  --reporters cli,htmlextra --reporter-htmlextra-export ./$REPORT_CA
       fi

       if [ "$rateplans_qa" == "true" ]; then
         SIM_PREFIX="rateplans"
         REPORT_DA="report-${SIM_PREFIX}-DA-test.html"
         REPORT_CA="report-${SIM_PREFIX}-CA-test.html"
         newman run tests/integration/qa_integration/RatePlans_DA.json -e tests/integration/qa_integration/QA_Environment_DA.json --reporters cli,htmlextra --reporter-htmlextra-export ./$REPORT_DA || true
         newman run tests/integration/qa_integration/RatePlans_CA.json -e tests/integration/qa_integration/QA_Environment_CA.json --reporters cli,htmlextra --reporter-htmlextra-export ./$REPORT_CA
       fi

       if [ "$marketsharereport_qa" == "true" ]; then
         SIM_PREFIX="marketsharereport"
         REPORT_DA="report-${SIM_PREFIX}-DA-test.html"
         REPORT_CA="report-${SIM_PREFIX}-CA-test.html"
         newman run tests/integration/qa_integration/MarketShareReport_DA.json -e tests/integration/qa_integration/QA_Environment_DA.json  --reporters cli,htmlextra --reporter-htmlextra-export ./$REPORT_DA  || true
         newman run tests/integration/qa_integration/MarketShareReport_CA.json -e tests/integration/qa_integration/QA_Environment_CA.json  --reporters cli,htmlextra --reporter-htmlextra-export ./$REPORT_CA
       fi

       if [ "$auditlogs_qa" == "true" ]; then
         SIM_PREFIX="auditlogs"
         REPORT_DA="report-${SIM_PREFIX}-DA-test.html"
         REPORT_CA="report-${SIM_PREFIX}-CA-test.html"
         newman run tests/integration/qa_integration/AuditLogs_DA.json -e tests/integration/qa_integration/QA_Environment_DA.json --reporters cli,htmlextra --reporter-htmlextra-export ./$REPORT_DA || true
         newman run tests/integration/qa_integration/AuditLogs_CA.json -e tests/integration/qa_integration/QA_Environment_CA.json --reporters cli,htmlextra --reporter-htmlextra-export ./$REPORT_CA
       fi

       if [ "$automationrule_qa" == "true" ]; then
         SIM_PREFIX="automationrule"
         REPORT_DA="report-${SIM_PREFIX}-DA-test.html"
         REPORT_CA="report-${SIM_PREFIX}-CA-test.html"
         newman run tests/integration/qa_integration/AutomationRule_DA.json -e tests/integration/qa_integration/QA_Environment_DA.json  --reporters cli,htmlextra --reporter-htmlextra-export ./$REPORT_DA || true
         newman run tests/integration/qa_integration/AutomationRule_CA.json -e tests/integration/qa_integration/QA_Environment_CA.json  --reporters cli,htmlextra --reporter-htmlextra-export ./$REPORT_CA
       fi

       if [ "$authorization_qa" == "true" ]; then
         SIM_PREFIX="authorization"
         REPORT_DA="report-${SIM_PREFIX}-DA-test.html"
         REPORT_CA="report-${SIM_PREFIX}-CA-test.html"
         newman run tests/integration/qa_integration/Authorization_DA.json -e tests/integration/qa_integration/QA_Environment_DA.json  --reporters cli,htmlextra --reporter-htmlextra-export  ./$REPORT_DA  || true
         newman run tests/integration/qa_integration/Authorization_CA.json -e tests/integration/qa_integration/QA_Environment_CA.json --reporters cli,htmlextra --reporter-htmlextra-export  ./$REPORT_CA
       fi
  allow_failure: true
  artifacts:
    when: always
    paths:
      - "report-*-DA-test.html"  
      - "report-*-CA-test.html"
  #when: manual
  rules:
    - if: '$CLUSTER_ENVIRONMENT == "spogtest" && $CI_COMMIT_BRANCH == "main"'
      when: manual
isort:
  stage: lint
  extends: .common-python-test
  needs: ["requirements"]
  variables:
    COMMAND: isort --check --diff .
  script:
    - docker run ${CONTAINER_OPTIONS} --network ${NETWORK_NAME} ${TEST_IMAGE} ${RUN_CMD} "${COMMAND}"
  rules:
    - !reference [.python-test-rules, rules]

black:
  stage: lint
  extends: .common-python-test
  needs: ["requirements", "isort"]
  variables:
    COMMAND: black --check --diff .
  script:
    - docker run ${CONTAINER_OPTIONS} --network ${NETWORK_NAME} ${TEST_IMAGE} ${RUN_CMD} "${COMMAND}"
  rules:
    - !reference [.python-test-rules, rules]
