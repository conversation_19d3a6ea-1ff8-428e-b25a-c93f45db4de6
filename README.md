# Mononglass - Single Pane of Glass API

## Local setup actions

### Create the `.env` file:

```shell
cd <path-to-project-directory>
cp example.env src/.env
vim src/.env
# fill in the required variables
```

### Install [poetry](https://python-poetry.org/docs/#installation)

Poetry >= 1.2.0 should be used.

To install poetry you can run:\
`curl -sSL https://install.python-poetry.org | python3 -`

To upgrade poetry:\
`poetry self update`

You can find more detailed information in the official documentation: https://python-poetry.org/docs.

### Configure credentials for private package(Nexus)
Get credentials from the Confluence page: https://nextgenclearing.atlassian.net/wiki/spaces/DEVOPS/pages/446595097/Nexus+Artifact+Repository

And use them in the command:
```shell
poetry config http-basic.nexus <username> <password>
```

### (Optional) configure poetry to use the existing environment

see https://python-poetry.org/docs/managing-environments/

### Activate the virtual environment

Note: poetry will create one if not configured in previous step

```shell
poetry shell
```

### Install dependencies

```shell
make install-all-deps
```

### Install pre-commit

```shell
pre-commit install
```

### Start application

```shell
cd src
export APPLICATION_ENV=local
python -m app.main
```

### Examine documentation

Go to http://localhost:8000/docs

## Running locally with OTEL instrumentation

Before running application via `opentelementry-instrumentation` export next environment variables:

```shell
export OTEL_SERVICE_NAME="partner-apps-api"
export OTEL_PYTHON_LOG_CORRELATION=true

# will not export any data
export OTEL_LOGS_EXPORTER="none"
export OTEL_TRACES_EXPORTER="none"
export OTEL_METRICS_EXPORTER="none"
```
