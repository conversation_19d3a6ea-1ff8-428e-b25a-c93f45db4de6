FROM uk-london-1.ocir.io/lrpfi3ly7ayq/nv2/images/fastapi:3.10-poetry

ARG PIP_INDEX_URL
WORKDIR /app

RUN addgroup --system monoglass && adduser --system --ingroup monoglass monoglass

COPY pyproject.toml poetry.lock ./

ARG NEXUS_USERNAME
# ARG NEXUS_PASSWORD
ARG NEXUS_PW
# ENV POETRY_HTTP_BASIC_NEXUS_USERNAME=$NEXUS_USERNAME
# ENV POETRY_HTTP_BASIC_NEXUS_PASSWORD=$NEXUS_PASSWORD
RUN poetry config http-basic.nexus $NEXUS_USERNAME $NEXUS_PW

ENV POETRY_VIRTUALENVS_CREATE=false
RUN poetry install --no-root

COPY src .
COPY manage.sh .
RUN chmod +x manage.sh

RUN chown -R monoglass:monoglass /app
USER monoglass

ENTRYPOINT ["./manage.sh"]
CMD ["start_service"]
