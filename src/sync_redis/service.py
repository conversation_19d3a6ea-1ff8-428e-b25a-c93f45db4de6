import textwrap
from abc import ABC, abstractmethod
from uuid import UUID

from api.rate_plans.schemas import CreateRatePlanRequest, UpdateRatePlanRequest
from auth.exceptions import NotFound
from common.funcs import convert_to_bytes
from common.types import Unit
from rate_plans.services import AbstractRatePlanService
from sync_redis.adapters.externalapi import AbstractRedisAPI
from sync_redis.adapters.repository import AbstractRepository
from sync_redis.domain import model

DATA_UNITS = [
    Unit.BYTES,
    Unit.KB,
    Unit.MB,
    Unit.GB,
    Unit.TB,
    Unit.PB,
]


class AbstractRedisService(ABC):
    @abstractmethod
    def create_redis_rule(self, account_id: list[int]):
        ...

    @abstractmethod
    def update_redis_rule(self, account_id: list[int], rule_uuid: UUID | None = None):
        ...

    @abstractmethod
    def update_rule_status(
        self,
        rule_status: bool,
        account_id: int | None = None,
        rule_uuid: UUID | None = None,
    ) -> bool:
        ...

    @abstractmethod
    def delete_redis_rule(
        self, account_id: int | None = None, rule_uuid: UUID | None = None
    ):
        ...

    @abstractmethod
    def allocate_sims(self, account_id: int, rate_plan_id: int):
        ...

    @abstractmethod
    def create_redis_rate_plan(
        self,
        rate_plan_id: int,
        rate_plan_create: CreateRatePlanRequest,
        rate_plan_service: AbstractRatePlanService,
    ):
        ...

    @abstractmethod
    def update_redis_rate_plan(
        self,
        rate_plan_id: int,
        rate_plan_update: UpdateRatePlanRequest,
        rate_plan_service: AbstractRatePlanService,
    ):
        ...

    @abstractmethod
    def delete_redis_rate_plan(self, rate_plan_id: int):
        ...


class RedisService(AbstractRedisService):
    def __init__(
        self,
        repository: AbstractRepository,
        redis_api: AbstractRedisAPI,
    ):
        self.repository = repository
        self.redis_api = redis_api

    def create_redis_rule(self, account_id: list[int], rule_uuid: UUID | None = None):
        for acc_id in account_id:
            if not isinstance(acc_id, int):
                raise TypeError(
                    f"Expected account_id to be of type int, but got {type(acc_id)}"
                )
            rules_data = self.repository.get_rules(acc_id, rule_uuid=rule_uuid)
            if not isinstance(rules_data, model.RuleInfo):
                raise TypeError("Expected rules_data to be of type RuleInfo")
            for rule_data in rules_data.rules:
                if not isinstance(rule_data.simUsageLimit, int):
                    raise TypeError(
                        textwrap.dedent(
                            f"""
                        Expected simUsageLimit to be of type int,
                        but got {type(rule_data.simUsageLimit)}
                    """
                        ).strip()
                    )
                if not isinstance(rule_data.unit, Unit):
                    raise TypeError("Expected rule_data.unit to be of type Unit")
                if rule_data.unit in DATA_UNITS:
                    rule_data.sizeInBytes = convert_to_bytes(
                        rule_data.simUsageLimit, rule_data.unit
                    )
                else:
                    rule_data.sizeInBytes = rule_data.simUsageLimit
            self.redis_api.create_redis_rule(rules_data)

    def update_redis_rule(self, account_id: list[int], rule_uuid: UUID | None = None):
        if not isinstance(account_id[0], int):
            raise TypeError(
                f"Expected account_id to be of type int, but got {type(account_id)}"
            )
        if not isinstance(rule_uuid, UUID):
            raise TypeError(
                f"Expected rule_uuid to be of type UUID, but got {type(rule_uuid)}"
            )
        rule_data = self.repository.get_rules(account_id[0], rule_uuid=rule_uuid)
        if not isinstance(rule_data, model.Rules):
            raise TypeError("Expected rule_data to be of type Rules")
        if not isinstance(rule_data.simUsageLimit, int):
            raise TypeError(
                textwrap.dedent(
                    f"""
                Expected simUsageLimit to be of type int,
                but got {type(rule_data.simUsageLimit)}
                """
                ).strip()
            )
        if not isinstance(rule_data.unit, Unit):
            raise TypeError("Expected rule_data.unit to be of type Unit")

        if rule_data.unit in DATA_UNITS:
            rule_data.sizeInBytes = convert_to_bytes(
                rule_data.simUsageLimit, rule_data.unit
            )
        else:
            rule_data.sizeInBytes = rule_data.simUsageLimit
        self.redis_api.update_redis_rule(rule_data, str(rule_uuid))

    def update_rule_status(
        self,
        rule_status: bool,
        account_id: int | None = None,
        rule_uuid: UUID | None = None,
    ) -> bool:
        if not isinstance(rule_uuid, UUID):
            raise TypeError(
                f"Expected rule_uuid to be of type UUID, but got {type(rule_uuid)}"
            )
        if rule_status is False:
            return self.redis_api.update_rule_status(str(rule_uuid))
        else:
            account_id = self.repository.get_get_rule_by_uuid(rule_uuid)
            return self.create_redis_rule([account_id])

    def delete_redis_rule(
        self, account_id: int | None = None, rule_uuid: UUID | None = None
    ):
        if not isinstance(rule_uuid, UUID):
            raise TypeError(
                f"Expected rule_uuid to be of type UUID, but got {type(rule_uuid)}"
            )
        return self.redis_api.delete_redis_rule(str(rule_uuid))

    def allocate_sims(self, account_id: int, rate_plan_id: int):
        imsis = self.repository.get_imsis(account_id, rate_plan_id=rate_plan_id)
        return self.redis_api.allocate_sims(account_id, rate_plan_id, imsis)

    def _apply_rate_model_codes(self, origination_groups: list):
        rate_model_code_mapper = {
            1: "PAYG",
            2: "INDI",
            3: "FIXED",
            4: "FLEXI",
        }
        if origination_groups:
            first_group = origination_groups[0]
            for attr in ["data", "voice_mo", "voice_mt", "sms"]:
                rate_group = getattr(first_group, attr)
                rate_group.model_code = rate_model_code_mapper.get(
                    rate_group.rate_model
                )

    def create_redis_rate_plan(
        self,
        rate_plan_id: int,
        rate_plan_create: CreateRatePlanRequest,
        rate_plan_service: AbstractRatePlanService,
    ):
        rates_data = model.RatePlanInfo(
            accountId=rate_plan_create.account_id,
            name=rate_plan_create.name,
            accessFee=rate_plan_create.access_fee,
            currency=rate_plan_create.currency,
            isDefault=rate_plan_create.is_default,
            simLimit=rate_plan_create.sim_limit,
            allowanceUsed=rate_plan_create.allowance_used,
            originationGroups=rate_plan_create.origination_groups,
            ratePlanId=rate_plan_id,
        )
        self._apply_rate_model_codes(rates_data.originationGroups)
        self.redis_api.create_redis_rate_plan(rates_data)

    def update_redis_rate_plan(
        self,
        rate_plan_id: int,
        rate_plan_update: UpdateRatePlanRequest,
        rate_plan_service: AbstractRatePlanService,
    ):
        rate_plan_request = model.RatePlanRequest(
            accountId=rate_plan_update.account_id,
            name=rate_plan_update.name,
            accessFee=rate_plan_update.access_fee,
            currency=rate_plan_update.currency,
            isDefault=rate_plan_update.is_default,
            simLimit=rate_plan_update.sim_limit,
            allowanceUsed=rate_plan_update.allowance_used,
            originationGroups=rate_plan_update.origination_groups,
            ratePlanId=rate_plan_id,
        )
        self._apply_rate_model_codes(rate_plan_request.originationGroups)
        try:
            self.redis_api.update_redis_rate_plan(rate_plan_request, rate_plan_id)
        except NotFound:
            rates_data = model.RatePlanInfo(
                accountId=rate_plan_update.account_id,
                name=rate_plan_update.name,
                accessFee=rate_plan_update.access_fee,
                currency=rate_plan_update.currency,
                isDefault=rate_plan_update.is_default,
                simLimit=rate_plan_update.sim_limit,
                allowanceUsed=rate_plan_update.allowance_used,
                originationGroups=rate_plan_update.origination_groups,
                ratePlanId=rate_plan_id,
            )
            self._apply_rate_model_codes(rates_data.originationGroups)
            self.redis_api.create_redis_rate_plan(rates_data)

    def delete_redis_rate_plan(self, rate_plan_id: int):
        self.redis_api.delete_redis_rate_plan(rate_plan_id)
