import io
import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Callable, Iterable

from defusedxml.lxml import fromstring
from fastapi import UploadFile
from PIL import Image, UnidentifiedImageError
from pydantic import AnyHttpUrl
from svgutils.transform import SVGFigure

from accounts.adapters.exceptions import (
    AccountDeletionError,
    DigitalIdentityUserExist,
    UserExistsWithBT,
)
from accounts.domain import dto
from accounts.domain.dto import Username
from accounts.domain.exceptions import (
    AccountAlreadyExists,
    AccountDoesNotExist,
    MediaError,
    OrganizationAlreadyExists,
    OrganizationDoesNotExist,
)
from accounts.domain.model import (
    Account,
    AccountSummary,
    Organization,
    SalesChannel,
    SimAccountInfoDetails,
    User,
)
from accounts.domain.ports import (
    AbstractAccountRepository,
    AbstractDigitalIdentity,
    AbstractOrganizationRepository,
    AbstractUserRepository,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    UpdateAccountHandler,
)
from accounts.exceptions import (
    InviteAlreadyAccepted,
    UserAlreadyRegistered,
    UserInvitedToDifferentAccount,
    UserInvitedWithAnotherRole,
    UserNotFound,
)
from api.decorators import measure_execution_time
from app.config import logger
from auth.dto import AuthenticatedUser
from automation.adapters.repository import AbstractAutomationRepository
from common.file_storage import AbstractFileStorage
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from common.types import ContentType, ImageFormat


class MediaService:
    def __init__(self, file_storage: AbstractFileStorage):
        self.file_storage = file_storage

    @staticmethod
    def _parse_svg_image(content: bytes, width: int, height: int) -> bytes:
        fig = SVGFigure()
        fig.root = fromstring(content)
        fig.set_size([str(width), str(height)])
        return fig.to_str()

    @staticmethod
    def _parse_raster_image(content: bytes, width: int, height: int) -> bytes:
        try:
            image = Image.open(io.BytesIO(content))
        except UnidentifiedImageError:
            raise MediaError("Image format not supported.")

        if image.height > height or image.width > width:
            image.thumbnail((width, height), Image.Resampling.LANCZOS)

        image_buf = io.BytesIO()
        image.save(image_buf, format=image.format)
        return image_buf.getvalue()

    def upload_image(
        self,
        file: UploadFile,
        folder_path: str,
        max_width: int = 100,
        max_height: int = 100,
    ) -> str:
        content_type = ContentType.parse(file.content_type)
        if content_type.tail not in ImageFormat.__dict__.values():
            raise MediaError(
                f"Bad content type: '{file.content_type}', expected "
                f"'image/png or image/svg'."
            )
        parser = (
            self._parse_svg_image
            if content_type.mime_type == "image/svg+xml"
            else self._parse_raster_image
        )
        file_obj = parser(file.file.read(), max_width, max_height)
        image_key = f"{folder_path}/{uuid.uuid4().hex}{content_type.extension}"
        self.file_storage.upload_file(
            filename=image_key,
            file_obj=file_obj,
            content_type=content_type.mime_type,
        )
        return image_key

    def get_file_url(self, file_key: str) -> AnyHttpUrl:
        url = self.file_storage.generate_file_url(file_key)
        return AnyHttpUrl(url, scheme="https")

    def check_if_file_exits(self, file_key: str) -> None:
        if not self.file_storage.file_exists(file_key):
            raise MediaError(f"File not found: {file_key}.")


class AbstractAccountService(
    CreateAccountHandler,
    UpdateAccountHandler,
    DeleteAccountHandler,
    SyncOrganizationsHandler,
    AccountView,
    ABC,
):
    ...


class AccountService(AbstractAccountService):
    def __init__(
        self,
        user: AuthenticatedUser,
        account_repository: AbstractAccountRepository,
        organization_repository: AbstractOrganizationRepository,
        media_service: MediaService,
        automation_repository: AbstractAutomationRepository,
    ):
        self.user = user
        self.accounts = account_repository
        self.organizations = organization_repository
        self.media = media_service
        self.automation_repository = automation_repository

    def _validate_account(self, account: dto.CreateAccount | dto.UpdateAccount) -> None:
        if account.logo_key is not None:
            self.media.check_if_file_exits(account.logo_key)

        if account.is_billable and account.sales_channel is SalesChannel.TRIAL:
            raise ValueError(
                "An Account with the TRIAL sales channel should not be charged."
            )

    def create_account(self, account: dto.CreateAccount) -> int:
        try:
            self._validate_account(account)
            organization_id = self.organizations.add_client(
                parent_id=self.user.organization.id,
                client=Organization(name=account.name),
            )
            account_model = account.to_model(organization_id)
            self.accounts.add(account_model)
        except OrganizationAlreadyExists:
            raise AccountAlreadyExists()
        return account_model.id

    def get(self, account_id: int) -> dto.Account:
        account = self._get_account_model(account_id)
        return dto.Account.from_model(account, self._get_logo_url(account.logo_key))

    def account_count(
        self, account_ids: list[int] | None = None, searching: Searching | None = None
    ) -> int:
        return self.accounts.account_count(account_ids, searching=searching)

    @measure_execution_time
    def list(
        self,
        account_ids: list[int] | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> Iterable[dto.GetAccount]:
        accounts = self.accounts.query(
            account_ids, ordering, searching, pagination=pagination
        )
        response = (
            dto.GetAccount.from_model_get_acc(a, self._get_logo_url(a.logo_key))
            for a in accounts
        )
        return response

    def update(self, account_id: int, update_account: dto.UpdateAccount) -> dto.Account:
        self._validate_account(update_account)
        account = self._get_account_model(account_id)
        account = update_account.merge_model(account)
        organization = self.organizations.get(account.organization_id)
        if organization is not None:
            try:
                organization.name = account.name
                _ = self.organizations.update(organization)
            except OrganizationAlreadyExists:
                raise AccountAlreadyExists()
            except OrganizationDoesNotExist:
                raise AccountDoesNotExist()
        self.accounts.update(account)
        return dto.Account.from_model(account, self._get_logo_url(account.logo_key))

    def delete(self, account_id: int) -> None:
        account = self._get_account_model(account_id)
        account_rules_count = self.automation_repository.count_rules_by_account(
            id=account.id
        )
        if account_rules_count:
            raise AccountDeletionError(
                "Account removal failed: active rules are"
                " associated with it. Please resolve them first."
            )
        try:
            self.organizations.delete(account.organization_id)
        except OrganizationDoesNotExist:
            pass
        self.accounts.delete(account)

    def get_billing_settings(self, account_id: int) -> dto.BillingSettings:
        account = self._get_account_model(account_id)
        return dto.BillingSettings.from_model(account)

    def set_billing_settings(
        self, account_id: int, billing_settings: dto.BillingSettings
    ) -> dto.BillingSettings:
        account = self._get_account_model(account_id)
        account.sim_charge = billing_settings.sim_charge
        account.payment_terms = billing_settings.payment_terms
        self.accounts.update(account)
        return dto.BillingSettings.from_model(account)

    def sync_accounts_with_organizations(
        self, root_organization_name: str, log: Callable[[str], None] = print
    ) -> None:
        log(f"Synchronization started for root organization: {root_organization_name}")
        root_organization = self.organizations.get_by_name(root_organization_name)
        if not root_organization:
            raise RuntimeError(
                f'Parent organization "{root_organization_name}" not found.'
            )
        parent_id = root_organization.id
        if not parent_id:
            raise AssertionError("Saved organization should have an ID")
        for account in self.accounts.query():
            log(f"processing Account {account.name}")
            organization = self.organizations.get_by_name(account.name)
            if organization:
                log(f"found organization with ID {organization.id}")
                organization_id = organization.id
            else:
                organization_id = self.organizations.add_client(
                    parent_id, Organization(name=account.name)
                )
                log(f"created organization with ID {organization_id}")

            if not organization_id:
                raise AssertionError("Expected filled Organization ID")
            account.organization_id = organization_id
            self.accounts.add(account)
            log(f"Account {account.name} synchronized")

    def _get_account_model(self, account_id: int) -> Account:
        account = self.accounts.get(account_id)
        if account is None:
            raise AccountDoesNotExist()
        return account

    def _get_logo_url(self, logo_key: str | None) -> AnyHttpUrl | None:
        if logo_key is None:
            return logo_key
        return self.media.get_file_url(logo_key)

    def get_account_names(self) -> Iterable[dto.AccountNames]:
        return self.accounts.get_account_names()

    def get_account_by_organization_id(self, organization_id: int):
        account = self.accounts.get_by_organization_id(organization_id)
        if account is None:
            raise OrganizationDoesNotExist
        return self.accounts.get_by_organization_id(organization_id)

    def get_sim_account_info(self, search: str) -> SimAccountInfoDetails:
        response = self.accounts.get_sim_account_info(search=search)
        response.account_logo_url = self._get_logo_url(response.account_logo_key)
        return response

    def get_account_summary(self, account_id: int) -> AccountSummary:
        result = self.accounts.get_account_summary(account_id=account_id)
        return result


class AbstractUserService(ABC):
    @abstractmethod
    def invite_user(self, account: dto.Account, user_invite: dto.UserInvite) -> str:
        ...

    @abstractmethod
    def accept_user_invite(self, event: dto.SAFEvent) -> None:
        ...


class UserService(AbstractUserService):
    def __init__(
        self,
        account_repository: AbstractAccountRepository,
        organization_repository: AbstractOrganizationRepository,
        user_repository: AbstractUserRepository,
        digital_identity: AbstractDigitalIdentity,
    ):
        self.account_repository = account_repository
        self.organization_repository = organization_repository
        self.user_repository = user_repository
        self.digital_identity = digital_identity

    def invite_user(self, account: dto.Account, invite_data: dto.UserInvite) -> str:
        logger.info(f"User_invite_data  : {invite_data} Account_Details : {account}")
        user = self.user_repository.get_by_email(invite_data.email)
        logger.info(f"User_Details : {user}")
        if user is None:
            user_id = self.organization_repository.create_organization_member(
                organization_id=account.organization_id,
                user_invite=invite_data,
            )
            user = User(
                id=user_id,
                email=invite_data.email,
                account_id=account.id,
                role=invite_data.role,
            )
            logger.info(f"User_Details : {user}")
        if account.id != user.account_id:
            raise UserInvitedToDifferentAccount(user.id, user.account_id)

        if user.registered:
            logger.info(f"User_already_registered : {user.email}")
            raise UserAlreadyRegistered(user.id)

        if invite_data.role != user.role:
            logger.info(f"Invite_Data : {invite_data}")
            raise UserInvitedWithAnotherRole(user.role)

        invite_id = user.create_invite()
        try:
            logger.info(f"send_invite_id  : {invite_id}")
            saf_invite_id = self.digital_identity.send_invite(
                reference_id=invite_id,
                email=user.email,
            )
        except DigitalIdentityUserExist as e:
            user.set_saf_identity(saf_id=e.user_id, registration_ts=datetime.now())
            self.user_repository.add(user)
            raise UserAlreadyRegistered(user.id)
        except UserExistsWithBT as e:
            logger.info(f"UserExistsWithBT : {e.user_id}")
            logger.info(f"UserExists : {user}")
            self.user_repository.add(user)
            return str(e.user_id)

        user.confirm_invite_sending(invite_id, saf_invite_id)
        self.user_repository.add(user)
        return str(invite_id)

    def accept_user_invite(self, event: dto.SAFEvent) -> None:
        logger.critical(f"**EVENT passed details:- {event} ")
        user = self.user_repository.get_by_email(event.email)
        logger.critical(f"**Get User details:- {user} ")
        if user is None:
            raise UserNotFound
        elif user.registered:
            raise InviteAlreadyAccepted

        user.set_saf_identity(
            saf_id=event.user_id,
            registration_ts=event.registration_ts,
            reference_id=event.reference_id,
        )
        account = self.account_repository.get(user.account_id)
        if not account:
            raise AccountDoesNotExist

        logger.critical(f"**Account details:- {account} ")

        org = self.organization_repository.get_by_name(account.name)
        if not org:
            raise AccountDoesNotExist
        if not org.id:
            raise AccountDoesNotExist
        logger.critical(f"**Organization details:- {org} ")
        self.organization_repository.update_organization_member_username(
            organization_id=org.id,
            user_id=user.id,
            username=Username(user.saf_id),
        )
        logger.critical(f"**User details:- {user} ")
        self.user_repository.add(user)
