from typing import Callable, Iterable

from accounts.domain import dto
from accounts.domain.model import Account<PERSON><PERSON><PERSON>y, SimAccountInfoDetails
from accounts.services import AbstractAccountService, AbstractUserService
from auth.dto import AuthenticatedUser, Service
from auth.exceptions import ForbiddenError, NotFound
from auth.permissions import (
    is_account_admin,
    is_account_member,
    is_authorized_service,
    is_distributor_admin,
    is_distributor_client,
    is_distributor_staff,
    is_org_member,
)
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching


class AccountServiceAuthProxy(AbstractAccountService):
    """Authorization version of the account service"""

    def __init__(
        self, account_service: AbstractAccountService, user: AuthenticatedUser
    ):
        self.account_service = account_service
        self.user = user

    def create_account(self, account: dto.CreateAccount) -> int:
        if is_distributor_staff(self.user):
            pass
        else:
            raise ForbiddenError

        return self.account_service.create_account(account)

    def update(self, account_id: int, account: dto.UpdateAccount) -> dto.Account:
        if is_distributor_staff(self.user):
            pass
        else:
            raise ForbiddenError

        return self.account_service.update(account_id, account)

    def delete(self, account_id: int) -> None:
        if is_distributor_staff(self.user):
            pass
        else:
            raise ForbiddenError

        return self.account_service.delete(account_id)

    def get(self, account_id: int) -> dto.Account:
        if is_distributor_staff(self.user):
            pass
        elif is_account_member(self.user, account_id):
            pass
        else:
            raise NotFound

        return self.account_service.get(account_id)

    def set_billing_settings(
        self, account_id: int, billing_settings: dto.BillingSettings
    ) -> dto.BillingSettings:
        if is_distributor_staff(self.user):
            pass
        else:
            raise ForbiddenError

        return self.account_service.set_billing_settings(account_id, billing_settings)

    def get_billing_settings(self, account_id: int) -> dto.BillingSettings:
        if is_distributor_staff(self.user):
            pass
        else:
            raise ForbiddenError

        return self.account_service.get_billing_settings(account_id)

    def account_count(
        self, account_ids: list[int] | None = None, searching: Searching | None = None
    ) -> int:
        if is_distributor_staff(self.user):
            pass
        else:
            raise ForbiddenError
        return self.account_service.account_count(account_ids, searching=searching)

    def list(
        self,
        account_ids: list[int] | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> Iterable[dto.GetAccount]:
        if is_distributor_staff(self.user):
            pass
        elif (
            is_account_member(self.user, account_ids[0])
            if account_ids is not None
            else False
        ):
            pass
        else:
            raise ForbiddenError

        return self.account_service.list(
            account_ids, ordering, searching, pagination=pagination
        )

    def sync_accounts_with_organizations(
        self, root_organization_name: str, log: Callable[[str], None] = print
    ) -> None:
        raise NotImplementedError(
            "Method can be called only as CLI command, without authorization."
        )

    def get_account_names(self) -> Iterable[dto.AccountNames]:
        return self.account_service.get_account_names()

    def get_account_by_organization_id(self, organization_id: int):
        if is_distributor_staff(self.user):
            pass
        elif is_org_member(self.user, organization_id):
            pass
        else:
            raise NotFound

        return self.account_service.get_account_by_organization_id(organization_id)

    def get_sim_account_info(self, search: str) -> SimAccountInfoDetails:
        return self.account_service.get_sim_account_info(search=search)

    def get_account_summary(self, account_id: int) -> AccountSummary:
        if is_distributor_staff(self.user):
            pass
        elif is_distributor_client(self.user):
            if account_id != self.user.organization.account.id:  # type: ignore
                raise ForbiddenError
        else:
            raise ForbiddenError
        return self.account_service.get_account_summary(account_id=account_id)


class UserServiceAuthProxy(AbstractUserService):
    def __init__(
        self, user_service: AbstractUserService, actor: AuthenticatedUser | Service
    ):
        self.user_service = user_service
        self.actor = actor

    def invite_user(self, account: dto.Account, user_invite: dto.UserInvite) -> str:
        if is_authorized_service(self.actor):
            raise ForbiddenError
        elif is_distributor_staff(self.actor):
            pass
        elif (
            is_account_admin(self.actor, account.id)  # type: ignore
            and user_invite.role == "User"
        ):
            pass
        else:
            raise ForbiddenError
        return self.user_service.invite_user(account, user_invite)

    def accept_user_invite(self, event: dto.SAFEvent) -> None:
        if is_authorized_service(self.actor) or is_distributor_admin(self.actor):
            return self.user_service.accept_user_invite(event)
        else:
            raise ForbiddenError
