from uuid import UUID

from accounts.domain.model import AccountRole
from app.exceptions import Conflict


class InviteExist(Conflict):
    client_message = "Invite has already been sent."


class UserAlreadyRegistered(Conflict):
    client_message = "User: {} already registered."

    def __init__(self, user_id: UUID) -> None:
        self.client_message = self.client_message.format(user_id)


class UserInvitedToDifferentAccount(Conflict):
    client_message = "This email has already been taken."
    debug_message = "User: {} already invited by another account: {}."

    def __init__(self, user_id: UUID, account_id: int) -> None:
        self.debug_message = self.debug_message.format(user_id, account_id)


class UserInvitedWithAnotherRole(Conflict):
    client_message = "User has already been invited with the role: {}."

    def __init__(self, role: AccountRole) -> None:
        self.client_message = self.client_message.format(role)


class InviteAlreadyAccepted(Exception):
    ...


class UserNotFound(Exception):
    ...
