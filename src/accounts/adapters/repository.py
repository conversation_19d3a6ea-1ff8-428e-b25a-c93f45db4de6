import logging
import os
import uuid
from typing import Iterable, TypedDict, cast
from uuid import UUID

from fastapi.encoders import jsonable_encoder
from more_itertools import first, first_true
from platform_api_client import PlatformAPIClient, PlatformAPIError
from pydantic import EmailStr
from sqlalchemy import asc, func, select, text
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session
from sqlalchemy.sql.functions import count
from starlette import status

from accounts.adapters import orm
from accounts.adapters.exceptions import (
    AccountDeletionError,
    DigitalIdentityUserExist,
    UserExistsWithBT,
)
from accounts.domain import dto, model
from accounts.domain.exceptions import (
    OrganizationAlreadyExists,
    OrganizationDoesNotExist,
    SimAccountDataNotFound,
    UserNotFound,
)
from accounts.domain.model import AccountSummary, Organization, User
from accounts.domain.ports import (
    AbstractAccountRepository,
    AbstractDigitalIdentity,
    AbstractOrganizationRepository,
    AbstractUserRepository,
)
from app.config import logger, settings
from auth.exceptions import ConflictException, ForbiddenError
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching, apply_search_to_sql
from common.types import SimStatus
from openapi.domain.model import TokenResponse


class InMemoryAccountRepository(AbstractAccountRepository):
    def __init__(self):
        self.accounts: dict[int, model.GetAccount] = {}

    def add(self, account: model.Account) -> int:
        id_ = len(self.accounts) + 1
        account.id = id_
        self.accounts[id_] = account  # type: ignore
        return id_

    def get(self, account_id: int) -> model.Account | None:
        return self.accounts.get(account_id)

    def get_by_organization_id(self, organization_id: int) -> model.Account | None:
        return first_true(
            self.accounts.values(),
            pred=lambda a: a.organization_id == organization_id,
        )

    def update(self, account: model.Account) -> None:
        self.accounts[account.id] = account  # type: ignore

    def query(
        self,
        account_ids: list[int] | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> Iterable[model.GetAccount]:
        accounts = list(self.accounts.values())

        if account_ids is not None:
            return (a for a in accounts if a.id in account_ids)
        return accounts

    def delete(self, account: model.Account) -> None:
        del self.accounts[account.id]

    def account_count(
        self, account_ids: list[int] | None = None, searching: Searching | None = None
    ) -> int:
        accounts = list(self.accounts.values())
        if account_ids is not None:
            return len(accounts)
        return len(accounts)

    def get_account_names(self) -> Iterable[dto.AccountNames]:
        return self.get_account_names()

    def get_sim_account_info(self, search: str) -> model.SimAccountInfoDetails:
        ...

    def get_account_summary(self, account_id: int) -> AccountSummary:
        ...


class DatabaseAccountRepository(AbstractAccountRepository):
    def __init__(self, session: Session):
        self.session = session

    def add(self, account: model.Account) -> int:
        self.session.add(account)
        self.session.flush()
        account_traffic_thresholds = model.AccountTrafficThresholds(
            account_id=account.id,
            carrier_name="EE",
            warning_threshold=account.warning_threshold,
        )
        account_traffic_charges = model.AccountTrafficCharges(
            account_id=account.id,
            carrier_name="EE",
            threshold_charge=account.threshold_charge,
        )
        self.session.add(account_traffic_thresholds)
        self.session.add(account_traffic_charges)
        self.session.commit()
        self.session.refresh(account)
        self.session.refresh(account_traffic_thresholds)
        self.session.refresh(account_traffic_charges)
        return account.id

    def get(self, account_id: int) -> model.Account | None:
        account_result = (
            self.session.query(
                model.Account,
                model.AccountTrafficThresholds,
                model.AccountTrafficCharges,
            )
            .select_from(model.Account)
            .join(
                model.AccountTrafficThresholds,
                model.Account.id == model.AccountTrafficThresholds.account_id,
            )
            .join(
                model.AccountTrafficCharges,
                model.Account.id == model.AccountTrafficCharges.account_id,
            )
            .filter(model.Account.id == account_id)
            .one_or_none()
        )
        if account_result is not None:
            account_result, traffic_threshold, threshold_charge = account_result
            account_result.warning_threshold = traffic_threshold.warning_threshold
            account_result.threshold_charge = threshold_charge.threshold_charge
            account_result.carrier_name = threshold_charge.carrier_name
        return account_result

    def get_by_organization_id(self, organization_id: int) -> model.Account | None:
        return (
            self.session.query(model.Account)
            .filter_by(organization_id=organization_id)
            .one_or_none()
        )

    def update(self, account: model.Account) -> None:
        self.session.merge(account)
        threshold_warning = {
            model.AccountTrafficThresholds.warning_threshold: account.warning_threshold
        }
        threshold_charge = {
            model.AccountTrafficCharges.threshold_charge: account.threshold_charge
        }
        self.session.query(model.AccountTrafficThresholds).filter(
            model.AccountTrafficThresholds.account_id == account.id
        ).update(threshold_warning)
        self.session.query(model.AccountTrafficCharges).filter(
            model.AccountTrafficCharges.account_id == account.id
        ).update(threshold_charge)
        self.session.commit()

    def query(
        self,
        account_ids: list[int] | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> Iterable[model.GetAccount]:
        order_by_column = ordering.field if ordering else "name"
        order_direction = ordering.order.upper() if ordering else "ASC"
        params = {
            "search_term": searching.search if searching else None,
            "account_id_param": account_ids,
            "order_by_column": order_by_column,
            "order_direction": order_direction,
            "page_offset": pagination.offset if pagination else None,
            "page_size": pagination.page_size if pagination else None,
        }

        query = text(  # nosem
            """
            SELECT * FROM get_account_details(
                :search_term,
                :account_id_param,
                :order_by_column,
                :order_direction,
                :page_offset,
                :page_size
            )
            """
        )

        return self.session.execute(query, params).fetchall()  # type: ignore

    def account_count(
        self, account_ids: list[int] | None = None, searching: Searching | None = None
    ) -> int:
        query = select(count(model.Account.id))
        if account_ids is not None:
            query = query.where(orm.account.c.id.in_(account_ids))
        if searching is not None:
            query = apply_search_to_sql(searching, model.Account, query)
        return self.session.execute(query).scalar_one()

    def delete(self, account: model.Account) -> None:
        try:
            self.session.delete(account)
            self.session.commit()
        except IntegrityError as e:
            logging.error(f"Cannot delete account: {e}")
            raise AccountDeletionError(
                ("The account could not be deleted." "The account has an allocations.")
            )

    def get_account_names(self) -> Iterable[dto.AccountNames]:
        account = model.Account
        query = select(account.id, account.name, account.organization_id).order_by(
            asc(account.name)
        )
        for row in self.session.execute(query).mappings():
            yield dto.AccountNames(**row)

    def get_sim_account_info(self, search: str) -> model.SimAccountInfoDetails:

        sim_info = func.search_info(search)
        query = select("*").select_from(sim_info)
        account_info = self.session.execute(query).all()

        if len(account_info) == 0:
            raise SimAccountDataNotFound(
                f"No Sim data found while searching with '{search}'"
            )
        account_search = model.SimAccountInfoDetails(
            imsi=account_info[0].imsi,
            iccid=account_info[0].iccid,
            msisdn=account_info[0].msisdn,
            sim_status=SimStatus[account_info[0].sim_status],
            account_id=account_info[0].account_id,
            account_name=account_info[0].account_name,
            account_logo_key=account_info[0].account_logo_key,
        )
        return account_search

    def get_account_summary(self, account_id: int) -> AccountSummary:
        account_summary = func.get_account_summary_count(account_id)

        account_summary_details = select("*").select_from(account_summary)
        result = self.session.execute(account_summary_details).one()

        return model.AccountSummary(**result)  # type: ignore


class InMemoryOrganizationRepository(AbstractOrganizationRepository):
    def __init__(self):
        self.organizations: dict[int, Organization] = {}
        self.users: dict[tuple[int, UUID], dto.AccountUser] = {}

    def add_client(self, parent_id: int, client: Organization) -> int:
        for o in self.organizations.values():
            if o.name == client.name:
                raise OrganizationAlreadyExists()
        client.id = len(self.organizations) + 1
        self.organizations[client.id] = client
        return client.id

    def get(self, organization_id: int) -> Organization | None:
        return self.organizations.get(organization_id, None)

    def get_by_name(self, name: str) -> Organization | None:
        return next((o for o in self.organizations.values() if o.name == name), None)

    def update(self, client: Organization) -> Organization:
        if client.id is None:
            raise AssertionError("Expected ID to update the client")
        if client.id not in self.organizations:
            raise OrganizationDoesNotExist()
        for o in self.organizations.values():
            if o.name == client.name and o.id != client.id:
                raise OrganizationAlreadyExists()
        self.organizations[client.id] = client
        return client

    def delete(self, organization_id: int) -> None:
        try:
            del self.organizations[organization_id]
        except KeyError:
            raise OrganizationDoesNotExist()

    def create_organization_member(
        self, organization_id: int, user_invite: dto.UserInvite
    ) -> UUID:
        logger.info(f"organization_id : {organization_id}")
        logger.info(f"user_invite : {user_invite}")
        user_id = uuid.uuid4()
        logger.info(f"user_id : {user_id}")
        self.users[(organization_id, user_id)] = dto.AccountUser(
            id=user_id,
            email=user_invite.email,
            username=dto.Username(user_invite.email),
            first_name=user_invite.first_name,
            last_name=user_invite.last_name,
            role=user_invite.role,
        )
        return user_id

    def get_organization_member(
        self, organization_id: int, user_id: UUID
    ) -> dto.AccountUser | None:
        return self.users.get((organization_id, user_id))

    def update_organization_member_username(
        self, organization_id: int, user_id: UUID, username: dto.Username
    ) -> None:
        user = self.users.get((organization_id, user_id))
        if user is None:
            raise UserNotFound(organization_id, user_id)

        user.username = username

    def get_access_token(
        self,
    ) -> TokenResponse:
        ...


class RoleNotSupported(Exception):
    ...


class OrganizationUser(TypedDict):
    id: UUID
    email: EmailStr
    username: dto.Username
    firstName: str | None
    lastName: str | None
    roles: list[str]
    enabled: bool
    emailVerified: bool


class HTTPOrganizationRepository(AbstractOrganizationRepository):
    ORGANIZATIONS_URL = "/v1/organizations/"
    ORGANIZATION_URL = "/v1/organizations/{organization_id}"
    ORGANIZATION_USERS_URL = "/v1/organizations/{organization_id}/users"
    ORGANIZATION_USER_URL = "/v1/organizations/{organization_id}/users/{user_id}"
    CREATE_CLIENT_URL = "/v1/organizations/{organization_id}/clients"
    UPDATE_CLIENT_URL = "/v1/organizations/clients/{organization_id}"

    ACCESS_TOKEN_URL = os.getenv("ACCESS_TOKEN_URL", "/v1/organizations/api/token")

    def __init__(self, api_client: PlatformAPIClient):
        self.api = api_client

    def add_client(self, parent_id: int, client: Organization) -> int:
        url = self.CREATE_CLIENT_URL.format(organization_id=parent_id)
        try:
            payload = jsonable_encoder(client, exclude_none=True)
            response = self.api.post(url, json=payload)
            organization = self.api.parse_payload(response, Organization)
        except PlatformAPIError as e:
            match e.status_code:
                case status.HTTP_409_CONFLICT:
                    raise OrganizationAlreadyExists()
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    raise e
        else:
            if organization.id is None:
                raise AssertionError("Created organization must have an ID.")
            return organization.id

    def get(self, organization_id: int) -> Organization | None:
        url = self.ORGANIZATION_URL.format(organization_id=organization_id)
        try:
            response = self.api.get(url)
            organization = self.api.parse_payload(response, Organization)
        except PlatformAPIError as e:
            match e.status_code:
                case status.HTTP_404_NOT_FOUND:
                    return None
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    raise e
        else:
            return organization

    def get_by_name(self, name: str) -> Organization | None:
        url = self.ORGANIZATIONS_URL
        try:
            response = self.api.get(url)
            organizations = self.api.parse_payload(response, list[Organization])
        except PlatformAPIError as e:
            match e.status_code:
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    raise e
        else:
            return next((o for o in organizations if o.name == name), None)

    def update(self, client: Organization) -> Organization:
        if client.id is None:
            raise AssertionError("Expected ID to update the client")
        url = self.UPDATE_CLIENT_URL.format(organization_id=client.id)
        try:
            payload = jsonable_encoder(client, include={"name"})
            response = self.api.put(url, json=payload)
            updated_client = self.api.parse_payload(response, Organization)
        except PlatformAPIError as e:
            match e.status_code:
                case status.HTTP_409_CONFLICT:
                    raise OrganizationAlreadyExists()
                case status.HTTP_404_NOT_FOUND:
                    raise OrganizationDoesNotExist(client.id)
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    raise e
        else:
            if updated_client.id is None:
                raise AssertionError("Updated client must have an ID.")
            return updated_client

    def delete(self, organization_id: int) -> None:
        url = self.ORGANIZATION_URL.format(organization_id=organization_id)
        try:
            self.api.delete(url)
        except PlatformAPIError as e:
            match e.status_code:
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case status.HTTP_404_NOT_FOUND:
                    raise OrganizationDoesNotExist(organization_id)
                case _:
                    raise e

    def create_organization_member(
        self, organization_id: int, user_invite: dto.UserInvite
    ) -> UUID:
        logger.info(f"COM_ORG_ID : {organization_id}")
        url = self.ORGANIZATION_USERS_URL.format(organization_id=organization_id)
        data = dict(
            email=user_invite.email,
            firstName=user_invite.first_name,
            lastName=user_invite.last_name,
            role=self._account_role_to_org_role(user_invite.role),
            enabled=True,
            emailVerified=True,
        )
        payload = jsonable_encoder(data)
        logger.info(f"payload : {payload}")
        logger.info(f"org_url : {url}")
        try:
            response = self.api.post(url, json=payload)
            logger.info(f"response : {response}")
            created_user = self.api.parse_payload(response, OrganizationUser)
        except PlatformAPIError as e:
            logger.error(f"PlatformAPIError_E : {str(e)}")
            logger.info(f"PlatformAPIError_status : {e.status_code}")
            match e.status_code:
                case status.HTTP_409_CONFLICT:
                    logger.error(f"409APIError : {user_invite.email}")
                    raise ConflictException(user_invite.email)
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    raise e
        else:
            return created_user["id"]

    def get_organization_member(
        self, organization_id: int, user_id: UUID
    ) -> dto.AccountUser | None:
        url = self.ORGANIZATION_USER_URL.format(
            organization_id=organization_id,
            user_id=user_id,
        )
        try:
            response = self.api.get(url)
            org_user = self.api.parse_payload(response, OrganizationUser)
        except PlatformAPIError as e:
            match e.status_code:
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case status.HTTP_404_NOT_FOUND:
                    return None
                case _:
                    raise e

        try:
            return self._org_user_to_account_user(org_user)
        except RoleNotSupported:
            return None

    def update_organization_member_username(
        self, organization_id: int, user_id: UUID, username: dto.Username
    ) -> None:
        url = self.ORGANIZATION_USER_URL.format(
            organization_id=organization_id, user_id=user_id
        )
        data = dict(
            username=username,
        )
        payload = jsonable_encoder(data)
        try:
            self.api.patch(url, json=payload)
        except PlatformAPIError as e:
            match e.status_code:
                case status.HTTP_404_NOT_FOUND:
                    raise UserNotFound(organization_id, user_id)
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    raise e

    def get_access_token(
        self,
    ) -> TokenResponse:
        url = self.ACCESS_TOKEN_URL
        try:
            logger.debug("Org api/token api call")
            response_data = self.api.get(url)
            logger.info(f"Org api/token response : {response_data}")
        except PlatformAPIError as e:
            logger.error(f"PlatformAPIError_E : {str(e)}")
            raise e
        response = response_data.json()
        return TokenResponse(**response)

    @classmethod
    def _org_user_to_account_user(cls, org_user: "OrganizationUser") -> dto.AccountUser:
        account_roles = map(cls._org_role_to_account_role, org_user["roles"])
        # ["User", "Admin"] -> "Admin"
        # ["User"] -> "User"
        roles_by_priority = sorted(
            account_roles, key=lambda r: r == "Admin", reverse=True
        )
        role = first(roles_by_priority)
        return dto.AccountUser(
            id=org_user["id"],
            username=org_user["username"],
            email=org_user["email"],
            first_name=org_user["firstName"],
            last_name=org_user["lastName"],
            role=role,
        )

    @classmethod
    def _org_role_to_account_role(cls, org_role: str) -> model.AccountRole:
        client_org_roles = {
            "ClientAdmin": "Admin",
            "ClientUser": "User",
        }
        try:
            account_role = client_org_roles[org_role]
        except KeyError:
            raise RoleNotSupported(org_role)
        return cast(model.AccountRole, account_role)

    @classmethod
    def _account_role_to_org_role(cls, account_role: str) -> str:
        return {
            "Admin": "ClientAdmin",
            "User": "ClientUser",
        }[account_role]


class InMemoryUserRepository(AbstractUserRepository):
    def __init__(self, users: list[User] | None = None):
        self.users = users or []

    def add(self, user: User) -> None:
        for existing_user in list(self.users):
            if existing_user.email == user.email:
                self.users.remove(existing_user)
        self.users.append(user)

    def get_by_email(self, email: str) -> User | None:
        return first_true(self.users, pred=lambda i: i.email == email)


class DatabaseUserRepository(AbstractUserRepository):
    def __init__(self, session: Session):
        self.session = session

    def add(self, user: User) -> None:
        self.session.add(user)
        self.session.commit()

    def get_by_email(self, email: str) -> User | None:
        return self.session.query(User).filter_by(email=email).one_or_none()


class InMemoryDigitalIdentity(AbstractDigitalIdentity):
    def __init__(self) -> None:
        self.invites: dict[EmailStr, tuple[str, str]] = {}

    def send_invite(self, reference_id: UUID, email: EmailStr) -> str:
        existing_user = self.invites.get(email, None)
        if existing_user is not None:
            raise DigitalIdentityUserExist(existing_user[1])
        invite_id = str(uuid.uuid4())
        self.invites[email] = (invite_id, str(reference_id))
        return invite_id


class HTTPDigitalIdentity(AbstractDigitalIdentity):
    CREATE_USER_URL = "/v2/digital-identity/assisted"

    class UserAddedResponse(TypedDict):
        inviteId: str

    class UserExistsResponse(TypedDict):
        objectId: str
        responseType: str

    def __init__(self, api_client: PlatformAPIClient):
        self.api = api_client

    def send_invite(self, reference_id: UUID, email: EmailStr) -> str:
        data = dict(
            referenceId=str(reference_id),
            email=email,
            validationDataType="NOTSET",
            brand="BT",
            serviceName="BT IoT Portal",
            journeyType="CREATE",
            redirectUri=settings.LANDING_PAGE_URL,
        )
        payload = jsonable_encoder(data)
        headers = {"APIGW-Tracking-Header": str(uuid.uuid4())}
        try:
            response = self.api.post(
                self.CREATE_USER_URL, json=payload, headers=headers
            )
            created_user = self.api.parse_payload(response, self.UserAddedResponse)
            logger.info(f"User_response {response.json()}")
            return created_user["inviteId"]
        except PlatformAPIError as e:
            result_json = response.json()
            match e.status_code:
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError
                case status.HTTP_409_CONFLICT:
                    logger.info(f"result_json_objectId {result_json['objectId']}")
                    raise UserExistsWithBT(result_json["objectId"])
                case status.HTTP_200_OK:
                    # TODO:  need to refactor this code
                    # existing_user = self.api.parse_payload(
                    #     response, self.UserExistsResponse
                    # )
                    # raise DigitalIdentityUserExist(result_json["objectId"])
                    logger.info(f"result_json_objectId {result_json['objectId'][0]}")
                    raise UserExistsWithBT(result_json["objectId"][0])
                case _:
                    raise e
