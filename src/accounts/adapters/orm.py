from sqlalchemy import (
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Column,
    Date,
    DateTime,
    Enum,
    Foreign<PERSON>ey,
    Identity,
    Integer,
    Numeric,
    String,
    Table,
)
from sqlalchemy.dialects import postgresql
from sqlalchemy.ext.associationproxy import association_proxy
from sqlalchemy.orm import relationship
from sqlalchemy.orm.collections import column_mapped_collection

from accounts.domain import model
from accounts.domain.dto import AgreementNumber
from accounts.domain.model import ContactName, PhoneNumber, Postcode, SimProfile
from common.db import mapper_registry

account = Table(
    "account",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("name", String(model.OrganizationName.max_length), nullable=False),
    Column("status", Enum(model.AccountStatus, name="account_status"), nullable=False),
    Column(
        "agreement_number",
        String(length=AgreementNumber.max_length),
        nullable=False,
    ),
    Column("currency", CHAR(model.CurrencyCode.max_length), nullable=False),
    <PERSON>umn(
        "industry_vertical",
        Enum(model.IndustryVertical, name="industry_vertical"),
        nullable=False,
    ),
    Column(
        "sales_channel", Enum(model.SalesChannel, name="sales_channel"), nullable=False
    ),
    Column("sales_person", String(), nullable=False),
    Column("contract_end_date", Date(), nullable=False),
    Column("is_billable", Boolean(), nullable=False),
    Column("organization_id", Integer, nullable=False),
    Column("contact_name", String(length=ContactName.max_length), nullable=True),
    Column("email", String(), nullable=True),
    Column("phone", String(length=PhoneNumber.max_length), nullable=True),
    Column("job_title", String(), nullable=True),
    Column("country", CHAR(model.CountryCode.max_length), nullable=True),
    Column("state_region", String(), nullable=True),
    Column("city", String(), nullable=True),
    Column("address1", String(), nullable=True),
    Column("address2", String(), nullable=True),
    Column("postcode", String(length=Postcode.max_length), nullable=True),
    Column("logo_key", String(length=64), nullable=True),
    Column("sim_charge", Numeric, nullable=False, server_default="2"),
    Column("payment_terms", Integer, nullable=False, server_default="30"),
    Column(
        "sim_profile",
        Enum(SimProfile, name="sim_profile"),
        nullable=False,
    ),
)
account_product_type = Table(
    "account_product_type",
    mapper_registry.metadata,
    Column("account_id", ForeignKey("account.id"), primary_key=True),
    Column(
        "product_type", Enum(model.ProductType, name="product_type"), primary_key=True
    ),
)
user = Table(
    "user",
    mapper_registry.metadata,
    Column("id", postgresql.UUID(as_uuid=True), primary_key=True),
    Column("email", String(), nullable=False, unique=True),
    Column("account_id", ForeignKey("account.id"), nullable=False),
    Column("role", String(length=255), nullable=False),
    Column("saf_id", String()),
    Column("saf_registered_at", DateTime()),
)
saf_invite = Table(
    "saf_invite",
    mapper_registry.metadata,
    Column("id", postgresql.UUID(as_uuid=True), primary_key=True),
    Column("saf_id", String(), nullable=True),
    Column("created_at", DateTime(), nullable=False),
    Column("accepted_at", DateTime()),
    Column("user_id", ForeignKey("user.id"), nullable=False),
)
account_traffic_thresholds = Table(
    "account_traffic_thresholds",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("account_id", ForeignKey("account.id"), nullable=False),
    Column("carrier_name", String(), nullable=False),
    Column("warning_threshold", Integer, nullable=False, server_default="90"),
)

account_traffic_charges = Table(
    "account_traffic_charges",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("account_id", ForeignKey("account.id"), nullable=False),
    Column("carrier_name", String(), nullable=False),
    Column(
        "threshold_charge",
        Numeric(precision=18, scale=5),
        nullable=False,
        server_default="0.003",
    ),
)


@mapper_registry.mapped
class AccountProductType:
    __table__ = account_product_type


def start_mappers():
    from rate_plans.adapters.orm import RatePlan

    mapper_registry.map_imperatively(
        model.Account,
        account,
        properties=dict(
            account_product_types=relationship(
                AccountProductType, cascade="all, delete-orphan"
            ),
            _invoices=relationship(
                "Invoice",
                primaryjoin="Account.id == Invoice.account_id",
                foreign_keys="Invoice.account_id",
                back_populates="account",
                cascade="all, delete-orphan",
            ),
            _rate_plans=relationship(
                RatePlan,
                primaryjoin="Account.id == RatePlan.account_id",
                foreign_keys="RatePlan.account_id",
                cascade="all, delete-orphan",
            ),
            account_traffic_thresholds=relationship(
                model.AccountTrafficThresholds,
                primaryjoin="Account.id == model.AccountTrafficThresholds.account_id",
                foreign_keys="model.AccountTrafficThresholds.account_id",
                cascade="all, delete-orphan",
            ),
            account_traffic_charges=relationship(
                model.AccountTrafficCharges,
                primaryjoin="Account.id == model.AccountTrafficCharges.account_id",
                foreign_keys="model.AccountTrafficCharges.account_id",
                cascade="all, delete-orphan",
            ),
        ),
    )
    model.Account.product_types = association_proxy(
        "account_product_types",
        "product_type",
        creator=lambda v: AccountProductType(product_type=v),
    )
    mapper_registry.map_imperatively(
        model.User,
        user,
        properties=dict(
            saf_invites=relationship(
                model.SAFInvite,
                collection_class=column_mapped_collection(saf_invite.c.id),
                cascade="all, delete-orphan",
            )
        ),
    )
    mapper_registry.map_imperatively(
        model.SAFInvite,
        saf_invite,
    )
    mapper_registry.map_imperatively(
        model.AccountTrafficThresholds, account_traffic_thresholds
    )
    mapper_registry.map_imperatively(
        model.AccountTrafficCharges,
        account_traffic_charges,
    )


def stop_mappers():
    mapper_registry.dispose()
