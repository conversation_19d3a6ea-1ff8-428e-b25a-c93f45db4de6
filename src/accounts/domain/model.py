import re
import uuid
from dataclasses import InitVar, dataclass, field
from datetime import date, datetime, timezone
from decimal import Decimal
from enum import Enum
from typing import Literal
from uuid import UUID

from pydantic import ConstrainedInt, ConstrainedStr, EmailStr

from common.types import ICCID, IMSI, MSISDN, CountryCode, CurrencyCode, SimStatus


class SimProfile(str, Enum):
    DATA_ONLY = "DATA_ONLY"
    VOICE_SMS_DATA = "VOICE_SMS_DATA"


class PaymentTerms(ConstrainedInt):
    ge = 1
    le = 99


class OrganizationName(ConstrainedStr):
    min_length = 3
    max_length = 256
    strip_whitespace = True


class ContactName(ConstrainedStr):
    min_length = 3
    max_length = 60
    strip_whitespace = True


class PhoneNumber(ConstrainedStr):
    regex = re.compile(r"\+?\d+")
    min_length = 9
    max_length = 16


class Postcode(ConstrainedStr):
    max_length = 15
    strip_whitespace = True


class ProductType(str, Enum):
    NATIONAL_ROAMING = "National Roaming"


class SalesChannel(str, Enum):
    WHOLESALE = "Wholesale"
    INDIRECT = "Indirect"
    INTERNAL = "Internal"
    TRIAL = "Trial"


class AccountStatus(str, Enum):
    ACTIVE = "Active"
    CLOSED = "Closed"
    SUSPENDED = "Suspended"


class IndustryVertical(str, Enum):
    AEROSPACE = "Aerospace"
    AGGREGATOR = "Aggregator"
    AGRICULTURE = "Agriculture"
    CONSTRUCTION = "Construction"
    DEFENCE = "Defence"
    EDUCATION = "Education"
    ENVIRONMENT = "Environment"
    FOOD = "Food"
    HEALTH_CARE = "Health Care"
    LOCAL_GOVT_OR_PUBLIC_SECTOR = "Local Govt/Public Sector"
    LOGISTICS_AND_DISTRIBUTION = "Logistics & Distribution"
    MANUFACTURING = "Manufacturing"
    RETAIL_OR_MARKETING = "Retail / Marketing"
    SECURITY = "Security"
    TELECOMMUNICATIONS = "Telecommunications"
    TRANSPORT = "Transport"
    UTILITIES = "Utilities"
    OTHERS = "Others"


@dataclass
class Account:
    id: int = field(init=False)
    name: OrganizationName
    status: AccountStatus
    agreement_number: str
    currency: CurrencyCode
    product_types: list[ProductType]
    industry_vertical: IndustryVertical
    sales_channel: SalesChannel
    sales_person: str
    contract_end_date: date
    is_billable: bool
    organization_id: int
    sim_profile: SimProfile

    # Contact info
    contact_name: ContactName | None = None
    email: EmailStr | None = None
    phone: PhoneNumber | None = None
    job_title: str | None = None

    # Shipping address
    country: CountryCode | None = None
    state_region: str | None = None
    city: str | None = None
    address1: str | None = None
    address2: str | None = None
    postcode: Postcode | None = None

    # Billing settings
    sim_charge: Decimal = Decimal(2)
    payment_terms: PaymentTerms = PaymentTerms(30)

    # Thresholds values
    threshold_charge: Decimal | None = None
    carrier_name: str | None = None
    warning_threshold: int | None = None

    logo_key: str | None = None
    _id: InitVar[int | None] = None

    def __post_init__(self, _id: int | None):
        if _id is not None:
            self.id = _id


@dataclass(kw_only=True)
class GetAccount(Account):
    # Additional fields
    sims_total: int
    active_sims_total: int
    plans_total: int
    users_total: int
    default_rate_plan: str | None = None


@dataclass
class Organization:
    name: OrganizationName
    id: int | None = None


@dataclass(kw_only=True)
class UserInvite:
    """Tracks the user's invitation to register on the portal."""

    account_id: int
    email: EmailStr
    user_id: UUID
    created_at: datetime = field(default_factory=datetime.utcnow)

    external_id: str | None = None
    external_user_id: str | None = None
    external_registered_at: datetime | None = None

    @property
    def sent(self) -> bool:
        return self.external_id is not None

    @property
    def accepted(self) -> bool:
        return self.external_user_id is not None

    def confirm_sending(self, external_invite_id: str) -> None:
        self.external_id = external_invite_id

    def confirm_acceptance(
        self, external_user_id: str, registration_time: datetime
    ) -> None:
        self.external_user_id = external_user_id
        if registration_time.tzinfo:
            # convert to naive UTC
            registration_time = registration_time.astimezone(timezone.utc).replace(
                tzinfo=None
            )
        self.external_registered_at = registration_time


AccountRole = Literal["Admin", "User"]


@dataclass
class User:
    id: UUID
    email: EmailStr

    account_id: int
    role: AccountRole

    saf_id: str | None = None
    saf_registered_at: datetime | None = None

    saf_invites: dict[UUID, "SAFInvite"] = field(default_factory=dict)

    def create_invite(self) -> UUID:
        invite = SAFInvite(
            saf_id=None,
            created_at=datetime.now(),
        )
        self.saf_invites[invite.id] = invite
        return invite.id

    def confirm_invite_sending(self, reference_id: UUID, saf_id: str) -> None:
        self.saf_invites[reference_id].saf_id = saf_id

    def set_saf_identity(
        self,
        *,
        saf_id: str,
        registration_ts: datetime,
        reference_id: str | None = None,
    ):
        self.saf_id = saf_id
        self.saf_registered_at = registration_ts
        if reference_id is None:
            return
        try:
            invite_id = UUID(reference_id)
        except ValueError:
            return

        invite = self.saf_invites.get(invite_id)
        if invite:
            invite.accepted_at = registration_ts

    @property
    def registered(self) -> bool:
        return self.saf_id is not None


@dataclass(kw_only=True)
class SAFInvite:
    id: UUID = field(default_factory=uuid.uuid4)
    saf_id: str | None
    created_at: datetime
    accepted_at: datetime | None = None

    @property
    def sent(self) -> bool:
        return self.saf_id is not None


@dataclass
class AccountNames:
    id: int
    name: str
    organization_id: int


@dataclass
class AccountTrafficThresholds:
    account_id: int | None = None
    carrier_name: str | None = None
    warning_threshold: int | None = None


@dataclass
class AccountTrafficCharges:
    account_id: int | None = None
    carrier_name: str | None = None
    threshold_charge: Decimal | None = None


@dataclass
class AccountId:
    id: int
    name: str


@dataclass
class SimDetails:
    imsi: IMSI
    iccid: ICCID
    msisdn: MSISDN
    sim_status: SimStatus
    account_id: int
    account_name: str | None = None


@dataclass
class SimAccountInfoDetails(SimDetails):
    account_logo_key: str | None = None
    account_logo_url: str | None = None


@dataclass
class AccountSummary:
    account_sim_count: int | None = 0
    account_rate_plan_count: int | None = 0
    account_automation_rule_count: int | None = 0
