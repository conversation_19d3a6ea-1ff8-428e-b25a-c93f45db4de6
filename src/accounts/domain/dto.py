import re
from datetime import date, datetime
from decimal import Decimal
from typing import TYPE_CHECKING
from uuid import UUID

from pydantic import AnyHttpUrl, BaseModel, ConstrainedStr, EmailStr, Field, validator
from pydantic.datetime_parse import parse_date

from accounts.domain import model
from accounts.domain.model import (
    AccountRole,
    AccountStatus,
    ContactName,
    IndustryVertical,
    OrganizationName,
    PaymentTerms,
    PhoneNumber,
    Postcode,
    ProductType,
    SalesChannel,
    SimProfile,
)
from common.types import CountryCode, CurrencyCode, Enumeration, KeyEnumeration

if TYPE_CHECKING:
    from pydantic.typing import CallableGenerator


class Account(BaseModel):
    # Account info
    id: int
    name: str
    logo_url: AnyHttpUrl | None
    logo_key: str | None
    status: Enumeration = Field(name="AccountStatus")
    is_billable: bool

    agreement_number: str
    currency: CurrencyCode

    industry_vertical: Enumeration
    sales_channel: Enumeration
    product_types: list[Enumeration]

    sales_person: str
    contract_end_date: date

    # Contact info
    contact_name: ContactName | None
    email: EmailStr | None
    phone: PhoneNumber | None
    job_title: str | None

    # Shipping address
    country: CountryCode | None
    state_region: str | None
    city: str | None
    address1: str | None
    address2: str | None
    postcode: Postcode | None

    # Billing settings
    sim_charge: Decimal
    payment_terms: PaymentTerms

    # Thresholds Values
    threshold_charge: Decimal | None = None
    carrier_name: str | None = None
    warning_threshold: int | None = None

    organization_id: int

    # Sim Profile
    sim_profile: Enumeration

    @property
    def is_active(self) -> bool:
        return self.status == model.AccountStatus.ACTIVE

    @classmethod
    def from_model(
        cls, account: model.Account, logo_url: AnyHttpUrl | None
    ) -> "Account":
        return cls(
            id=account.id,
            name=account.name,
            logo_url=logo_url,
            logo_key=account.logo_key,
            status=Enumeration.from_enum_value(account.status),
            is_billable=account.is_billable,
            agreement_number=account.agreement_number,
            currency=account.currency,
            industry_vertical=Enumeration.from_enum_value(account.industry_vertical),
            sales_person=account.sales_person,
            sales_channel=Enumeration.from_enum_value(account.sales_channel),
            product_types=[
                Enumeration.from_enum_value(p) for p in account.product_types
            ],
            contract_end_date=account.contract_end_date,
            contact_name=account.contact_name,
            email=account.email,
            phone=account.phone,
            job_title=account.job_title,
            country=account.country,
            state_region=account.state_region,
            city=account.city,
            address1=account.address1,
            address2=account.address2,
            postcode=account.postcode,
            sim_charge=account.sim_charge,
            payment_terms=account.payment_terms,
            organization_id=account.organization_id,
            carrier_name=account.carrier_name,
            warning_threshold=account.warning_threshold,
            threshold_charge=account.threshold_charge,
            sim_profile=Enumeration.from_enum_value(account.sim_profile),
        )


class GetAccount(Account):
    # Additional info
    sims_total: int
    active_sims_total: int
    plans_total: int
    users_total: int
    default_rate_plan: str | None

    @classmethod
    def from_model_get_acc(
        cls, account: model.GetAccount, logo_url: AnyHttpUrl | None
    ) -> "GetAccount":
        return cls(
            id=account.id,
            name=account.name,
            logo_url=logo_url,
            logo_key=account.logo_key,
            status=Enumeration.from_enum_value(AccountStatus[account.status]),
            is_billable=account.is_billable,
            agreement_number=account.agreement_number,
            currency=account.currency,
            industry_vertical=Enumeration.from_enum_value(
                IndustryVertical[account.industry_vertical]
            ),
            sales_person=account.sales_person,
            sales_channel=Enumeration.from_enum_value(
                SalesChannel[account.sales_channel]
            ),
            product_types=[
                Enumeration.from_enum_value(ProductType[p])
                for p in account.product_types
            ],
            contract_end_date=account.contract_end_date,
            contact_name=account.contact_name,
            email=account.email,
            phone=account.phone,
            job_title=account.job_title,
            country=account.country,
            state_region=account.state_region,
            city=account.city,
            address1=account.address1,
            address2=account.address2,
            postcode=account.postcode,
            sim_charge=account.sim_charge,
            payment_terms=account.payment_terms,
            organization_id=account.organization_id,
            carrier_name=account.carrier_name,
            warning_threshold=account.warning_threshold,
            threshold_charge=account.threshold_charge,
            sims_total=account.sims_total,
            active_sims_total=account.active_sims_total,
            plans_total=account.plans_total,
            users_total=account.users_total,
            default_rate_plan=account.default_rate_plan,
            sim_profile=Enumeration.from_enum_value(SimProfile[account.sim_profile]),
        )


class NonPastDate(date):
    @classmethod
    def __get_validators__(cls) -> "CallableGenerator":
        yield parse_date
        yield cls.validate

    @classmethod
    def validate(cls, value: date) -> date:
        if value < date.today():
            raise ValueError("date passed")

        return value


class AgreementNumber(ConstrainedStr):
    regex = re.compile("^[a-zA-Z0-9]*$")
    max_length = 12


class ImageKey(ConstrainedStr):
    min_length = 32  # len(uuid.uuid4().hex)


class UpdatableAccountFields(BaseModel):
    name: OrganizationName
    agreement_number: AgreementNumber
    logo_key: ImageKey | None = None
    status: KeyEnumeration.generate(  # type: ignore[valid-type]
        model.AccountStatus
    ) | None = None
    sales_channel: KeyEnumeration.generate(  # type: ignore[valid-type]
        model.SalesChannel
    )
    sales_person: str = Field(min_length=1)
    industry_vertical: KeyEnumeration.generate(  # type: ignore[valid-type]
        model.IndustryVertical
    )
    contract_end_date: NonPastDate
    is_billable: bool

    # Contact info
    contact_name: ContactName | None = None
    email: EmailStr | None = None
    phone: PhoneNumber | None = None
    job_title: str | None = None

    # Shipping address
    country: CountryCode | None = None
    state_region: str | None = None
    city: str | None = None
    address1: str | None = None
    address2: str | None = None
    postcode: Postcode | None = None

    # Threshold values
    threshold_charge: Decimal | None = None
    warning_threshold: int | None = None

    # Billing settings
    sim_charge: Decimal
    payment_terms: PaymentTerms

    # Sim Profile
    sim_profile: SimProfile  # type: ignore[valid-type]

    @validator("email", pre=True, always=True)
    def email_to_lower(cls, email_id):
        if email_id:
            return email_id.lower()
        return email_id


class UpdateAccount(UpdatableAccountFields):
    def merge_model(self, account: model.Account) -> model.Account:
        return model.Account(
            _id=account.id,
            name=self.name,
            logo_key=self.logo_key,
            status=model.AccountStatus[self.status] if self.status else account.status,
            agreement_number=self.agreement_number,
            currency=account.currency,
            industry_vertical=model.IndustryVertical[self.industry_vertical],
            product_types=account.product_types,
            sales_channel=model.SalesChannel[self.sales_channel],
            sales_person=self.sales_person,
            contract_end_date=self.contract_end_date,
            is_billable=self.is_billable,
            organization_id=account.organization_id,
            contact_name=self.contact_name,
            email=self.email,
            phone=self.phone,
            job_title=self.job_title,
            country=self.country,
            state_region=self.state_region,
            city=self.city,
            address1=self.address1,
            address2=self.address2,
            postcode=self.postcode,
            sim_charge=self.sim_charge,
            payment_terms=self.payment_terms,
            carrier_name=account.carrier_name,
            warning_threshold=self.warning_threshold,
            threshold_charge=self.threshold_charge,
            sim_profile=self.sim_profile,
        )


class CreateAccount(UpdatableAccountFields):
    status: KeyEnumeration.generate(model.AccountStatus)  # type: ignore[valid-type]
    currency: CurrencyCode
    product_types: set[  # type: ignore[valid-type]
        KeyEnumeration.generate(model.ProductType)
    ] = Field(min_items=1)

    def to_model(self, organization_id: int) -> model.Account:
        return model.Account(
            name=self.name,
            status=model.AccountStatus[self.status],
            agreement_number=self.agreement_number,
            logo_key=self.logo_key,
            currency=self.currency,
            industry_vertical=model.IndustryVertical[self.industry_vertical],
            product_types=[model.ProductType[p] for p in self.product_types],
            sales_channel=model.SalesChannel[self.sales_channel],
            sales_person=self.sales_person,
            contract_end_date=self.contract_end_date,
            is_billable=self.is_billable,
            organization_id=organization_id,
            contact_name=self.contact_name,
            email=self.email,
            phone=self.phone,
            job_title=self.job_title,
            country=self.country,
            state_region=self.state_region,
            city=self.city,
            address1=self.address1,
            address2=self.address2,
            postcode=self.postcode,
            sim_charge=self.sim_charge,
            payment_terms=self.payment_terms,
            warning_threshold=self.warning_threshold,
            threshold_charge=self.threshold_charge,
            sim_profile=self.sim_profile,
        )


class BillingSettings(BaseModel):
    sim_charge: Decimal
    payment_terms: PaymentTerms

    @classmethod
    def from_model(cls, account: model.Account) -> "BillingSettings":
        return cls(sim_charge=account.sim_charge, payment_terms=account.payment_terms)


class Username(ConstrainedStr):
    max_length = 255


class AccountUserBaseAttrs(BaseModel):
    email: EmailStr
    role: AccountRole
    first_name: str | None = Field(None, max_length=255)  # Keycloak constraint
    last_name: str | None = Field(None, max_length=255)

    @validator("email", pre=True, always=True)
    def email_to_lower(cls, email_id):
        if email_id:
            return email_id.lower()
        return email_id


class UserInvite(AccountUserBaseAttrs):
    ...


class AccountUser(AccountUserBaseAttrs):
    id: UUID
    username: Username


class SAFEvent(BaseModel):
    email: EmailStr
    user_id: str
    registration_ts: datetime
    reference_id: str | None = None


class AccountNames(BaseModel):
    id: int
    name: str
    organization_id: int

    @classmethod
    def from_model(cls, account_names: model.AccountNames) -> "AccountNames":
        return cls(
            id=account_names.id,
            name=account_names.name,
            organization_id=account_names.organization_id,
        )
