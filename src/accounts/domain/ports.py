from abc import ABC, abstractmethod
from typing import Callable, Iterable
from uuid import UUID

from pydantic import EmailStr

from accounts.domain import dto, model
from accounts.domain.model import AccountSummary, Organization, User
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from openapi.domain.model import TokenResponse

# primary / inbound


class CreateAccountHandler(ABC):
    @abstractmethod
    def create_account(self, account: dto.CreateAccount) -> int:
        ...


class UpdateAccountHandler(ABC):
    @abstractmethod
    def update(self, account_id: int, account: dto.UpdateAccount) -> dto.Account:
        ...

    @abstractmethod
    def set_billing_settings(
        self, account_id: int, billing_settings: dto.BillingSettings
    ) -> dto.BillingSettings:
        ...


class DeleteAccountHandler(ABC):
    @abstractmethod
    def delete(self, account_id: int) -> None:
        ...


class AccountView(ABC):
    @abstractmethod
    def get(self, account_id: int) -> dto.Account:
        ...

    @abstractmethod
    def account_count(
        self, account_ids: list[int] | None = None, searching: Searching | None = None
    ) -> int:
        ...

    @abstractmethod
    def list(
        self,
        account_ids: list[int] | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> Iterable[dto.GetAccount]:
        ...

    @abstractmethod
    def get_billing_settings(self, account_id: int) -> dto.BillingSettings:
        ...

    @abstractmethod
    def get_account_names(self) -> Iterable[dto.AccountNames]:
        ...

    @abstractmethod
    def get_account_by_organization_id(
        self, organization_id: int
    ) -> model.AccountId | None:
        ...

    @abstractmethod
    def get_sim_account_info(self, search: str) -> model.SimAccountInfoDetails:
        ...

    @abstractmethod
    def get_account_summary(self, account_id: int) -> AccountSummary:
        ...


class SyncOrganizationsHandler(ABC):
    @abstractmethod
    def sync_accounts_with_organizations(
        self, root_organization_name: str, log: Callable[[str], None] = print
    ) -> None:
        ...


# secondary / outbound


class AbstractAccountRepository(ABC):
    @abstractmethod
    def add(self, account: model.Account) -> int:
        ...

    @abstractmethod
    def get(self, account_id: int) -> model.Account | None:
        ...

    @abstractmethod
    def get_by_organization_id(self, organization_id: int) -> model.Account | None:
        ...

    @abstractmethod
    def update(self, account: model.Account) -> None:
        ...

    @abstractmethod
    def query(
        self,
        account_ids: list[int] | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> Iterable[model.GetAccount]:
        ...

    @abstractmethod
    def delete(self, account: model.Account) -> None:
        ...

    @abstractmethod
    def account_count(
        self, account_ids: list[int] | None = None, searching: Searching | None = None
    ) -> int:
        ...

    @abstractmethod
    def get_account_names(self) -> Iterable[dto.AccountNames]:
        ...

    @abstractmethod
    def get_sim_account_info(self, search: str) -> model.SimAccountInfoDetails:
        ...

    @abstractmethod
    def get_account_summary(self, account_id: int) -> AccountSummary:
        ...


class AbstractOrganizationRepository(ABC):
    @abstractmethod
    def add_client(self, parent_id: int, client: Organization) -> int:
        ...

    @abstractmethod
    def get(self, organization_id: int) -> Organization | None:
        ...

    @abstractmethod
    def get_by_name(self, name: str) -> Organization | None:
        ...

    @abstractmethod
    def update(self, client: Organization) -> Organization:
        ...

    @abstractmethod
    def delete(self, organization_id: int) -> None:
        ...

    @abstractmethod
    def create_organization_member(
        self, organization_id: int, user_invite: dto.UserInvite
    ) -> UUID:
        ...

    @abstractmethod
    def get_organization_member(
        self, organization_id: int, user_id: UUID
    ) -> dto.AccountUser | None:
        ...

    @abstractmethod
    def update_organization_member_username(
        self, organization_id: int, user_id: UUID, username: dto.Username
    ) -> None:
        ...

    @abstractmethod
    def get_access_token(
        self,
    ) -> TokenResponse:
        ...


class AbstractUserRepository(ABC):
    @abstractmethod
    def add(self, user: User) -> None:
        ...

    @abstractmethod
    def get_by_email(self, email: str) -> User | None:
        ...


class AbstractDigitalIdentity(ABC):
    @abstractmethod
    def send_invite(self, reference_id: UUID, email: EmailStr) -> str:
        ...
