from pydantic.dataclasses import dataclass

from common.types import ICCID, IMSI, MSISDN


@dataclass
class SIMCardAccountAuditLogs:
    id: str
    imsi: IMSI
    request_type: str
    field: str
    action: str
    iccid: str | None
    msisdn: str | None
    prior_value: str | None
    new_value: str | None
    client_ip: str | None = None
    created_by: str | None = None
    created_date: str | None = None


@dataclass
class Providerlog:
    activityId: str
    simActivityLogUuid: str
    imsi: IMSI
    iccid: ICCID
    msisdn: MSISDN
    requestType: str
    auditDate: str
    message: str
    status: str
    workId: str
    priorStatus: str
