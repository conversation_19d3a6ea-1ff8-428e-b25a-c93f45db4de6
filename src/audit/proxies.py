from audit.services import AbstractInternalAuditService
from auth.dto import AuthenticatedUser
from auth.exceptions import ForbiddenError
from auth.permissions import is_distributor_client, is_distributor_staff
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching


class AuditServiceAuthProxy(AbstractInternalAuditService):
    def __init__(
        self, audit_service: AbstractInternalAuditService, user: AuthenticatedUser
    ) -> None:
        self.audit_service = audit_service
        self.user = user

    def system_audit(
        self,
        account_id: int,
        is_client: bool | None = False,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ):
        if is_distributor_staff(self.user):
            return self.audit_service.system_audit(
                account_id=account_id,
                pagination=pagination,
                ordering=ordering,
                searching=searching,
            )
        elif is_distributor_client(self.user):
            return self.audit_service.system_audit(
                account_id=self.user.organization.account.id,  # type: ignore
                is_client=True,
                pagination=pagination,
                ordering=ordering,
                searching=searching,
            )
        else:
            raise ForbiddenError
