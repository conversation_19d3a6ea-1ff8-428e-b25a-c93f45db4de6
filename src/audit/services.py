from abc import ABC, abstractmethod
from typing import Iterator

from audit.domain import model
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from common.types import IMSI
from sim.adapters.repository import AbstractSimRepository
from sim.domain.ports import AbstractAuditService


class AbstractInternalAuditService(ABC):
    @abstractmethod
    def system_audit(
        self,
        account_id: int,
        is_client: bool | None = False,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.SIMCardAccountAuditLogs], int]:
        ...


class AuditService(AbstractInternalAuditService):
    def __init__(
        self,
        sim_repository: AbstractSimRepository,
        audit_service: AbstractAuditService,
    ):
        self.sim_repository = sim_repository
        self.audit_service = audit_service

    def system_audit(
        self,
        account_id: int,
        is_client: bool | None = False,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.SIMCardAccountAuditLogs], int]:
        """Function for audit logs of activate and deactivate"""
        sim_cards = self.sim_repository.get_sim_cards(account_id=account_id)
        sim_list = list(sim_cards)
        imsis = [IMSI(sim.imsi) for sim in sim_list]

        audit_logs = self.audit_service.get_account_audit_api(
            imsi=imsis,
            is_client=is_client,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        return audit_logs.results, audit_logs.totalCount
