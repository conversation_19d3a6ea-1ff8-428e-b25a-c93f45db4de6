from abc import ABC, abstractmethod

from fastapi import status
from fastapi.encoders import jsonable_encoder
from platform_api_client import PlatformAPIClient, PlatformAPIError
from pydantic import EmailError, EmailStr

from app.config import logger, settings
from auth.exceptions import ForbiddenError, Unauthorized
from email_helper.exceptions import MailAPIError  # type: ignore


class AbstractMailService(ABC):
    @abstractmethod
    def send_mail(
        self, subject: str, name_from: str, recipients: list[str], html_body: str
    ):
        """Send an email with the given parameters."""
        ...

    def validate_emails(self, recipients: list[str]):
        invalid = [email for email in recipients if not self._is_valid_email(email)]
        if invalid:
            raise ValueError(f"Invalid email(s): {', '.join(invalid)}")

    def _is_valid_email(self, email: str) -> bool:
        try:
            EmailStr.validate(email)
            return True
        except EmailError:
            return False


class MailServiceAPI(AbstractMailService):
    """Implementation of mail service that sends emails via an external API."""

    SEND_MAIL_API = "/v1/email/send"

    def __init__(self, api_client: PlatformAPIClient):
        self.api = api_client

    def send_mail(
        self, subject: str, name_from: str, recipients: list[str], html_body: str
    ):
        """Send an email through the external mail API."""
        try:
            self.validate_emails(recipients=recipients)
            url = f"{settings.APP_BASE_URL}{self.SEND_MAIL_API}"
            logger.info(f"Mail: {url}")

            payload = {
                "subject": subject,
                "name_from": name_from,
                "recipients": [{"email": email} for email in recipients],
                "html": html_body,
            }

            response = self.api.post(url=url, json=jsonable_encoder(payload))

            if response.status_code == 202:
                return response.json()
            raise MailAPIError()

        except PlatformAPIError as e:
            logger.error(f"Error in send mail: {e}")

            if e.status_code == status.HTTP_401_UNAUTHORIZED:
                raise Unauthorized("Unauthorized access.")
            elif e.status_code == status.HTTP_403_FORBIDDEN:
                raise ForbiddenError()
            raise e

        except ConnectionError:
            raise ConnectionError("Failed to connect to the Mail API")
        except ValueError as e:
            raise ValueError(f"Invalid email(s): {e}")


class FakeMailServiceAPI(AbstractMailService):
    def send_mail(
        self, subject: str, name_from: str, recipients: list[str], html_body: str
    ):
        """Simulate sending an email by printing to console."""
        self.validate_emails(recipients=recipients)
        logger.info("[FAKE EMAIL SENT]")
        logger.info(f"Subject: {subject}")
        logger.info(f"From: {name_from}")
        logger.info(f"To: {recipients}")
        logger.info(f"HTML: {html_body}")

        return {"status": "fake_sent", "recipients": recipients}
