from app.config import logger
from common.types import UploadFileStatus

subject_map = {
    UploadFileStatus.PENDING.value: "Background " "Process {process_name} Started",
    UploadFileStatus.COMPLETED.value: "Background " "Process {process_name} Completed",
    UploadFileStatus.FAILED.value: "Background Process "
    "{process_name} Failed to be Completed",
    UploadFileStatus.PARTIALLY.value: "Background "
    "Process {process_name} Partially Completed",
}

body_map = {
    UploadFileStatus.PENDING.value: (
        "Dear User,<br><br>"
        "This email confirms that the background process "
        "<b>{process_name}</b> you initiated has been successfully "
        "started at {date_str} {time_str}.<br><br>"
        "This process may take some time. You'll receive another update "
        "once it's completed.<br><br>"
        "For your reference, "
        "the request ID is: <b>"
        "<a href='{result_link}'> {process_id} </a>"
        "</b><br><br>"
    ),
    UploadFileStatus.COMPLETED.value: (
        "Dear User,<br><br>"
        "We are pleased to inform you that the background process "
        "<b>{process_name}</b> has been successfully completed "
        "at {date_str} {time_str}.<br><br>"
        "You can view the results here: "
        "<b><a href='{result_link}'> {process_id} </a></b><br><br>"
    ),
    UploadFileStatus.FAILED.value: (
        "Dear User,<br><br>"
        "Be aware that the background process "
        "<b>{process_name}</b> "
        "request ID: <b><a href='{result_link}'> "
        "{process_id} </a> </b> encountered an issue and "
        "wasn't completed successfully. "
        "This occurred at {date_str} {time_str}.<br><br>"
        "The system reported the following error:<br>"
        "<b>{message}</b><br>"
    ),
    UploadFileStatus.PARTIALLY.value: (
        "Dear User,<br><br>"
        "We are pleased to inform you that the background process "
        "<b>{process_name}</b> has been partially completed "
        "at {date_str} {time_str}.<br><br>"
        "You can view the results here: "
        "<b><a href='{result_link}'> {process_id} </a></b><br><br>"
    ),
}


def build_subject_and_body(values: dict, status: str):
    try:
        body = body_map[status].format(**values)
        subject = subject_map[status].format(**values)
        return subject, body
    except KeyError as e:
        err_msg = f"Either status: {status} not define in body_map/subject_map"
        logger.error(f"{err_msg}: {e}")
        raise ValueError(err_msg)
