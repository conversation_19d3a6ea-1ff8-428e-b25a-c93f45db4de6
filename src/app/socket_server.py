import logging

import socketio  # type: ignore
from socketio import AsyncRedisManager

from app.config import settings
from socket_events.sim.events import get_sim_socket_events

logger = logging.getLogger(__name__)


def get_redis_manager(redis_host, redis_port):
    try:
        return AsyncRedisManager(f"redis://{redis_host}:{redis_port}")
    except Exception as e:
        logger.error(
            f"[Redis] Failed to connect to Redis at {redis_host}:{redis_port}: {e}"
        )
        return None


class SocketServer:
    """
    Socket.IO server abstraction with event handling.

    This class handles general socket events like connect and disconnect,
    while specific domain events (like sim events) are handled by specialized
    event handlers registered with this server.
    """

    def __init__(
        self,
        cors_allowed_origins: list[str] = [settings.APP_LANDING_PAGE_URL or ""],
        async_mode: str = "asgi",
        mgr=get_redis_manager(settings.REDIS_DB_HOST, settings.REDIS_DB_PORT),
        ping_timout: int = 10,
        ping_interval: int = 30,
    ):
        if mgr is None:
            logger.warning(
                "[Socket] Redis manager is not connected."
                " Falling back to in-memory broadcasting."
            )
        self.sio = socketio.AsyncServer(
            cors_allowed_origins=cors_allowed_origins,
            async_mode=async_mode,
            client_manager=mgr,
            ping_timeout=ping_timout,
            ping_interval=ping_interval,
        )
        self._register_core_event_handlers()
        self._register_domain_event_handlers()

    def _register_core_event_handlers(self) -> None:
        self.sio.on("connect", self.handle_connect)
        self.sio.on("disconnect", self.handle_disconnect)

    def _register_domain_event_handlers(self) -> None:
        self.sim_events = get_sim_socket_events(self.sio)

    async def handle_connect(self, sid: str, environ, auth) -> None:
        logger.info(f"[Socket] Client connected: sid={sid}")
        logger.info(f"[Socket] connected with url - {settings.APP_BASE_URL}")
        await self.sio.emit("server_message", {"msg": "Connected!"}, to=sid)

    async def handle_disconnect(self, sid: str) -> None:
        logger.info(f"[Socket] Client disconnected: sid={sid}")


# Singleton instance
socket_server = SocketServer()
sio = socket_server.sio
sim_events = socket_server.sim_events
