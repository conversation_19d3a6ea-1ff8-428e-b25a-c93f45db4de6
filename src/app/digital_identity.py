from functools import cache
from typing import TypedDict

import httpx
from platform_api_client import PlatformAPIClient
from pydantic import AnyHttpUrl

from app.config import settings


class DigitalIdentityConfig(TypedDict):
    BASE_URL: AnyHttpUrl
    CLIENT_ID: str
    CLIENT_SECRET: str


@cache
def digital_identity_config() -> DigitalIdentityConfig:
    """Configuration for the Digital Identity API"""

    if settings.DIGITAL_IDENTITY_URL is None:
        raise AssertionError("DIGITAL_IDENTITY_URL must not be None")
    if settings.DIGITAL_IDENTITY_CLIENT_ID is None:
        raise AssertionError("DIGITAL_IDENTITY_CLIENT_ID must not be None")
    if settings.DIGITAL_IDENTITY_CLIENT_SECRET is None:
        raise AssertionError("DIGITAL_IDENTITY_CLIENT_SECRET must not be None")

    return DigitalIdentityConfig(
        BASE_URL=settings.DIGITAL_IDENTITY_URL,
        CLIENT_ID=settings.DIGITAL_IDENTITY_CLIENT_ID,
        CLIENT_SECRET=settings.DIGITAL_IDENTITY_CLIENT_SECRET,
    )


def get_digital_identity_access_token(config: DigitalIdentityConfig) -> str:
    """Return the access token for the Digital Identity API"""

    data = dict(grant_type="client_credentials")
    transport = httpx.Client(
        auth=httpx.BasicAuth(
            username=config["CLIENT_ID"],
            password=config["CLIENT_SECRET"],
        )
    )
    token_url = f"{config['BASE_URL']}/oauth/accesstoken"
    with PlatformAPIClient(transport) as client:
        response = client.post(token_url, data=data)
        payload = response.json()
        return payload["access_token"]


def get_digital_identity_client() -> PlatformAPIClient:
    """Return the HTTP client for the Digital Identity API"""

    config = digital_identity_config()
    access_token = get_digital_identity_access_token(config)
    return PlatformAPIClient(base_url=config["BASE_URL"], auth_token=access_token)
