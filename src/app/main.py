from uuid import uuid4

import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.staticfiles import StaticFiles
from prometheus_client import CONTENT_TYPE_LATEST, generate_latest
from prometheus_fastapi_instrumentator import PrometheusFastApiInstrumentator
from socketio import AS<PERSON><PERSON><PERSON>  # type: ignore
from starlette.middleware.base import BaseHTTPMiddleware
from uvicorn.middleware.proxy_headers import ProxyHeadersMiddleware

from api import cdrrouter as cdr_router
from api import health_check, notification_router
from api import openapirouter as openapi_router
from api import router as api_router
from api.exception_handlers import EXCEPTION_HANDLERS
from app import constants
from app.config import settings
from app.constants import APP_DIR
from app.socket_server import sio
from common.utils import trace_id_var


class TraceIDMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        trace_id = request.headers.get("x-trace-id", str(uuid4()))
        trace_id_var.set(trace_id)
        response = await call_next(request)
        response.headers["x-trace-id"] = trace_id
        return response


app = FastAPI(
    title=constants.APPLICATION_NAME,
    openapi_url="/v1/glass/openapi.json",
    exception_handlers=EXCEPTION_HANDLERS,
)

app.include_router(api_router, prefix="/v1/glass")
app.include_router(cdr_router)
app.include_router(notification_router)
app.include_router(openapi_router)

app.include_router(health_check.router, prefix="/v1")
app.add_middleware(ProxyHeadersMiddleware, trusted_hosts="*")
app.add_middleware(TraceIDMiddleware)

socket_app = ASGIApp(sio, socketio_path="/ws")
app.mount("/socket", socket_app)


@app.get("/custom-metrics")
def custom_metrics():

    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)


PrometheusFastApiInstrumentator(should_group_status_codes=True).instrument(app)

if settings.APPLICATION_ENV != "prod":
    static_path = APP_DIR.parent / "static"
    static_path.mkdir(exist_ok=True)
    app.mount(
        "/static",
        StaticFiles(directory=static_path),
        name="static",
    )


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.APP_HOST,
        port=settings.APP_PORT,
        reload=True,
        log_config=None,  # we have our own logging initialization
    )  # pragma: no cover
