import argparse
from typing import Callable

from accounts.adapters.repository import (
    DatabaseAccountRepository,
    HTTPOrganizationRepository,
)
from accounts.services import AccountService, MediaService
from app.db.session import make_session
from app.platform import get_organizations_api_client
from app.security import get_service_access_token
from auth.services import FakeAuthService
from automation.adapters.repository import DatabaseAutomationRepository
from common.file_storage import LocalFileStorage


def main(organization_name: str, log: Callable[[str], None] = print):
    log("Obtaining a service access token")
    access_token = get_service_access_token()
    with (
        make_session() as session,
        get_organizations_api_client(access_token) as api_client,
    ):
        account_service = AccountService(
            user=FakeAuthService().authenticated_user,
            account_repository=DatabaseAccountRepository(session),
            organization_repository=HTTPOrganizationRepository(api_client),
            media_service=MediaService(LocalFileStorage("")),
            automation_repository=DatabaseAutomationRepository(session),
        )
        account_service.sync_accounts_with_organizations(organization_name, log=log)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        prog="Sync Accounts with Organizations",
        description=(
            "Creates organizations for existing account. "
            "If organization with account's name already exists, "
            "updates account's organization ID."
        ),
    )
    parser.add_argument(
        "--root-organization-name",
        default="Nextgen Clearing",
        required=False,
        help="Name of the root organization",
    )
    args = parser.parse_args()
    main(args.root_organization_name)
