from functools import cache

import boto3
from mypy_boto3_s3 import S3ServiceResource

from app.config import settings


@cache
def get_s3_client() -> S3ServiceResource:
    config = dict(
        region_name=settings.S3_REGION_NAME,
        aws_access_key_id=settings.S3_ACCESS_KEY_ID,
        aws_secret_access_key=settings.S3_SECRET_ACCESS_KEY,
        endpoint_url=settings.S3_ENDPOINT_URL,
    )
    if not all(config.values()):
        raise RuntimeError("No S3 configuration provided, set configuration variables")
    return boto3.resource("s3", **config)  # type: ignore
