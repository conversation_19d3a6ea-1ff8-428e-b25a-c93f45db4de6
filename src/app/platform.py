from platform_api_client import PlatformAPIClient

from app.config import settings
from sync_redis.adapters.externalapi import FakeRedisAPI


def get_organizations_api_client(auth_token: str) -> PlatformAPIClient:
    if settings.ORGANIZATIONS_URL is None:
        raise RuntimeError("ORGANIZATION_URL setting missing")

    platform_client = PlatformAPIClient(
        base_url=settings.ORGANIZATIONS_URL,
        auth_token=auth_token,
    )
    return platform_client


def get_account_api_client(auth_token: str) -> PlatformAPIClient:
    if settings.APP_BASE_URL is None:
        raise RuntimeError("ACCOUNTS_URL setting missing")
    platform_client = PlatformAPIClient(
        base_url=settings.APP_BASE_URL,
        auth_token=auth_token,
    )
    return platform_client


def get_market_share_api_client(auth_token: str) -> PlatformAPIClient:
    if settings.APP_BASE_URL is None:
        raise RuntimeError("MARKET_SHARE_URL setting missing")
    platform_client = PlatformAPIClient(
        base_url=settings.APP_BASE_URL,
        auth_token=auth_token,
    )
    return platform_client


def get_authorization_api_client(auth_token: str) -> PlatformAPIClient:
    if settings.APP_BASE_URL is None:
        raise RuntimeError("APP_BASE_URL is missing")
    return PlatformAPIClient(base_url=settings.APP_BASE_URL, auth_token=auth_token)


def get_mail_api_client(auth_token: str) -> PlatformAPIClient:
    if settings.APP_BASE_URL is None:
        raise RuntimeError("APP_BASE_URL is missing")
    return PlatformAPIClient(base_url=settings.APP_BASE_URL, auth_token=auth_token)


def get_open_api_client() -> PlatformAPIClient:
    if settings.APP_BASE_URL is None:
        raise RuntimeError("APP_BASE_URL setting missing")
    platform_client = PlatformAPIClient(
        base_url=settings.APP_BASE_URL,
    )
    return platform_client


def get_auditlog_api_client(auth_token: str) -> PlatformAPIClient:
    if settings.APP_BASE_URL is None:
        raise RuntimeError("AUDITLOG_URL setting missing")
    platform_client = PlatformAPIClient(
        base_url=settings.APP_BASE_URL,
        auth_token=auth_token,
    )
    return platform_client


def get_redis_api_client(auth_token: str) -> PlatformAPIClient:
    if settings.APP_BASE_URL is None:
        raise RuntimeError("REDIS_URL setting missing")
    platform_client = PlatformAPIClient(
        base_url=settings.APP_BASE_URL,
        auth_token=auth_token,
    )
    return platform_client


def get_mail_service_api_client(auth_token: str) -> PlatformAPIClient:
    if settings.APP_BASE_URL is None:
        raise RuntimeError("MAIL_SERVICE_URL setting missing")
    platform_client = PlatformAPIClient(
        base_url=settings.APP_BASE_URL,
        auth_token=auth_token,
    )
    return platform_client


def fake_redis_api_client():
    return FakeRedisAPI()
