from functools import cache

from sqlalchemy import create_engine
from sqlalchemy.engine import URL, Engine, make_url
from sqlalchemy.orm import Session

from accounts.adapters import orm as accounts_orm
from app.config import settings
from authorization.adapters import orm as auth_orm
from automation.adapters import orm as automation_orm
from billing.adapters import orm as billing_orm
from cdrdata.adapters import orm as cdr_orm
from orders.adapters import orm as order_orm
from rating.adapters import orm as rating_orm
from sim.adapters import orm as sim_orm

import redis  # isort: skip


def get_url() -> URL:
    if not settings.DATABASE_URI:
        raise RuntimeError(
            "No database configuration provided, set DATABASE_URI variable"
        )

    return make_url(settings.DATABASE_URI)


@cache
def get_engine() -> Engine:
    accounts_orm.start_mappers()
    rating_orm.start_mappers()
    billing_orm.start_mappers()
    sim_orm.start_mappers()
    cdr_orm.start_mappers()
    auth_orm.start_mappers()
    automation_orm.start_mappers()
    order_orm.start_mappers()
    return create_engine(get_url(), pool_pre_ping=True)


def make_session() -> Session:
    return Session(get_engine(), autocommit=False, autoflush=False)


r = redis.StrictRedis(
    host=settings.REDIS_DB_HOST,
    port=settings.REDIS_DB_PORT,
    db=settings.REDIS_DB_NUMBER,
    decode_responses=True,
)


@cache
def get_redis_session() -> redis.StrictRedis:
    return r
