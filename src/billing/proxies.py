from decimal import Decimal
from typing import Iterable

from accounts.domain.model import AccountStatus
from auth.dto import AuthenticatedUser
from auth.exceptions import ForbiddenError, NotFound
from auth.permissions import (
    is_account_member,
    is_distributor_client,
    is_distributor_staff,
)
from billing.domain import model
from billing.services import AbstractBillingService
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from common.types import Month, ServiceUsage


class BillingServiceAuthProxy(AbstractBillingService):
    def __init__(
        self, billing_service: AbstractBillingService, user: AuthenticatedUser
    ) -> None:
        self.billing_service = billing_service
        self.user = user

    def _is_invoice_available_to_distributor_client(
        self, invoice: model.Invoice
    ) -> bool:
        return (
            is_account_member(self.user, invoice.account_id)
            and invoice.is_published
            # and invoice.has_rating
            and invoice.is_billable
            and (
                invoice.account.status  # type: ignore
                in [AccountStatus.ACTIVE, AccountStatus.SUSPENDED]
            )
        )

    def get_invoice(self, invoice_id: int) -> model.Invoice:
        invoice = self.billing_service.get_invoice(invoice_id)
        if not (
            is_distributor_staff(self.user)
            or self._is_invoice_available_to_distributor_client(invoice)
        ):
            raise NotFound
        return invoice

    def publish_invoice(  # type: ignore
        self,
        invoice_ids: list[int],
        client_ip: str,
    ) -> tuple[list[model.Invoice], set[int]]:
        published_by = self.user.email
        if not is_distributor_staff(self.user):
            raise ForbiddenError
        published_by = self.user.email
        return self.billing_service.publish_invoice(
            invoice_ids, client_ip=client_ip, published_by=published_by
        )

    def unpublish_invoice(  # type: ignore
        self, invoice_id: int, client_ip: str
    ) -> None:
        published_by = self.user.email

        if not is_distributor_staff(self.user):
            raise ForbiddenError
        published_by = self.user.email
        return self.billing_service.unpublish_invoice(
            invoice_id, client_ip=client_ip, published_by=published_by
        )

    def get_sim_usage(
        self,
        invoice: model.Invoice,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterable[model.SubscriptionSIM], int]:
        if not (
            is_distributor_staff(self.user)
            or self._is_invoice_available_to_distributor_client(invoice)
        ):
            raise NotFound

        return self.billing_service.get_sim_usage(
            invoice, pagination=pagination, ordering=ordering, searching=searching
        )

    def get_sim_usage_export(
        self,
        invoice: model.Invoice,
    ) -> list[dict]:
        if not (
            is_distributor_staff(self.user)
            or self._is_invoice_available_to_distributor_client(invoice)
        ):
            raise NotFound

        return self.billing_service.get_sim_usage_export(invoice)

    def add_invoice_adjustment(
        self, invoice_id: int, adjustment: model.Adjustment
    ) -> model.Adjustment:
        if not is_distributor_staff(self.user):
            raise ForbiddenError
        return self.billing_service.add_invoice_adjustment(invoice_id, adjustment)

    def remove_invoices(self, month: Month) -> None:
        if not is_distributor_staff(self.user):
            raise ForbiddenError
        return self.billing_service.remove_invoices(month)

    def update_invoice_adjustment(
        self, invoice_id: int, adjustment: model.Adjustment
    ) -> None:
        if not is_distributor_staff(self.user):
            raise ForbiddenError
        return self.billing_service.update_invoice_adjustment(invoice_id, adjustment)

    def get_invoices(
        self, month: Month | None = None, account_ids: list[int] | None = None
    ) -> Iterable[model.Invoice]:
        if is_distributor_staff(self.user):
            return self.billing_service.get_invoices(month, account_ids=account_ids)

        elif is_distributor_client(self.user):
            invoices = self.billing_service.get_invoices(
                month, account_ids=[self.user.organization.account.id]  # type: ignore
            )
            return filter(self._is_invoice_available_to_distributor_client, invoices)
        else:
            raise ForbiddenError

    def get_invoices_new(
        self,
        month: Month | None = None,
        account_ids: list[int] | None = None,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterable[model.Invoice], int, dict]:
        if is_distributor_staff(self.user):
            return self.billing_service.get_invoices_new(
                month,
                account_ids=account_ids,
                pagination=pagination,
                searching=searching,
            )

        elif is_distributor_client(self.user):
            return self.billing_service.get_invoices_new(
                # month,
                account_ids=[self.user.organization.account.id],  # type: ignore
                pagination=pagination,
                searching=searching,
            )
        else:
            raise ForbiddenError

    def generate_invoices(
        self, month: Month, account_ids: list[int] | None = None
    ) -> None:
        if not is_distributor_staff(self.user):
            raise ForbiddenError
        return self.billing_service.generate_invoices(month, account_ids)

    def remove_invoice_adjustment(self, invoice_id: int, adjustment_id: int) -> None:
        if not is_distributor_staff(self.user):
            raise ForbiddenError
        return self.billing_service.remove_invoice_adjustment(invoice_id, adjustment_id)

    def get_sim_total_usage(
        self, invoice: model.Invoice, searching: Searching | None = None
    ) -> tuple[Iterable[ServiceUsage], Decimal]:
        return self.billing_service.get_sim_total_usage(invoice, searching=searching)

    def invoice_reconciliation(
        self, month: Month
    ) -> Iterable[model.ReconciliationDetails] | str:
        return self.billing_service.invoice_reconciliation(month)

    def get_monthly_reconciliation(
        self, month: Month
    ) -> Iterable[model.ReconciliationDetails] | str:
        return self.billing_service.get_monthly_reconciliation(month)
