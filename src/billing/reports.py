from collections import OrderedDict
from datetime import date, datetime
from decimal import Decimal
from enum import Enum
from functools import cache
from itertools import chain
from typing import TYPE_CHECKING, Any, Iterable, Literal

from more_itertools import side_effect
from pydantic import BaseModel

from api.renders import make_csv_renderer
from billing.domain.model import Invoice
from common.types import CurrencyCode, Month, Service

if TYPE_CHECKING:
    from pydantic.typing import CallableGenerator


class InvoiceDate(datetime):
    @classmethod
    def __get_validators__(cls) -> "CallableGenerator":
        yield cls.validate

    @classmethod
    def validate(cls, v: Any) -> "InvoiceDate":
        if isinstance(v, datetime):
            return InvoiceDate.fromtimestamp(v.timestamp())
        if isinstance(v, date):
            return InvoiceDate(v.year, v.month, v.day)
        raise TypeError("date or datetime expected")


class BillingMonth(int):
    @classmethod
    def __get_validators__(cls) -> "CallableGenerator":
        yield cls.validate

    @classmethod
    def validate(cls, v: Any) -> "BillingMonth":
        if isinstance(v, int):
            return BillingMonth(v)
        raise TypeError("int expected")


COLUMN_MAP = OrderedDict(
    [
        ("record_type", "Charge Record Type"),
        ("account_id", "Account ID"),
        ("account_name", "Account Name"),
        ("operator_account_id", "Operator Account ID"),
        ("account_tax_id", "AccountTaxID"),
        ("billing_cycle", "Billing Cycle"),
        ("billing_month", "Billing Month"),
        ("billing_year", "Billing Year"),
        ("invoice_id", "Invoice ID"),
        ("currency_code", "Currency Code"),
        ("billing_flag", "Billing Flag"),
        ("invoice_date", "Invoice Date"),
        ("due_date", "Due Date"),
        ("total_subscribers", "Total Subscribers"),
        ("total_active_subscribers", "Total Active Subscribers"),
        (
            "total_active_charged_subscribers",
            "Total Active Subscribers Charged in Period",
        ),
        ("total_volume", "Total Volume"),
        ("total_included_volume_used", "Total Included Volume Used"),
        ("total_overage_volume_used", "Total Overage Volume Used"),
        ("total_roaming_volume_used", "Total Overage Volume Used"),
        ("total_charges", "Total Charges"),
        ("total_overage_charges", "Total Overage Charges"),
        ("total_roaming_charges", "Total Roaming Charges"),
    ]
)


class RecordType(str, Enum):
    INVOICE_TTL = "INVOICE_TTL"

    SUBS_MONTHLY = "SUBS_MONTHLY"
    SUBS_PREPAID = "SUBS_PREPAID"
    SUBS_EVENT = "SUBS_EVENT"

    ACTIVATIONS = "ACTIVATIONS"
    OTHER_CHARGES = "OTHER_CHARGES"
    TAXES = "TAXES"
    DISCOUNT = "DISCOUNT"

    DATA_MONTHLY = "DATA_MONTHLY"
    SMSCOMBINED_MONTHLY = "SMSCOMBINED_MONTHLY"
    SMSMO_MONTHLY = "SMSMO_MONTHLY"
    SMSMT_MONTHLY = "SMSMT_MONTHLY"
    VOICECOMBINED_MONTHLY = "VOICECOMBINED_MONTHLY"
    VOICEMO_MONTHLY = "VOICEMO_MONTHLY"
    VOICEMT_MONTHLY = "VOICEMT_MONTHLY"
    CSDCOMBINED_MONTHLY = "CSDCOMBINED_MONTHLY"
    CSDMO_MONTHLY = "CSDMO_MONTHLY"
    CSDMT_MONTHLY = "CSDMT_MONTHLY"

    DATA_PREPAID = "DATA_PREPAID"
    SMSCOMBINED_PREPAID = "SMSCOMBINED_PREPAID"
    SMSMO_PREPAID = "SMSMO_PREPAID"
    SMSMT_PREPAID = "SMSMT_PREPAID"
    VOICECOMBINED_PREPAID = "VOICECOMBINED_PREPAID"
    VOICEMO_PREPAID = "VOICEMO_PREPAID"
    VOICEMT_PREPAID = "VOICEMT_PREPAID"
    CSDCOMBINED_PREPAID = "CSDCOMBINED_PREPAID"
    CSDMO_PREPAID = "CSDMO_PREPAID"
    CSDMT_PREPAID = "CSDMT_PREPAID"

    DATA_EVENT = "DATA_EVENT"
    SMSCOMBINED_EVENT = "SMSCOMBINED_EVENT"
    SMSMO_EVENT = "SMSMO_EVENT"
    SMSMT_EVENT = "SMSMT_EVENT"
    VOICECOMBINED_EVENT = "VOICECOMBINED_EVENT"
    VOICEMO_EVENT = "VOICEMO_EVENT"
    VOICEMT_EVENT = "VOICEMT_EVENT"
    CSDCOMBINED_EVENT = "CSDCOMBINED_EVENT"
    CSDMO_EVENT = "CSDMO_EVENT"
    CSDMT_EVENT = "CSDMT_EVENT"


SUBSCRIPTION_RECORD_TYPE = Literal[
    RecordType.SUBS_MONTHLY, RecordType.SUBS_PREPAID, RecordType.SUBS_EVENT
]


class Record(BaseModel):
    record_type: RecordType
    account_id: int
    account_name: str
    operator_account_id: int | None
    account_tax_id: str | None
    billing_cycle: int = -1
    billing_month: BillingMonth
    billing_year: int
    invoice_id: int
    currency_code: CurrencyCode | None = CurrencyCode("GBP")
    billing_flag: bool | None
    invoice_date: InvoiceDate | None
    due_date: InvoiceDate | None
    total_subscribers: int | None
    total_active_subscribers: int | None
    total_active_charged_subscribers: int | None
    total_volume: int | None
    total_included_volume_used: int | None
    total_overage_volume_used: int | None
    total_roaming_volume_used: int | None
    total_charges: Decimal
    total_overage_charges: Decimal | None
    total_roaming_charges: Decimal | None

    class Config:
        allow_population_by_field_name = True
        alias_generator = COLUMN_MAP.__getitem__
        json_encoders = {
            InvoiceDate: "{:%Y%m%d%H%M%S}".format,
            BillingMonth: lambda x: format(date(2000, x, 1), "%B"),
            Decimal: "{:.4f}".format,
        }


class SummaryRecord(Record):
    record_type: Literal[RecordType.INVOICE_TTL] = RecordType.INVOICE_TTL
    total_subscribers: int
    total_active_subscribers: int
    total_active_charged_subscribers: int
    total_charges: Decimal


class SubscriptionNumbers(BaseModel):
    total_subscribers: int
    total_active_subscribers: int
    total_active_charged_subscribers: int
    total_charges: Decimal

    @classmethod
    def empty(cls) -> "SubscriptionNumbers":
        return cls(
            total_subscribers=0,
            total_active_subscribers=0,
            total_active_charged_subscribers=0,
            total_charges=Decimal("0"),
        )


class SubscriptionRecord(SubscriptionNumbers, Record):
    record_type: Literal[
        RecordType.SUBS_MONTHLY, RecordType.SUBS_PREPAID, RecordType.SUBS_EVENT
    ]


class NonUsageRecord(Record):
    record_type: Literal[
        RecordType.ACTIVATIONS,
        RecordType.OTHER_CHARGES,
        RecordType.TAXES,
        RecordType.DISCOUNT,
    ]
    total_charges: Decimal


class UsageNumbers(BaseModel):
    total_volume: int
    total_included_volume_used: int
    total_overage_volume_used: int
    total_roaming_volume_used: int
    total_charges: Decimal
    total_overage_charges: Decimal = Decimal("0")
    total_roaming_charges: Decimal = Decimal("0")

    @classmethod
    def empty(cls) -> "UsageNumbers":
        return cls.construct(
            total_volume=0,
            total_included_volume_used=0,
            total_overage_volume_used=0,
            total_roaming_volume_used=0,
            total_charges=Decimal("0"),
            total_overage_charges=Decimal("0"),
            total_roaming_charges=Decimal("0"),
        )


class UsageBasedRecord(UsageNumbers, Record):
    ...


for _model in (
    Record,
    SummaryRecord,
    SubscriptionRecord,
    NonUsageRecord,
    UsageBasedRecord,
):
    if not list(COLUMN_MAP) == list(_model.__fields__):
        raise AssertionError(
            f"{_model.__name__} fields set and order must be: {list(COLUMN_MAP)}."
        )


class InvoiceChargesReport:
    def __init__(self, month: Month, account_names: dict[int, str]):
        self.month = month
        self.account_names = account_names

    @cache
    def base_data(self, invoice: Invoice) -> dict[str, Any]:
        return dict(
            account_id=invoice.account_id,
            account_name=self.account_names[invoice.account_id],
            billing_month=self.month.month,
            billing_year=self.month.year,
            currency_code="GBP",
            invoice_id=invoice.id,
        )

    def invoice_records(self, invoice: Invoice) -> Iterable[Record]:
        """Yield all records for the invoice."""
        subscriptions = (
            self.monthly_subscription_record(invoice),
            self.prepaid_subscription_record(invoice),
            self.event_subscription_record(invoice),
        )
        non_usage_records = (
            self.activations_record(invoice),
            self.other_charges_record(invoice),
            self.taxes_record(invoice),
            self.discount_record(invoice),
        )
        usage_records = chain.from_iterable(
            [
                self.monthly_usage_records(invoice),
                self.prepaid_usage_records(invoice),
                self.event_usage_records(invoice),
            ]
        )
        all_records = chain.from_iterable(
            [subscriptions, non_usage_records, usage_records]
        )

        summary = SummaryRecord(
            **self.base_data(invoice),
            invoice_date=invoice.invoice_date,
            due_date=invoice.due_date,
            total_subscribers=0,
            total_active_subscribers=0,
            total_active_charged_subscribers=0,
            total_charges=Decimal("0"),
        )

        def add_to_summary(u: Record):
            if isinstance(u, SubscriptionRecord):
                summary.total_subscribers += u.total_subscribers
                summary.total_active_subscribers += u.total_active_subscribers
                summary.total_active_charged_subscribers += (
                    u.total_active_charged_subscribers
                )
            summary.total_charges += u.total_charges

        yield from side_effect(add_to_summary, all_records)
        yield summary

    def monthly_subscription_record(self, invoice: Invoice) -> SubscriptionRecord:
        """
        Counts of unique SIMs participating in monthly subscriptions on the invoice and
        the total subscription charges for all SIMs on monthly subscriptions.
        """
        subs_monthly = SubscriptionRecord(
            **self.base_data(invoice),
            record_type=RecordType.SUBS_MONTHLY,
            total_charges=sum((s.charge for s in invoice.subscriptions), 0),
            total_subscribers=sum((s.sims_total for s in invoice.subscriptions), 0),
            total_active_subscribers=sum(
                (s.sims_active for s in invoice.subscriptions), 0
            ),
            total_active_charged_subscribers=sum(
                (s.sims_active for s in invoice.subscriptions), 0
            ),
        )
        return subs_monthly

    def prepaid_subscription_record(self, invoice: Invoice) -> SubscriptionRecord:
        """
        Counts of unique SIMs participating in prepaid subscriptions on the invoice and
        the total subscription charges for all SIMs on prepaid subscriptions.
        Unique subscriber counts include any SIM with a prepaid term that spanned any
        part of the billing cycle.
        """
        return SubscriptionRecord(
            **self.base_data(invoice),
            record_type=RecordType.SUBS_PREPAID,
            **SubscriptionNumbers.empty().dict(),
        )

    def event_subscription_record(self, invoice: Invoice) -> SubscriptionRecord:
        """
        Counts of unique SIMs with events active on the invoice and the total
        subscription charges for events. Unique subscriber counts include any SIM with
        an event term that spanned any part of the billing cycle.
        """
        return SubscriptionRecord(
            **self.base_data(invoice),
            record_type=RecordType.SUBS_EVENT,
            **SubscriptionNumbers.empty().dict(),
        )

    def activations_record(self, invoice: Invoice) -> NonUsageRecord:
        """Total activation charges on the invoice."""
        return NonUsageRecord(
            **self.base_data(invoice),
            record_type=RecordType.ACTIVATIONS,
            total_charges=Decimal("0"),
        )

    def other_charges_record(self, invoice: Invoice) -> NonUsageRecord:
        """Total "other charges" included on the invoice."""
        return NonUsageRecord(
            **self.base_data(invoice),
            record_type=RecordType.OTHER_CHARGES,
            total_charges=sum(a.amount for a in invoice.adjustments),
        )

    def taxes_record(self, invoice: Invoice) -> NonUsageRecord:
        """Total tax charges included on the invoice."""
        return NonUsageRecord(
            **self.base_data(invoice),
            record_type=RecordType.TAXES,
            total_charges=Decimal("0"),
        )

    def discount_record(self, invoice: Invoice) -> NonUsageRecord:
        """Total discount amount applied to the invoice."""
        return NonUsageRecord(
            **self.base_data(invoice),
            record_type=RecordType.DISCOUNT,
            total_charges=Decimal("0"),
        )

    def monthly_usage_records(self, invoice: Invoice) -> Iterable[UsageBasedRecord]:
        """Usage volume and charges for data usage booked against monthly rate plans."""
        for services, record_type in (
            ((Service.DATA,), RecordType.DATA_MONTHLY),
            ((Service.SMS_MO,), RecordType.SMSMO_MONTHLY),
            ((Service.SMS_MT,), RecordType.SMSMT_MONTHLY),
            ((), RecordType.SMSCOMBINED_MONTHLY),
            ((Service.VOICE_MO,), RecordType.VOICEMO_MONTHLY),
            ((Service.VOICE_MT,), RecordType.VOICEMT_MONTHLY),
            ((), RecordType.VOICECOMBINED_MONTHLY),
            ((), RecordType.CSDMO_MONTHLY),
            ((), RecordType.CSDMT_MONTHLY),
            ((), RecordType.CSDCOMBINED_MONTHLY),
        ):
            usage_numbers = self._monthly_usage_numbers(invoice, *services)
            yield UsageBasedRecord(
                record_type=record_type,
                **self.base_data(invoice),
                **usage_numbers.dict(),
            )

    def _monthly_usage_numbers(
        self, invoice: Invoice, *services: Service
    ) -> UsageNumbers:
        """Usage volume and charges for data usage booked against monthly rate plans."""
        total = UsageNumbers.empty()
        for usage in invoice.usages:
            if usage.service in services:
                total.total_included_volume_used += usage.volume
                total.total_charges += usage.charge
        total.total_volume = total.total_included_volume_used
        return total

    def prepaid_usage_records(self, invoice: Invoice) -> Iterable[UsageBasedRecord]:
        """Usage volume and charges for data usage booked against prepaid rate plans."""
        for record_type in [
            RecordType.DATA_PREPAID,
            RecordType.SMSCOMBINED_PREPAID,
            RecordType.SMSMO_PREPAID,
            RecordType.SMSMT_PREPAID,
            RecordType.VOICECOMBINED_PREPAID,
            RecordType.VOICEMO_PREPAID,
            RecordType.VOICEMT_PREPAID,
            RecordType.CSDCOMBINED_PREPAID,
            RecordType.CSDMO_PREPAID,
            RecordType.CSDMT_PREPAID,
        ]:
            yield self._empty_usage_record(invoice, record_type)

    def event_usage_records(self, invoice: Invoice) -> Iterable[UsageBasedRecord]:
        """Usage volume and charges for data usage booked against event plans."""
        for record_type in [
            RecordType.DATA_EVENT,
            RecordType.SMSCOMBINED_EVENT,
            RecordType.SMSMO_EVENT,
            RecordType.SMSMT_EVENT,
            RecordType.VOICECOMBINED_EVENT,
            RecordType.VOICEMO_EVENT,
            RecordType.VOICEMT_EVENT,
            RecordType.CSDCOMBINED_EVENT,
            RecordType.CSDMO_EVENT,
            RecordType.CSDMT_EVENT,
        ]:
            yield self._empty_usage_record(invoice, record_type)

    def _empty_usage_record(
        self, invoice: Invoice, record_type: RecordType
    ) -> UsageBasedRecord:
        return UsageBasedRecord(
            record_type=record_type,
            **self.base_data(invoice),
            **UsageNumbers.empty().dict(),
        )


render = make_csv_renderer(Record)
