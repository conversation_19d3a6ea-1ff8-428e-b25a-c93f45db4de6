from sqlalchemy import (
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Column,
    Date,
    DateTime,
    Enum,
    Foreign<PERSON>ey,
    Identity,
    Integer,
    Numeric,
    String,
    Table,
    UniqueConstraint,
    and_,
    false,
    select,
)
from sqlalchemy.orm import column_property, relationship
from sqlalchemy.orm.collections import column_mapped_collection
from sqlalchemy.sql import functions

from billing.domain import model
from billing.domain.model import AdjustmentType, InvoiceRatingState
from common.db import mapper_registry
from common.types import ICCID, IMSI, Service, Unit
from sim.domain.model import MSISDN

billing_cycle = Table(
    "billing_cycle",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("month", Date, nullable=False, unique=True),
)
invoice = Table(
    "invoice",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("billing_cycle_id", Integer, ForeignKey("billing_cycle.id"), nullable=False),
    Column("account_id", Integer, nullable=False),
    Column("published_at", DateTime, nullable=True),
    Column("due_days", Integer, nullable=False),
    Column(
        "rating_state",
        Enum(
            InvoiceRatingState,
            name="invoice_rating_state",
            metadata=mapper_registry.metadata,
        ),
        nullable=False,
    ),
    UniqueConstraint("billing_cycle_id", "account_id"),
)

subscription = Table(
    "subscription",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("invoice_id", Integer, ForeignKey("invoice.id"), nullable=False),
    Column("rate_plan_id", Integer, nullable=False),
    Column("name", String, nullable=False),
    Column("access_fee", Numeric, nullable=False),
    Column("sims_total", Integer, nullable=False, server_default="0"),
    Column("sim_charge", Numeric, nullable=False),
)

subscription_sim = Table(
    "subscription_sim",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("sim_id", Integer, nullable=False),
    Column("subscription_id", Integer, ForeignKey("subscription.id"), nullable=False),
    Column("iccid", String(length=ICCID.max_length), nullable=False),
    Column("imsi", String(length=IMSI.max_length), nullable=False),
    Column("msisdn", String(length=MSISDN.max_length), nullable=False),
    Column("first_time_activated", Boolean, nullable=False, default=false()),
    Column("sim_status", Enum(model.SimStatus, name="sim_status"), nullable=True),
)

# New billing logic to map subscription_sim_allocation table with ORM
subscription_sim_allocation = Table(
    "subscription_sim_allocation_details",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("invoice_id", Integer, ForeignKey("invoice.id"), nullable=False),
    Column("sim_id", Integer, nullable=False),
    Column("allocation_id", Integer, nullable=False),
    Column("allocation_date", DateTime, nullable=False),
)
# New billing logic to map subscription_sim_allocation table with ORM

sim_usage = Table(
    "sim_usage",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("billing_cycle_id", Integer, ForeignKey("billing_cycle.id")),
    Column("imsi", String(length=IMSI.max_length), nullable=False),
    Column(
        "service",
        Enum(Service, name="service", metadata=mapper_registry.metadata),
        nullable=False,
    ),
    Column("volume", BigInteger, nullable=False),
    Column("charge", Numeric, nullable=True),
)

adjustment = Table(
    "adjustment",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("invoice_id", Integer, ForeignKey("invoice.id"), nullable=False),
    Column("date", Date, nullable=False),
    Column(
        "type",
        Enum(
            AdjustmentType,
            create_constraint=True,
            native_enum=False,
            name="adjustment_type",
        ),
        nullable=True,
    ),
    Column("amount", Numeric, nullable=False),
)

invoice_overage_charge = Table(
    "invoice_overage_charge",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("invoice_id", Integer, ForeignKey("invoice.id"), nullable=False),
    Column("sim_count", Integer, nullable=False),
    Column(
        "service",
        Enum(
            Service,
            name="service",
            metadata=mapper_registry.metadata,
        ),
        nullable=False,
    ),
    Column("total_allowance", BigInteger, nullable=False),
    Column("total_usage", BigInteger, nullable=False),
    Column("usage_variance", BigInteger, nullable=False),
    Column("usage_variance_coverted", Numeric, nullable=False),
    Column("total_charge", Numeric, nullable=False),
    Column("rate_model_id", Integer, nullable=False),
    Column("overage_fee", Numeric, nullable=False),
    Column("overage_per", Integer, nullable=False),
    Column(
        "overage_unit",
        Enum(Unit, name="unit", metadata=mapper_registry.metadata),
        nullable=False,
    ),
    Column("isoverage", Boolean, nullable=False),
    Column("overage_charge", Numeric, nullable=False),
)

invoice_account_charge = Table(
    "invoice_account_charge",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("invoice_id", Integer, ForeignKey("invoice.id"), nullable=False),
    Column("rate_plan_id", Integer, ForeignKey("rate_plan.id"), nullable=False),
    Column("account_charge", Numeric, nullable=False),
)


def start_mappers() -> None:
    mapper_registry.map_imperatively(
        model.BillingCycle,
        billing_cycle,
        properties=dict(
            _invoices=relationship(
                model.Invoice,
                collection_class=column_mapped_collection(invoice.c.account_id),
                cascade="all, delete-orphan",
                back_populates="billing_cycle",
            ),
            _sim_usage=relationship(
                model.SimUsage,
                cascade="all, delete-orphan",
                back_populates="_billing_cycle",
            ),
        ),
    )
    mapper_registry.map_imperatively(
        model.Invoice,
        invoice,
        properties=dict(
            billing_cycle=relationship(model.BillingCycle, back_populates="_invoices"),
            _adjustments=relationship(
                model.Adjustment,
                cascade="all, delete-orphan",
                back_populates="_invoices",
            ),
            _subscription_sim_allocation=relationship(
                model.SubscriptionSimAllocation,
                cascade="all, delete-orphan",
                back_populates="_invoices",
            ),
            account=relationship(
                "Account",
                primaryjoin="Invoice.account_id == Account.id",
                foreign_keys="Invoice.account_id",
                back_populates="_invoices",
            ),
            subscriptions=relationship(
                model.Subscription,
                cascade="all, delete-orphan",
                back_populates="_invoice",
            ),
            _invoice_overage_charge=relationship(
                model.InvoiceOverageCharge,
                cascade="all, delete-orphan",
                back_populates="_invoices",
            ),
            _invoice_account_charge=relationship(
                model.InvoiceFixedCharge,
                cascade="all, delete-orphan",
                back_populates="_invoices",
            ),
            _new_sims=column_property(
                select(functions.count(subscription_sim_allocation.c.id))
                .where(
                    and_(
                        subscription_sim_allocation.c.invoice_id == invoice.c.id,
                    )
                )
                .scalar_subquery()
            ),
        ),
    )
    mapper_registry.map_imperatively(
        model.Subscription,
        subscription,
        properties=dict(
            _invoice=relationship(model.Invoice, back_populates="subscriptions"),
            sims=relationship(
                model.SubscriptionSIM,
                cascade="all, delete-orphan",
                back_populates="subscription",
            ),
            _sims_active=column_property(
                select(functions.count(subscription_sim.c.id))
                .where(
                    and_(
                        subscription_sim.c.subscription_id == subscription.c.id,
                        # New billing logic to get total active sim count
                        subscription_sim.c.sim_status
                        == subscription_sim.c.sim_status
                        is None
                        or subscription_sim.c.sim_status
                        != model.SimStatus.READY_FOR_ACTIVATION,
                        # New billing logic to get total active sim count
                    )
                )
                .scalar_subquery(),
            ),
        ),
    )
    mapper_registry.map_imperatively(
        model.SubscriptionSIM,
        subscription_sim,
        properties=dict(
            subscription=relationship(model.Subscription, back_populates="sims"),
            usage=relationship(
                model.SimUsage,
                secondary="join(Subscription, Invoice).join(BillingCycle)",
                primaryjoin=(
                    "and_("
                    "SubscriptionSIM.subscription_id == Subscription.id,"
                    "SubscriptionSIM.imsi == SimUsage.imsi"
                    ")"
                ),
                secondaryjoin="SimUsage.billing_cycle_id == BillingCycle.id",
                viewonly=True,
            ),
        ),
    )
    # New billing logic to map table with invoice
    mapper_registry.map_imperatively(
        model.SubscriptionSimAllocation,
        subscription_sim_allocation,
        properties=dict(
            _invoices=relationship(
                model.Invoice, back_populates="_subscription_sim_allocation"
            )
        ),
    )
    # New billing logic to map table with invoice
    mapper_registry.map_imperatively(
        model.Adjustment,
        adjustment,
        properties=dict(
            _invoices=relationship(model.Invoice, back_populates="_adjustments")
        ),
    )
    mapper_registry.map_imperatively(
        model.SimUsage,
        sim_usage,
        properties=dict(
            _billing_cycle=relationship(model.BillingCycle, back_populates="_sim_usage")
        ),
    )
    mapper_registry.map_imperatively(
        model.InvoiceOverageCharge,
        invoice_overage_charge,
        properties=dict(
            _invoices=relationship(
                model.Invoice, back_populates="_invoice_overage_charge"
            )
        ),
    )
    mapper_registry.map_imperatively(
        model.InvoiceFixedCharge,
        invoice_account_charge,
        properties=dict(
            _invoices=relationship(
                model.Invoice, back_populates="_invoice_account_charge"
            )
        ),
    )


def stop_mappers() -> None:
    mapper_registry.dispose()
