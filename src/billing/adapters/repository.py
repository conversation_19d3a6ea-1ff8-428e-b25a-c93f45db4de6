from abc import ABC, abstractmethod
from datetime import datetime
from decimal import Decimal
from itertools import chain, groupby
from operator import attrgetter
from typing import Callable, Iterable, Protocol

import more_itertools
from sqlalchemy import and_, asc, desc, func, nulls_first, nulls_last, select, text
from sqlalchemy.orm import Session
from sqlalchemy.sql import Select, functions
from sqlalchemy.sql.functions import coalesce

from accounts.domain.model import AccountStatus, PaymentTerms, SalesChannel
from app.config import logger
from billing.adapters import orm
from billing.constants import usage_ordering_fields_mapper
from billing.domain import model
from billing.exceptions import BillingCycleDoesNotExist
from common.ordering import Ordering, OrderingResolver
from common.pagination import Pagination
from common.searching import Searching, apply_search_to_sql
from common.types import Month, Service, ServiceSet, ServiceUsage
from rating.domain.model import IMSI


class AbstractBillingCycleRepository(ABC):
    @abstractmethod
    def add(self, billing_cycle: model.BillingCycle) -> None:
        ...

    @abstractmethod
    def get(self, month: Month) -> model.BillingCycle | None:
        ...

    @abstractmethod
    def remove(self, month: Month) -> None:
        ...

    @abstractmethod
    def get_invoices(
        self,
        billing_cycle: model.BillingCycle | None = None,
        account_ids: list[int] | None = None,
    ) -> Iterable[model.Invoice]:
        ...

    @abstractmethod
    def get_invoices_new(
        self,
        billing_cycle: model.BillingCycle | None = None,
        account_ids: list[int] | None = None,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterable[model.Invoice], int, dict]:
        ...

    @abstractmethod
    def get_invoice_by_id(self, invoice_id: int) -> model.Invoice | None:
        ...

    @abstractmethod
    def get_service_usages(
        self,
        billing_cycle: model.BillingCycle,
        imsi_list: list[IMSI],
        invoice_id: int,
    ) -> Iterable[ServiceUsage]:
        ...

    @abstractmethod
    def query_sim_usage(
        self,
        billing_cycle: model.BillingCycle,
        imsi_list: list[IMSI] | None = None,
        services: set[Service] | None = None,
    ) -> Iterable[model.SimUsage]:
        ...

    @abstractmethod
    def get_subscription_sims(
        self,
        invoice_id: int,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Iterable[model.SubscriptionSIM]:
        ...

    @abstractmethod
    def get_subscription_sims_export(
        self,
        invoice_id: int,
    ) -> list[dict]:
        ...

    @abstractmethod
    def get_subscription_sims_count(
        self,
        invoice_id: int,
        searching: Searching | None = None,
    ) -> int:
        ...

    @abstractmethod
    def update_sim_usage(
        self, billing_cycle: model.BillingCycle, usage: Iterable[model.SimUsage]
    ) -> None:
        ...

    @abstractmethod
    def update_invoice(self, invoice: model.Invoice) -> None:
        ...

    @abstractmethod
    def get_invoice_reconciliation(
        self,
        from_date: datetime,
        to_date: datetime,
        billing_cycle: model.BillingCycle,
    ) -> Iterable[model.ReconciliationAgg]:
        ...

    @abstractmethod
    def get_monthly_reconciliation(
        self,
        from_date: datetime,
        to_date: datetime,
    ) -> Iterable[model.ReconciliationAgg]:
        ...

    # New Billing logic for audit we need to add this
    @abstractmethod
    def invoice_allocation_details(
        self, invoice_id: int, imsi_list: Iterable[IMSI], month: Month
    ):
        ...

    @abstractmethod
    def reset_invoice_allocation_details(self, invoice_id: int):
        ...

    @abstractmethod
    def reset_invoice_overage_charge(self, invoice_id: int):
        ...

    @abstractmethod
    def invoice_overage_charge(self, account_id: int, invoice_id: int, month: Month):
        ...

    @abstractmethod
    def reset_invoice_account_charge(self, invoice_id: int):
        ...

    @abstractmethod
    def invoice_account_charge(self, account_id: int, invoice_id: int, month: Month):
        ...

    # New Billing logic for audit we need to add this

    @abstractmethod
    def get_total_account_charge_by_invoice_id(
        self, invoice_id: int
    ) -> model.InvoiceFixedChargeDetails:
        ...


class InMemoryBillingCycleRepository(AbstractBillingCycleRepository):
    def __init__(self) -> None:
        self.billing_cycles: dict[Month, model.BillingCycle] = {}

    @property
    def _all_invoices(self) -> Iterable[model.Invoice]:
        for bc in self.billing_cycles.values():
            yield from bc._invoices.values()  # noqa

    @property
    def _all_adjustments(self) -> Iterable[model.Adjustment]:
        for invoice in self._all_invoices:
            yield from invoice.adjustments

    def add(self, billing_cycle: model.BillingCycle) -> None:
        for i, invoice in enumerate(billing_cycle._invoices.values()):  # noqa
            invoice.billing_cycle = billing_cycle
            if not hasattr(invoice, "published_at"):
                invoice.published_at = None
            if not hasattr(invoice, "_adjustments"):
                invoice._adjustments = []
            if not hasattr(invoice, "id"):
                invoice.id = len(list(self._all_invoices)) + (i + 1)
            if not hasattr(invoice, "account"):
                invoice.account = model.Account(
                    id=invoice.account_id,
                    status=AccountStatus.ACTIVE,
                    is_billable=True,
                    sales_channel=SalesChannel.WHOLESALE,
                    payment_terms=PaymentTerms(30),
                    sim_charge=Decimal(0),
                    contract_end_date=datetime.today(),
                )
        bucket = more_itertools.bucket(billing_cycle.sim_usage, attrgetter("imsi"))
        for imsi in list(bucket):
            sim = self._find_sim(billing_cycle, imsi)
            if sim:
                sim.usage = list(bucket[imsi])
        self.billing_cycles[billing_cycle.month] = billing_cycle

    def get(self, month: Month) -> model.BillingCycle | None:
        return self.billing_cycles.get(month)

    def remove(self, month: Month) -> None:
        del self.billing_cycles[month]

    def get_invoices(
        self,
        billing_cycle: model.BillingCycle | None = None,
        account_ids: list[int] | None = None,
    ) -> Iterable[model.Invoice]:
        for invoice in self._all_invoices:
            filtered_by_billing_cycle = (
                billing_cycle is not None and invoice.billing_cycle != billing_cycle
            )
            filtered_by_account_id = (
                account_ids is not None and invoice.account_id not in account_ids
            )
            if filtered_by_billing_cycle or filtered_by_account_id:
                continue
            yield invoice

    def get_invoices_new(
        self,
        billing_cycle: model.BillingCycle | None = None,
        account_ids: list[int] | None = None,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterable[model.Invoice], int, dict]:
        invoice_details = []
        for invoice in self._all_invoices:
            filtered_by_billing_cycle = (
                billing_cycle is not None and invoice.billing_cycle != billing_cycle
            )
            filtered_by_account_id = (
                account_ids is not None and invoice.account_id not in account_ids
            )
            if filtered_by_billing_cycle or filtered_by_account_id:
                continue
            invoice_details.append(invoice)
        total_count = 5
        total_invoice_summary = {
            "totalCount": total_count,
            "totalSims": 500,
            "totalActiveSims": 500,
            "totalSimCharge": 500,
            "totalDataVolume": 500,
            "totalVoiceVolume": 500,
            "totalSmsVolume": 500,
        }

        return invoice_details, total_count, total_invoice_summary

    def get_invoice_by_id(self, invoice_id: int) -> model.Invoice | None:
        for invoice in self._all_invoices:
            if invoice.id == invoice_id:
                return invoice
        else:
            return None

    def _find_sim(
        self, billing_cycle: model.BillingCycle, imsi: IMSI
    ) -> model.SubscriptionSIM | None:
        for invoice in billing_cycle.invoices:
            for subscription in invoice.subscriptions:
                for sim in subscription.sims:
                    if sim.imsi == imsi:
                        return sim
        else:
            return None

    def get_service_usages(
        self,
        billing_cycle: model.BillingCycle,
        imsi_list: list[IMSI],
        invoice_id: int,
    ) -> Iterable[ServiceUsage]:
        key = attrgetter("service")
        filtered_sim_usages = [
            s for s in billing_cycle.sim_usage if s.imsi in imsi_list
        ]
        empty_service_list = [s for s in Service]
        for service_name, sim_usage in groupby(
            sorted(filtered_sim_usages, key=key), key=key
        ):
            sim_usage_list = list(sim_usage)
            yield ServiceUsage(
                service=service_name,
                volume=sum((s.volume for s in sim_usage_list), start=0),
                bulk_overage_charge=Decimal(0),
                sub_charge=Decimal(0),
                total_overage_charge=Decimal(0),
                charge=sum(
                    (s.charge for s in sim_usage_list if s.charge is not None),
                    start=Decimal("0"),
                ),
            )
            empty_service_list.remove(service_name)

        for empty_service in empty_service_list:
            yield ServiceUsage(
                service=empty_service,
                volume=0,
                bulk_overage_charge=Decimal(0),
                sub_charge=Decimal(0),
                total_overage_charge=Decimal(0),
                charge=Decimal(0),
            )

    def query_sim_usage(
        self,
        billing_cycle: model.BillingCycle,
        imsi_list: list[IMSI] | None = None,
        services: set[Service] | None = None,
    ) -> Iterable[model.SimUsage]:
        conditions = []
        if imsi_list:
            conditions.append(lambda x: x.imsi in imsi_list)  # type: ignore[operator]
        if services:
            conditions.append(lambda x: x.service in services)  # type: ignore[operator]

        return (r for r in billing_cycle.sim_usage if all(c(r) for c in conditions))

    def update_sim_usage(
        self, billing_cycle: model.BillingCycle, usage: Iterable[model.SimUsage]
    ) -> None:
        def eq(a: model.SimUsage, b: model.SimUsage) -> bool:
            return a.imsi == b.imsi and a.service == b.service

        for new in usage:
            for old in billing_cycle.sim_usage:
                if not eq(new, old):
                    continue
                old.charge = new.charge
                sim = self._find_sim(billing_cycle, new.imsi)
                if not sim:
                    break

                for usage_record in sim.usage:
                    if eq(usage_record, old):
                        usage_record.charge = new.charge
                        break
                else:
                    raise AssertionError(
                        f"usage record must be attached to SIM({new.imsi})"
                        f" if it's attached to billing cycle"
                    )

                break
            else:
                raise AssertionError("SimUsage must be preserved.")

    def update_invoice(self, invoice: model.Invoice) -> None:
        for in_mem_invoice in self._all_invoices:
            if in_mem_invoice.id == invoice.id:
                in_mem_invoice.__dict__.update(invoice.__dict__)
                for a in in_mem_invoice._adjustments:
                    if a.id is None:
                        a.id = more_itertools.ilen(self._all_adjustments) + 1

    def get_subscription_sims(
        self,
        invoice_id: int,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Iterable[model.SubscriptionSIM]:
        invoice = self.get_invoice_by_id(invoice_id)
        if not invoice:
            return []
        sims: Iterable[model.SubscriptionSIM] = chain.from_iterable(
            (sub.sims for sub in invoice.subscriptions)
        )
        if searching:
            sims = filter(lambda sim: searching.filter(sim), sims)  # type:ignore
        if ordering:

            def usages_order_rule(services: ServiceSet, field: str) -> Callable:
                return lambda sim: sum(
                    [getattr(i, field) for i in sim.usage if i.service in services]
                )

            mapped_fields = {
                "rate_plan_name": attrgetter("subscription.name"),
                "subscription_charge": attrgetter("subscription.access_fee"),
            }

            order_resolver = OrderingResolver(
                mapped_fields=mapped_fields,
                additional_rules={
                    key.order_name: usages_order_rule(key.services, key.field)
                    for key in usage_ordering_fields_mapper.values()
                },
                ordering=ordering,
            )
            sims = order_resolver.sort_records(list(sims))

        if pagination:
            return pagination.slice(list(sims))
        return sims

    def get_subscription_sims_export(
        self,
        invoice_id: int,
    ) -> list[dict]:
        ...

    def get_subscription_sims_count(
        self,
        invoice_id: int,
        searching: Searching | None = None,
    ) -> int:
        return more_itertools.ilen(
            self.get_subscription_sims(invoice_id, searching=searching)
        )

    def get_invoice_reconciliation(
        self,
        from_date: datetime,
        to_date: datetime,
        billing_cycle: model.BillingCycle,
    ) -> Iterable[model.ReconciliationAgg]:
        invoice_data = [
            {
                "service": "VOICE",
                "simImsi": None,
                "cdrImsi": "234588570010011",
                "simUsage": 70,
                "cdrUsage": 5,
                "variance": 65,
            },
            {
                "service": "DATA",
                "simImsi": None,
                "cdrImsi": "234588570010011",
                "simUsage": 0,
                "cdrUsage": 5000,
                "variance": 5000,
            },
        ]

        invoice_data_details: list[model.ReconciliationAgg] = [
            model.ReconciliationAgg(**invoice)  # type: ignore
            for invoice in invoice_data
        ]

        return invoice_data_details

    def get_monthly_reconciliation(
        self,
        from_date: datetime,
        to_date: datetime,
    ) -> Iterable[model.ReconciliationAgg]:
        invoice_data = [
            {
                "service": "VOICE",
                "simImsi": None,
                "cdrImsi": "234588570010011",
                "simUsage": 70,
                "cdrUsage": 5,
                "variance": 65,
            },
            {
                "service": "DATA",
                "simImsi": None,
                "cdrImsi": "234588570010011",
                "simUsage": 0,
                "cdrUsage": 5000,
                "variance": 5000,
            },
        ]

        invoice_data_details: list[model.ReconciliationAgg] = [
            model.ReconciliationAgg(**invoice)  # type: ignore
            for invoice in invoice_data
        ]

        return invoice_data_details

    # New Billing logic for audit we need to add this
    def invoice_allocation_details(
        self, invoice_id: int, imsi_list: Iterable[IMSI], month: Month
    ):
        return None

    def reset_invoice_allocation_details(self, invoice_id: int):
        return None

    def reset_invoice_overage_charge(self, invoice_id: int):
        return None

    def invoice_overage_charge(self, account_id: int, invoice_id: int, month: Month):
        return None

    def reset_invoice_account_charge(self, invoice_id: int):
        return None

    def invoice_account_charge(self, account_id: int, invoice_id: int, month: Month):
        return None

    # New Billing logic for audit we need to add this

    def get_total_account_charge_by_invoice_id(
        self, invoice_id: int
    ) -> model.InvoiceFixedChargeDetails:
        return model.InvoiceFixedChargeDetails(account_charge=Decimal(0))


class DatabaseBillingCycleRepository(AbstractBillingCycleRepository):
    def __init__(self, session: Session) -> None:
        self.session = session

    def add(self, billing_cycle: model.BillingCycle) -> None:
        self.session.add(billing_cycle)
        self.session.commit()

    def get(self, month: Month) -> model.BillingCycle | None:
        query = select(model.BillingCycle).filter_by(month=month)
        result = self.session.execute(query)
        bc = result.scalar_one_or_none()
        if bc:
            bc.month = Month.from_date(bc.month)
        return bc

    def remove(self, month: Month) -> None:
        cycle = self.get(month)
        if not cycle:
            raise BillingCycleDoesNotExist()
        self.session.delete(cycle)
        self.session.commit()

    def map_invoices(
        self, invoice_summary, adjustments, subscriptions, imsi_usage
    ) -> list[model.Invoice]:
        invoices = []

        for inv_row in invoice_summary:
            (
                invoice_id,
                account_id,
                account_name,
                logo_url,
                status,
                is_billable,
                industry_vertical,
                currency,
                contract_end_date,
                billing_cycle,
                invoice_date,
                due_date,
                is_published,
                rating_state,
                total_account_charge,
                new_sims,
                billing_cycle_id,
                due_days,
            ) = inv_row

            invoice = model.Invoice(account_id=account_id, due_days=due_days)
            invoice.id = invoice_id
            invoice.billing_cycle = model.BillingCycle(
                month=Month.from_date(billing_cycle)
            )
            invoice.published_at = invoice_date
            invoice.rating_state = model.InvoiceRatingState[rating_state]
            invoice._new_sims = int(new_sims)
            invoice._total_account_charge = Decimal(str(total_account_charge))

            # Optional: Add account details as a custom attribute
            invoice._account_info = model.BillingAccount(  # type: ignore
                id=account_id,
                name=account_name,
                logo_key=logo_url,
                status=model.AccountStatus[status],
                is_billable=is_billable,
                industry_vertical=industry_vertical,
                currency=currency,
                contract_end_date=contract_end_date,
            )

            # Add subscriptions
            for sub_row in filter(lambda s: s[1] == invoice_id, subscriptions):
                (
                    sub_id,
                    _,
                    rate_plan_id,
                    name,
                    access_fee,
                    sims_total,
                    sim_charge,
                    sims_active,
                ) = sub_row

                invoice.add_subscription(
                    rate_plan_id=rate_plan_id,
                    rate_plan_name=name,
                    access_fee=Decimal(str(access_fee)),
                    sims_total=sims_total,
                    sims_active=sims_active,
                    sim_charge=Decimal(str(sim_charge)),
                )

            # Pre-initialize all services with default values
            service_usage_map = {
                service: {
                    "service": service,
                    "volume": 0,
                    "charge": Decimal(0),
                    "totalOverageCharge": Decimal(0),
                    "bulkOverageCharge": Decimal(0),
                }
                for service in ["DATA", "SMS_MO", "SMS_MT", "VOICE_MO", "VOICE_MT"]
            }

            for usage_row in filter(lambda u: u[0] == invoice_id, imsi_usage):
                (_, service, volume, charge, total_overage, bulk_overage) = usage_row

                usage = service_usage_map[service]
                usage["service"] = service
                usage["volume"] += int(volume)  # type: ignore
                usage["charge"] += Decimal(str(charge or 0))  # type: ignore
                usage["totalOverageCharge"] += Decimal(  # type: ignore
                    str(total_overage or 0)
                )
                usage["bulkOverageCharge"] += Decimal(  # type: ignore
                    str(bulk_overage or 0)
                )

            for usage in service_usage_map.values():
                invoice.usages.append(
                    model.ServiceUsage(
                        service=usage["service"],  # type: ignore
                        volume=usage["volume"],  # type: ignore
                        sub_charge=usage["charge"],  # type: ignore
                        total_overage_charge=usage["totalOverageCharge"],  # type: ignore # noqa
                        bulk_overage_charge=usage["bulkOverageCharge"],  # type: ignore
                        charge=usage["charge"] + usage["totalOverageCharge"] + usage["bulkOverageCharge"],  # type: ignore # noqa
                    )
                )

            # Add adjustments
            for adj_row in filter(lambda a: a[1] == invoice_id, adjustments):
                (adj_id, _, adj_date, amount, adj_type) = adj_row

                invoice._adjustments.append(
                    model.Adjustment(
                        id=adj_id,
                        date=adj_date,
                        type=model.AdjustmentType[adj_type] if adj_type else None,
                        amount=Decimal(str(amount)),
                    )
                )

            invoices.append(invoice)

        return invoices

    def get_invoices_new(
        self,
        billing_cycle: model.BillingCycle | None = None,
        account_ids: list[int] | None = None,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterable[model.Invoice], int, dict]:
        get_invoice_summary_func = func.get_invoice_summary(
            [billing_cycle.id] if billing_cycle else None,  # type: ignore
            account_ids,
            pagination.page_size if pagination else None,
            pagination.offset if pagination else None,
            searching.search if searching else None,
        )
        summary_query = select("*").select_from(get_invoice_summary_func)
        invoice_summary = self.session.execute(summary_query).fetchall()

        invoice_ids = [
            invoice_details.invoice_id for invoice_details in invoice_summary
        ]
        billing_cycle_ids = [
            invoice_details.billing_cycle_id for invoice_details in invoice_summary
        ]
        logger.info(f"invoice_ids: {invoice_ids}")
        logger.info(f"billing_cycle_ids: {billing_cycle_ids}")
        logger.info(
            "billing_cycle:"
            f" {[billing_cycle.id] if billing_cycle else None}"  # type: ignore
        )

        get_adjustment_func = func.get_invoice_adjustments(invoice_ids)
        adjustment_query = select("*").select_from(get_adjustment_func)
        adjustment = self.session.execute(adjustment_query).fetchall()

        get_subscriptions_func = func.get_invoice_subscriptions(invoice_ids)
        subscription_query = select("*").select_from(get_subscriptions_func)
        subscriptions = self.session.execute(subscription_query).fetchall()

        get_imsi_usage_func = func.get_imsi_usage(
            invoice_ids, billing_cycle_ids, account_ids
        )
        imsi_usage_query = select("*").select_from(get_imsi_usage_func)
        imsi_usage = self.session.execute(imsi_usage_query).fetchall()

        invoice_summary_total_func = func.get_invoice_summary_total(
            [billing_cycle.id] if billing_cycle else None,  # type: ignore
            account_ids,
            searching.search if searching else None,
        )
        invoice_summary_total_query = select("*").select_from(
            invoice_summary_total_func
        )

        (
            total_invoice,
            total_sims,
            new_sims,
            total_active_sims,
            total_charge,
            new_sim_charge,
            total_adjustment,
            service_charge,
            overage_charge,
            total_invoice_charge,
            data_volume,
            voice_volume,
            sms_volume,
        ) = self.session.execute(invoice_summary_total_query).one()

        total_sim_charge = (
            total_charge + new_sim_charge + service_charge + total_adjustment
        )
        total_invoice_summary = {
            "totalCount": total_invoice,
            "totalSims": total_sims,
            "totalActiveSims": total_active_sims,
            "totalSimCharge": total_sim_charge,
            "totalDataVolume": data_volume,
            "totalVoiceVolume": voice_volume,
            "totalSmsVolume": sms_volume,
        }
        invoices_data = self.map_invoices(
            invoice_summary, adjustment, subscriptions, imsi_usage
        )

        return invoices_data, total_invoice, total_invoice_summary

    def get_invoices(
        self,
        billing_cycle: model.BillingCycle | None = None,
        account_ids: list[int] | None = None,
    ) -> Iterable[model.Invoice]:
        query = select(model.Invoice)
        if billing_cycle is not None:
            query = query.filter_by(billing_cycle=billing_cycle)
        if account_ids is not None:
            query = query.filter(
                model.Invoice.account_id.in_(account_ids)  # type: ignore
            )
        return self.session.execute(query).scalars()

    def get_invoice_by_id(self, invoice_id: int) -> model.Invoice | None:
        query = select(model.Invoice).filter_by(id=invoice_id)
        return self.session.execute(query).scalar_one_or_none()

    def get_total_account_charge_by_invoice_id(
        self, invoice_id: int
    ) -> model.InvoiceFixedChargeDetails:
        inovice_account_charge = orm.invoice_account_charge
        query = select(
            coalesce(func.sum((inovice_account_charge.c.account_charge)), 0).label(
                "account_charge"
            )
        ).filter_by(invoice_id=invoice_id)

        response = self.session.execute(query).first()
        return model.InvoiceFixedChargeDetails(
            account_charge=response.account_charge  # type: ignore
        )

    def get_service_usages(
        self, billing_cycle: model.BillingCycle, imsi_list: list[IMSI], invoice_id: int
    ) -> Iterable[ServiceUsage]:
        source = model.SimUsage
        invoice_charge = orm.invoice_overage_charge

        usage_cte = (
            select(
                source.service,
                source.charge,
                source.volume,
            )
            .filter_by(
                _billing_cycle=billing_cycle,
            )
            .where(source.imsi.in_(imsi_list))  # type: ignore
            .cte("sim_usages")
        )

        services = set(Service)
        service_stmt = func.unnest(list(services)).table_valued("service")

        query = (
            select(
                service_stmt.c.service,
                coalesce(func.sum(usage_cte.c.volume), 0).label("volume"),
                coalesce(func.sum(usage_cte.c.charge), 0).label("sub_charge"),
                coalesce(invoice_charge.c.total_charge, 0).label(
                    "total_overage_charge"
                ),
                coalesce(invoice_charge.c.overage_charge, 0).label(
                    "bulk_overage_charge"
                ),
                (
                    coalesce(func.sum(usage_cte.c.charge), 0)
                    + coalesce(invoice_charge.c.total_charge, 0)
                    + coalesce(invoice_charge.c.overage_charge, 0)
                ).label("charge"),
            )
            .select_from(
                service_stmt.outerjoin(
                    usage_cte, usage_cte.c.service == service_stmt.c.service
                ).outerjoin(
                    invoice_charge,
                    and_(
                        invoice_charge.c.service == service_stmt.c.service,
                        invoice_charge.c.invoice_id == invoice_id,
                    ),
                ),
            )
            .group_by(
                service_stmt.c.service,
                invoice_charge.c.total_charge,
                invoice_charge.c.overage_charge,
            )
            .order_by(service_stmt.c.service)
        )

        for row in self.session.execute(query):
            yield ServiceUsage(
                service=row.service,
                volume=row.volume,
                sub_charge=row.sub_charge,
                total_overage_charge=row.total_overage_charge,
                bulk_overage_charge=row.bulk_overage_charge,
                charge=row.charge,
            )

    def query_sim_usage(
        self,
        billing_cycle: model.BillingCycle,
        imsi_list: list[IMSI] | None = None,
        services: set[Service] | None = None,
    ) -> Iterable[model.SimUsage]:
        conditions = []
        if services is not None:
            conditions.append(orm.sim_usage.c.service.in_(services))
        if imsi_list is not None:
            conditions.append(orm.sim_usage.c.imsi.in_(imsi_list))
        query = (
            select(model.SimUsage)
            .filter_by(_billing_cycle=billing_cycle)
            .filter(*conditions)
        )
        yield from self.session.execute(
            query, execution_options={"yield_per": 1000}
        ).scalars()

    def update_sim_usage(
        self, billing_cycle: model.BillingCycle, usage: Iterable[model.SimUsage]
    ) -> None:
        self.session.add_all(list(usage))
        self.session.commit()

    def update_invoice(self, invoice: model.Invoice) -> None:
        self.session.merge(invoice)
        self.session.commit()

    def get_subscription_sims(
        self,
        invoice_id: int,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Iterable[model.SubscriptionSIM]:
        stmt = self._sim_stmt(invoice_id, searching=searching)
        if ordering:
            stmt = self._order_stmt(stmt, ordering=ordering)
        if pagination:
            stmt = stmt.offset(pagination.offset).limit(pagination.page_size)
        stmt = stmt.order_by(nulls_first(asc(model.SubscriptionSIM.sim_id)))
        yield from self.session.execute(
            stmt, execution_options={"yield_per": 1000}
        ).scalars()

    def get_subscription_sims_export(
        self,
        invoice_id: int,
    ) -> list[dict]:
        sql = text(
            """
            SELECT *
            FROM get_invoice_details(:invoice_id)
        """
        )
        result = self.session.execute(sql, {"invoice_id": invoice_id})
        data = [dict(row) for row in result]
        return data

    def get_subscription_sims_count(
        self,
        invoice_id: int,
        searching: Searching | None = None,
    ) -> int:
        stmt = self._sim_stmt(invoice_id, searching=searching)
        return self.session.execute(
            select(functions.count(stmt.c.id)).select_from(stmt)
        ).scalar_one()

    # New Billing logic for audit we need to add this
    def invoice_allocation_details(
        self, invoice_id: int, imsi_list: Iterable[IMSI], month: Month
    ):
        invoice_allocation_details_function = func.subscription_sim_allocation_details(
            invoice_id, imsi_list, month
        )
        query = select("*").select_from(invoice_allocation_details_function)
        self.session.execute(query)

    def reset_invoice_allocation_details(self, invoice_id: int):
        reset_invoice_allocation_details_function = (
            func.reset_subscription_sim_allocation_details(invoice_id)
        )
        query = select("*").select_from(reset_invoice_allocation_details_function)
        self.session.execute(query)

    def invoice_overage_charge(self, account_id: int, invoice_id: int, month: Month):
        invoice_overage_charge_function = func.invoice_overage_charge_details(
            account_id, invoice_id, month
        )
        query = select("*").select_from(invoice_overage_charge_function)
        self.session.execute(query)

    def reset_invoice_overage_charge(self, invoice_id: int):
        reset_invoice_overage_charge_function = (
            func.reset_invoice_overage_charge_details(invoice_id)
        )
        query = select("*").select_from(reset_invoice_overage_charge_function)
        self.session.execute(query)

    def invoice_account_charge(self, account_id: int, invoice_id: int, month: Month):
        invoice_account_charge_function = func.invoice_account_charge_details(
            account_id, invoice_id, month
        )
        query = select("*").select_from(invoice_account_charge_function)
        self.session.execute(query)

    def reset_invoice_account_charge(self, invoice_id: int):
        reset_invoice_account_charge_function = (
            func.reset_invoice_account_charge_details(invoice_id)
        )
        query = select("*").select_from(reset_invoice_account_charge_function)
        self.session.execute(query)

    # New Billing logic for audit we need to add this

    @classmethod
    def _sim_stmt(
        cls,
        invoice_id: int,
        searching: Searching | None = None,
    ) -> Select:
        stmt = (
            select(model.SubscriptionSIM)
            .join(orm.subscription)
            .add_columns(orm.subscription)
        ).filter(orm.subscription.c.invoice_id == invoice_id)
        if searching is not None:
            stmt = apply_search_to_sql(searching, model.SubscriptionSIM, stmt)
        return stmt

    @classmethod
    def _order_stmt(cls, stmt: Select, ordering: Ordering) -> Select:
        order_direction, null_order = (
            (desc, nulls_last)
            if ordering.order.lower() == "desc"
            else (asc, nulls_first)
        )

        fields_mapper = {
            "id": model.SubscriptionSIM.sim_id,
            "iccid": model.SubscriptionSIM.iccid,
            "msisdn": model.SubscriptionSIM.msisdn,
            "imsi": model.SubscriptionSIM.imsi,
            "rate_plan_name": orm.subscription.c.name,
            "subscription_charge": orm.subscription.c.access_fee,
        }

        if ordering.field in fields_mapper:
            stmt = stmt.order_by(
                null_order(order_direction(fields_mapper[ordering.field]))
            )
        elif ordering.field in usage_ordering_fields_mapper:
            ordering_field = usage_ordering_fields_mapper[ordering.field]
            sub_query = (
                select(
                    orm.sim_usage.c.billing_cycle_id,
                    orm.sim_usage.c.imsi,
                    coalesce(func.sum(orm.sim_usage.c.volume), 0).label("volume"),
                    coalesce(func.sum(orm.sim_usage.c.charge), 0).label("charge"),
                )
                .filter(orm.sim_usage.c.service.in_(ordering_field.services))
                .group_by(orm.sim_usage.c.billing_cycle_id, orm.sim_usage.c.imsi)
                .subquery()
            )

            stmt = (
                stmt.join(orm.invoice)
                .outerjoin(
                    sub_query,
                    and_(
                        sub_query.c.imsi == orm.subscription_sim.c.imsi,
                        sub_query.c.billing_cycle_id == orm.invoice.c.billing_cycle_id,
                    ),
                )
                .add_columns(sub_query)
            )

            stmt = stmt.order_by(
                null_order(order_direction(getattr(sub_query.c, ordering_field.field)))
            )
        stmt = stmt.order_by(null_order(order_direction(model.SubscriptionSIM.sim_id)))
        return stmt

    def get_invoice_reconciliation(
        self,
        from_date: datetime,
        to_date: datetime,
        billing_cycle: model.BillingCycle,
    ) -> Iterable[model.ReconciliationAgg]:
        usage_data_function = func.invoice_recon(
            from_date,
            to_date,
            billing_cycle.id,  # type: ignore
        )

        query = select("*").select_from(usage_data_function)
        for row in self.session.execute(query).fetchall():
            yield model.ReconciliationAgg(
                service=row.service,
                simImsi=row.su_imsi,
                cdrImsi=row.cdr_imsi,
                simUsage=row.su_usage,
                cdrUsage=row.cdr_usage,
                variance=row.su_cdr_variance,
            )

    def get_monthly_reconciliation(
        self,
        from_date: datetime,
        to_date: datetime,
    ) -> Iterable[model.ReconciliationAgg]:
        usage_data_function = func.monthly_recon(
            from_date,
            to_date,
        )

        query = select("*").select_from(usage_data_function)
        for row in self.session.execute(query).fetchall():
            yield model.ReconciliationAgg(
                service=row.service,
                simImsi=row.mu_imsi,
                cdrImsi=row.cdr_imsi,
                simUsage=row.mu_usage,
                cdrUsage=row.cdr_usage,
                variance=row.mu_cdr_variance,
            )


class AbstractSimView(Protocol):
    @abstractmethod
    def get_sim_count(self, *, rate_plan_ids: list[int]) -> int:
        ...

    @abstractmethod
    def get_sim_cards(
        self, *, rate_plan_ids: list[int] | None = None
    ) -> Iterable[model.SubscriptionSIM]:
        ...


class AbstractAccountRepository(Protocol):
    @abstractmethod
    def query(self, account_ids: list[int] | None = None) -> Iterable[model.Account]:
        ...


class InMemoryAccountRepository(AbstractAccountRepository):
    def __init__(self, accounts: Iterable[model.Account]):
        self.accounts = list(accounts)

    def query(self, account_ids: list[int] | None = None) -> Iterable[model.Account]:
        if account_ids is not None:
            return (a for a in self.accounts if a.id in account_ids)
        return self.accounts
