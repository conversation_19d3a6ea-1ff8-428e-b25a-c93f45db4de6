# flake8: noqa
# Below procedures are created while implementating new billing logic
class Procedures:
    @staticmethod
    def create_subscription_sim_allocation_procedure():
        return """
            CREATE OR REPLACE FUNCTION subscription_sim_allocation_details(
            invoice_id_param INT,
            imsi_array TEXT[],
            month_param DATE
        )
        RETURNS VOID AS $$
        BEGIN
            INSERT INTO subscription_sim_allocation_details
                (invoice_id, sim_id, allocation_id, allocation_date)
            SELECT
                invoice_id_param,
                sc.id,
                sc.allocation_id,
                al.created_at
            FROM
                sim_card AS sc
            INNER JOIN
                allocation AS al ON al.id = sc.allocation_id
            WHERE
                to_char(
                    date_trunc('month', month_param), 'YYYY-MM'
                    )=to_char(
                        date_trunc('month', al.created_at), 'YYYY-MM'
                        )
                and
                sc.imsi = ANY(imsi_array) AND NOT EXISTS
                (
                    select id from subscription_sim_allocation_details
                    where invoice_id = invoice_id_param AND
                    subscription_sim_allocation_details.sim_id=sc.id);
        END;
        $$ LANGUAGE plpgsql;
        """

    @staticmethod
    def create_get_account_details():
        return """
        CREATE OR REPLACE FUNCTION get_account_details(
            search_term TEXT DEFAULT NULL,
            account_id_param INT[] DEFAULT NULL,
            order_by_column TEXT DEFAULT 'id',
            order_direction TEXT DEFAULT 'ASC',
            page_offset INT DEFAULT NULL,
            page_size INT DEFAULT NULL
        )
        RETURNS TABLE (
            id INT,
            name VARCHAR,
            logo_key VARCHAR,
            status VARCHAR,
            is_billable BOOLEAN,
            agreement_number VARCHAR,
            currency CHAR(3),
            industry_vertical VARCHAR,
            sales_channel VARCHAR,
            sales_person VARCHAR,
            contract_end_date DATE,
            contact_name VARCHAR,
            email VARCHAR,
            phone VARCHAR,
            job_title VARCHAR,
            country CHAR(3),
            state_region VARCHAR,
            city VARCHAR,
            address1 VARCHAR,
            address2 VARCHAR,
            postcode VARCHAR,
            sim_charge NUMERIC,
            payment_terms INT,
            organization_id INT,
            carrier_name VARCHAR,
            warning_threshold INT,
            threshold_charge NUMERIC,
            sims_total INT,
            active_sims_total INT,
            plans_total INT,
            default_rate_plan VARCHAR,
            product_types VARCHAR[],
            users_total INT
        ) AS $$
        DECLARE
            final_order_by_column TEXT := COALESCE(order_by_column, 'id');
            final_order_direction TEXT := COALESCE(order_direction, 'ASC');
            pagination_clause TEXT := '';
            order_clause TEXT;
        BEGIN
            -- Pagination clause
            IF page_size IS NOT NULL AND page_offset IS NOT NULL THEN
                pagination_clause := ' LIMIT ' || page_size || ' OFFSET ' || page_offset;
            END IF;

            -- Order clause (to avoid SQL injection issues)
            order_clause := 'ORDER BY ' || quote_ident(final_order_by_column) || ' ' || final_order_direction;

            -- Execute query
            RETURN QUERY EXECUTE
            '
            WITH rate_plan_info AS (
                SELECT
                    account_id,
                    count(*)::INT AS plans_total,
                    max(name) FILTER (WHERE is_default) AS default_rate_plan
                FROM rate_plan
                GROUP BY account_id
            ),
            sim_counts AS (
                SELECT
                    account_id,
                    count(*)::INT AS sims_total,
                    count(*) FILTER (WHERE sc.sim_status = ''ACTIVE'') AS active_sims_total
                FROM allocation
                JOIN sim_card sc ON sc.imsi = allocation.imsi
                GROUP BY account_id
            ),
            product_types AS (
                SELECT
                    apt.account_id,
                    COALESCE(array_agg(apt.product_type::VARCHAR), ARRAY[]::VARCHAR[]) AS product_types
                FROM account_product_type apt
                GROUP BY apt.account_id
            )
            SELECT
                a.id,
                a.name,
                a.logo_key,
                a.status::VARCHAR,
                a.is_billable,
                a.agreement_number,
                a.currency,
                a.industry_vertical::VARCHAR,
                a.sales_channel::VARCHAR,
                a.sales_person,
                a.contract_end_date,
                a.contact_name,
                a.email,
                a.phone,
                a.job_title,
                a.country,
                a.state_region,
                a.city,
                a.address1,
                a.address2,
                a.postcode,
                a.sim_charge,
                a.payment_terms,
                a.organization_id,
                att.carrier_name,
                COALESCE(att.warning_threshold, 0) AS warning_threshold,
                COALESCE(atc.threshold_charge, 0) AS threshold_charge,
                COALESCE(sc.sims_total, 0)::INT AS sims_total,
                COALESCE(sc.active_sims_total, 0)::INT AS active_sims_total,
                COALESCE(rp.plans_total, 0)::INT AS plans_total,
                rp.default_rate_plan::VARCHAR AS default_rate_plan,
                COALESCE(pt.product_types, ARRAY[]::VARCHAR[]) AS product_types,
                0 AS users_total
            FROM
                account a
            LEFT JOIN
                account_traffic_thresholds att ON a.id = att.account_id
            LEFT JOIN
                account_traffic_charges atc ON a.id = atc.account_id
            LEFT JOIN
                sim_counts sc ON a.id = sc.account_id
            LEFT JOIN
                rate_plan_info rp ON a.id = rp.account_id
            LEFT JOIN
                product_types pt ON a.id = pt.account_id
            WHERE
                ($1 IS NULL OR a.id = ANY($1)) AND
                (
                    $2 IS NULL OR $2 = '''' OR EXISTS (
                        SELECT 1 FROM (
                            VALUES
                                (a.name),
                                (a.sales_person),
                                (a.email),
                                (a.phone),
                                (a.status::TEXT),
                                (a.currency),
                                (a.industry_vertical::TEXT),
                                (a.sales_channel::TEXT),
                                (a.contract_end_date::TEXT)
                        ) AS search_columns(col)
                        WHERE col ILIKE ''%'' || $2 || ''%''
                    )
                )
            ' || order_clause || pagination_clause
            USING account_id_param, search_term;
        END;
        $$ LANGUAGE plpgsql;
        """

    @staticmethod
    def create_get_account_details_view():
        return """
        CREATE OR REPLACE VIEW account_details_view AS
        SELECT *
        FROM get_account_details(NULL, NULL, 'id', 'ASC', NULL, NULL);
        """

    @staticmethod
    def drop_all_tables() -> str:
        return """
        DO $$
        DECLARE
            tbl RECORD;
            typ RECORD;
        BEGIN
            -- Drop all tables in all schemas
            FOR tbl IN
                SELECT schemaname, tablename
                FROM pg_tables
                WHERE schemaname NOT IN ('pg_catalog', 'information_schema')
            LOOP
                EXECUTE 'DROP TABLE IF EXISTS ' || quote_ident(tbl.schemaname)
                || '.' || quote_ident(tbl.tablename) || ' CASCADE';
            END LOOP;

            -- Drop all types in all schemas
            FOR typ IN
                SELECT n.nspname as schema_name, t.typname as type_name
                FROM pg_type t
                LEFT JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace
                WHERE n.nspname NOT IN ('pg_catalog', 'information_schema')
                  AND t.typtype = 'e'  -- Only drop enumerated types
            LOOP
                EXECUTE 'DROP TYPE IF EXISTS ' || quote_ident(typ.schema_name)
                || '.' || quote_ident(typ.type_name) || ' CASCADE';
            END LOOP;
        END $$;
        """

    @staticmethod
    def reset_invoice_allocation_details():
        return """
        CREATE OR REPLACE FUNCTION reset_subscription_sim_allocation_details(
            invoice_id_param INT
        )
        RETURNS VOID AS $$
            BEGIN
            DELETE FROM subscription_sim_allocation_details
            WHERE
                invoice_id = invoice_id_param;
            END;
            $$ LANGUAGE plpgsql;
        """

    @staticmethod
    def delete_subscription_sim_allocation_procedure():
        return """
        DROP FUNCTION IF EXISTS subscription_sim_allocation_details(INT, TEXT[]);
        DROP FUNCTION IF EXISTS subscription_sim_allocation_details(INT, TEXT[],DATE);
        """

    @staticmethod
    def delete_reset_invoice_allocation_details():
        return """ DROP FUNCTION IF EXISTS reset_subscription_sim_allocation_details(
            INT
            ); """

    @staticmethod
    def delete_get_account_details():
        return """ DROP FUNCTION IF EXISTS get_account_details(
            TEXT, INT[], TEXT, TEXT, INT, INT
            ); """

    @staticmethod
    def delete_get_account_details_view():
        return """ DROP VIEW IF EXISTS account_details_view; """


# Above procedures are created while implementating new billing logic
