from pydantic import BaseModel

from common.types import Service, ServiceSet


class OrderingFieldsMapper(BaseModel):
    order_name: str
    services: ServiceSet

    @property
    def field(self):
        return self.order_name.rsplit("_", 1)[-1]


usage_ordering_fields_mapper: dict[str, OrderingFieldsMapper] = {
    key: OrderingFieldsMapper(order_name=key, services=services)
    for key, services in {
        "data_charge": {Service.DATA},
        "voice_charge": {Service.VOICE_MO, Service.VOICE_MT},
        "sms_charge": {Service.SMS_MO, Service.SMS_MT},
        "data_volume": {Service.DATA},
        "sms_volume": {Service.SMS_MO, Service.SMS_MT},
        "sms_mo_volume": {Service.SMS_MO},
        "sms_mt_volume": {Service.SMS_MT},
        "voice_volume": {Service.VOICE_MO, Service.VOICE_MT},
        "voice_mo_volume": {Service.VOICE_MO},
        "voice_mt_volume": {Service.VOICE_MT},
    }.items()
}
