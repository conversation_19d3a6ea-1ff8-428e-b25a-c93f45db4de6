import json
import logging
import time
from abc import ABC, abstractmethod
from dataclasses import asdict
from typing import Any, Dict

import requests
from fastapi.encoders import jsonable_encoder

from app.config import logger, settings
from app.security import get_kafka_service_access_token


class AbstractKafkaAPI(ABC):
    @abstractmethod
    def submit_to_kafka(
        self, key: str, audit_details: list, topic: str | None = None
    ) -> bool:
        ...


class HTTPKafkaAPI(AbstractKafkaAPI):
    SUMBIT_KAFKA_RECORDS = "/topics/{topic}"

    def __init__(self) -> None:
        self.headers = {
            "Content-Type": "application/vnd.kafka.json.v2+json",
        }

    def get_access_kafka_token(self) -> str:
        max_retries = settings.KAFKA_MAX_RETRIES
        retry_delay = settings.KAFKA_RETRY_DELAY
        if max_retries is None or retry_delay is None:
            raise ValueError("Kafka retry settings are missing.")
        for attempt in range(1, max_retries + 1):
            try:
                token = get_kafka_service_access_token()
                return f"Bearer {token}"
            except Exception as e:
                if attempt == max_retries:
                    logger.error(
                        f"Failed to get Kafka token after {max_retries} attempts: {e}"
                    )
                    raise
                logger.error(
                    f"Retrying to get Kafka token (attempt {attempt}/{max_retries})..."
                )
                time.sleep(retry_delay * attempt)
        raise RuntimeError("Kafka token retrieval fell through logic")

    def construct_kafka_message(self, key: str, audit_details: list) -> Dict[str, Any]:
        audit_json = jsonable_encoder([asdict(audit) for audit in audit_details])

        message = {"records": [{"key": key, "value": {"audits": audit_json}}]}
        logger.info("Kafka message constructed.")
        return message

    def send_to_kafka(
        self, message: Dict[str, Any], token: str, topic: str | None = None
    ) -> bool:
        logging.info(f"Submitting message to Kafka on topic '{topic}'.")

        self.headers["Authorization"] = token
        response = requests.post(
            url=f"{settings.KAFKA_BASE_URL}{self.SUMBIT_KAFKA_RECORDS.format(topic=topic)}",  # noqa
            headers=self.headers,
            data=json.dumps(message),
            timeout=settings.TIMEOUT,
        )
        if response.status_code != 200:
            logging.error(f"Failed to submit message to Kafka: {response.text}")
            response.raise_for_status()
        logging.info(f"Message submitted to Kafka: {response.json()}")
        return True

    def submit_to_kafka(
        self, key: str, audit_details: list, topic: str | None = None
    ) -> bool:
        BATCH_SIZE = 800
        if not audit_details:
            return True

        token = self.get_access_kafka_token()

        if topic is None:
            logger.error("TOPIC setting is missing.")
            raise ValueError("TOPIC setting is missing.")

        all_success = True
        total_batches = (len(audit_details) + BATCH_SIZE - 1) // BATCH_SIZE

        # Inline batching loop
        for i in range(0, len(audit_details), BATCH_SIZE):
            batch = audit_details[i : i + BATCH_SIZE]
            message = self.construct_kafka_message(key, batch)
            success = self.send_to_kafka(topic=topic, message=message, token=token)
            if not success:
                all_success = False
        logger.info(f"All {total_batches} batches processed.")

        return all_success
