from itertools import product
from operator import attrgetter
from typing import Any, Callable, Optional, Type, TypeVar

from pydantic import BaseModel, ConstrainedStr
from sqlalchemy import String, any_, cast, or_
from sqlalchemy.sql import Select


class Search(ConstrainedStr):
    max_length = 512


T = TypeVar("T")


class Searching(BaseModel):
    search: Search
    fields: set[str]

    @classmethod
    def build(
        cls,
        fields: set[str],
    ) -> Callable[[], Optional["Searching"]]:
        def factory(search: Search | None = None) -> Optional["Searching"]:
            if not search:
                return None
            return Searching(search=search, fields=fields)

        return factory

    @property
    def terms(self) -> list[str]:
        params = self.search.replace("\x00", "")
        return params.split()

    def filter(self, instance: T) -> bool:
        values = (attrgetter(field)(instance) for field in self.fields)
        filtered_values = filter(lambda v: v, values)
        return any(
            [term in value for value, term in product(filtered_values, self.terms)]
        )


class SearchingFilterBuilder:
    def __call__(self, searching: Searching, model: Type[T], stmt: Select) -> Select:
        self.stmt = stmt
        self.searching = searching
        self.model = model
        self.wrapped_terms = self._wrap_terms()
        self.model_columns = self._get_model_columns()
        return self._build_filter()

    def _get_model_column(self, model: Any, field: str) -> Any:
        if "." not in field:
            return getattr(model, field)
        else:
            field, subfields = field.split(".", 1)
            relationship = getattr(model, field)
            child_model = relationship.property.mapper.class_
            return self._get_model_column(child_model, subfields)

    def _get_model_columns(self) -> list[Any]:
        colums = []
        for field in self.searching.fields:
            column = self._get_model_column(self.model, field)
            colums.append(column)
        return colums

    def _wrap_terms(self) -> list[str]:
        return [f"%{term}%" for term in self.searching.terms]

    def _build_filter(self) -> Select:
        criteria = []
        for model_column in self.model_columns:
            stmt = cast(model_column, String).ilike(any_(self.wrapped_terms))
            criteria.append(stmt)
        return self.stmt.filter(or_(*criteria))


apply_search_to_sql = SearchingFilterBuilder()
