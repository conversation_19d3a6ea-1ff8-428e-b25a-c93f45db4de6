import operator
from functools import reduce
from operator import attrgetter
from typing import Sequence, Set

from typing_extensions import assert_never

from common.constants import (
    BYTES,
    DBYTES,
    DGB,
    DKB,
    DMB,
    DMINUTE,
    DPB,
    DTB,
    GB,
    KB,
    MB,
    MINUTE,
    PB,
    TB,
)
from rating.domain.model import Service, UsageGroup


def disjoint(values: Sequence[UsageGroup]) -> bool:
    """Return True if groups have a null intersection."""

    for i in range(len(values) - 1):
        if intersects(values[i], values[i + 1]):
            return False
    return True


def intersects(a: UsageGroup, b: UsageGroup) -> bool:
    """Return True if groups have intersection."""

    return not a.services.isdisjoint(b.services)


def service_coverage(values: Sequence[UsageGroup]) -> Set[Service]:
    """Return combined set of services in usage groups."""
    return reduce(operator.or_, map(attrgetter("services"), values))


def price_ratio(a: UsageGroup) -> int:
    """Return price/usage units ratio."""
    service, *_ = a.services
    match service:
        case Service.DATA:
            price_unit = a.rates[0].price_unit  # type: ignore
            if price_unit == "BYTES":
                return BYTES
            elif price_unit == "KB":
                return KB
            elif price_unit == "MB":
                return MB
            elif price_unit == "GB":
                return GB
            elif price_unit == "TB":
                return TB
            elif price_unit == "PB":
                return PB
            else:
                raise ValueError(f"Unsupported price unit: {price_unit}")

        case Service.VOICE_MT | Service.VOICE_MO:
            return MINUTE
        case Service.SMS_MT | Service.SMS_MO:
            return 1
        case _:
            assert_never(service)


def range_ratio(a: UsageGroup) -> int:
    """Return range/usage units ratio."""
    service, *_ = a.services
    match service:
        case Service.DATA:
            range_unit = a.rates[0].range_unit  # type: ignore
            if range_unit == "BYTES":
                return BYTES
            elif range_unit == "KB":
                return KB
            elif range_unit == "MB":
                return MB
            elif range_unit == "GB":
                return GB
            elif range_unit == "TB":
                return TB
            elif range_unit == "PB":
                return PB
            else:
                raise ValueError(f"Unsupported range unit: {range_unit}")

        case Service.VOICE_MT | Service.VOICE_MO:
            return MINUTE
        case Service.SMS_MT | Service.SMS_MO:
            return 1
        case _:
            assert_never(service)


def overage_ratio(a: UsageGroup) -> int:
    """Return price/usage units ratio."""
    service, *_ = a.services
    match service:
        case Service.DATA:
            overage_unit = a.rates[0].overage_unit  # type: ignore
            if overage_unit == "BYTES":
                return BYTES
            elif overage_unit == "KB":
                return KB
            elif overage_unit == "MB":
                return MB
            elif overage_unit == "GB":
                return GB
            elif overage_unit == "TB":
                return TB
            elif overage_unit == "PB":
                return PB
            else:
                raise ValueError(f"Unsupported overage unit: {overage_unit}")

        case Service.VOICE_MT | Service.VOICE_MO:
            return MINUTE
        case Service.SMS_MT | Service.SMS_MO:
            return 1
        case _:
            assert_never(service)


def overage_ratio_divisor(a: str) -> int:
    """Return price/usage units ratio."""
    overage_unit = a
    if overage_unit == "BYTES":
        return DBYTES
    elif overage_unit == "KB":
        return DKB
    elif overage_unit == "MB":
        return DMB
    elif overage_unit == "GB":
        return DGB
    elif overage_unit == "TB":
        return DTB
    elif overage_unit == "PB":
        return DPB
    elif overage_unit == "Min":
        return DMINUTE
    elif overage_unit == "SMS":
        return 1
    else:
        raise ValueError(f"Unsupported overage unit: {overage_unit}")
