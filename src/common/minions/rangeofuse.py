from copy import copy
from enum import IntEnum, auto
from typing import Sequence, TypeVar, overload

from _operator import attrgetter

from rating.domain.model import RangeOfUse


def sort(ranges: Sequence[RangeOfUse]) -> list[RangeOfUse]:
    """Returns ranges sorted by range_from."""

    return list(sorted(ranges, key=attrgetter("range_from")))


def contiguous(ranges: Sequence[RangeOfUse]) -> bool:
    """Returns True if there are no gaps between ranges."""

    sorted_values = sort(ranges)
    for i in range(len(sorted_values) - 1):
        if not abut(sorted_values[i], sorted_values[i + 1]):
            return False
    return True


def abut(a: RangeOfUse, b: RangeOfUse) -> bool:
    """Return True if no gap between ranges."""

    return a.range_to == b.range_from


def is_complete(ranges: Sequence[RangeOfUse]) -> bool:
    """
    Return True if ranges concatenates into global range (from 0 to None) without gaps.
    """

    if not ranges:
        raise ValueError("ranges parameter cannot be empty.")
    if not contiguous(ranges):
        return False
    return (0, None) == (ranges[0].range_from, ranges[-1].range_to)


class VolumePosition(IntEnum):
    BELOW = auto()
    INSIDE = auto()
    ABOVE = auto()


def volume_position(r: RangeOfUse, volume: int) -> VolumePosition:
    if volume < r.range_from:
        return VolumePosition.BELOW
    elif r.range_to is None or volume < r.range_to:
        return VolumePosition.INSIDE
    else:
        return VolumePosition.ABOVE


def coverage(r: RangeOfUse) -> int:
    """Returns volume amount covered by range."""
    if r.range_to is None:
        raise ValueError("Unlimited range provided.")
    return r.range_to - r.range_from


RT = TypeVar("RT", bound=RangeOfUse)


@overload
def adjust_units(value: RT, ratio: int) -> RT:
    ...


@overload
def adjust_units(value: Sequence[RT], ratio: int) -> Sequence[RT]:
    ...


def adjust_units(value: RT | Sequence[RT], ratio: int) -> RT | Sequence[RT]:
    if isinstance(value, Sequence):
        return [_adjust_units(r, ratio) for r in value]
    return _adjust_units(value, ratio)


def _adjust_units(r: RT, ratio: int) -> RT:
    new = copy(r)
    new.range_from *= ratio
    if new.range_to is not None:
        new.range_to *= ratio
    return new
