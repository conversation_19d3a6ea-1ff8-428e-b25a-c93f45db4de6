import math
from typing import Callable, Generic, Iterable, Sequence, TypeVar

from fastapi import Query
from pydantic import BaseModel, NonNegativeInt, PositiveInt
from pydantic.generics import GenericModel

T = TypeVar("T")


class InvalidPage(Exception):
    ...


class Pagination(BaseModel):
    """Pagination parameters."""

    page: PositiveInt
    page_size: PositiveInt

    @staticmethod
    def query(
        default_page_size: int = 50, max_page_size: int = 200
    ) -> Callable[[], "Pagination"]:
        """Constructs query parameters for a paginated response."""

        if default_page_size > max_page_size:
            raise ValueError(
                f"default page {default_page_size} is greater than max {max_page_size}"
            )

        def extract_query(
            page: int = Query(1, ge=1),
            page_size: int = Query(default_page_size, ge=1, le=max_page_size),
        ) -> Pagination:
            return Pagination(page=page, page_size=page_size)

        return extract_query

    @property
    def offset(self) -> int:
        """Zero-based offset for collection."""
        return (self.page - 1) * self.page_size

    def slice(self, collection: Sequence[T]) -> Sequence[T]:
        """Get a fragment of the collection that matches the pagination parameters."""
        return collection[self.offset : self.offset + self.page_size]


PaginatedResponseT = TypeVar("PaginatedResponseT", bound="PaginatedResponse")


class PaginatedResponse(GenericModel, Generic[T]):
    page: PositiveInt
    page_size: PositiveInt
    last_page: PositiveInt
    total_count: NonNegativeInt
    summary: dict | None = None
    results: list[T]

    @classmethod
    def from_iterable(
        cls: type[PaginatedResponseT],
        pagination: Pagination,
        results: Iterable[T],
        total_count: NonNegativeInt,
        summary: dict | None = None,
        **kwargs,
    ) -> PaginatedResponseT:
        """Wraps the collection's page into a paginated response."""
        last_page = math.ceil(total_count / pagination.page_size) or 1
        if pagination.page > last_page:
            raise InvalidPage(
                f"Page {pagination.page} is out of boundary: last page is {last_page}."
            )
        return cls(
            page=pagination.page,
            page_size=pagination.page_size,
            last_page=last_page,
            total_count=total_count,
            summary=summary,
            results=list(results),
            **kwargs,
        )

    @classmethod
    def from_full_sequence(
        cls, pagination: Pagination, items: Sequence[T]
    ) -> "PaginatedResponse[T]":
        """Response for a specific page from a whole collection."""
        return cls.from_iterable(
            pagination,
            pagination.slice(items),
            len(items),
        )
