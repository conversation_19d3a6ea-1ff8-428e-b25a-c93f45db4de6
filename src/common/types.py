import mimetypes
import re
from collections import defaultdict
from datetime import date, datetime
from decimal import Decimal
from enum import Enum
from typing import TYPE_CHECKING, Any, ClassVar, Literal, Protocol, TypeAlias

from dateutil.relativedelta import relativedelta
from pydantic import BaseModel, ConstrainedStr, NonNegativeInt, errors, root_validator
from pydantic.dataclasses import dataclass

if TYPE_CHECKING:
    from pydantic.typing import CallableGenerator


class Month(date):
    """Date without a day or simply Month."""

    @classmethod
    def __get_validators__(cls) -> "CallableGenerator":
        yield cls.validate

    @classmethod
    def __modify_schema__(cls, field_schema: dict[str, Any]) -> None:
        field_schema.update(
            type="string",
            format="month",
            pattern=r"^20\d{2}-(0[1-9]|1[0-2])$",
            examples=["2021-01", "2022-12", "2019-08"],
            example="2022-11",
        )

    @classmethod
    def validate(cls, v: Any) -> "Month":
        if isinstance(v, date):
            return cls.from_date(v)
        elif isinstance(v, str):
            return cls.from_str(v)
        else:
            raise TypeError("string or date expected")

    @classmethod
    def from_date(cls, v: date) -> "Month":
        if v.day != 1:
            raise ValueError('day should be exactly "1"')
        return cls(v.year, v.month, v.day)

    @classmethod
    def from_str(cls, v: str) -> "Month":
        d = datetime.strptime(v, "%Y-%m").date()
        return cls.from_date(d)

    @classmethod
    def today(cls) -> "Month":
        today = date.today()
        return cls.from_date(date(today.year, today.month, 1))

    def next(self) -> "Month":
        return Month.from_date(self + relativedelta(months=1))

    def prev(self) -> "Month":
        return Month.from_date(self - relativedelta(months=1))

    def __str__(self) -> str:
        return f"{self:%Y-%m}"

    def __repr__(self) -> str:
        return f"Month({self.year}, {self.month})"


class ContentType(BaseModel):
    head: str
    tail: str

    @root_validator
    def pre_save_validator(cls, values: dict[str, str]) -> dict[str, str]:
        cls._validate_mime_type(f"{values['head']}/{values['tail']}")
        return values

    @classmethod
    def parse(cls, mime_type: str) -> "ContentType":
        cls._validate_mime_type(mime_type)
        content = mime_type.split("/")
        return cls(head=content[0], tail=content[1])

    @staticmethod
    def _validate_mime_type(mime_type: str) -> None:
        if mime_type not in mimetypes.types_map.values():
            raise ValueError("Invalid Content-type")

    @property
    def mime_type(self) -> str:
        return f"{self.head}/{self.tail}"

    @property
    def extension(self) -> str:
        return mimetypes.guess_extension(self.mime_type) or ""


class CountryCode(ConstrainedStr):
    min_length = 2
    max_length = 2
    to_upper = True


class CurrencyCode(ConstrainedStr):
    min_length = 3
    max_length = 3
    to_upper = True


class DigitalStr(ConstrainedStr):
    regex = re.compile(r"\d+")

    @classmethod
    def from_int(cls, v: int):
        str_v = str(v)
        v_len = len(str_v)
        if cls.min_length is not None and v_len < cls.min_length:
            return cls(f"{str_v:0>{cls.min_length}}")
        if cls.max_length is not None and v_len > cls.max_length:
            raise errors.AnyStrMaxLengthError(limit_value=cls.max_length)
        return cls(str_v)


class IMSI(DigitalStr):
    min_length = 14
    max_length = 15


class SimStatus(str, Enum):
    READY_FOR_ACTIVATION = "Ready for Activation"
    ACTIVE = "Active"
    DEACTIVATED = "Deactivated"
    PENDING = "Pending"


class DefaultRole(str, Enum):
    ClientAdmin = "Admin"
    ClientUser = "User"
    DistributorUser = "DistributorUser"
    Client_ReadOnly = "Client_ReadOnly"


class RangeOfUse(Protocol):
    """
    Used to set rates according to usage volumes.
    range_from < range_to and None means infinity for range_to.
    """

    range_from: NonNegativeInt
    range_to: NonNegativeInt | None


class Service(str, Enum):
    """GSM service provided by a mobile operator."""

    VOICE_MO = "VOICE_MO"
    VOICE_MT = "VOICE_MT"
    SMS_MO = "SMS_MO"
    SMS_MT = "SMS_MT"
    DATA = "DATA"


VoiceService: TypeAlias = Literal[Service.VOICE_MO, Service.VOICE_MT]
SMSService: TypeAlias = Literal[Service.SMS_MO, Service.SMS_MT]
DataService: TypeAlias = Literal[Service.DATA]

ServiceSet: TypeAlias = set[VoiceService] | set[SMSService] | set[DataService]


class UsageGroup(Protocol):
    """A set of usage parameters."""

    services: ServiceSet


class FormFactor(str, Enum):
    """IMSI range types for SIM."""

    STANDARD = "STANDARD"
    MICRO = "MICRO"
    NANO = "NANO"
    eSIM_MFF2 = "eSIM_MFF2"
    eSIM_MFF2_eUICC = "eSIM_MFF2_eUICC"


@dataclass
class ServiceUsage:
    service: Service
    volume: int
    sub_charge: Decimal
    total_overage_charge: Decimal
    bulk_overage_charge: Decimal
    charge: Decimal


class Enumeration(BaseModel):
    ref: str
    name: str

    @classmethod
    def from_enum_value(cls, v: Enum) -> "Enumeration":
        return cls(ref=v.name, name=v.value)


class EnumerationList(BaseModel):
    __root__: list[Enumeration]

    @classmethod
    def from_enum(cls, e: type[Enum]) -> "EnumerationList":
        return cls.parse_obj([dict(ref=p.name, name=p.value) for p in e])


class KeyEnumeration:
    _model_enum: ClassVar[defaultdict[type, type[Enum]]] = defaultdict()

    @classmethod
    def generate(cls, source: type[Enum]) -> type[Enum]:
        existing_enum = cls._model_enum.get(source)
        if existing_enum:
            return existing_enum
        enum_obj = Enum(  # type: ignore
            source.__name__,
            {m.name: m.name for m in list(source)},
            type=str,
        )
        cls._model_enum[source] = enum_obj
        return enum_obj


class Carrier(ConstrainedStr):
    to_upper = True


class ICCID(DigitalStr):
    min_length = 19
    max_length = 20


class MSISDN(DigitalStr):
    min_length = 8
    max_length = 15


class ImageFormat(str):
    png = "png"
    svg = "svg+xml"


class Version(str):
    """Custom datatype for representing version strings."""

    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def __modify_schema__(cls, field_schema: dict[str, Any]):
        field_schema.update(
            type="string",
            format="version",
            pattern=r"^\d+(\.\d+){2}$",
            examples=["1.0.0", "1.2.3", "1.0.9", "1.3.3"],
            example="1.2.3",
        )

    @classmethod
    def validate(cls, v):
        if isinstance(v, str):
            if cls.is_valid_version(v):
                return cls(v)
            else:
                raise ValueError("Invalid version format.")
        else:
            raise TypeError("Invalid type for Version. Expected a string.")

    @staticmethod
    def is_valid_version(v):
        # Using a regex pattern to validate the version string (0-9 and periods only).
        pattern = r"^\d+(\.\d+){2}$"
        return bool(re.match(pattern, v))


class FormFactorCode(str, Enum):
    """Form factor code."""

    _2FF = "2FF"
    _3FF = "3FF"
    _4FF = "4FF"
    eSIM_MFF2 = "eSIM_MFF2"
    eSIM_MFF2_eUICC = "eSIM_MFF2_eUICC"


form_factor_mapping = {
    FormFactorCode._2FF: FormFactor.STANDARD,
    FormFactorCode._3FF: FormFactor.MICRO,
    FormFactorCode._4FF: FormFactor.NANO,
    FormFactorCode.eSIM_MFF2: FormFactor.eSIM_MFF2,
    FormFactorCode.eSIM_MFF2_eUICC: FormFactor.eSIM_MFF2_eUICC,
}


class DataUnit(str, Enum):
    """Unit of data usage."""

    BYTES = "BYTES"
    KB = "KB"
    MB = "MB"
    GB = "GB"
    TB = "TB"
    PB = "PB"


class VoiceUnit(str, Enum):
    """Unit of voice usage."""

    Mins_MO = "Mins_MO"
    Mins_MT = "Mins_MT"
    Mins_MO_MT = "Mins_MO_MT"


class SMSUnit(str, Enum):
    """Unit of SMS usage."""

    SMS = "SMS"


class DataPercentage(str, Enum):
    """Unit of percentage usage."""

    PERCENTAGE = "PERCENTAGE"


class VoiceMOPercentage(str, Enum):
    """Unit of voice MO percentage usage."""

    VOICE_MO_PERCENTAGE = "VOICE_MO_PERCENTAGE"


class VoiceMTPercentage(str, Enum):
    """Unit of voice MT percentage usage."""

    VOICE_MT_PERCENTAGE = "VOICE_MT_PERCENTAGE"


class VoiceMOMTPercentage(str, Enum):
    """Unit of voice MOMT percentage usage."""

    VOICE_MOMT_PERCENTAGE = "VOICE_MOMT_PERCENTAGE"


class SMSPercentageUnit(str, Enum):
    SMS_MO_PERCENTAGE = "SMS_MO_PERCENTAGE"


class Unit(str, Enum):
    """Combined unit of usage."""

    BYTES = DataUnit.BYTES.value
    KB = DataUnit.KB.value
    MB = DataUnit.MB.value
    GB = DataUnit.GB.value
    TB = DataUnit.TB.value
    PB = DataUnit.PB.value
    Min = "Min"
    Mins_MO = VoiceUnit.Mins_MO.value
    Mins_MT = VoiceUnit.Mins_MT.value
    Mins_MO_MT = VoiceUnit.Mins_MO_MT.value
    Unit = "Unit"
    SMS = SMSUnit.SMS.value
    PERCENTAGE = DataPercentage.PERCENTAGE.value
    VOICE_MO_PERCENTAGE = VoiceMOPercentage.VOICE_MO_PERCENTAGE.value
    VOICE_MT_PERCENTAGE = VoiceMTPercentage.VOICE_MT_PERCENTAGE.value
    VOICE_MOMT_PERCENTAGE = VoiceMOMTPercentage.VOICE_MOMT_PERCENTAGE.value
    SMS_MO_PERCENTAGE = SMSPercentageUnit.SMS_MO_PERCENTAGE.value


CONVERSION_FACTORS = {
    Unit.BYTES: 1,
    Unit.KB: 1024,
    Unit.MB: 1024**2,
    Unit.GB: 1024**3,
    Unit.TB: 1024**4,
    Unit.PB: 1024**5,
}


class DataVolume(int):
    min_value = 1
    max_value = 9999999999999999


class RULE_NAME(ConstrainedStr):
    min_length = 1
    max_length = 50


class SimProfile(str, Enum):
    DATA_ONLY = "DATA_ONLY"
    VOICE_SMS_DATA = "VOICE_SMS_DATA"


class UploadFileStatus(str, Enum):
    PENDING = "Pending"
    COMPLETED = "Completed"
    FAILED = "Failed"
    RECEIVED = "File Received"
    PARTIALLY = "Partially Completed"
