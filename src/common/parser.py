import codecs
from abc import abstractmethod
from collections import deque
from csv import Dict<PERSON><PERSON><PERSON>
from typing import Iterable, Iterator, Sequence, TypeVar

_T_co = TypeVar("_T_co", covariant=True)


class ParsingError(Exception):
    """Raised when an error occurred during parsing a file"""


class BaseCSVParser(Iterable[_T_co]):
    REQUIRED_FIELDNAMES: tuple = ()

    def __init__(self, file: Iterable[bytes], delimiter=",") -> None:
        self.reader = DictReader(
            codecs.iterdecode(file, "utf-8-sig"), delimiter=delimiter
        )
        self._validate_fieldnames(self.reader.fieldnames)

    def __iter__(self) -> Iterator[_T_co]:
        return self

    def __next__(self) -> _T_co:
        if self.reader is None:
            raise AssertionError("Reader must not be None")
        row = next(self.reader)
        return self._parse_row(row)

    @abstractmethod
    def _parse_row(self, row: dict[str, str]) -> _T_co:
        ...

    def _validate_fieldnames(self, fieldnames: Sequence[str] | None) -> None:
        if not fieldnames or not set(self.REQUIRED_FIELDNAMES).issubset(
            set(fieldnames)
        ):
            miss_fields = list(
                set(self.REQUIRED_FIELDNAMES).difference(set(fieldnames or {}))
            )
            raise ParsingError(f"CSV is missing several required fields: {miss_fields}")


class BaseCSVParserRowInList(BaseCSVParser, Iterable[_T_co]):
    def __init__(self, file: Iterable[bytes]):
        self._records: deque[_T_co] = deque()
        super().__init__(file)

    def __next__(self) -> _T_co:
        while not self._records:
            if self.reader is None:
                raise AssertionError("Reader must not be None")
            row = next(self.reader)
            records = self._parse_row(row)
            self._records.extend(records)
        return self._records.popleft()

    @abstractmethod
    def _parse_row(self, row: dict[str, str]) -> list[_T_co]:
        ...
