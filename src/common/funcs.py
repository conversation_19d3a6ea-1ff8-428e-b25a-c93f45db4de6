from calendar import monthrange
from datetime import datetime

from common.types import CONVERSION_FACTORS, Month, Unit


def convert_to_bytes(sim_usage_limit: int, unit: Unit) -> int:
    """
    Convert a given size with unit to bytes.

    Parameters:
    - sim_usage_limit (int): The size to be converted.
    - unit (str): It should be: 'BYTES', 'KB', 'MB', 'GB', 'TB'.

    Returns:
    - int: The size in bytes.
    """
    units = CONVERSION_FACTORS

    if unit not in units:
        raise ValueError(f"Unknown unit: {unit}")

    return sim_usage_limit * units[unit]


def Derive_Month_Start_End_Date(month: Month):
    year = month.year
    month_number = month.month
    start_date = datetime(year, month_number, 1)
    last_day = monthrange(year, month_number)[1]
    end_date = datetime(year, month_number, last_day)
    return start_date, end_date
