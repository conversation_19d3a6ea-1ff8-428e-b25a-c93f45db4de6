import operator
from collections import defaultdict
from enum import Enum
from functools import reduce
from operator import attrgetter
from typing import Callable, ClassVar, Sequence

from fastapi import Query
from pydantic import BaseModel


class OrderDirection(str, Enum):
    ASC = "ASC"
    DESC = "DESC"


class Ordering(BaseModel):
    """Ordering parameters."""

    _model_choices: ClassVar[defaultdict[type, list[type]]] = defaultdict(list)
    field: str
    order: OrderDirection

    @classmethod
    def query(
        cls,
        model: type[BaseModel],
        default_ordering: str,
        ordering_fields: tuple[str, ...],
    ) -> Callable[[], "Ordering"]:
        """Constructs query parameter for an ordered response."""

        choices_name = f"{model.__name__}Ordering"
        existing_choices_count = len(cls._model_choices[model])
        if existing_choices_count:
            choices_name = f"{choices_name}{existing_choices_count + 1}"
        choices = Enum(  # type: ignore
            choices_name,
            reduce(
                operator.or_,
                (
                    {f"{field}_asc": field, f"{field}_desc": f"-{field}"}
                    for field in ordering_fields
                ),
            ),
            type=str,
        )
        cls._model_choices[model].append(choices)
        default_choice = choices(default_ordering)

        def extract_query(
            ordering: choices = Query(
                default_choice,
                description='Sort by field, use the "-" prefix for descending order',
            ),
        ) -> Ordering:
            field, order = ordering.name.rsplit("_", 1)
            return Ordering(field=field, order=OrderDirection[order.upper()])

        return extract_query


class OrderingResolver:
    def __init__(
        self,
        mapped_fields: dict[str, attrgetter],
        ordering: Ordering,
        additional_rules: dict[str, Callable] | None = None,
    ):
        self.main_rules = mapped_fields
        self.additional_rules = additional_rules
        self.ordering = ordering

    def sort_records(self, records: Sequence) -> list:
        ordering_rule = self._generate_ordering_rule()
        return sorted(
            records,
            key=lambda record: ordering_rule(record),
            reverse=self.ordering.order.lower() == "desc",
        )

    def _generate_ordering_rule(self) -> Callable:
        if self.additional_rules and self.ordering.field in self.additional_rules:
            return self.additional_rules[self.ordering.field]
        else:
            if self.ordering.field in self.main_rules:
                field_attr = self.main_rules[self.ordering.field]
            else:
                field_attr = attrgetter(self.ordering.field)
            return lambda record: (
                field_attr(record) is None,
                field_attr(record),
            )
