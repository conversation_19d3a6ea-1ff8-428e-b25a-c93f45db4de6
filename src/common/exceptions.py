class VersionException(Exception):
    ...


class VersionNotFound(VersionException):
    ...


class AnalyticsAPIError(Exception):
    def __init__(self):
        super().__init__("Analytics api error")


class Unprocessable(Exception):
    ...


class OpenApiError(Exception):
    ...


class AuditAPIError(Exception):
    def __init__(self):
        super().__init__("Audit api error")


class MediaError(Exception):
    ...
