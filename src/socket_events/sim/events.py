"""Socket event handlers for SIM-related events."""

import logging
from typing import List

logger = logging.getLogger(__name__)


class SimSocketEvents:
    """
    Socket.IO event handlers for SIM-related events.
    """

    def __init__(self, sio):
        self.sio = sio
        self.register_events()

    def register_events(self) -> None:
        """Register all SIM-related event handlers."""
        pass

    async def sim_details_update(self, imsi_list: List, status: str) -> None:
        event_data = {"imsi": imsi_list, "simStatus": status}
        try:
            await self.sio.emit("sim_details_update", event_data)
            logger.info("[Socket] Emitted 'sim_details_update' event")
            logger.debug(f"[Socket] Payload: {event_data}")
        except Exception as e:
            logger.error(
                f"[Socket] Failed to emit 'sim_details_update' event: {str(e)}"
            )
            logger.debug(f"[Socket] Event data: {event_data}")


def get_sim_socket_events(sio):
    """
    Create and return a SimSocketEvents instance.

    Args:
        sio: The Socket.IO server instance

    Returns:
        SimSocketEvents: The SIM socket events handler
    """
    return SimSocketEvents(sio)
