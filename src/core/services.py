from abc import ABC, abstractmethod

from core.adapters.repository import AbstractCoreRepository
from core.domain import model


class AbstractCoreService(ABC):
    @abstractmethod
    def create_partitions(
        self, create_collection: model.CreatePartitions
    ) -> model.CreatePartitionsResponse:
        ...


class CoreService(AbstractCoreService):
    def __init__(self, repository: AbstractCoreRepository):
        self.repository = repository

    def create_partitions(
        self, create_collection: model.CreatePartitions
    ) -> model.CreatePartitionsResponse:
        return self.repository.create_partitions(create_collection)
