from abc import ABC, abstractmethod

from sqlalchemy import func, select
from sqlalchemy.orm import Session

from app.config import logger
from core.domain import model


class AbstractCoreRepository(ABC):
    @abstractmethod
    def create_partitions(
        self, partitions: model.CreatePartitions
    ) -> model.CreatePartitionsResponse:
        ...


class DataBaseCoreRepository(AbstractCoreRepository):
    def __init__(self, session: Session) -> None:
        self.session = session

    def create_partitions(
        self, partitions: model.CreatePartitions
    ) -> model.CreatePartitionsResponse:
        created: list[str] = []
        skipped: list[str] = []
        for partition in partitions.partitions:
            try:
                function_response = func.create_next_month_partition(
                    partition, partitions.month
                )
                query = select("*").select_from(function_response)
                response = self.session.execute(query)
                message = str(response.scalar())

                if "Partition created" in message:
                    created.append(f"{partition}_{partitions.month}")
                    logger.info(f"Partition created successfully: {partition}")
                elif "Partition already exists" in message:
                    skipped.append(f"{partition}_{partitions.month}")
                    logger.info(f"Partition already exists, skipping: {partition}")
                else:
                    logger.warning(
                        f"Unexpected response for partition '{partition}': {message}"
                    )

            except Exception as e:
                logger.error(f"Error creating partition '{partition}': {e}")
                raise
        self.session.commit()
        return model.CreatePartitionsResponse(
            created=created,
            skipped=skipped,
        )
