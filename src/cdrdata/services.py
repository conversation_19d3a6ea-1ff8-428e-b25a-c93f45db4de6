import json
import logging
import uuid
import xml.etree.ElementTree as ET  # nosec
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Iterator
from uuid import UUID, uuid4

from defusedxml.ElementTree import fromstring

from caching.service import AbstractRedisService
from cdrdata.adapters.repository import AbstractCdrRepository
from cdrdata.domain import model
from cdrdata.domain.ports import AbstractMarketShare
from common.types import ICCID, IMSI, Month


class AbstractCdrService(ABC):
    @abstractmethod
    def add_cdr(self, xml_string: str, cdr_object_id: str, file_key: str) -> UUID:
        ...

    @abstractmethod
    def get_cdr_last_session(self, imsi: IMSI) -> datetime:
        ...

    @abstractmethod
    def add_cdr_to_analytics(self, xml_string: dict) -> model.AnalyticsCDRObject:
        ...

    @abstractmethod
    def is_valid_xml_cdr(self, xml_string: str) -> bool:
        ...

    @abstractmethod
    def get_duplicate_cdr(self, month: Month) -> Iterator[model.DuplicateCdr]:
        ...

    @abstractmethod
    def is_duplicate_cdr(
        self, xml_string: str, redis_service: AbstractRedisService
    ) -> str | None:
        ...

    @abstractmethod
    def get_updated_cdr(self, s3_key, xml_string: str):
        ...


class CdrService(AbstractCdrService):
    def __init__(
        self,
        cdr_repository: AbstractCdrRepository,
        market_share: AbstractMarketShare,
    ):
        self.cdr_repository = cdr_repository
        self.market_share = market_share

    @staticmethod
    def create_cdr_data(cdr: model.CDR) -> model.CDRData | None:
        if cdr.data is None:
            return None
        return model.CDRData(
            cdr_uuid=str(uuid4()),
            session_starttime=cdr.data.session_starttime,
            session_endtime=cdr.data.session_endtime,
            duration=cdr.data.duration,
            data_volume=cdr.data.data_volume,
        )

    @staticmethod
    def create_cdr_voice(cdr: model.CDR) -> model.CDRVoice | None:
        if cdr.voice is None:
            return None
        return model.CDRVoice(
            cdr_uuid=str(uuid4()),
            call_date=cdr.voice.call_date,
            call_number=cdr.voice.call_number,
            call_minutes=cdr.voice.call_minutes,
        )

    @staticmethod
    def create_cdr_sms(cdr: model.CDR) -> model.CDRSMS | None:
        if cdr.sms is None:
            return None
        return model.CDRSMS(
            cdr_uuid=str(uuid4()),
            date_sent=cdr.sms.date_sent,
            sent_from=cdr.sms.sent_from,
            sent_to=cdr.sms.sent_to,
        )

    # TODO Need to do same for voice,sms
    @staticmethod
    def create_data_usage_aggregate(cdr: model.CDR) -> model.DataUsageAggregate | None:
        if cdr.data is None:
            return None
        year = cdr.data.session_endtime.year
        month = cdr.data.session_endtime.month
        return model.DataUsageAggregate(
            cdr_uuid=str(uuid4()),
            imsi=cdr.imsi,
            iccid=cdr.iccid,
            usage=cdr.data.data_volume,
            month=Month(year=year, month=month, day=1),
        )

    @staticmethod
    def create_voice_aggregate(cdr: model.CDR) -> model.VoiceUsageAggregate | None:
        if cdr.voice is None:
            return None
        year = cdr.voice.call_date.year
        month = cdr.voice.call_date.month
        return model.VoiceUsageAggregate(
            cdr_uuid=str(uuid4()),
            imsi=cdr.imsi,
            iccid=cdr.iccid,
            voice_usage=cdr.voice.call_minutes,
            month=Month(year=year, month=month, day=1),
        )

    @staticmethod
    def create_sms_aggregate(cdr: model.CDR) -> model.SMSUsageAggregate | None:
        if cdr.sms is None:
            return None
        year = cdr.sms.date_sent.year
        month = cdr.sms.date_sent.month
        return model.SMSUsageAggregate(
            cdr_uuid=str(uuid4()),
            imsi=cdr.imsi,
            iccid=cdr.iccid,
            total_sms=1,
            month=Month(year=year, month=month, day=1),
        )

    @staticmethod
    def create_cdr_voice_mo_mt_incamel(
        cdr: model.CDR,
    ) -> model.CDRVoiceMOMTIncamel | None:
        if cdr.voice is None:
            return None
        year = cdr.voice.call_date.year
        month = cdr.voice.call_date.month
        return model.CDRVoiceMOMTIncamel(
            imsi=cdr.imsi,
            iccid=cdr.iccid,
            usage=cdr.voice.call_minutes,
            type=cdr.type,
            month=Month(year=year, month=month, day=1),
        )

    @staticmethod
    def create_cdr_sms_mo_mt(cdr: model.CDR) -> model.CDRSMSMOMT | None:
        if cdr.sms is None:
            return None
        year = cdr.sms.date_sent.year
        month = cdr.sms.date_sent.month
        return model.CDRSMSMOMT(
            imsi=cdr.imsi,
            iccid=cdr.iccid,
            usage=1,
            type=cdr.type,
            month=Month(year=year, month=month, day=1),
        )

    @staticmethod
    def create_cdr(
        cdr: model.CDR,
        cdr_data: model.CDRData | None,
        cdr_voice: model.CDRVoice | None,
        cdr_sms: model.CDRSMS | None,
        data_usage_aggregate=model.DataUsageAggregate | None,
        voice_usage_aggregate=model.VoiceUsageAggregate | None,
        sms_usage=model.SMSUsageAggregate | None,
        voice_mo_mt_incamel=model.CDRVoiceMOMTIncamel | None,
        sms_mo_mt=model.CDRSMSMOMT | None,
    ) -> model.CDR:
        return model.CDR(
            uuid=uuid.uuid4(),
            imsi=cdr.imsi,
            iccid=cdr.iccid,
            type=cdr.type,
            country=cdr.country,
            country_name=cdr.country_name,
            carrier=cdr.carrier,
            data=cdr_data,
            voice=cdr_voice,
            sms=cdr_sms,
            data_usage=data_usage_aggregate,
            voice_usage=voice_usage_aggregate,
            sms_usage=sms_usage,
            voice_mo_mt_incamel=voice_mo_mt_incamel,
            sms_mo_mt=sms_mo_mt,
            cdr_object_id=cdr.cdr_object_id,
        )

    @staticmethod
    def parse_xml_string(xml_string: str) -> ET.Element:
        root = fromstring(xml_string)
        return root

    @staticmethod
    def extract_imsi(root: ET.Element) -> str | None:
        imsi_elem_subscriber = root.find("Subscriber")
        if imsi_elem_subscriber is None:
            raise ValueError("Subscriber not found.")
        imsi_elem_value = imsi_elem_subscriber.text
        if imsi_elem_value is None or len(imsi_elem_value.split()) < 2:
            raise ValueError("IMSI not found.")
        imsi = imsi_elem_value.split()[1]
        return imsi

    @staticmethod
    def extract_iccid(root: ET.Element) -> str | None:
        iccid_elem = root.find("SubscriberReference")
        if iccid_elem is None:
            raise ValueError("Subscriber reference not found.")
        iccid = iccid_elem.text
        return iccid

    @staticmethod
    def extract_country(root: ET.Element) -> str | None:
        country_elems = root.findall(".//Country")
        for country_elem in country_elems:
            iso_code = country_elem.get("ISOCode")
            if iso_code:
                country = iso_code
                return country
        raise ValueError("Country ISO not found.")

    @staticmethod
    def extract_country_name(root: ET.Element) -> str | None:
        country_elems = root.findall(".//Country")
        for country_elem in country_elems:
            iso_code = country_elem.get("ISOCode")
            if iso_code:
                country_name = country_elem.text
                return country_name
        raise ValueError("Country name not found.")

    @staticmethod
    def extract_carrier(root: ET.Element) -> str | None:
        carrier_elem = root.find("Leg1TADIG")
        if carrier_elem is None or carrier_elem.text is None:
            carrier_elem = root.find("Leg2TADIG")
            if carrier_elem is None or carrier_elem.text is None:
                raise ValueError("Carrier not found.")
        if not len(carrier_elem.text) == 5:
            raise ValueError(f"Invalid carrier {carrier_elem.text}")
        carrier = carrier_elem.text.upper()
        return carrier

    @staticmethod
    def extract_cdr_id(root: ET.Element) -> str:
        cdr_id_elem = root.find("ID")
        if cdr_id_elem is None or cdr_id_elem.text is None:
            raise ValueError("CdrId not found.")
        cdr_id = str(cdr_id_elem.text)
        return cdr_id

    @staticmethod
    def extract_leg_types(root):
        leg_type_elements = root.find("Type")
        if leg_type_elements is None:
            raise ValueError("Leg type not found.")
        leg_type = leg_type_elements.text
        return leg_type

    @staticmethod
    def extract_cdr_data(root) -> model.CDRData:
        session_starttime_elem = root.find("ConnectTime")
        if session_starttime_elem is None:
            raise ValueError("Connect time not found.")
        session_starttime_str = session_starttime_elem.text
        if session_starttime_str is None:
            raise ValueError("Connect time not valid.")
        session_starttime = datetime.strptime(
            session_starttime_str, "%Y-%m-%d %H:%M:%S"
        )

        session_endtime_elem = root.find("EndTime")
        if session_endtime_elem is None:
            raise ValueError("End time not found.")
        session_endtime_str = session_endtime_elem.text
        if session_endtime_str is None:
            raise ValueError("End time not valid.")
        session_endtime = datetime.strptime(session_endtime_str, "%Y-%m-%d %H:%M:%S")

        duration_elem = root.find("Duration")
        if duration_elem is None:
            raise ValueError("Duration not found.")
        duration = duration_elem.text

        data_volume_elem = root.find("Bytes")
        if data_volume_elem is None:
            raise ValueError("Bytes not found.")
        data_volume = data_volume_elem.text

        return model.CDRData(
            cdr_uuid=str(uuid.uuid4()),
            session_starttime=session_starttime,
            session_endtime=session_endtime,
            duration=duration,
            data_volume=data_volume,
        )

    @staticmethod
    def extract_cdr_voice(root) -> model.CDRVoice:
        call_date_elem = root.find("EndTime")
        if call_date_elem is None:
            raise ValueError("End time not found.")
        call_date_str = call_date_elem.text
        if call_date_str is None:
            raise ValueError("End time not valid.")
        call_date = datetime.strptime(call_date_str, "%Y-%m-%d %H:%M:%S")

        leg_elements = root.findall(".//Leg")
        for leg in leg_elements:
            leg_type = leg.find("LegType")
            if leg_type is not None and leg_type.text in (
                "mo-camel-b-direct",
                "mt-inbound-delivery",
            ):
                number_element = leg.find("Number")
                if number_element is None:
                    raise ValueError("Call number not found")
                call_number = number_element.text

        call_minutes_elem = root.find("Duration")
        if call_minutes_elem is None:
            raise ValueError("Call minutes not found.")
        call_minutes = call_minutes_elem.text

        return model.CDRVoice(
            cdr_uuid=str(uuid.uuid4()),
            call_date=call_date,
            call_number=call_number,
            call_minutes=call_minutes,
        )

    @staticmethod
    def extract_cdr_sms(root) -> model.CDRSMS:
        sms_date_elem = root.find("EndTime")
        if sms_date_elem is None:
            raise ValueError("End time not found.")
        sms_date_str = sms_date_elem.text
        date_sent = sms_date_elem.text
        if sms_date_str is None:
            raise ValueError("End time not valid.")

        data_sent_elem = root.find("Duration")
        if data_sent_elem is None:
            raise ValueError("Data sent not found.")

        leg_elements = root.findall(".//Leg")
        for leg in leg_elements:
            leg_type = leg.find("LegType")
            if leg_type is not None and leg_type.text in (
                "mo-sms-origination",
                "mt-sms-intercept",
            ):
                sent_from_elem = leg.find("Number")
                if sent_from_elem is None:
                    raise ValueError("Sent from not found")
                sent_from = sent_from_elem.text

            if leg_type is not None and leg_type.text in (
                "mo-sms-termination",
                "mt-sms-termination",
            ):
                sent_to_elem = leg.find("Number")
                if sent_to_elem is None:
                    raise ValueError("Sent to not found")
                sent_to = sent_to_elem.text

        return model.CDRSMS(
            cdr_uuid=str(uuid.uuid4()),
            date_sent=date_sent,
            sent_from=sent_from,
            sent_to=sent_to,
        )

    def add_cdr(self, xml_string: str, cdr_object_id: str, file_key: str) -> UUID:
        root = self.parse_xml_string(str(xml_string))
        imsi = self.extract_imsi(root)
        iccid = self.extract_iccid(root)
        country = self.extract_country(root)
        country_name = self.extract_country_name(root)
        carrier = self.extract_carrier(root)

        leg_type = self.extract_leg_types(root)

        cdr_data = None
        cdr_voice = None
        cdr_sms = None
        data_usage_aggregate = None
        voice_usage_aggregate = None
        sms_usage = None
        voice_mo_mt_incamel = None
        sms_mo_mt = None

        if leg_type == "gsm-data":
            cdr_data = self.extract_cdr_data(root)
        elif leg_type in ("incamel", "out", "in"):
            cdr_voice = self.extract_cdr_voice(root)
        elif leg_type in ("sms", "sms-in"):
            cdr_sms = self.extract_cdr_sms(root)

        cdr = model.CDR(
            uuid=uuid.uuid4(),
            imsi=IMSI(imsi),
            iccid=ICCID(iccid),
            type=leg_type,
            country=country,
            country_name=country_name,
            carrier=carrier,
            data=cdr_data,
            voice=cdr_voice,
            sms=cdr_sms,
            data_usage=data_usage_aggregate,
            voice_usage=voice_usage_aggregate,
            sms_usage=sms_usage,
            voice_mo_mt_incamel=voice_mo_mt_incamel,
            sms_mo_mt=sms_mo_mt,
            cdr_object_id=cdr_object_id,
        )

        cdr_data = self.create_cdr_data(cdr)
        cdr_voice = self.create_cdr_voice(cdr)
        cdr_sms = self.create_cdr_sms(cdr)
        data_usage_aggregate = self.create_data_usage_aggregate(cdr)
        voice_usage_aggregate = self.create_voice_aggregate(cdr)
        sms_usage = self.create_sms_aggregate(cdr)
        voice_mo_mt_incamel = self.create_cdr_voice_mo_mt_incamel(cdr)
        sms_mo_mt = self.create_cdr_sms_mo_mt(cdr)
        cdr_obj = self.create_cdr(
            cdr,
            cdr_data,
            cdr_voice,
            cdr_sms,
            data_usage_aggregate,
            voice_usage_aggregate,
            sms_usage=sms_usage,
            voice_mo_mt_incamel=voice_mo_mt_incamel,
            sms_mo_mt=sms_mo_mt,
        )
        cdr_obj_uuid = self.cdr_repository.add_cdr(cdr_obj)
        cdr_references = model.CDRReferences(
            cdr_uuid=str(cdr_obj_uuid),
            file_key=file_key,
        )
        self.cdr_repository.add_cdr_references(cdr_references)
        return cdr_obj_uuid

    def get_cdr_last_session(self, imsi: IMSI) -> datetime:
        return self.cdr_repository.get_cdr_last_session(imsi)

    def add_cdr_to_analytics(self, xml_string: dict) -> model.AnalyticsCDRObject:
        return self.market_share.add_cdr_analytics_api(xml_string)

    def is_valid_xml_cdr(self, xml_string: str) -> bool:
        try:
            root = self.parse_xml_string(str(xml_string))
            self.extract_imsi(root)
            self.extract_iccid(root)
            self.extract_country(root)
            self.extract_country_name(root)
            self.extract_carrier(root)

            leg_type = self.extract_leg_types(root)

            if leg_type == "gsm-data":
                self.extract_cdr_data(root)
            elif leg_type in ("incamel", "out", "in"):
                self.extract_cdr_voice(root)
            elif leg_type in ("sms", "sms-in"):
                self.extract_cdr_sms(root)
            else:
                logging.info("Invalid leg type in cdr")
                return False
            return True
        except ValueError as e:
            logging.info(f"Error in xml string. Error: {e}")
            return False

    def is_duplicate_cdr(
        self, xml_string: str, redis_service: AbstractRedisService
    ) -> str | None:
        root = self.parse_xml_string(str(xml_string))
        cdr_id = self.extract_cdr_id(root)
        cdr = redis_service.is_duplicate_cdr(cdr_id)
        if not cdr:
            session_endtime_elem = root.find("EndTime")
            logging.info(f"Duplicate CDR received. Existing cdr_id : {cdr_id}")
            if session_endtime_elem is not None:
                return str(session_endtime_elem.text)
            else:
                logging.warning("session_endtime_elem is None")
                return None
        return None

    def get_duplicate_cdr(self, month: Month) -> Iterator[model.DuplicateCdr]:
        return self.cdr_repository.get_duplicate_cdr(month)

    def get_updated_cdr(self, s3_key, xml_string: str):
        root = self.parse_xml_string(str(xml_string))
        type = self.extract_leg_types(root)
        imsi = root.find("Subscriber").text  # type: ignore
        iccid = self.extract_iccid(root)
        leg1_tadig = self.extract_carrier(root)
        create_time = root.find("CreateTime").text  # type: ignore
        logging.info(f"Raw CDR: {root}")

        bytes = 0
        duration = 0

        cdr = {
            "Subscriber": imsi,
            "SubscriberReference": iccid,
            "Type": type,
        }
        if type == "gsm-data":
            cdr_data = self.extract_cdr_data(root)
            bytes = cdr_data.data_volume
            duration = cdr_data.duration
            end_time = cdr_data.session_endtime
            cdr["CreateTime"] = create_time
            cdr["EndTime"] = end_time.strftime("%Y-%m-%d %H:%M:%S")
            cdr["Leg1TADIG"] = leg1_tadig
            cdr["Duration"] = duration
            cdr["Bytes"] = bytes
        elif type in ("incamel", "out", "in"):
            cdr_voice = self.extract_cdr_voice(root)
            duration = cdr_voice.call_minutes
            end_time = cdr_voice.call_date
            cdr["CreateTime"] = create_time
            cdr["EndTime"] = end_time.strftime("%Y-%m-%d %H:%M:%S")
            cdr["Leg1TADIG"] = leg1_tadig
            cdr["Duration"] = duration
        elif type in ("sms", "sms-in"):
            cdr_sms = self.extract_cdr_sms(root)
            end_time = cdr_sms.date_sent
            duration = root.find("Duration").text  # type: ignore
            cdr["CreateTime"] = create_time
            cdr["EndTime"] = end_time.strftime("%Y-%m-%d %H:%M:%S")
            cdr["Leg1TADIG"] = leg1_tadig
            cdr["Duration"] = duration

        update_cdr = {
            "s3_key": s3_key,
            "cdr": cdr,
        }
        return json.dumps(update_cdr, indent=2)
