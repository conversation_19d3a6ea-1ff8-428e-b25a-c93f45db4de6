import requests
from requests.exceptions import ConnectionError

from app.config import settings
from cdrdata.domain.model import AnalyticsCDRObject
from cdrdata.domain.ports import AbstractMarketShare
from common.exceptions import AnalyticsAPIError


class MarketShareAPI(AbstractMarketShare):
    def __init__(self):
        self.data = {}

    def add_cdr_analytics_api(self, xml_string: dict) -> AnalyticsCDRObject:
        try:
            headers = {"Content-Type": "application/json"}
            cdr_url = f"{settings.APP_BASE_URL}{settings.CDR_ANALYTICS_API}"
            response = requests.post(
                url=cdr_url, headers=headers, data=xml_string, timeout=settings.TIMEOUT
            )

            if response.status_code == 200:
                return AnalyticsCDRObject(cdr_object_id=response.json()["cdrObjectId"])
            else:
                raise AnalyticsAPIError
        except ConnectionError:
            raise ConnectionError("Failed to connect to the analytics API")
