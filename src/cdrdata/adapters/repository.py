import datetime
import uuid
from abc import ABC, abstractmethod
from typing import Iterable, Iterator
from uuid import UUID

from more_itertools import ilen
from sqlalchemy import MetaData, String, Table, desc, func, or_, select, union
from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.exc import IntegrityError, NoSuchTableError
from sqlalchemy.orm import Session
from sqlalchemy.sql.functions import count

from app.config import logger
from cdrdata.adapters import orm
from cdrdata.domain import model
from cdrdata.exceptions import NoTableExist
from common.pagination import Pagination
from common.searching import Searching
from common.types import ICCID, IMSI, Month


class AbstractCdrRepository(ABC):
    @abstractmethod
    def add_cdr(self, cdr: model.CDR) -> UUID:
        ...

    @abstractmethod
    def add_carrier(self, carrier_name: str):
        ...

    @abstractmethod
    def connection_history(
        self,
        imsi: IMSI,
        month: Month,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.SimCDRHistory]:
        ...

    @abstractmethod
    def connection_history_count(
        self, imsi: IMSI, month: Month, searching: Searching | None = None
    ) -> int:
        ...

    @abstractmethod
    def get_sim_usage_by_imsi(
        self,
        imsi: list[IMSI],
        month: Month,
    ) -> Iterable[model.DataUsageAggregate]:
        ...

    @abstractmethod
    def get_sim_usage_total_by_imsi(
        self,
        imsi: list[IMSI],
        month: Month,
    ) -> int:
        ...

    @abstractmethod
    def usage_monthly_total(self, iccid: list[ICCID]) -> int:
        ...

    @abstractmethod
    def get_cdr_last_session(
        self,
        imsi: IMSI,
    ) -> datetime.datetime:
        ...

    @abstractmethod
    def voice_connection_history(
        self,
        imsi: IMSI,
        month: Month,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.SimVoiceCDRHistory]:
        ...

    @abstractmethod
    def voice_connection_history_count(
        self, imsi: IMSI, month: Month, searching: Searching | None = None
    ) -> int:
        ...

    @abstractmethod
    def sms_connection_history(
        self,
        imsi: IMSI,
        month: Month,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.SimSMSCDRHistory]:
        ...

    @abstractmethod
    def sms_connection_history_count(
        self, imsi: IMSI, month: Month, searching: Searching | None = None
    ) -> int:
        ...

    @abstractmethod
    def get_duplicate_cdr(self, month: Month) -> Iterator[model.DuplicateCdr]:
        ...

    @abstractmethod
    def is_duplicate_cdr(self, cdr: model.CDR) -> UUID | None:
        ...

    @abstractmethod
    def add_cdr_references(self, cdr_references: model.CDRReferences):
        ...


class InMemoryCdrRepository(AbstractCdrRepository):
    def __init__(self):
        self.cdr_record: dict[UUID, model.CDR] = {}
        self.sim_cdr_data = {}
        self.sim_cdr_voice = {}
        self.sim_cdr_sms = {}
        self.usage_data = []
        self.cdr_last_session = []

    def add_connection_history(self, sim_cdr_data):
        self.sim_cdr_data = sim_cdr_data

    def add_usage_data(self, usage_data):
        self.usage_data = usage_data

    def add_cdr(self, cdr: model.CDR) -> UUID:
        """Function for add CDR based on leg type"""
        cdr.uuid = uuid.uuid4()
        self.cdr_record[cdr.uuid] = cdr

        if cdr.data is not None:
            cdr.data.cdr_uuid = str(cdr.uuid)
            self.cdr_record[cdr.uuid].data = cdr.data
        if cdr.data_usage is not None:
            cdr.data_usage.cdr_uuid = str(cdr.uuid)
            self.cdr_record[cdr.uuid].data_usage = cdr.data_usage
        if cdr.voice is not None:
            cdr.voice.cdr_uuid = str(cdr.uuid)
            self.cdr_record[cdr.uuid].voice = cdr.voice
        if cdr.sms is not None:
            cdr.sms.cdr_uuid = str(cdr.uuid)
            self.cdr_record[cdr.uuid].sms = cdr.sms

        return cdr.uuid

    def add_carrier(self, carrier_name: str):
        for carrier_ in self.sim_cdr_data:
            if not carrier_:
                carrier_ = carrier_name

    def add_voice_connection_history(self, sim_voice_cdr):
        self.sim_cdr_voice = sim_voice_cdr

    def add_sms_connection_history(self, sim_sms_cdr):
        self.sim_cdr_sms = sim_sms_cdr
        print(self.sim_cdr_sms)

    def connection_history(
        self,
        imsi: IMSI,
        month: Month,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.SimCDRHistory]:
        if self.sim_cdr_data["imsi"] == imsi:
            yield model.SimCDRHistory(
                iccid=self.sim_cdr_data["iccid"],
                country=self.sim_cdr_data["country"],
                carrier=self.sim_cdr_data["carrier"],
                session_starttime=self.sim_cdr_data["sessionStarttime"],
                session_endtime=self.sim_cdr_data["sessionEndtime"],
                duration=self.sim_cdr_data["duration"],
                data_volume=self.sim_cdr_data["dataVolume"],
                imsi=self.sim_cdr_data["imsi"],
                country_name=self.sim_cdr_data["countryName"],
                carrier_name=self.sim_cdr_data["carrierName"],
            )

    def connection_history_count(
        self, imsi: IMSI, month: Month, searching: Searching | None = None
    ) -> int:
        return ilen(
            cdr
            for cdr in self.connection_history(
                imsi=imsi, month=month, searching=searching
            )
        )

    def get_sim_usage_by_imsi(
        self,
        imsi: list[IMSI],
        month: Month,
    ) -> list[model.DataUsageAggregate]:
        month = Month(year=2023, month=5, day=1)
        result = []
        for data in self.usage_data:
            if data["imsi"] in imsi and data["month"] == month:
                result.append(data)
        return result

    def get_sim_usage_total_by_imsi(
        self,
        imsi: list[IMSI],
        month: Month,
    ) -> int:
        return 50

    def usage_monthly_total(self, iccid_list: list[ICCID]) -> int:
        month = Month(year=2023, month=5, day=1)
        total_usage = sum(
            cdr_data["usage"]
            for cdr_data in self.usage_data
            if cdr_data["iccid"] in iccid_list and cdr_data["month"] == month
        )
        return total_usage

    def add_cdr_last_session(
        self, last_session_cdr: model.lastSessionCDR
    ) -> model.lastSessionCDR:
        self.cdr_last_session.append(last_session_cdr)
        return last_session_cdr

    def validate_last_session(self, iccid: ICCID, imsi: IMSI) -> bool:
        for last_session in self.cdr_last_session:
            if last_session.iccid == iccid and last_session.imsi == imsi:
                return True
        return False

    def update_last_session(self, last_session_cdr: model.lastSessionCDR):
        for last_session_data in self.cdr_last_session:
            last_session_data.last_session = last_session_cdr

    def get_cdr_last_session(
        self,
        imsi: IMSI,
    ) -> datetime.datetime:
        for last_session_cdr in self.cdr_last_session:
            if last_session_cdr.imsi == imsi:
                return last_session_cdr.last_session
        return datetime.datetime.now()

    def voice_connection_history(
        self,
        imsi: IMSI,
        month: Month,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.SimVoiceCDRHistory]:
        if self.sim_cdr_voice["imsi"] == imsi:
            yield model.SimVoiceCDRHistory(
                imsi=self.sim_cdr_voice["imsi"],
                iccid=self.sim_cdr_voice["iccid"],
                country=self.sim_cdr_voice["country"],
                carrier=self.sim_cdr_voice["carrier"],
                call_date=self.sim_cdr_voice["callDate"],
                call_number=self.sim_cdr_voice["callNumber"],
                call_minutes=self.sim_cdr_voice["callMinutes"],
                country_name=self.sim_cdr_voice["countryName"],
                carrier_name=self.sim_cdr_voice["carrierName"],
            )

    def voice_connection_history_count(
        self, imsi: IMSI, month: Month, searching: Searching | None = None
    ) -> int:
        return ilen(
            cdr
            for cdr in self.connection_history(
                imsi=imsi, month=month, searching=searching
            )
        )

    def sms_connection_history(
        self,
        imsi: IMSI,
        month: Month,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.SimSMSCDRHistory]:
        if self.sim_cdr_sms["imsi"] == imsi:
            yield model.SimSMSCDRHistory(
                imsi=self.sim_cdr_sms["imsi"],
                iccid=self.sim_cdr_sms["iccid"],
                country=self.sim_cdr_sms["country"],
                carrier=self.sim_cdr_sms["carrier"],
                date_sent=self.sim_cdr_sms["DateSent"],
                sent_from=self.sim_cdr_sms["SentFrom"],
                sent_to=self.sim_cdr_sms["SentTo"],
                country_name=self.sim_cdr_sms["countryName"],
                carrier_name=self.sim_cdr_sms["carrierName"],
            )

    def sms_connection_history_count(
        self, imsi: IMSI, month: Month, searching: Searching | None = None
    ) -> int:
        return ilen(
            cdr
            for cdr in self.connection_history(
                imsi=imsi, month=month, searching=searching
            )
        )

    def get_duplicate_cdr(self, month: Month) -> Iterator[model.DuplicateCdr]:
        ...

    def is_duplicate_cdr(self, cdr: model.CDR) -> UUID | None:
        ...

    def add_cdr_references(self, cdr_references: model.CDRReferences):
        ...


class DatabaseCdrRepository(AbstractCdrRepository):
    def __init__(self, session: Session) -> None:
        self.session = session

    def _get_usage_details(self, iccid: ICCID, month: Month) -> str | None:
        """Function to get ICCID for data usage"""
        source = model.DataUsageAggregate
        query = select(source.iccid)
        query = query.filter(
            source.iccid == str(iccid),
            source.month == month,
        )
        iccid_row = self.session.execute(query).first()
        if iccid_row is not None:
            return str(iccid_row)
        else:
            return None

    def _get_voice_usage_details(self, iccid: ICCID, month: Month) -> str | None:
        """Function to get ICCID for voice usage"""
        source = model.VoiceUsageAggregate
        query = select(source.iccid)
        query = query.filter(
            source.iccid == str(iccid),
            source.month == month,
        )
        iccid_row = self.session.execute(query).first()
        if iccid_row is not None:
            return str(iccid_row)
        else:
            return None

    def _aggregate_usage_details(self, iccid: ICCID, usage: int, month: Month):
        """Function to get MSISDN from IMSI"""
        table = model.DataUsageAggregate
        self.session.query(table).filter(
            table.iccid == iccid,
            table.month == month,
        ).update({table.usage: (table.usage + usage)}, synchronize_session=False)
        self.session.commit()

    def _aggregate_voice_usage_details(
        self, iccid: ICCID, voice_usage: int, month: Month
    ):
        """Function to get MSISDN from IMSI"""
        table = model.VoiceUsageAggregate
        self.session.query(table).filter(
            table.iccid == iccid,
            table.month == month,
        ).update(
            {table.voice_usage: (table.voice_usage + voice_usage)},
            synchronize_session=False,
        )
        self.session.commit()

    def _upsert_aggregate_sms_usage_details(self, sms_usage: model.SMSUsageAggregate):
        stmt = (
            insert(model.SMSUsageAggregate)
            .values(
                cdr_uuid=str(sms_usage.cdr_uuid),
                imsi=str(sms_usage.imsi),
                iccid=str(sms_usage.iccid),
                month=sms_usage.month,
                total_sms=sms_usage.total_sms,
            )
            .on_conflict_do_update(
                index_elements=["iccid", "month"],
                set_={
                    model.SMSUsageAggregate.total_sms: (
                        model.SMSUsageAggregate.total_sms + sms_usage.total_sms
                    )
                },
            )
        )
        self.session.execute(stmt)
        self.session.commit()

    def _upsert_voice_mo_mt_usage_details(
        self, imsi: IMSI, iccid: ICCID, usage: int, type: str, month: Month
    ):
        stmt = (
            insert(model.CDRVoiceMOMTIncamel)
            .values(
                imsi=str(imsi),
                iccid=str(iccid),
                month=month,
                usage=usage,
                type=type,
            )
            .on_conflict_do_update(
                index_elements=["iccid", "month", "type"],
                set_={
                    model.CDRVoiceMOMTIncamel.usage: (
                        model.CDRVoiceMOMTIncamel.usage + usage
                    )
                },
            )
        )
        self.session.execute(stmt)
        self.session.commit()

    def _upsert_sms_mo_mt_usage_details(
        self, imsi: IMSI, iccid: ICCID, usage: int, type: str, month: Month
    ):
        stmt = (
            insert(model.CDRSMSMOMT)
            .values(
                imsi=str(imsi),
                iccid=str(iccid),
                usage=usage,
                type=type,
                month=month,
            )
            .on_conflict_do_update(
                index_elements=["iccid", "month", "type"],
                set_={model.CDRSMSMOMT.usage: (model.CDRSMSMOMT.usage + usage)},
            )
        )
        self.session.execute(stmt)
        self.session.commit()

    def _process_last_session(self, last_session_cdr):
        if self.validate_last_session(
            self, last_session_cdr.iccid, last_session_cdr.imsi
        ):
            self.update_last_session(self, last_session_cdr)
        else:
            self.add_cdr_last_session(self, last_session_cdr)

    def add_carrier(self, carrier: str):
        carrier_ = model.CarrierName
        exists = (
            self.session.query(carrier_.carrier)
            .filter(carrier_.carrier == carrier)
            .first()
        )
        if not exists:
            carrier_name = model.CarrierName(carrier=carrier, carrier_name=None)
            self.session.add(carrier_name)
            self.session.commit()

    def add_cdr(self, cdr: model.CDR) -> UUID:
        try:
            """Function for add CDR based on leg type"""
            self.add_carrier(str(cdr.carrier))
            self.session.add(cdr)
            self.session.flush()

            # TODO Need to do same for voice,sms
            # TODO This agrregate part will need to change as it
            # can not be part of the cdr data.
            if cdr.data is not None:
                cdr.data.cdr_uuid = str(cdr.uuid)
                self.session.add(cdr.data)
                if cdr.data_usage is not None:
                    cdr.data_usage.cdr_uuid = str(cdr.uuid)
                    iccid_ = self._get_usage_details(cdr.iccid, cdr.data_usage.month)
                    if iccid_ is None:
                        self.session.add(cdr.data_usage)
                    else:
                        self._aggregate_usage_details(
                            cdr.iccid, cdr.data.data_volume, cdr.data_usage.month
                        )
                last_session_cdr = model.lastSessionCDR(
                    iccid=ICCID(cdr.iccid),
                    imsi=IMSI(cdr.imsi),
                    last_session=cdr.data.session_endtime,
                )
                self._process_last_session(last_session_cdr)
            if cdr.voice is not None:
                cdr.voice.cdr_uuid = str(cdr.uuid)
                self.session.add(cdr.voice)
                if cdr.voice_usage is not None:
                    cdr.voice_usage.cdr_uuid = str(cdr.uuid)
                    iccid_ = self._get_voice_usage_details(
                        cdr.iccid, cdr.voice_usage.month
                    )
                    if iccid_ is None:
                        self.session.add(cdr.voice_usage)
                    else:
                        self._aggregate_voice_usage_details(
                            cdr.iccid, cdr.voice.call_minutes, cdr.voice_usage.month
                        )
                    self._upsert_voice_mo_mt_usage_details(
                        cdr.voice_usage.imsi,
                        cdr.voice_usage.iccid,
                        cdr.voice.call_minutes,
                        cdr.type,
                        cdr.voice_usage.month,
                    )
                last_session_cdr = model.lastSessionCDR(
                    iccid=ICCID(cdr.iccid),
                    imsi=IMSI(cdr.imsi),
                    last_session=cdr.voice.call_date,
                )
                self._process_last_session(last_session_cdr)

            if cdr.sms is not None:
                cdr.sms.cdr_uuid = str(cdr.uuid)
                self.session.add(cdr.sms)
                if cdr.sms_usage is not None:
                    cdr.sms_usage.cdr_uuid = str(cdr.uuid)
                    self._upsert_aggregate_sms_usage_details(cdr.sms_usage)
                    self._upsert_sms_mo_mt_usage_details(
                        cdr.sms_usage.imsi,
                        cdr.sms_usage.iccid,
                        cdr.sms_usage.total_sms,
                        cdr.type,
                        cdr.sms_usage.month,
                    )
                last_session_cdr = model.lastSessionCDR(
                    iccid=ICCID(cdr.iccid),
                    imsi=IMSI(cdr.imsi),
                    last_session=cdr.sms.date_sent,
                )
                self._process_last_session(last_session_cdr)
            self.session.commit()
            return cdr.uuid
        except IntegrityError:
            raise NoTableExist(cdr.imsi)

    def connection_history_count(
        self,
        imsi: IMSI,
        month: Month,
        searching: Searching | None = None,
    ) -> int:
        try:

            cdr_current_month = self._get_partition_table(month, table_prefix="cdr")
            carrier = orm.carrier_name
            partition_table = self._get_partition_table(month, table_prefix="cdr_data")

            def create_query(cdr_table, partition_table, imsi):
                cdr_query = (
                    select(count(cdr_table.c.uuid).label("cdr_count"))
                    .join(
                        partition_table, partition_table.c.cdr_uuid == cdr_table.c.uuid
                    )
                    .join(carrier, carrier.c.carrier == cdr_table.c.carrier)
                    .filter(
                        cdr_table.c.imsi == imsi,
                    )
                )

                if searching:
                    search_value = searching.search
                    search_filter = or_(
                        cdr_table.c.imsi.ilike(f"%{search_value}%"),
                        cdr_table.c.iccid.ilike(f"%{search_value}%"),
                        cdr_table.c.country.ilike(f"%{search_value}%"),
                        cdr_table.c.country_name.ilike(f"%{search_value}%"),
                        cdr_table.c.carrier.ilike(f"%{search_value}%"),
                        partition_table.c.data_volume.cast(String).ilike(
                            f"%{search_value}%"
                        ),
                        partition_table.c.session_endtime.cast(String).ilike(
                            f"%{search_value}%"
                        ),
                        partition_table.c.session_starttime.cast(String).ilike(
                            f"%{search_value}%"
                        ),
                        partition_table.c.duration.cast(String).ilike(
                            f"%{search_value}%"
                        ),
                        carrier.c.carrier_name.cast(String).ilike(f"%{search_value}%"),
                    )

                    # Apply filter only if search_term is not None
                    cdr_query = cdr_query.filter(
                        or_(search_value is None, search_filter)
                    )

                return cdr_query

            # Query for the current month
            query_current = create_query(
                cdr_table=cdr_current_month,
                partition_table=partition_table,
                imsi=imsi,
            )

            query = query_current
            current_month = Month.today()
            if current_month != month:
                cdr_next_month = self._get_partition_table(
                    month.next(), table_prefix="cdr"
                )
                # Query for the next month
                query_next = create_query(
                    cdr_table=cdr_next_month,
                    partition_table=partition_table,
                    imsi=imsi,
                )
                query = query_current.union_all(query_next)
            query = select(func.sum(query.subquery().c.cdr_count).label("cdr_count"))

            return self.session.execute(query).scalar_one()
        except NoSuchTableError as e:
            logger.error(f"No such table error occoured.: {str(e)}")
            raise NoTableExist(imsi, month)

    def connection_history(
        self,
        imsi: IMSI,
        month: Month,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.SimCDRHistory]:

        try:
            cdr_current_month = self._get_partition_table(month, table_prefix="cdr")
            carrier = orm.carrier_name
            partition_table = self._get_partition_table(month, table_prefix="cdr_data")

            def create_query(cdr_table, partition_table, imsi):
                cdr_query = (
                    select(
                        cdr_table.c.iccid,
                        cdr_table.c.country,
                        cdr_table.c.carrier,
                        partition_table.c.session_starttime,
                        partition_table.c.session_endtime,
                        partition_table.c.duration,
                        partition_table.c.data_volume,
                        cdr_table.c.imsi,
                        cdr_table.c.country_name,
                        func.coalesce(carrier.c.carrier_name, carrier.c.carrier).label(
                            "carrier_name"
                        ),
                    )
                    .join(
                        partition_table, partition_table.c.cdr_uuid == cdr_table.c.uuid
                    )
                    .join(carrier, carrier.c.carrier == cdr_table.c.carrier)
                    .filter(
                        cdr_table.c.imsi == imsi,
                    )
                )

                if searching:
                    search_value = searching.search
                    search_filter = or_(
                        cdr_table.c.imsi.ilike(f"%{search_value}%"),
                        cdr_table.c.iccid.ilike(f"%{search_value}%"),
                        cdr_table.c.country.ilike(f"%{search_value}%"),
                        cdr_table.c.country_name.ilike(f"%{search_value}%"),
                        cdr_table.c.carrier.ilike(f"%{search_value}%"),
                        partition_table.c.data_volume.cast(String).ilike(
                            f"%{search_value}%"
                        ),
                        partition_table.c.session_endtime.cast(String).ilike(
                            f"%{search_value}%"
                        ),
                        partition_table.c.session_starttime.cast(String).ilike(
                            f"%{search_value}%"
                        ),
                        partition_table.c.duration.cast(String).ilike(
                            f"%{search_value}%"
                        ),
                        carrier.c.carrier_name.cast(String).ilike(f"%{search_value}%"),
                    )

                    # Apply filter only if search_term is not None
                    cdr_query = cdr_query.filter(
                        or_(search_value is None, search_filter)
                    )
                return cdr_query

            # Query for the current month
            query_current = create_query(
                cdr_table=cdr_current_month,
                partition_table=partition_table,
                imsi=imsi,
            )

            query = query_current
            current_month = Month.today()
            if current_month != month:
                cdr_next_month = self._get_partition_table(
                    month.next(), table_prefix="cdr"
                )
                # Query for the next month
                query_next = create_query(
                    cdr_table=cdr_next_month,
                    partition_table=partition_table,
                    imsi=imsi,
                )
                query = query_current.union_all(query_next)

            if pagination is not None:
                query = query.limit(pagination.page_size).offset(pagination.offset)

            query = query.order_by(desc(partition_table.c.session_endtime))
            results = self.session.execute(query).mappings()
            for row in results:
                yield model.SimCDRHistory(**row)
        except NoSuchTableError as e:
            logger.error(f"No such table error occoured.: {str(e)}")
            raise NoTableExist(imsi, month)

    def get_sim_usage_by_imsi(
        self,
        imsi: list[IMSI],
        month: Month,
    ) -> list[model.DataUsageAggregate]:
        try:
            partition_table = self._get_partition_table(
                month, table_prefix="cdr_data_aggregate"
            )
            query = select(
                partition_table.c.cdr_uuid,
                partition_table.c.imsi,
                partition_table.c.iccid,
                partition_table.c.usage,
                partition_table.c.month,
            )
            query = query.filter(
                partition_table.c.imsi.in_(imsi),
            )

            rows = self.session.execute(query).all()
            return [model.DataUsageAggregate(*row) for row in rows]
        except NoSuchTableError as e:
            logger.error(f"No such table error occoured.: {str(e)}")
            raise NoTableExist(imsi, month)

    def get_sim_usage_total_by_imsi(
        self,
        imsi: list[IMSI],
        month: Month,
    ) -> int:
        try:
            partition_table = self._get_partition_table(
                month, table_prefix="cdr_data_aggregate"
            )
            query = select(
                func.coalesce(func.sum(partition_table.c.usage), 0).label("total_usage")
            )
            query = query.filter(
                partition_table.c.imsi.in_(imsi),
            )

            total_usage = self.session.execute(query).scalar_one()
            return total_usage
        except NoSuchTableError as e:
            logger.error(f"No such table error occoured.: {str(e)}")
            raise NoTableExist(imsi, month)

    def usage_monthly_total(self, iccid: list[ICCID]) -> int:
        try:
            today = datetime.date.today()
            month = Month(year=today.year, month=today.month, day=1)
            partition_table = self._get_partition_table(
                month, table_prefix="cdr_data_aggregate"
            )
            query = select(func.sum(partition_table.c.usage)).filter(
                partition_table.c.iccid.in_(iccid),
            )
            return self.session.execute(query).scalar_one()
        except NoSuchTableError as e:
            logger.error(f"No such table error occoured.: {str(e)}")
            raise NoTableExist(iccid, month)

    @staticmethod
    def add_cdr_last_session(
        self, last_session_cdr: model.lastSessionCDR
    ) -> model.lastSessionCDR:
        self.session.add(last_session_cdr)
        return last_session_cdr

    @staticmethod
    def validate_last_session(self, iccid: ICCID, imsi: IMSI) -> bool:
        table = model.lastSessionCDR
        if (
            self.session.query(table.iccid, table.imsi)
            .filter(table.iccid == iccid, table.imsi == imsi)
            .first()
        ):
            return True
        return False

    @staticmethod
    def update_last_session(self, last_session_cdr: model.lastSessionCDR):
        table = model.lastSessionCDR
        self.session.query(table).filter(
            table.iccid == last_session_cdr.iccid,
            table.imsi == last_session_cdr.imsi,
        ).update(
            {table.last_session: last_session_cdr.last_session},
            synchronize_session=False,
        )

    def get_cdr_last_session(
        self,
        imsi: IMSI,
    ) -> datetime.datetime:
        table = model.lastSessionCDR
        return (
            self.session.query(table.last_session)
            .filter(table.imsi == imsi)
            .order_by(desc(table.last_session))
            .limit(1)
            .scalar()
        )

    def voice_connection_history(
        self,
        imsi: IMSI,
        month: Month,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.SimVoiceCDRHistory]:

        try:
            cdr_current_month = self._get_partition_table(month, table_prefix="cdr")
            carrier = orm.carrier_name
            partition_table = self._get_partition_table(month, table_prefix="cdr_voice")

            def create_query(cdr_table, partition_table, imsi):
                cdr_query = (
                    select(
                        cdr_table.c.iccid,
                        cdr_table.c.country,
                        cdr_table.c.carrier,
                        partition_table.c.call_date,
                        partition_table.c.call_number,
                        partition_table.c.call_minutes,
                        cdr_table.c.imsi,
                        cdr_table.c.country_name,
                        func.coalesce(carrier.c.carrier_name, carrier.c.carrier).label(
                            "carrier_name"
                        ),
                    )
                    .join(
                        partition_table, partition_table.c.cdr_uuid == cdr_table.c.uuid
                    )
                    .join(carrier, carrier.c.carrier == cdr_table.c.carrier)
                    .filter(
                        cdr_table.c.imsi == imsi,
                    )
                )

                if searching:
                    search_value = searching.search
                    search_filter = or_(
                        cdr_table.c.iccid.ilike(f"%{search_value}%"),
                        cdr_table.c.imsi.ilike(f"%{search_value}%"),
                        cdr_table.c.country.ilike(f"%{search_value}%"),
                        cdr_table.c.country_name.ilike(f"%{search_value}%"),
                        cdr_table.c.carrier.ilike(f"%{search_value}%"),
                        partition_table.c.call_date.cast(String).ilike(
                            f"%{search_value}%"
                        ),
                        partition_table.c.call_number.cast(String).ilike(
                            f"%{search_value}%"
                        ),
                        partition_table.c.call_minutes.cast(String).ilike(
                            f"%{search_value}%"
                        ),
                        carrier.c.carrier_name.cast(String).ilike(f"%{search_value}%"),
                    )

                    # Apply filter only if search_term is not None
                    cdr_query = cdr_query.filter(
                        or_(search_value is None, search_filter)
                    )
                return cdr_query

            # Query for the current month
            query_current = create_query(
                cdr_table=cdr_current_month,
                partition_table=partition_table,
                imsi=imsi,
            )

            query = query_current
            current_month = Month.today()
            if current_month != month:
                cdr_next_month = self._get_partition_table(
                    month.next(), table_prefix="cdr"
                )
                # Query for the next month
                query_next = create_query(
                    cdr_table=cdr_next_month,
                    partition_table=partition_table,
                    imsi=imsi,
                )
                query = query_current.union_all(query_next)
            if pagination is not None:
                query = query.limit(pagination.page_size).offset(pagination.offset)

            query = query.order_by(desc(partition_table.c.call_date))
            results = self.session.execute(query).mappings()
            for row in results:
                yield model.SimVoiceCDRHistory(**row)
        except NoSuchTableError as e:
            logger.error(f"No such table error occoured.: {str(e)}")
            raise NoTableExist(imsi, month)

    def voice_connection_history_count(
        self,
        imsi: IMSI,
        month: Month,
        searching: Searching | None = None,
    ) -> int:
        try:

            cdr_current_month = self._get_partition_table(month, table_prefix="cdr")
            carrier = orm.carrier_name
            partition_table = self._get_partition_table(month, table_prefix="cdr_voice")

            def create_query(cdr_table, partition_table, imsi):
                cdr_query = (
                    select(count(cdr_table.c.uuid).label("cdr_count"))
                    .join(
                        partition_table, partition_table.c.cdr_uuid == cdr_table.c.uuid
                    )
                    .join(carrier, carrier.c.carrier == cdr_table.c.carrier)
                    .filter(
                        cdr_table.c.imsi == imsi,
                    )
                )

                if searching:
                    search_value = searching.search
                    search_filter = or_(
                        cdr_table.c.iccid.ilike(f"%{search_value}%"),
                        cdr_table.c.imsi.ilike(f"%{search_value}%"),
                        cdr_table.c.country.ilike(f"%{search_value}%"),
                        cdr_table.c.country_name.ilike(f"%{search_value}%"),
                        cdr_table.c.carrier.ilike(f"%{search_value}%"),
                        partition_table.c.call_date.cast(String).ilike(
                            f"%{search_value}%"
                        ),
                        partition_table.c.call_number.cast(String).ilike(
                            f"%{search_value}%"
                        ),
                        partition_table.c.call_minutes.cast(String).ilike(
                            f"%{search_value}%"
                        ),
                        carrier.c.carrier_name.cast(String).ilike(f"%{search_value}%"),
                    )

                    # Apply filter only if search_term is not None
                    cdr_query = cdr_query.filter(
                        or_(search_value is None, search_filter)
                    )
                return cdr_query

            # Query for the current month
            query_current = create_query(
                cdr_table=cdr_current_month,
                partition_table=partition_table,
                imsi=imsi,
            )

            query = query_current
            current_month = Month.today()
            if current_month != month:
                cdr_next_month = self._get_partition_table(
                    month.next(), table_prefix="cdr"
                )
                # Query for the next month
                query_next = create_query(
                    cdr_table=cdr_next_month,
                    partition_table=partition_table,
                    imsi=imsi,
                )
                query = query_current.union_all(query_next)
            query = select(func.sum(query.subquery().c.cdr_count).label("cdr_count"))

            return self.session.execute(query).scalar_one()
        except NoSuchTableError as e:
            logger.error(f"No such table error occoured.: {str(e)}")
            raise NoTableExist(imsi, month)

    def sms_connection_history(
        self,
        imsi: IMSI,
        month: Month,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.SimSMSCDRHistory]:

        try:
            cdr_current_month = self._get_partition_table(month, table_prefix="cdr")
            carrier = orm.carrier_name
            partition_table = self._get_partition_table(month, table_prefix="cdr_sms")

            def create_query(cdr_table, partition_table, imsi):
                cdr_query = (
                    select(
                        cdr_table.c.iccid,
                        cdr_table.c.country,
                        cdr_table.c.carrier,
                        partition_table.c.sent_from,
                        partition_table.c.sent_to,
                        partition_table.c.date_sent,
                        cdr_table.c.imsi,
                        cdr_table.c.country_name,
                        func.coalesce(carrier.c.carrier_name, carrier.c.carrier).label(
                            "carrier_name"
                        ),
                    )
                    .join(
                        partition_table, partition_table.c.cdr_uuid == cdr_table.c.uuid
                    )
                    .join(carrier, carrier.c.carrier == cdr_table.c.carrier)
                    .filter(
                        cdr_table.c.imsi == imsi,
                    )
                )

                if searching:
                    search_value = searching.search
                    search_filter = or_(
                        cdr_table.c.iccid.ilike(f"%{search_value}%"),
                        cdr_table.c.imsi.ilike(f"%{search_value}%"),
                        cdr_table.c.country.ilike(f"%{search_value}%"),
                        cdr_table.c.country_name.ilike(f"%{search_value}%"),
                        cdr_table.c.carrier.ilike(f"%{search_value}%"),
                        partition_table.c.sent_from.cast(String).ilike(
                            f"%{search_value}%"
                        ),
                        partition_table.c.sent_to.cast(String).ilike(
                            f"%{search_value}%"
                        ),
                        partition_table.c.date_sent.cast(String).ilike(
                            f"%{search_value}%"
                        ),
                        carrier.c.carrier_name.cast(String).ilike(f"%{search_value}%"),
                    )

                    # Apply filter only if search_term is not None
                    cdr_query = cdr_query.filter(
                        or_(search_value is None, search_filter)
                    )
                return cdr_query

            # Query for the current month
            query_current = create_query(
                cdr_table=cdr_current_month,
                partition_table=partition_table,
                imsi=imsi,
            )

            query = query_current
            current_month = Month.today()
            if current_month != month:
                cdr_next_month = self._get_partition_table(
                    month.next(), table_prefix="cdr"
                )
                # Query for the next month
                query_next = create_query(
                    cdr_table=cdr_next_month,
                    partition_table=partition_table,
                    imsi=imsi,
                )
                query = query_current.union_all(query_next)
            if pagination is not None:
                query = query.limit(pagination.page_size).offset(pagination.offset)

            query = query.order_by(desc(partition_table.c.date_sent))
            results = self.session.execute(query).mappings()
            for row in results:
                yield model.SimSMSCDRHistory(**row)
        except NoSuchTableError as e:
            logger.error(f"No such table error occoured.: {str(e)}")
            raise NoTableExist(imsi, month)

    def sms_connection_history_count(
        self,
        imsi: IMSI,
        month: Month,
        searching: Searching | None = None,
    ) -> int:
        try:

            cdr_current_month = self._get_partition_table(month, table_prefix="cdr")
            carrier = orm.carrier_name
            partition_table = self._get_partition_table(month, table_prefix="cdr_sms")

            def create_query(cdr_table, partition_table, imsi):
                cdr_query = (
                    select(count(cdr_table.c.uuid).label("cdr_count"))
                    .join(
                        partition_table, partition_table.c.cdr_uuid == cdr_table.c.uuid
                    )
                    .join(carrier, carrier.c.carrier == cdr_table.c.carrier)
                    .filter(
                        cdr_table.c.imsi == imsi,
                    )
                )

                if searching:
                    search_value = searching.search
                    search_filter = or_(
                        cdr_table.c.iccid.ilike(f"%{search_value}%"),
                        cdr_table.c.imsi.ilike(f"%{search_value}%"),
                        cdr_table.c.country.ilike(f"%{search_value}%"),
                        cdr_table.c.country_name.ilike(f"%{search_value}%"),
                        cdr_table.c.carrier.ilike(f"%{search_value}%"),
                        partition_table.c.sent_from.cast(String).ilike(
                            f"%{search_value}%"
                        ),
                        partition_table.c.sent_to.cast(String).ilike(
                            f"%{search_value}%"
                        ),
                        partition_table.c.date_sent.cast(String).ilike(
                            f"%{search_value}%"
                        ),
                        carrier.c.carrier_name.cast(String).ilike(f"%{search_value}%"),
                    )

                    # Apply filter only if search_term is not None
                    cdr_query = cdr_query.filter(
                        or_(search_value is None, search_filter)
                    )
                return cdr_query

            # Query for the current month
            query_current = create_query(
                cdr_table=cdr_current_month,
                partition_table=partition_table,
                imsi=imsi,
            )

            query = query_current
            current_month = Month.today()
            if current_month != month:
                cdr_next_month = self._get_partition_table(
                    month.next(), table_prefix="cdr"
                )
                # Query for the next month
                query_next = create_query(
                    cdr_table=cdr_next_month,
                    partition_table=partition_table,
                    imsi=imsi,
                )
                query = query_current.union_all(query_next)
            query = select(func.sum(query.subquery().c.cdr_count).label("cdr_count"))
            return self.session.execute(query).scalar_one()
        except NoSuchTableError as e:
            logger.error(f"No such table error occoured.: {str(e)}")
            raise NoTableExist(imsi, month)

    def get_duplicate_cdr(self, month: Month) -> Iterator[model.DuplicateCdr]:
        try:
            cdr_reference = orm.cdr_references

            cdr_current_month = self._get_partition_table(month, table_prefix="cdr")
            partition_table = self._get_partition_table(month, table_prefix="cdr_data")

            def create_query(cdr_table, partition_table):
                return (
                    select(
                        cdr_table.c.imsi,
                        partition_table.c.session_starttime,
                        partition_table.c.session_endtime,
                        partition_table.c.duration,
                        partition_table.c.data_volume,
                        func.string_agg(cdr_reference.c.file_key, ",").label(
                            "file_key"
                        ),
                        func.count().label("num_duplicates"),
                    )
                    .join(cdr_table, cdr_table.c.uuid == partition_table.c.cdr_uuid)
                    .join(cdr_reference, cdr_table.c.uuid == cdr_reference.c.cdr_uuid)
                    .group_by(
                        cdr_table.c.imsi,
                        partition_table.c.session_starttime,
                        partition_table.c.session_endtime,
                        partition_table.c.duration,
                        partition_table.c.data_volume,
                    )
                    .having(func.count() > 1)
                )

            # Query for the current month
            query_current = create_query(
                cdr_table=cdr_current_month,
                partition_table=partition_table,
            )

            query = query_current
            current_month = Month.today()
            if current_month != month:
                cdr_next_month = self._get_partition_table(
                    month.next(), table_prefix="cdr"
                )
                # Query for the next month
                query_next = create_query(
                    cdr_table=cdr_next_month,
                    partition_table=partition_table,
                )
                query = query_current.union_all(query_next)
            for row in self.session.execute(query).mappings():
                file_keys = row["file_key"].split(",")
                modified_row = {**row, "file_key": file_keys}
                yield model.DuplicateCdr(**modified_row)

        except NoSuchTableError as e:
            logger.error(f"No such table error occoured.: {str(e)}")
            raise NoTableExist(month=month)

    def is_duplicate_cdr(self, cdr: model.CDR) -> UUID | None:
        try:
            current_month = Month.today()
            months_to_check = [current_month, current_month.prev()]
            queries = []

            sim_cdr_data = None
            sim_cdr_voice = None
            sim_cdr_sms = None

            # Determine which type of CDR we're checking
            if cdr.data:
                cdr_data_month = Month(
                    year=cdr.data.session_endtime.year,
                    month=cdr.data.session_endtime.month,
                    day=1,
                )
                sim_cdr_data = self._get_partition_table(cdr_data_month, "cdr_data")
            elif cdr.voice:
                cdr_voice_month = Month(
                    year=cdr.voice.call_date.year,
                    month=cdr.voice.call_date.month,
                    day=1,
                )
                sim_cdr_voice = self._get_partition_table(cdr_voice_month, "cdr_voice")
            elif cdr.sms:
                cdr_sms_month = Month(
                    year=cdr.sms.date_sent.year,
                    month=cdr.sms.date_sent.month,
                    day=1,
                )
                sim_cdr_sms = self._get_partition_table(cdr_sms_month, "cdr_sms")

            for month in months_to_check:
                sim_cdr = self._get_partition_table(month, "cdr")

                query = select(sim_cdr.c.uuid).filter(
                    sim_cdr.c.imsi == cdr.imsi,
                    sim_cdr.c.iccid == cdr.iccid,
                    sim_cdr.c.country == cdr.country,
                    sim_cdr.c.carrier == cdr.carrier,
                    sim_cdr.c.country_name == cdr.country_name,
                    sim_cdr.c.type == cdr.type,
                )

                if cdr.data and sim_cdr_data is not None:
                    query = query.join(
                        sim_cdr_data, sim_cdr_data.c.cdr_uuid == sim_cdr.c.uuid
                    ).filter(
                        sim_cdr_data.c.session_endtime == cdr.data.session_endtime,
                        sim_cdr_data.c.session_starttime == cdr.data.session_starttime,
                        sim_cdr_data.c.data_volume == cdr.data.data_volume,
                        sim_cdr_data.c.duration == cdr.data.duration,
                    )
                elif cdr.voice and sim_cdr_voice is not None:
                    query = query.join(
                        sim_cdr_voice, sim_cdr_voice.c.cdr_uuid == sim_cdr.c.uuid
                    ).filter(
                        sim_cdr_voice.c.call_date == cdr.voice.call_date,
                        sim_cdr_voice.c.call_number == cdr.voice.call_number,
                        sim_cdr_voice.c.call_minutes == cdr.voice.call_minutes,
                    )
                elif cdr.sms and sim_cdr_sms is not None:
                    query = query.join(
                        sim_cdr_sms, sim_cdr_sms.c.cdr_uuid == sim_cdr.c.uuid
                    ).filter(
                        sim_cdr_sms.c.date_sent == cdr.sms.date_sent,
                        sim_cdr_sms.c.sent_from == cdr.sms.sent_from,
                        sim_cdr_sms.c.sent_to == cdr.sms.sent_to,
                    )

                queries.append(query)

            combined_query = union(*queries)
            return self.session.execute(combined_query.limit(1)).scalar()

        except NoSuchTableError as e:
            logger.error(f"No such table error occurred: {str(e)}")
            raise NoTableExist(cdr.imsi, month)

    def add_cdr_references(self, cdr_references: model.CDRReferences):
        self.session.add(cdr_references)
        self.session.commit()

    def _get_partition_table(self, month: Month, table_prefix: str):
        partition_table_name = f"{table_prefix}_{month.strftime('%Y_%m')}"
        metadata = MetaData()
        return Table(partition_table_name, metadata, autoload_with=self.session.bind)
