from datetime import datetime
from uuid import uuid4

from sqlalchemy import (
    BigI<PERSON>ger,
    Column,
    Date,
    DateTime,
    ForeignKey,
    Identity,
    Index,
    Integer,
    String,
    Table,
    UniqueConstraint,
)
from sqlalchemy.dialects import postgresql
from sqlalchemy.orm import relationship

from cdrdata.domain import model
from common.db import mapper_registry
from common.types import ICCID, IMSI

last_session_cdr = Table(
    "last_session_cdr",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("iccid", String(length=ICCID.max_length), nullable=False),
    Column("imsi", String(length=IMSI.max_length), nullable=False),
    Column("last_session", DateTime, nullable=False),
)

carrier_name = Table(
    "carrier_name",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("carrier", String(length=5), nullable=False),
    Column("carrier_name", String(), nullable=True),
    UniqueConstraint("carrier"),
)

cdr_references = Table(
    "cdr_references",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column(
        "cdr_uuid",
        postgresql.UUID(),
        nullable=False,
    ),
    Column("file_key", String(), nullable=False),
)

sim_cdr = Table(
    "cdr",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("uuid", postgresql.UUID(as_uuid=True), default=uuid4, nullable=False),
    Column("imsi", String(length=IMSI.max_length), nullable=True),
    Column("iccid", String(length=ICCID.max_length), nullable=False),
    Column("country", String(), nullable=False),
    Column("country_name", String(), nullable=True),
    Column("carrier", String(), ForeignKey("carrier_name.carrier"), nullable=False),
    Column(
        "created_at",
        DateTime,
        primary_key=True,
        default=datetime.utcnow,
        nullable=False,
    ),
    Column("cdr_object_id", String(25), nullable=True),
    Column("type", String(15), nullable=False),
    Index("idx_cdr_new_created_at", "created_at"),
    Index("idx_cdr_new_imsi", "imsi"),
    Index("ix_cdr_type", "type"),
    postgresql_partition_by="RANGE (created_at)",
)

sim_cdr_data = Table(
    "cdr_data",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column(
        "cdr_uuid",
        postgresql.UUID(),
        nullable=False,
    ),
    Column("session_starttime", DateTime, nullable=False),
    Column("session_endtime", DateTime, primary_key=True, nullable=False),
    Column("duration", Integer, nullable=False),
    Column("data_volume", BigInteger, nullable=False),
    Index("idx_cdr_data_new_session_endtime", "session_endtime"),
    Index("idx_cdr_data_new_cdr_uuid", "cdr_uuid"),
    postgresql_partition_by="RANGE (session_endtime)",
)

sim_cdr_voice = Table(
    "cdr_voice",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column(
        "cdr_uuid",
        postgresql.UUID(),
        nullable=False,
    ),
    Column("call_date", DateTime, primary_key=True, nullable=False),
    Column("call_number", String(), nullable=False),
    Column("call_minutes", Integer, nullable=False),
    Index("idx_cdr_voice_new_call_date", "call_date"),
    postgresql_partition_by="RANGE (call_date)",
)

sim_cdr_sms = Table(
    "cdr_sms",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column(
        "cdr_uuid",
        postgresql.UUID(),
        nullable=False,
    ),
    Column("date_sent", DateTime, primary_key=True, nullable=False),
    Column("sent_from", String(), nullable=False),
    Column("sent_to", String(), nullable=False),
    Index("idx_cdr_sms_new_date_sent", "date_sent"),
    postgresql_partition_by="RANGE (date_sent)",
)

cdr_data_aggregate = Table(
    "cdr_data_aggregate",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column(
        "cdr_uuid",
        postgresql.UUID(),
        nullable=False,
    ),
    Column("imsi", String(length=IMSI.max_length), nullable=True),
    Column("iccid", String(length=ICCID.max_length), nullable=False),
    Column("usage", BigInteger, nullable=False),
    Column("month", Date, primary_key=True, nullable=False),
    Index("idx_cdr_data_aggregate_new_month", "month"),
    UniqueConstraint("iccid", "month", name="uq_cdr_data_aggregate_new_iccid"),
    postgresql_partition_by="RANGE (month)",
)

cdr_voice_aggregate = Table(
    "cdr_voice_aggregate",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column(
        "cdr_uuid",
        postgresql.UUID(),
        nullable=False,
    ),
    Column("imsi", String(length=IMSI.max_length), nullable=False),
    Column("iccid", String(length=ICCID.max_length), nullable=False),
    Column("voice_usage", BigInteger, nullable=False),
    Column("month", Date, primary_key=True, nullable=False),
    Index("idx_cdr_voice_aggregate_new_month", "month"),
    UniqueConstraint("iccid", "month", name="uq_cdr_voice_aggregate_new_iccid"),
    postgresql_partition_by="RANGE (month)",
)

cdr_sms_aggregate = Table(
    "cdr_sms_aggregate",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column(
        "cdr_uuid",
        postgresql.UUID(),
        nullable=False,
    ),
    Column("imsi", String(length=IMSI.max_length), nullable=False),
    Column("iccid", String(length=ICCID.max_length), nullable=False),
    Column("total_sms", BigInteger, nullable=False),
    Column("month", Date, primary_key=True, nullable=False),
    UniqueConstraint("iccid", "month", name="uq_cdr_sms_aggregate"),
    Index("idx_cdr_sms_aggregate_month", "month"),
    postgresql_partition_by="RANGE (month)",
)


cdr_voice_mo_mt_incamel = Table(
    "cdr_voice_mo_mt_incamel",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column(
        "imsi",
        String(length=IMSI.max_length),
        nullable=False,
    ),
    Column("iccid", String(length=ICCID.max_length), nullable=False),
    Column("usage", BigInteger, nullable=False),
    Column("type", String(15), nullable=False),
    Column("month", Date, primary_key=True, nullable=False),
    Index("idx_cdr_voice_mo_mt_incamel_month_type", "month", "type"),
    UniqueConstraint(
        "iccid", "month", "type", name="uq_cdr_voice_mo_mt_incamel_iccid_month_type"
    ),
    postgresql_partition_by="RANGE (month)",
)

cdr_sms_mo_mt = Table(
    "cdr_sms_mo_mt",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column(
        "imsi",
        String(length=IMSI.max_length),
        nullable=False,
    ),
    Column("iccid", String(length=ICCID.max_length), nullable=False),
    Column("usage", BigInteger, nullable=False),
    Column("type", String(15), nullable=False),
    Column("month", Date, primary_key=True, nullable=False),
    Index("idx_cdr_sms_mo_mt_month_type", "month", "type"),
    UniqueConstraint(
        "iccid", "month", "type", name="uq_cdr_sms_mo_mt_iccid_month_type"
    ),
    postgresql_partition_by="RANGE (month)",
)


def start_mappers() -> None:
    mapper_registry.map_imperatively(
        model.CDR,
        sim_cdr,
        properties=dict(
            _carrier_name=relationship(
                model.CarrierName,
                back_populates="_cdr",
                cascade="all, delete-orphan",
                single_parent=True,
            ),
        ),
    )
    mapper_registry.map_imperatively(
        model.CDRData,
        sim_cdr_data,
    )
    mapper_registry.map_imperatively(
        model.CDRVoice,
        sim_cdr_voice,
    )
    mapper_registry.map_imperatively(
        model.CDRSMS,
        sim_cdr_sms,
    )
    mapper_registry.map_imperatively(
        model.DataUsageAggregate,
        cdr_data_aggregate,
    )
    mapper_registry.map_imperatively(
        model.VoiceUsageAggregate,
        cdr_voice_aggregate,
    )
    mapper_registry.map_imperatively(
        model.SMSUsageAggregate,
        cdr_sms_aggregate,
    )
    mapper_registry.map_imperatively(
        model.lastSessionCDR,
        last_session_cdr,
    )
    mapper_registry.map_imperatively(
        model.CDRReferences,
        cdr_references,
    )
    mapper_registry.map_imperatively(
        model.CarrierName,
        carrier_name,
        properties=dict(
            _cdr=relationship(model.CDR, back_populates="_carrier_name"),
        ),
    )
    mapper_registry.map_imperatively(
        model.CDRVoiceMOMTIncamel,
        cdr_voice_mo_mt_incamel,
    )
    mapper_registry.map_imperatively(
        model.CDRSMSMOMT,
        cdr_sms_mo_mt,
    )


def stop_mappers() -> None:
    mapper_registry.dispose()
