from dataclasses import InitVar
from datetime import datetime
from uuid import UUID

from pydantic.dataclasses import dataclass

from common.types import ICCID, IMSI, Carrier, Month


@dataclass
class CDRData:
    cdr_uuid: str
    session_starttime: datetime
    session_endtime: datetime
    duration: int
    data_volume: int


@dataclass
class CDRVoice:
    cdr_uuid: str
    call_date: datetime
    call_number: str
    call_minutes: int


@dataclass
class CDRSMS:
    cdr_uuid: str
    date_sent: datetime
    sent_from: str
    sent_to: str


@dataclass
class DataUsageAggregate:
    cdr_uuid: str
    imsi: IMSI | None
    iccid: ICCID
    usage: int
    month: Month


@dataclass
class VoiceUsageAggregate:
    cdr_uuid: str
    imsi: IMSI
    iccid: ICCID
    voice_usage: int
    month: Month


@dataclass
class CDRVoiceMOMTIncamel:
    imsi: IMSI
    iccid: ICCID
    usage: int
    type: str
    month: Month


@dataclass
class SMSUsageAggregate:
    cdr_uuid: str
    imsi: IMSI
    iccid: ICCID
    total_sms: int
    month: Month


@dataclass
class CDRSMSMOMT:
    imsi: IMSI
    iccid: ICCID
    usage: int
    type: str
    month: Month


@dataclass
class CDR:
    uuid: UUID
    imsi: IMSI
    iccid: ICCID
    type: str
    country: str | None
    country_name: str | None
    carrier: str | None
    data: CDRData | None
    voice: CDRVoice | None
    sms: CDRSMS | None
    data_usage: DataUsageAggregate | None
    voice_usage: VoiceUsageAggregate | None
    sms_usage: SMSUsageAggregate | None
    voice_mo_mt_incamel: CDRVoiceMOMTIncamel | None
    sms_mo_mt: CDRSMSMOMT | None
    cdr_object_id: str | None
    _uuid: InitVar[UUID | None] = None

    def __post_init__(self, _uuid: UUID | None):
        if _uuid is not None:
            self.uuid = _uuid


@dataclass
class SimCDRHistory:
    country: str
    carrier: Carrier
    session_starttime: datetime
    iccid: ICCID
    session_endtime: datetime
    duration: int
    data_volume: int
    imsi: IMSI
    country_name: str
    carrier_name: str


@dataclass
class lastSessionCDR:
    iccid: ICCID
    imsi: IMSI
    last_session: datetime


@dataclass
class CarrierName:
    carrier: str
    carrier_name: str | None = None


@dataclass
class AnalyticsCDRObject:
    cdr_object_id: str


@dataclass
class SimVoiceCDRHistory:
    iccid: ICCID
    imsi: IMSI
    country: str
    carrier: Carrier
    call_date: datetime
    call_number: str
    call_minutes: int
    country_name: str
    carrier_name: str


@dataclass
class SimSMSCDRHistory:
    iccid: ICCID
    imsi: IMSI
    country: str
    carrier: Carrier
    date_sent: datetime
    sent_from: str
    sent_to: str
    country_name: str
    carrier_name: str


@dataclass
class DuplicateCdr:
    imsi: IMSI
    session_starttime: datetime
    session_endtime: datetime
    duration: int
    data_volume: int
    num_duplicates: int
    file_key: list[str]


@dataclass
class CDRReferences:
    cdr_uuid: str
    file_key: str
