from abc import ABC, abstractmethod

from fastapi.encoders import jsonable_encoder
from platform_api_client import PlatformAPIError
from pydantic.errors import EmailError
from pydantic.networks import EmailStr
from starlette import status

from app.config import logger, settings
from app.platform import PlatformAPIClient
from auth.exceptions import ForbiddenError, Unauthorized
from mail_delivery.exception import MailAPIError


class AbstractMailService(ABC):
    @abstractmethod
    def send_mail(
        self, subject: str, name_from: str, recipients: list[EmailStr], html_body: str
    ):
        pass

    def validate_emails(self, recipients: list[EmailStr]):
        invalid = []
        for email in recipients:
            try:
                EmailStr.validate(email)
            except EmailError:
                invalid.append(email)
        if invalid:
            raise ValueError(f"Invalid email(s): {', '.join(invalid)}")


class MailServiceAPI(AbstractMailService):
    SEND_MAIL_API = "/v1/email/send"

    def __init__(self, api_client: PlatformAPIClient):
        self.api = api_client

    def send_mail(
        self, subject: str, name_from: str, recipients: list[EmailStr], html_body: str
    ):
        try:
            self.validate_emails(recipients=recipients)
            url = f"{settings.APP_BASE_URL}{self.__class__.SEND_MAIL_API}"
            payload = {
                "subject": subject,
                "name_from": name_from,
                "recipients": [{"email": email} for email in recipients],
                "html": html_body,
            }
            logger.info(f"Mail: {url}")
            logger.info(f"Payload: {payload}")
            response = self.api.post(url=url, json=jsonable_encoder(payload))
            if response.status_code == 202:
                return response.json()
            else:
                raise MailAPIError
        except PlatformAPIError as e:
            logger.error("Error in a send mail .:")
            logger.error(f"Error {e} .:")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    raise e
        except ConnectionError:
            raise ConnectionError("Failed to connect to the Mail API")


class FakeMailServiceAPI(AbstractMailService):
    def send_mail(
        self, subject: str, name_from: str, recipients: list[EmailStr], html_body: str
    ):
        self.validate_emails(recipients)
        print("[FAKE EMAIL SENT]")
        print("Subject:", subject)
        print("From:", name_from)
        print("To:", recipients)
        print("HTML:", html_body)
        return {"status": "fake_sent", "recipients": recipients}
