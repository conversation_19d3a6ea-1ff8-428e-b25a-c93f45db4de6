class AuthorizationError(Exception):
    ...


class GroupNotFound(AuthorizationError):
    def __init__(self, group_id):
        super().__init__(f"Group with id {group_id} not found.")


class PolicyNotFound(AuthorizationError):
    def __init__(self, role_id):
        super().__init__(f"Policy not found associated with id {role_id}")


class RoleNotFound(AuthorizationError):
    def __init__(self, role_id):
        super().__init__(f"Role not found associated with id {role_id}")


class MaxNumberInvalid(AuthorizationError):
    def __init__(self):
        super().__init__("max Number should be a multiple of 10.")


class GroupRoleNotFound(AuthorizationError):
    def __init__(self, group_id):
        super().__init__(f"Group Roles Not Found for id {group_id} .")


class RoleDeletionError(AuthorizationError):
    ...


class PolicyAlreadyExist(AuthorizationError):
    ...
