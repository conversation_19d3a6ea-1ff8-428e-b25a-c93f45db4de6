from datetime import datetime

from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    Column,
    DateTime,
    Enum,
    Foreign<PERSON>ey,
    Identity,
    Integer,
    String,
    Table,
    UniqueConstraint,
)
from sqlalchemy.dialects import postgresql
from sqlalchemy.orm import relationship

from authorization.domain import model
from common.db import mapper_registry

user_role = Table(
    "user_role",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("role", String(length=50), nullable=False),
    <PERSON>umn("rolegroup", Enum(model.RoleGroup, name="rolegroup"), nullable=False),
    <PERSON>umn("is_default", <PERSON><PERSON>an, nullable=False),
    Column("created_at", DateTime, default=datetime.utcnow, nullable=False),
    Column("created_by", String(length=128), nullable=False),
    <PERSON>umn(
        "role_uuid",
        postgresql.UUID(as_uuid=True),
        nullable=True,
    ),
    UniqueConstraint("role"),
)

role_group = Table(
    "role_group",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    <PERSON>umn(
        "role_id",
        Integer,
        ForeignKey("user_role.id"),
        nullable=False,
    ),
    Column(
        "organization_id",
        Integer,
        nullable=False,
    ),
)


def start_mappers() -> None:
    mapper_registry.map_imperatively(
        model.UserRole,
        user_role,
        properties=dict(
            _role_group=relationship(
                model.RoleGroups,
                back_populates="_user_role",
                cascade="all, delete-orphan",
            )
        ),
    )
    mapper_registry.map_imperatively(
        model.RoleGroups,
        role_group,
        properties=dict(
            _user_role=relationship(model.UserRole, back_populates="_role_group"),
        ),
    )


def stop_mappers() -> None:
    mapper_registry.dispose()
