from abc import ABC, abstractmethod
from uuid import UUID

from sqlalchemy import and_, delete, func, or_, select
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from app.config import logger
from authorization.adapters import orm
from authorization.domain import model
from authorization.exceptions import RoleDeletionError
from common.pagination import Pagination
from common.searching import Searching, apply_search_to_sql
from common.types import DefaultRole


class AbstractAuthRepository(ABC):
    @abstractmethod
    def create_role(
        self,
        role_request: model.UserRole,
    ) -> None:
        ...

    @abstractmethod
    def create_role_for_client(
        self, role_request: model.UserRole, organization_id: int
    ) -> None:
        ...

    @abstractmethod
    def get_roles(
        self,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> list[model.DBRoleResponse]:
        ...

    @abstractmethod
    def get_roles_by_organization_id(
        self,
        organization_id: int,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> list[model.DBRoleResponse]:
        ...

    @abstractmethod
    def is_default(self, role_uuid: UUID) -> bool:
        ...

    @abstractmethod
    def delete_role(self, role_uuid: UUID) -> None:
        ...

    @abstractmethod
    def create_role_for_accounts(self, role_request: model.UserRole) -> None:
        ...

    @abstractmethod
    def get_role_info(self, role_name: str) -> model.DBRoleResponse:
        ...

    @abstractmethod
    def update_role_uuid(self, role_info: model.RoleResponse) -> None:
        ...


class DatabaseAuthRepository(AbstractAuthRepository):
    def __init__(self, session: Session) -> None:
        self.session = session
        self.condition_true = True

    def create_role(
        self,
        role_request: model.UserRole,
    ) -> None:
        self.session.add(role_request)
        self.session.flush()
        self.session.commit()

    def create_role_for_accounts(self, role_request: model.UserRole) -> None:
        self.session.add(role_request)
        self.session.flush()
        role_group_function = func.role_group_function_v2(role_request.id)
        query = select("*").select_from(role_group_function)
        self.session.execute(query)
        self.session.commit()

    def create_role_for_client(
        self, role_request: model.UserRole, organization_id: int
    ) -> None:
        self.session.add(role_request)
        self.session.flush()
        self.session.add(
            model.RoleGroups(
                role_id=role_request.id,
                organization_id=organization_id,
            )
        )
        self.session.commit()

    def get_roles(
        self,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> list[model.DBRoleResponse]:
        role = orm.user_role
        query = (
            select(
                role.c.role,
                role.c.rolegroup,
                role.c.is_default,
                role.c.created_by,
                role.c.role_uuid,
            )
            .filter(
                and_(
                    role.c.role != DefaultRole.DistributorUser.name,
                    role.c.role != DefaultRole.ClientUser.name,
                )
            )
            .order_by(role.c.role)
        )
        if searching is not None:
            query = apply_search_to_sql(searching, model.UserRole, query)
        if pagination is not None:
            query = query.limit(pagination.page_size).offset(pagination.offset)
        result = self.session.execute(query).all()
        logger.info(f"All Roles : {result}")
        return [model.DBRoleResponse(**role) for role in result]  # type: ignore

    def get_roles_by_organization_id(
        self,
        organization_id: int,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> list[model.DBRoleResponse]:
        role = orm.user_role
        group = orm.role_group
        TRUE = True
        FALSE = False
        query = (
            select(
                role.c.role,
                role.c.rolegroup,
                role.c.is_default,
                role.c.created_by,
                role.c.role_uuid,
            )
            .outerjoin(
                group,
                and_(
                    group.c.role_id == role.c.id,
                    group.c.organization_id == organization_id,
                ),
            )
            .filter(
                or_(
                    and_(
                        role.c.is_default == TRUE,
                        or_(
                            role.c.role == DefaultRole.ClientAdmin.name,
                            role.c.role == DefaultRole.Client_ReadOnly.name,
                        ),
                    ),
                    and_(
                        role.c.is_default == FALSE,
                        group.c.organization_id == organization_id,
                        group.c.role_id.isnot(None),
                    ),
                ),
            )
            .order_by(role.c.role)
        )
        if searching is not None:
            query = apply_search_to_sql(searching, model.UserRole, query)
        if pagination is not None:
            query = query.limit(pagination.page_size).offset(pagination.offset)
        result = self.session.execute(query).all()
        return [model.DBRoleResponse(**role) for role in result]  # type: ignore

    def is_default(self, role_uuid: UUID) -> bool:
        try:
            role = orm.user_role
            role_id = (
                self.session.query(role.c.id)
                .filter(role.c.role_uuid == role_uuid)
                .scalar()
            )
            if role_id:
                default_role = self.session.query(role.c.is_default).filter(
                    role.c.id == role_id
                )
                return bool(self.session.execute(default_role).scalar())
            return True
        except IntegrityError as e:
            logger.error(f"Error:{e}")
            raise RoleDeletionError("Error in database operation")

    def delete_role(self, role_uuid: UUID) -> None:
        try:
            role = orm.user_role
            group = orm.role_group
            role_id = (
                self.session.query(role.c.id)
                .filter(role.c.role_uuid == role_uuid)
                .scalar()
            )
            if role_id:
                delete_group_query = delete(group).where(group.c.role_id == role_id)
                self.session.execute(delete_group_query)
                delete_role_query = delete(role).where(role.c.id == role_id)
                self.session.execute(delete_role_query)
            self.session.commit()
        except IntegrityError as e:
            logger.error(f"Error:{e}")
            raise RoleDeletionError((f"Role {role_uuid} could not be deleted."))

    def get_role_info(self, role_name: str) -> model.DBRoleResponse:
        role = orm.user_role
        query = select(
            role.c.role,
            role.c.rolegroup,
            role.c.is_default,
            role.c.created_by,
            role.c.role_uuid,
        ).filter(role.c.role == role_name)
        role_data = self.session.execute(query).first()
        if role_data:
            return model.DBRoleResponse(
                role_uuid=role_data.role_uuid,
                role=role_data.role,
                rolegroup=role_data.rolegroup,
                created_by=role_data.created_by,
                is_default=role_data.is_default,
            )
        else:
            raise ValueError("Role data not available")

    def update_role_uuid(self, role_info: model.RoleResponse) -> None:
        user_role = orm.user_role
        self.session.query(user_role).filter(user_role.c.role == role_info.name).update(
            {user_role.c.role_uuid: role_info.id}
        )
        self.session.commit()
