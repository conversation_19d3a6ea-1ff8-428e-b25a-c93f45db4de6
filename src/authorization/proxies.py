from typing import Iterator
from uuid import UUID

from pydantic import EmailStr

from app.config import logger
from auth.dto import AuthenticatedUser
from auth.exceptions import ForbiddenError
from auth.permissions import is_distributor_client, is_distributor_staff
from authorization.domain import model
from authorization.services import AbstractAuthorizationAPI
from common.pagination import Pagination
from common.searching import Searching


class AuthorizationAuthProxy(AbstractAuthorizationAPI):
    def __init__(
        self,
        authorization: AbstractAuthorizationAPI,
        user: AuthenticatedUser,
    ) -> None:
        self.authorization = authorization
        self.user = user

    def get_roles(  # type: ignore
        self,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.GroupRole], int]:
        if is_distributor_staff(self.user):
            logger.info("is_distributor_staff")
            yield from self.authorization.get_roles(
                pagination=pagination,
                searching=searching,
            )
        elif is_distributor_client(self.user):
            logger.info("is_distributor_client")
            yield from self.authorization.get_group_role(
                organization_id=self.user.organization.account.id,  # type: ignore
                searching=searching,
                pagination=pagination,
            )
        else:
            raise ForbiddenError

    def get_group_role(
        self,
        organization_id: int,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> tuple[Iterator[model.GroupRole], int]:
        return self.authorization.get_group_role(
            organization_id=organization_id, searching=searching, pagination=pagination
        )

    def get_permissions(
        self,
        role_id: UUID,
        organization_id: int | None = None,
    ) -> model.PermissionsResponse:
        if is_distributor_staff(self.user):
            logger.info("is_distributor_staff")
            return self.authorization.get_permissions(role_id=role_id)
        elif is_distributor_client(self.user):
            logger.info("is_distributor_client")
            return self.authorization.get_permissions(
                role_id=role_id,
                organization_id=self.user.organization.account.id,  # type: ignore
            )
        else:
            raise ForbiddenError

    def group_list(self):
        return self.authorization.group_list()

    def create_role(
        self,
        role_request: model.RoleRequest,
        organization_id: int | None = None,
        created_by: EmailStr | None = None,
    ) -> model.RoleResponse:
        created_by = self.user.email
        if is_distributor_staff(self.user):
            logger.info("is_distributor_staff")
            return self.authorization.create_role(role_request, created_by=created_by)
        elif is_distributor_client(self.user):
            logger.info("is_distributor_client")
            return self.authorization.create_role(
                role_request,
                organization_id=self.user.organization.account.id,  # type: ignore
                created_by=created_by,
            )
        else:
            raise ForbiddenError

    def delete_role(self, role_uuid: UUID) -> None:
        return self.authorization.delete_role(role_uuid)

    # def get_permission_list(self) -> list[str]:
    #     return self.authorization.get_permission_list()

    def is_authorized(self, scope: str):
        return self.authorization.is_authorized(scope)

    def get_user_scope(self) -> model.UserScopeList:
        return self.get_user_scope()
