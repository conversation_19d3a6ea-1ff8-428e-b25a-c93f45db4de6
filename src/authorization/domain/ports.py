from abc import ABC, abstractmethod
from typing import Iterator
from uuid import UUID

from pydantic import EmailStr

from authorization.domain import model
from common.pagination import Pagination
from common.searching import Searching


class AbstractAuthorizationAPI(ABC):
    @abstractmethod
    def get_group_role(
        self,
        organization_id: int,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> tuple[Iterator[model.GroupRole], int]:
        ...

    @abstractmethod
    def group_list(self):
        ...

    @abstractmethod
    def get_permissions(
        self,
        role_id: UUID,
        organization_id: int | None = None,
    ) -> model.PermissionsResponse:
        ...

    @abstractmethod
    def get_roles(
        self,
        pagination: Pagination | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.GroupRole], int]:
        ...

    @abstractmethod
    def create_role(
        self,
        RoleRequest: model.RoleRequest,
        organization_id: int | None = None,
        created_by: EmailStr | None = None,
    ) -> model.RoleResponse:
        ...

    @abstractmethod
    def delete_role(self, role_uuid: UUID) -> None:
        ...

    @abstractmethod
    def is_authorized(self, scope: str):
        ...

    @abstractmethod
    def get_user_scope(self) -> model.UserScopeList:
        ...
