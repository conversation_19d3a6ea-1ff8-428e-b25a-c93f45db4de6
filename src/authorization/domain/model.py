import re
from dataclasses import InitVar
from enum import Enum
from uuid import UUID

from pydantic import BaseModel, EmailStr, Field, validator
from pydantic.dataclasses import dataclass


@dataclass
class Organization:
    id: int
    type: str


@dataclass
class Permissions:
    scopes: list[str]
    rsid: str
    rsname: str


@dataclass
class GroupRole:
    id: UUID
    name: str
    isDefault: bool = False
    description: str | None = None
    group: str | None = None
    permissions: int = 0
    userCount: int | None = 0
    createdBy: str | None = None


@dataclass
class Role:
    id: str


@dataclass
class Permission:
    id: UUID
    name: str
    title: str


@dataclass
class PermissionsResponse:
    id: UUID
    name: str
    roleGroup: str
    description: str | None = None
    permission: list[str] | None = None


@dataclass
class UserRole:
    role: str
    rolegroup: str
    created_by: EmailStr
    role_uuid: UUID
    is_default: bool = False

    _id: InitVar[int | None] = None

    def __post_init__(self, _id: int | None):
        if _id is not None:
            self.id = _id


@dataclass
class RoleGroups:
    role_id: int
    organization_id: int


class RoleGroup(str, Enum):
    MY_ORGANIZATION = "My Organization"
    ACCOUNT = "Account"


class RoleRequest(BaseModel):
    name: str = Field(min_length=1, max_length=50)
    description: str | None = Field(default=None, max_length=255)
    roleGroup: RoleGroup

    @validator("name")
    def validate_name(cls, value):
        # Check if the name contains only spaces
        if value.isspace():
            raise ValueError("Role name cannot consist of only spaces")

        # Check for special characters using regex
        if not re.match("^[a-zA-Z0-9_ -]*$", value):
            raise ValueError("Role name cannot contain special characters")
        return value.strip()


class RoleResponse(BaseModel):
    id: UUID
    name: str
    description: str | None
    composite: bool
    clientRole: bool
    containerId: str
    userCount: int | None = 0
    created_by: str | None = None


class RolePolicyResponse(RoleResponse):
    policy_id: UUID


class DBRoleResponse(BaseModel):
    role_uuid: UUID
    role: str
    rolegroup: RoleGroup
    created_by: str
    is_default: bool = False


class UserScope(BaseModel):
    name: str
    permission: list[Permission] | None = []


class UserScopeList(BaseModel):
    result: list[UserScope]
