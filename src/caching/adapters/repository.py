from abc import ABC, abstractmethod

from app.config import logger, settings

import redis  # isort: skip


class AbstractRedisRepository(ABC):
    @abstractmethod
    def is_duplicate_cdr(self, cdr_id: str) -> bool:
        ...


class RedisRepository(AbstractRedisRepository):
    def __init__(self, redis_client: redis.StrictRedis) -> None:
        self.redis = redis_client

    def is_duplicate_cdr(self, cdr_id: str) -> bool:
        """
        Store a CDR ID in Redis with a TTL of 1 day.
        Key format: cdr:<cdr_id>
        Value: <cdr_id>
        """
        key = f"cdr:{cdr_id}"

        # SET with NX ensures no overwrite if key exists
        result = self.redis.set(key, cdr_id, ex=settings.CDR_TTL_SECONDS, nx=True)

        if result:
            logger.info(f"Stored new CDR ID {cdr_id} in Redis with key '{key}'.")
            return True

        logger.debug(f"Duplicate CDR ID {cdr_id} found in Redis.")
        return False
