from abc import ABC, abstractmethod

from caching.adapters.repository import AbstractRedisRepository


class AbstractRedisService(ABC):
    @abstractmethod
    def is_duplicate_cdr(self, cdr_id: str) -> bool:
        ...


class RedisService(AbstractRedisService):
    def __init__(self, repository: AbstractRedisRepository):
        self.repository = repository

    def is_duplicate_cdr(self, cdr_id: str) -> bool:
        return self.repository.is_duplicate_cdr(cdr_id)
