import logging

from auth.dto import AuthenticatedUser
from openapi.domain import model
from openapi.services import AbstractOpenApiAuthService


class OpenApiServiceAuthProxy(AbstractOpenApiAuthService):
    def __init__(
        self, openapi_service: AbstractOpenApiAuthService, user: AuthenticatedUser
    ) -> None:
        self.openapi_service = openapi_service
        self.user = user

    def generate_access_token(self) -> model.TokenResponse:
        logging.debug(f"api/token User: {self.user}")
        return self.openapi_service.generate_access_token()
