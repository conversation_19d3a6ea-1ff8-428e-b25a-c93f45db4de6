from abc import ABC, abstractmethod

from accounts.domain.ports import AbstractOrganizationRepository
from app.config import logger
from auth.exceptions import ForbiddenError
from authorization.domain.ports import AbstractAuthorizationAPI
from openapi.domain import model


class AbstractOpenApiAuthService(ABC):
    @abstractmethod
    def generate_access_token(self) -> model.TokenResponse:
        ...


class OpenApiAuthService(AbstractOpenApiAuthService):
    def __init__(
        self,
        authorization: AbstractAuthorizationAPI,
        organization_repository: AbstractOrganizationRepository,
    ):
        self.authorization = authorization
        self.organization = organization_repository

    def validate_api_access(self):
        logger.debug("api/token validate_api_access")
        scopes = self.authorization.get_user_scope()
        for scope in scopes.result:
            for permission in scope.permission:
                if "API" in permission.name.upper():
                    logger.info(f"Access granted because of permission : {permission}")
                    return True
        logger.info("No API permission found.")
        return False

    def generate_access_token(self) -> model.TokenResponse:
        logger.debug("api/token generate_access_token")
        validated = self.validate_api_access()
        if not validated:
            raise ForbiddenError
        response = self.organization.get_access_token()
        logger.info(f"api/token Token Response:- {response}")
        return response
