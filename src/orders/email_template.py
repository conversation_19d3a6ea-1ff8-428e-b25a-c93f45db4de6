from dataclasses import dataclass
from datetime import datetime
from typing import Dict, Optional

from pydantic.networks import EmailStr

from orders.constants import OrderStatus, RecipientType


@dataclass
class OrderData:
    order_id: str
    order_uuid: str
    order_status: OrderStatus
    order_date: str
    sim_type_wise_qty: Dict[str, int]

    customer_account_ref: str
    person_name: str
    person_email: EmailStr
    phone_number: str

    customer_contact_name: str
    shipping_address_address1: str
    shipping_address_address2: str
    city: str
    state: str
    postal_code: str
    country: str

    web_link: str

    tracking_url: Optional[str] = None
    tracking_ref: Optional[str] = None
    additional_info: Optional[str] = None
    comments: Optional[str] = None


def replace_underscore_with_space(s: str) -> str:
    return s.replace("_", " ")


def generate_order_details(
    order_id,
    order_status,
    order_date_and_time,
    sim_type_wise_quantity: dict,
    web_link: str,
) -> str:
    quantity_wise_html_list = []
    for sim_type, quantity in sim_type_wise_quantity.items():
        quantity_wise_html_list.append(
            f"{quantity} - {replace_underscore_with_space(sim_type)}"
        )
    quantity_wise_html_text = "<br>".join(quantity_wise_html_list)

    status_colors = {
        OrderStatus.PENDING.value: "#0296D4",
        OrderStatus.APPROVED.value: "#30B281",
        OrderStatus.CANCELLED.value: "#F7735D",
        OrderStatus.SHIPPED.value: "#7E2EC6",
        OrderStatus.ONHOLD.value: "#FFDF00",
    }

    color = status_colors[order_status]

    status_dot = (
        f'<span style="height: 8px; width: 8px; background-color: {color}; '
        'border-radius: 50%; display: inline-block; margin-right: 6px;"></span>'
    )

    row_html = (
        '<tr style="border-bottom: 1px solid #DEDEE6;">'
        '<td style="width: 300px; color: grey; text-align: left;">'
        "Order Status:"
        "</td>"
        '<td style="font-weight: bold; color: #333333; text-align: left;">'
        f'<span style="display: inline-flex; align-items: center;">'
        f"{status_dot}"
        f"{order_status.capitalize()} "
        f"{'Approval' if order_status == OrderStatus.PENDING.value else ''}"
        "</span>"
        "</td>"
        "</tr>"
    )

    html = (
        '<table width="100%" cellpadding="6" cellspacing="0" '
        'style="border-collapse: collapse; background-color: #ffffff; '
        'margin-bottom: 16px;">'
        "<tr>"
        '<td colspan="2" style="font-weight: bold; font-size: 16px;">'
        "Order Details"
        "</td>"
        "</tr>"
        '<tr style="border-bottom: 1px solid #DEDEE6;">'
        '<td style="width: 300px; color: grey; text-align: left;">'
        "Order ID:"
        "</td>"
        '<td style="font-weight: bold; color: #333333; text-align: left;">'
        f"<a href='{web_link}'>#{order_id} </a>"
        "</td>"
        "</tr>"
        f"{row_html}"
        '<tr style="border-bottom: 1px solid #DEDEE6;">'
        '<td style="width: 300px; color: grey; text-align: left;">'
        "Order Date and Time:"
        "</td>"
        '<td style="font-weight: bold; color: #333333; text-align: left;">'
        f"{order_date_and_time}"
        "</td>"
        "</tr>"
        '<tr style="border-bottom: 1px solid #DEDEE6;">'
        '<td style="width: 300px; color: grey; text-align: left;">'
        "Quantity and SIM Type:"
        "</td>"
        '<td style="font-weight: bold; color: #333333; text-align: left;">'
        f"{quantity_wise_html_text}"
        "</td>"
        "</tr>"
        "</table>"
    )
    return html


def generate_customer_details(customer_ref, person_name, person_email, person_mob_no):
    html = f"""<table width="100%" cellpadding="6" cellspacing="0"
style="border-collapse: collapse; background-color: #ffffff;
margin-bottom: 16px;">
<tr>
  <td colspan="2" style="font-weight: bold; font-size: 16px;">
    Customer Details
  </td>
</tr>
<tr style="border-bottom: 1px solid #DEDEE6;">
  <td style="width: 300px; color: grey; text-align: left;">
    Customer Account ref (if known):
  </td>
  <td style="font-weight: bold; color: #333333; text-align: left;">
   {customer_ref}
  </td>
</tr>
<tr style="border-bottom: 1px solid #DEDEE6;">
  <td style="width: 300px; color: grey; text-align: left;">
    Name of person placing order:
  </td>
  <td style="font-weight: bold; color: #333333; text-align: left;">
  {person_name}
  </td>
</tr>
<tr style="border-bottom: 1px solid #DEDEE6;">
  <td style="width: 300px; color: grey; text-align: left;">
    Email of person placing order:
  </td>
  <td style="font-weight: bold; color: #333333; text-align: left;">
    {person_email}
  </td>
</tr>
<tr style="border-bottom: 1px solid #DEDEE6;">
  <td style="width: 300px; color: grey; text-align: left;">
    Phone number:
  </td>
  <td style="font-weight: bold; color: #333333; text-align: left;">
    {person_mob_no}
  </td>
</tr>
</table>"""
    return html


def generate_shipping_details(
    contact_customer_name, address1, address2, city, state, postal_code, country
):
    html = f"""
    <table width="100%" cellpadding="6"  ="0"
style="border-collapse: collapse; background-color: #ffffff;
margin-bottom: 16px;">
<tr>
  <td colspan="2" style="font-weight: bold; font-size: 16px;">
    Shipping Details
  </td>
</tr>
<tr style="border-bottom: 1px solid #DEDEE6;">
  <td style="width: 300px; color: grey; text-align: left;">
    Contact Customer Name:
  </td>
  <td style="font-weight: bold; color: #333333; text-align: left;">
    {contact_customer_name}
  </td>
</tr>
<tr style="border-bottom: 1px solid #DEDEE6;">
  <td style="width: 300px; color: grey; text-align: left;">
    Customer Shipping Address:
  </td>
  <td style="font-weight: bold; color: #333333; text-align: left;">
    {address1}<br>{address2}<br>{city}<br>{state}<br>{postal_code}<br>
    {country}
  </td>
</tr>
</table>
    """
    return html


def generate_tracking_details(tracking_url, tracking_ref):
    tracking_url = tracking_url or ""
    tracking_ref = tracking_ref or ""
    html = f"""
    <table width="100%" cellpadding="6" cellspacing="0"
style="border-collapse: collapse; background-color: #ffffff;
margin-bottom: 16px;">
<tr>
  <td colspan="2" style="font-weight: bold; font-size: 16px;">
    Tracking Details
  </td>
</tr>
<tr style="border-bottom: 1px solid #DEDEE6;">
  <td style="width: 300px; color: grey; text-align: left;">
   Tracking URL:
  </td>
  <td style="font-weight: bold; color: #333333; text-align: left;">
    {tracking_url}
  </td>
</tr>
<tr style="border-bottom: 1px solid #DEDEE6;">
  <td style="width: 300px; color: grey; text-align: left;">
    Tracking Reference
  </td>
  <td style="font-weight: bold; color: #333333; text-align: left;">
    {tracking_ref}
  </td>
</tr>
</table>
    """
    return html


def generate_additional_details(additional_inf):
    additional_inf = additional_inf or "N/A"
    html = f"""
        <table width="100%" cellpadding="6" cellspacing="0"
    style="border-collapse: collapse; background-color: #ffffff;
    margin-bottom: 16px;">
    <tr>
    <td colspan="2" style="font-weight: bold; font-size: 16px;">
        Additional Details
    </td>
    </tr>
    <tr style="border-bottom: 1px solid #DEDEE6;">
      <td style="width: 300px; color: grey; text-align: left;">
       Any other information:
      </td>
      <td style="font-weight: bold; color: #333333; text-align: left;">
        {additional_inf}
      </td>
    </tr>
    </table>
        """
    return html


def generate_reject_comments(comment):
    comment = comment or ""
    if comment:
        html = f"""
        <table width="100%" cellpadding="6" cellspacing="0"
        style="border-collapse: collapse; background-color: #ffffff;
        margin-bottom: 16px;">
        <tr>
        <td colspan="2" style="font-weight: bold; font-size: 16px;">
            Reason
        </td>
        </tr>
        <tr style="border-bottom: 1px solid #DEDEE6;">
          <td style="width: 300px; color: grey; text-align: left;">
          Comment:
          </td>
          <td style="font-weight: bold; color: #333333; text-align: left;">
            {comment}
          </td>
        </tr>
        </table>
            """
        return html
    else:
        return "<span>"


class OrderEmailBuilder:
    STATUS_AND_RECIPIENT_TYPE_HEADER_TEMPLATE = {
        (OrderStatus.PENDING.value, RecipientType.MANAGER.value): (
            "<p>Dear BT Manager,<br><br> "
            "We are pleased to inform you that a "
            "new order "
            "for SIM(s) has been processed received. "
            "Please find the details of the order below:</p>"
        ),
        (OrderStatus.PENDING.value, RecipientType.USER.value): (
            "<p>Dear User,<br><br>"
            "Thank you for your recent order "
            "for SIM(s). we are pleased to confirm that "
            "your order has been successfully processed."
            " Below are the details for your reference</p>"
        ),
        (OrderStatus.APPROVED.value, RecipientType.MANAGER.value): (
            "<p>Dear BT Manager,<br><br>"
            "We must inform you "
            "that the order "
            "has been successfully processed and approved.</p>"
        ),
        (OrderStatus.APPROVED.value, RecipientType.USER.value): (
            "<p>Dear User,<br><br>Thank you for your recent"
            " SIM(s) order. we are pleased to confirm that "
            "your order "
            " has been successfully processed and approved.</p>"
        ),
        (OrderStatus.CANCELLED.value, RecipientType.MANAGER.value): (
            "<p>Dear BT Manager,<br><br>"
            "We must inform you "
            "that the order "
            " has been cancelled.</p>"
        ),
        (OrderStatus.CANCELLED.value, RecipientType.USER.value): (
            "<p>Dear User,<br><br>Thank you for your recent"
            " SIM(s) order. Unfortunately, we must "
            "inform you that your "
            "has been cancelled.</p>"
        ),
        (OrderStatus.SHIPPED.value, RecipientType.MANAGER.value): (
            "<p>Dear BT Manager,<br><br>Thank you for your recent"
            " SIM(s) order. We are pleased to confirm that your order "
            "has been successfully processed and shipped.</p>"
        ),
        (OrderStatus.SHIPPED.value, RecipientType.USER.value): (
            "<p>Dear User,<br><br>Thank you for your recent"
            " SIM(s) order. "
            "We are pleased to confirm that your order "
            " has been successfully processed and shipped.</p>"
        ),
        (OrderStatus.ONHOLD.value, RecipientType.USER.value): (
            "<p>Dear User,<br><br>Thank you for your recent"
            " SIM(s) order. "
            "We are pleased to confirm that your order "
            " has been hold wait for futher update.</p>"
        ),
        (OrderStatus.ONHOLD.value, RecipientType.MANAGER.value): (
            "<p>Dear BT Manager,<br><br>Thank you for your recent"
            " SIM(s) order. "
            "We are pleased to confirm that your order "
            " has been hold wait for futher update.</p>"
        ),
    }

    STATUS_AND_RECIPIENT_TYPE_FOOTER_TEMPLATE = {
        (OrderStatus.PENDING.value, RecipientType.USER.value): (
            """
                <table width="100%" cellpadding="6" cellspacing="0"
                  style="border-collapse: collapse; background-color: #ffffff;
                    margin-botton:0px padding-botton:0px">
                  <tr>
                    <td colspan="2" >
                     Your order is now being processed, and we will notify you with
                     shipping updates as soon as they become available. Should you have
                     any questions or require further assistance regarding your order,
                     please do not hesitate to reach out to us. </td>
                  </tr>
                  <tr>
                    </tr>
                  <tr>
                    <td colspan="2" >
                        We appreciate your trust in our services and look forward to
                        supporting your connectivity needs.
                    </td>
                  </tr>
                </table>
                """
        ),
        (OrderStatus.APPROVED.value, RecipientType.USER.value): (
            """
                <table width="100%" cellpadding="6" cellspacing="0"
                  style="border-collapse: collapse; background-color: #ffffff;
                    margin-botton:0px padding-botton:0px">
                  <tr>
                    <td colspan="2" >
                     Your order is now being processed, and we will notify you with
                     shipping updates as soon as they become available. If you have
                     any questions or require further assistance regarding your order,
                     please do not hesitate to contact us.
                    </td>
                  </tr>
                  <tr>
                    </tr>
                  <tr>
                    <td colspan="2" >
                        We appreciate your business and look forward to supporting your
                        connectivity needs.
                    </td>
                  </tr>
                </table>
                """
        ),
        (OrderStatus.CANCELLED.value, RecipientType.USER.value): (
            """
                <table width="100%" cellpadding="6" cellspacing="0"
                  style="border-collapse: collapse; background-color: #ffffff;
                    margin-botton:0px padding-botton:0px">
                  <tr>
                    If you have any questions or require further assistance regarding
                    your order, please do not hesitate to contact us.
                    </td>
                  </tr>
                  <tr>
                    </tr>
                </table>
                """
        ),
        (OrderStatus.SHIPPED.value, RecipientType.USER.value): (
            """
                <table width="100%" cellpadding="6" cellspacing="0"
                  style="border-collapse: collapse; background-color: #ffffff;
                    margin-botton:0px padding-botton:0px">
                  <tr>
                    <td colspan="2" >
                     Your order has been shipped. You can track your shipment using the
                     tacking data in Tracking Details. If you have any questions or
                     require further assistance regarding your order, please do not
                      hesitate to contact us.
                    </td>
                  </tr>
                  <tr>
                    </tr>
                  <tr>
                    <td colspan="2">
                       We appreciate your business and look forward to supporting your
                       connectivity needs.
                    </td>
                  </tr>
                </table>
                """
        ),
        (OrderStatus.ONHOLD.value, RecipientType.USER.value): (
            """
                <table width="100%" cellpadding="6" cellspacing="0"
                  style="border-collapse: collapse; background-color: #ffffff;
                    margin-botton:0px padding-botton:0px">
                  <tr>
                    <td colspan="2" >
                     Your order has been on hold. If you have any questions or
                     require further assistance regarding your order, please do not
                      hesitate to contact us.
                    </td>
                  </tr>
                  <tr>
                    </tr>
                  <tr>
                    <td colspan="2">
                       We appreciate your business and look forward to supporting your
                       connectivity needs.
                    </td>
                  </tr>
                </table>
                """
        ),
    }

    def __init__(self, order_data: OrderData, recipient_type: RecipientType):
        self.data = order_data
        self.recipient_type = recipient_type

    def convert_date(self, original_date: str):
        dt = datetime.strptime(original_date, "%Y-%m-%d %H:%M")
        # Format to desired string
        formatted_date = dt.strftime("%d-%m-%Y %H:%M")
        return formatted_date

    def build_email_body(self) -> str:
        return (
            self._generate_header()
            + self._generate_order_details()
            + self._generate_customer_details()
            + self._generate_shipping_details()
            + self._generate_tracking_details()
            + self._generate_additional_details()
            + self._generate_reject_comments()
            + self._generate_footer_details()
        )

    def _generate_header(self) -> str:
        status = self.data.order_status
        order_id = self.data.order_id
        # web_link = self.data.web_link
        d = {"order_id": order_id}

        template = self.__class__.STATUS_AND_RECIPIENT_TYPE_HEADER_TEMPLATE.get(
            (status, self.recipient_type)
        )
        if template is None:
            raise ValueError(
                f"Unknown order status or "
                f"recipient type: {(status, self.recipient_type)}"
            )
        return template.format(**d)

    def _generate_order_details(self) -> str:
        return generate_order_details(
            order_id=self.data.order_id,
            order_status=self.data.order_status,
            order_date_and_time=self.convert_date(self.data.order_date),
            sim_type_wise_quantity=self.data.sim_type_wise_qty,
            web_link=self.data.web_link,
        )

    def _generate_customer_details(self) -> str:
        return generate_customer_details(
            customer_ref=self.data.customer_account_ref,
            person_name=self.data.person_name,
            person_email=self.data.person_email,
            person_mob_no=self.data.phone_number,
        )

    def _generate_shipping_details(self) -> str:
        return generate_shipping_details(
            contact_customer_name=self.data.customer_contact_name,
            address1=self.data.shipping_address_address1,
            address2=self.data.shipping_address_address2,
            city=self.data.city,
            state=self.data.state,
            postal_code=self.data.postal_code,
            country=self.data.country,
        )

    def _generate_tracking_details(self) -> str:
        if self.data.order_status == OrderStatus.SHIPPED:
            return generate_tracking_details(
                tracking_url=self.data.tracking_url,
                tracking_ref=self.data.tracking_ref,
            )
        else:
            return ""

    def _generate_additional_details(self) -> str:
        return generate_additional_details(self.data.additional_info)

    def _generate_reject_comments(self) -> str:
        return generate_reject_comments(self.data.comments)

    def _generate_footer_details(self) -> str:
        footer_template = self.__class__.STATUS_AND_RECIPIENT_TYPE_FOOTER_TEMPLATE.get(
            (self.data.order_status, self.recipient_type)
        )
        return footer_template or ""

    def get_subject(self) -> str:
        subject_prefix = "BT UK Multi-Network IoT SIM:"
        if self.data.order_status == OrderStatus.PENDING.value:
            if self.recipient_type == "manager":
                return f"{subject_prefix} new order of SIM(s) received"
            else:
                return f"{subject_prefix} new order of SIM(s) has been processed"
        else:
            return f"{subject_prefix} your SIM(s)" f" order status updated"
