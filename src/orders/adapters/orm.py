from datetime import datetime
from uuid import uuid4

from sqlalchemy import (
    TIMESTAMP,
    Column,
    ForeignKey,
    Integer,
    String,
    Table,
    Text,
    UniqueConstraint,
)
from sqlalchemy.dialects import postgresql
from sqlalchemy.orm import relationship

from common.db import mapper_registry
from orders.domain import model

orders = Table(
    "orders",
    mapper_registry.metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    Column("uuid", postgresql.UUID(as_uuid=True), default=uuid4, nullable=False),
    <PERSON>umn("order_id", String(length=20), unique=True, nullable=False),
    Column("order_date", TIMESTAMP, default=datetime.utcnow),
    Column("order_by", String(length=255)),
    Column("status", String(length=50)),
    UniqueConstraint("uuid"),
    schema="orders",
)

customers = Table(
    "customers",
    mapper_registry.metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    Column("uuid", postgresql.UUID(as_uuid=True), default=uuid4, nullable=False),
    Column("customer_id", postgresql.UUID(as_uuid=True), nullable=False),
    Column("customer_account_name", String(length=255)),
    Column("customer_account_id", Integer),
    Column("person_placing_order", String(length=255)),
    Column("customer_reference", String(length=255)),
    Column("customer_account_logo_url", Text),
    Column("customer_email", String(length=255)),
    Column("customer_contact_no", String(length=50)),
    Column("order_id", postgresql.UUID(as_uuid=True), ForeignKey("orders.orders.uuid")),
    UniqueConstraint("uuid"),
    schema="orders",
)

shipping_details = Table(
    "shipping_details",
    mapper_registry.metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    Column("uuid", postgresql.UUID(as_uuid=True), default=uuid4, nullable=False),
    Column("contact_name", String(length=255)),
    Column("address_line1", String(length=255)),
    Column("address_line2", String(length=255)),
    Column("city", String(length=100)),
    Column("state_or_region", String(length=100)),
    Column("postal_code", String(length=20)),
    Column("country", String(length=100)),
    Column("other_information", Text),
    Column("order_id", postgresql.UUID(as_uuid=True), ForeignKey("orders.orders.uuid")),
    UniqueConstraint("uuid"),
    schema="orders",
)

items = Table(
    "items",
    mapper_registry.metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    Column("uuid", postgresql.UUID(as_uuid=True), default=uuid4, nullable=False),
    Column("order_id", postgresql.UUID(as_uuid=True), ForeignKey("orders.orders.uuid")),
    Column("sim_type", String(length=100)),
    Column("quantity", Integer),
    UniqueConstraint("uuid"),
    schema="orders",
)

tracking = Table(
    "tracking",
    mapper_registry.metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    Column("uuid", postgresql.UUID(as_uuid=True), default=uuid4, nullable=False),
    Column("reference_id", String(length=100)),
    Column("reference_url", String(length=100)),
    Column("order_id", postgresql.UUID(as_uuid=True), ForeignKey("orders.orders.uuid")),
    UniqueConstraint("uuid"),
    schema="orders",
)

reject_reason = Table(
    "reject_reason",
    mapper_registry.metadata,
    Column("id", Integer, primary_key=True, autoincrement=True),
    Column("uuid", postgresql.UUID(as_uuid=True), default=uuid4, nullable=False),
    Column("comment", Text(), nullable=True),
    Column("order_id", postgresql.UUID(as_uuid=True), ForeignKey("orders.orders.uuid")),
    UniqueConstraint("uuid"),
    schema="orders",
)


def start_mappers() -> None:
    mapper_registry.map_imperatively(
        model.Order,
        orders,
        properties=dict(
            customers=relationship(
                model.OrderCustomer,
                back_populates="orders",
                cascade="all, delete-orphan",
            ),
            shipping_details=relationship(
                model.OrderShippingDetails,
                back_populates="orders",
                cascade="all, delete-orphan",
            ),
            items=relationship(
                model.OrderItem,
                back_populates="orders",
                cascade="all, delete-orphan",
            ),
            tracking=relationship(
                model.OrderTracking,
                back_populates="orders",
                cascade="all, delete-orphan",
            ),
            reject_reason=relationship(
                model.RejectReason,
                back_populates="orders",
                cascade="all, delete-orphan",
            ),
        ),
    )
    mapper_registry.map_imperatively(
        model.OrderCustomer,
        customers,
        properties=dict(
            orders=relationship(
                model.Order,
                back_populates="customers",
            ),
        ),
    )
    mapper_registry.map_imperatively(
        model.OrderShippingDetails,
        shipping_details,
        properties=dict(
            orders=relationship(
                model.Order,
                back_populates="shipping_details",
            ),
        ),
    )
    mapper_registry.map_imperatively(
        model.OrderItem,
        items,
        properties=dict(
            orders=relationship(
                model.Order,
                back_populates="items",
            ),
        ),
    )
    mapper_registry.map_imperatively(
        model.OrderTracking,
        tracking,
        properties=dict(
            orders=relationship(
                model.Order,
                back_populates="tracking",
            ),
        ),
    )
    mapper_registry.map_imperatively(
        model.RejectReason,
        reject_reason,
        properties=dict(
            orders=relationship(
                model.Order,
                back_populates="reject_reason",
            ),
        ),
    )


def stop_mappers() -> None:
    mapper_registry.dispose()
