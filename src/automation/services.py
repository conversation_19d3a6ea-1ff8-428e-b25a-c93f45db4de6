from abc import ABC, abstractmethod
from enum import Enum
from typing import Iterator, Type
from uuid import UUID

from fastapi import HTT<PERSON>Exception
from psycopg2.errors import ForeignKeyViolation, UniqueViolation  # type: ignore
from sqlalchemy.exc import IntegrityError

from api.automation.schemas import RuleDefinition
from app.config import logger
from authorization.domain.ports import AbstractAuthorizationAPI
from automation.adapters.repository import AbstractAutomationRepository
from automation.domain import model
from automation.exceptions import (
    CreateRuleError,
    NoRuleCategory,
    NoRuleDefinition,
    NoRuleTypeFound,
    RuleAccessError,
    RuleDetailsNotFound,
    RuleError,
    RuleRelyConflictError,
    RuleRelyError,
    UpdateLockRuleError,
)
from common.constants import (
    CTDU,
    DUESL,
    MBDUT,
    MBSUT,
    MBVMOUT,
    MBVMTUT,
    MBVUT,
    MPU,
    SUESL,
    VUESL,
)
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from common.types import (
    DataPercentage,
    DataUnit,
    Service,
    SMSPercentageUnit,
    SMSUnit,
    Unit,
    VoiceMOMTPercentage,
    VoiceMOPercentage,
    VoiceMTPercentage,
    VoiceUnit,
)
from rate_plans.exceptions import RatePlanError
from rate_plans.services import RatePlanService

PERCENTAGE_UNITS = [
    Unit.PERCENTAGE,
    Unit.VOICE_MO_PERCENTAGE,
    Unit.VOICE_MT_PERCENTAGE,
    Unit.VOICE_MOMT_PERCENTAGE,
    Unit.SMS_MO_PERCENTAGE,
]


DEFINITION_CODE_TO_UNIT_ENUM: dict[str, Type[Enum]] = {
    DUESL: DataUnit,
    VUESL: VoiceUnit,
    SUESL: SMSUnit,
    MBDUT: DataPercentage,
    MBVUT: VoiceMOMTPercentage,
    MBVMOUT: VoiceMOPercentage,
    MBVMTUT: VoiceMTPercentage,
    MBSUT: SMSPercentageUnit,
}


class AbstractAutomationService(ABC):
    @abstractmethod
    def create_rule(
        self,
        rules_detail: list[model.Rules],
        scope: str,
        authorization: AbstractAuthorizationAPI,
    ) -> model.CreateRuleDetails:
        ...

    @abstractmethod
    def update_rule(
        self,
        rules_uuid: UUID,
        rules_detail: model.Rules,
        scope: str,
        authorization: AbstractAuthorizationAPI,
    ) -> model.RuleDetails:
        ...

    @abstractmethod
    def get_rule_by_uuid(self, rule_uuid: UUID) -> model.RuleDetailsResponse:
        ...

    @abstractmethod
    def get_rule(
        self,
        account_id: int | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> tuple[model.RuleDetailResponseList, int]:
        ...

    @abstractmethod
    def get_actions(
        self,
        action_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.Actions]:
        ...

    @abstractmethod
    def get_actions_count(
        self,
        action_id: int | None = None,
        searching: Searching | None = None,
    ) -> int:
        ...

    @abstractmethod
    def get_notification(
        self,
        notification_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.Notifications]:
        ...

    @abstractmethod
    def get_notification_count(
        self,
        notification_id: int | None = None,
        searching: Searching | None = None,
    ) -> int:
        ...

    @abstractmethod
    def get_rule_type(
        self,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.RuleType], int]:
        ...

    @abstractmethod
    def get_rule_category(
        self,
        type_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.RuleCategoryDetails], int]:
        ...

    @abstractmethod
    def get_rule_definition(
        self,
        rule_category_id: int,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.RuleDefinitionDetails], int]:
        ...

    @abstractmethod
    def update_rule_status_by_rule_uuid(
        self, rule_uuid: UUID, rule_status: bool
    ) -> model.RuleStatus:
        ...

    @abstractmethod
    def update_lock_status_by_rule_uuid(
        self, rule_uuid: UUID, lock_status: bool
    ) -> model.LockStatus:
        ...

    @abstractmethod
    def delete_rule(self, rules_uuid: UUID) -> None:
        ...

    @abstractmethod
    def validate_rate_plan(
        self,
        rate_service: RatePlanService,
        rules_detail: list[model.Rules],
        validate_unit: bool = False,
    ) -> bool:
        ...

    @abstractmethod
    def check_permission(
        self,
        scope: str,
        authorization: AbstractAuthorizationAPI,
    ) -> bool:
        ...

    @abstractmethod
    def get_rule_rely(self, rule_definition_id: int) -> model.RuleResponseRely:
        ...

    @abstractmethod
    def create_rule_rely(self, rule_rely: model.RuleDetailsRely) -> bool:
        ...

    @abstractmethod
    def update_rule_rely(
        self,
        rely: model.RuleDetailsRely,
        rule_rely_id: int,
    ) -> bool:
        ...

    @abstractmethod
    def get_service_by_definition(self, rule_definition_code: str) -> list[str]:
        ...


class AutomationService(AbstractAutomationService):
    def __init__(
        self,
        automation_repository: AbstractAutomationRepository,
    ):
        self.automation_repository = automation_repository

    def _create_rules_notification(
        self, rules_notification_detail: list[model.RulesNotification]
    ) -> bool:
        for rules_notification in rules_notification_detail:
            create_rules_notification_response = (
                self.automation_repository.create_rules_notification(
                    rules_notification_detail=rules_notification,
                )
            )
        return create_rules_notification_response

    def _create_rules_action(
        self, rules_action: list[model.RulesAction], rules_uuid: UUID
    ) -> model.RuleDetails:
        for action in rules_action:
            rule_actions = []
            if action.target:
                rule_actions.append(
                    model.RulesAction(
                        action=action.action,
                        action_value=action.action_value,
                        rules_uuid=rules_uuid,
                    )
                )
                self.automation_repository.create_rules_action_mapping(
                    model.RulesActionMapping(
                        source=action.source,
                        target=action.target,
                        rules_uuid=rules_uuid,
                    ),
                    rules_uuid=rules_uuid,
                )
            else:
                rule_actions.append(
                    model.RulesAction(
                        action=action.action,
                        action_value=action.action_value,
                        rules_uuid=rules_uuid,
                    )
                )
        create_rules_action_response = self.automation_repository.create_rules_action(
            rules_action_detail=rules_action,
            rules_uuid=rules_uuid,
        )
        return create_rules_action_response

    def create_rule(
        self,
        rules_detail: list[model.Rules],
        scope: str,
        authorization: AbstractAuthorizationAPI,
    ) -> model.CreateRuleDetails:
        try:
            lock_value = next((rule.lock for rule in rules_detail), False)
            if lock_value and not self.check_permission(
                scope=scope, authorization=authorization
            ):
                raise UpdateLockRuleError(
                    "You do not have permission to create a locked rule."
                )
            create_rules_data = []

            self.automation_repository.create_rule(
                rules_detail=rules_detail,
            )
            for rule_detail in rules_detail:
                if rule_detail.notifications:
                    self._create_rules_notification(
                        rules_notification_detail=rule_detail.notifications,
                    )
                if rule_detail.actions:
                    self._create_rules_action(
                        rules_action=rule_detail.actions,
                        rules_uuid=rule_detail.uuid,
                    )
                create_rules_data.append(model.RuleDetails(uuid=rule_detail.uuid))

            return model.CreateRuleDetails(rules_uuid=create_rules_data)
        except IntegrityError as e:
            raise CreateRuleError(f"{str(e)}")

    def update_rule(
        self,
        rules_uuid: UUID,
        rules_detail: model.Rules,
        scope: str,
        authorization: AbstractAuthorizationAPI,
    ) -> model.RuleDetails:
        try:
            rule = self.automation_repository.get_rule_by_uuid(rules_uuid)
            if rule.account_id != rules_detail.account_id:
                raise RuleAccessError("Trying to update another account rule.")

            if rules_detail.lock and not self.check_permission(
                scope=scope, authorization=authorization
            ):
                raise UpdateLockRuleError(
                    "You do not have permission to create a locked rule."
                )
            if rule.lock and not self.check_permission(
                scope=scope, authorization=authorization
            ):
                raise UpdateLockRuleError(
                    "This rule has been locked by BT Administrator. You can’t edit."
                )
            reset_rules_detail = (
                self.automation_repository.reset_rules_action_and_notification(
                    rules_uuid=rules_uuid
                )
            )

            if not reset_rules_detail:
                self.automation_repository.update_rule(rules_detail=rules_detail)

                if rules_detail.notifications:
                    self._create_rules_notification(
                        rules_notification_detail=rules_detail.notifications,
                    )
                if rules_detail.actions:
                    self._create_rules_action(
                        rules_action=rules_detail.actions,
                        rules_uuid=rules_uuid,
                    )
                return model.RuleDetails(uuid=rules_uuid)
            else:
                logger.error(
                    f"""An error occoured while
                    updating rule details.: {reset_rules_detail}"""
                )
                raise ValueError(
                    f"""An error occoured while
                    updating rule details.: {reset_rules_detail}"""
                )
        except IntegrityError as e:
            logger.error(f"Update rule error.: {e}")
            raise RuleError(f"Update rule error.: {e}")

    def get_rule_by_uuid(self, rule_uuid: UUID) -> model.RuleDetailsResponse:
        rule = self.automation_repository.get_rule_by_uuid(rule_uuid)
        actions = self.automation_repository.get_actions_by_rule_uuid(rule.uuid)
        notifications = self.automation_repository.get_notification_by_rule_uuid(
            rule.uuid
        )

        return model.RuleDetailsResponse(
            **rule.__dict__, action=actions, notification=notifications
        )

    def _merge_action_notification(
        self, rule: model.RuleDetail
    ) -> model.RulesDetailsResponse:
        actions = self.automation_repository.get_actions_by_rule_uuid(rule.uuid)
        notifications = self.automation_repository.get_notification_by_rule_uuid(
            rule.uuid
        )
        return model.RulesDetailsResponse(
            **rule.__dict__, action=actions, notification=notifications
        )

    def get_rule(
        self,
        account_id: int | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> tuple[model.RuleDetailResponseList, int]:
        rule_response = self.automation_repository.get_rule(
            account_id=account_id,
            ordering=ordering,
            searching=searching,
            pagination=pagination,
        )

        rule_response_data = list(
            map(lambda rule: self._merge_action_notification(rule=rule), rule_response)
        )
        rule_count = self.automation_repository.get_rule_count(
            account_id=account_id, searching=searching
        )
        if not rule_response_data:
            raise RuleDetailsNotFound("No rules found")

        return model.RuleDetailResponseList(result=rule_response_data), rule_count

    def get_rule_type(
        self,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.RuleType], int]:
        """Function for rule name"""
        total = self.automation_repository.rule_type_count(
            searching=searching,
        )
        if total == 0:
            raise NoRuleTypeFound()
        rule_type = self.automation_repository.rule_type(
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        return rule_type, total

    def get_rule_category(
        self,
        type_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.RuleCategoryDetails], int]:
        """Function for rule category"""
        total = self.automation_repository.rule_category_count(
            type_id=type_id,
            searching=searching,
        )
        if total == 0:
            raise NoRuleCategory()

        rule_category = self.automation_repository.rule_category(
            type_id=type_id,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        return rule_category, total

    def get_rule_definition(
        self,
        rule_category_id: int,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.RuleDefinitionDetails], int]:
        """Function for rule name"""
        total = self.automation_repository.rule_definition_count(
            rule_category_id=rule_category_id,
            searching=searching,
        )
        if total == 0:
            raise NoRuleDefinition(rule_category_id=rule_category_id)

        rule_definition = self.automation_repository.rule_definition(
            rule_category_id=rule_category_id,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        return rule_definition, total

    def get_actions(
        self,
        action_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.Actions]:
        return self.automation_repository.get_actions(
            action_id, pagination, ordering, searching
        )

    def get_actions_count(
        self, action_id: int | None = None, searching: Searching | None = None
    ) -> int:
        return self.automation_repository.get_actions_count(action_id, searching)

    def get_notification(
        self,
        notification_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.Notifications]:
        yield from self.automation_repository.get_notification_list(
            notification_id=notification_id,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )

    def get_notification_count(
        self,
        notification_id: int | None = None,
        searching: Searching | None = None,
    ) -> int:
        return self.automation_repository.get_notification_count(
            notification_id=notification_id, searching=searching
        )

    def update_rule_status_by_rule_uuid(
        self, rule_uuid: UUID, rule_status: bool
    ) -> model.RuleStatus:
        try:
            response = self.automation_repository.update_rule_status_by_rule_uuid(
                rule_uuid=rule_uuid, rule_status=rule_status
            )

            if response:
                raise ValueError(
                    f"""An error occoured while
                    doing patch update rule details. {response}"""
                )
            rule_details = self.automation_repository.get_rule_by_uuid(rule_uuid)
            return model.RuleStatus(uuid=rule_details.uuid, status=rule_details.status)
        except IntegrityError as e:
            raise RuleError(f"Create rule error.: {e}")

    def delete_rule(self, rules_uuid: UUID) -> None:
        try:
            self.automation_repository.get_rule_by_uuid(rules_uuid)

            self.automation_repository.reset_rules_action_and_notification(
                rules_uuid=rules_uuid
            )

            self.automation_repository.delete_rule(rules_uuid=rules_uuid)
            return None
        except IntegrityError as e:
            raise RuleError(f"Delete rule error.: {e}")

    def update_lock_status_by_rule_uuid(
        self, rule_uuid: UUID, lock_status: bool
    ) -> model.LockStatus:
        try:

            response = self.automation_repository.update_lock_status_by_rule_uuid(
                rule_uuid=rule_uuid, lock_status=lock_status
            )

            if response:
                raise ValueError(
                    f"""An error occoured while
                    doing patch update rule details. {response}"""
                )
            rule_details = self.automation_repository.get_rule_by_uuid(rule_uuid)
            return model.LockStatus(uuid=rule_details.uuid, lock=rule_details.lock)
        except IntegrityError as e:
            raise RuleError(f"Create rule error.: {e}")

    def get_service_by_definition(self, rule_definition_code: str) -> list[str]:
        rule_to_service_map = {
            MBDUT: [Service.DATA],
            MBVUT: [Service.VOICE_MO, Service.VOICE_MT],
            MBVMOUT: [Service.VOICE_MO],
            MBVMTUT: [Service.VOICE_MT],
            MBSUT: [Service.SMS_MO],
        }
        services = rule_to_service_map.get(rule_definition_code, [])
        return [service.value for service in services]

    def validate_rule_units(
        self, rule: model.Rules, definitions: list[RuleDefinition]
    ) -> bool:
        try:
            definition_code = next(
                code.definition_code
                for code in definitions
                if code.id == rule.rule_definition_id
            )
        except StopIteration:
            raise RatePlanError(
                f"Rule unit must be valid respect to {rule.rule_definition_id}."
            )
        enum_cls = DEFINITION_CODE_TO_UNIT_ENUM.get(definition_code)
        if not enum_cls or rule.unit.value not in [e.value for e in enum_cls]:
            raise RatePlanError(
                f"Rule unit must be valid respect to {rule.rule_definition_id}."
            )
        return True

    def validate_rate_plan(
        self,
        rate_service: RatePlanService,
        rules_detail: list[model.Rules],
        validate_unit: bool = False,
    ) -> bool:
        for rule in rules_detail:
            rule_category = self.automation_repository.rule_definition(
                rule_category_id=rule.rule_category_id
            )
            rule_definition = rule.rule_definition_id

            results = list(map(RuleDefinition.from_model, rule_category))
            if not results:
                raise NoRuleDefinition(rule_category_id=rule_definition)

            if validate_unit:
                self.validate_rule_units(rule, results)

            rule_code = next((code.category_code for code in results))

            rule.actions = rule.actions or []
            for action in rule.actions:
                if action.action != model.RuleAction.CHANGE_RATE_PLAN:
                    continue
                source, target = action.source, action.target
                account_id = rule.account_id
                if rule_code == MPU:
                    rule_definition_code = next(
                        (
                            code.definition_code
                            for code in results
                            if code.id == rule_definition
                        )
                    )
                    service = self.get_service_by_definition(rule_definition_code)
                    if source is not None and target is not None:
                        rate_service.validate_fixed_flexi_plan(
                            rate_plan_id=[source], service=service
                        )
                        if rule.unit not in PERCENTAGE_UNITS:
                            raise RatePlanError(
                                f"Rule unit must be % for {rule_definition_code}."
                            )
                    else:
                        raise RatePlanError(
                            "Rate plan from field is required for "
                            f"{rule_definition_code}."
                        )

                if rule_code == CTDU:
                    if source is None and target is not None and account_id is not None:
                        rate_service.get_account_rate_plan(account_id, [target])
                        continue

                if not (source and target and account_id):
                    raise AssertionError(
                        "Source, target, and account_id must not be None"
                    )
                rate_service.get_account_rate_plan(account_id, [source, target])
        return True

    def check_permission(
        self, scope: str, authorization: AbstractAuthorizationAPI
    ) -> bool:
        return authorization.is_authorized(scope) == "PERMIT"

    def get_rule_rely(self, rule_definition_id: int) -> model.RuleResponseRely:
        rule_rely = self.automation_repository.get_rule_rely(
            rule_definition_id=rule_definition_id
        )
        if rule_rely is None:
            raise RuleRelyError(
                f"Rely with rule_definition_id={rule_definition_id} not exists."
            )
        return rule_rely

    def create_rule_rely(self, rule_rely: model.RuleDetailsRely) -> bool:
        try:
            return self.automation_repository.create_rule_rely(rule_rely)

        except IntegrityError as e:
            if isinstance(e.orig, UniqueViolation):
                raise RuleRelyConflictError(
                    "Rely with rule_definition_id="
                    f"{rule_rely.rule_definition_id} exists."
                )

            elif isinstance(e.orig, ForeignKeyViolation):
                raise RuleRelyError(
                    f"rule_definition_id={rule_rely.rule_definition_id} does not exist."
                )
            raise HTTPException(
                status_code=400, detail="An error occurred while creating rule rely."
            )

    def update_rule_rely(self, rely: model.RuleDetailsRely, rule_rely_id: int) -> bool:
        try:
            existing_rule_rely = self.automation_repository.get_rule_rely_by_id(
                rule_rely_id
            )
            if not existing_rule_rely:
                raise RuleRelyError(f"Rule rely with id={rule_rely_id} does not exist.")
            self.automation_repository.update_rule_rely(rely, rule_rely_id)
            return True

        except IntegrityError as e:
            if isinstance(e.orig, ForeignKeyViolation):
                raise RuleRelyError(
                    f"rule_definition_id={rely.rule_definition_id} does not exist."
                )

            raise HTTPException(
                status_code=400, detail="An error occurred while updating rule rely."
            )
