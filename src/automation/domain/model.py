from dataclasses import dataclass
from enum import Enum
from uuid import UUID

from pydantic import AnyHttpUrl, EmailStr

from common.types import RULE_NAME, Unit


class RuleAction(str, Enum):
    REACTIVATE_SIM = "Deactivate SIM"
    CHANGE_RATE_PLAN = "Change Rate Plan"


class ActionValue(str, Enum):
    IMMEDIATELY = "Immediately"
    AFTER_24_HOURS = "After 24 hours"
    AFTER_36_HOURS = "After 36 hours"
    NEXT_BILLING_CYCLE = "Next billing cycle"
    I_DONT_WANT_TO_REACTIVATE = "I don't want to reactivate"


class ChangeRatePlanValue(str, Enum):
    RETURN_TO_THE_PREVIOUS_RATE_PLAN = "Return to the previous rate plan"
    KEEP_THE_NEW_RATE_PLAN = "Keep the new rate plan"


@dataclass
class RuleType:
    id: int
    rule_type: str


@dataclass
class RuleCategory:
    id: int
    rule_type_id: int
    category: str


@dataclass
class RuleDefinition:
    id: int
    rule_category_id: int
    definition: str


@dataclass
class Actions:
    id: int
    action: str


@dataclass
class Notifications:
    id: int
    notification: str


@dataclass
class RulesAction:
    action: RuleAction
    action_value: str
    actions_id: int | None = None
    rules_uuid: UUID | None = None
    source: int | None = None
    target: int | None = None


@dataclass
class RulesActionMapping:
    source: int | None
    target: int
    rules_uuid: UUID | None = None


@dataclass
class NotificationValue:
    notification_value: str
    rules_notification_id: int | None = None


@dataclass
class RulesNotification:
    notifications_id: int
    notification: bool
    notification_value: list[NotificationValue]
    rules_uuid: UUID | None = None
    id: int | None = None


@dataclass
class Rules:
    uuid: UUID
    account_id: int | None
    rule_type_id: int
    rule_category_id: int
    rule_definition_id: int
    data_volume: int
    status: bool
    lock: bool
    unit: Unit
    created_by: str | None
    ip_address: str | None
    rule_name: RULE_NAME
    notifications: list[RulesNotification] | None
    actions: list[RulesAction] | None
    id: int | None = None


@dataclass
class RuleDetails:
    uuid: UUID


@dataclass
class CreateRuleDetails:
    rules_uuid: list[RuleDetails]


@dataclass
class Action:
    name: str
    action: str
    action_value: str


@dataclass
class RateplanAction:
    name: str
    action: str
    target: int
    action_value: str
    source: int | None


@dataclass
class Notification:
    name: str
    notification: bool
    notification_value: list[NotificationValue] | None = None


@dataclass
class NotificationChannels:
    email: list[EmailStr] | None = None
    sms: list[str] | None = None
    push: list[str] | None = None


@dataclass
class RulesTypeDetails:
    id: int
    rule_type: str


@dataclass
class RulesCategoryDetails:
    id: int
    rule_category: str


@dataclass
class RulesDefinitionDetails:
    id: int
    rule_definition: str


@dataclass
class RuleDetail:
    id: int
    uuid: UUID
    account_id: int
    rule_name: RULE_NAME
    data_volume: int
    unit: Unit
    status: bool
    lock: bool


@dataclass
class RulesData(RuleDetail):
    rule_type: str
    rule_category: str
    rule_definition: str


@dataclass
class RuleData(RuleDetail):
    rule_type: RulesTypeDetails
    rule_category: RulesCategoryDetails
    rule_definition: RulesDefinitionDetails


@dataclass
class RuleDetailsResponse(RuleData):
    action: list[Action | RateplanAction]
    notification: list[Notification]
    account_name: str | None = None
    logo_key: str | None = None
    logo_url: AnyHttpUrl | None = None


@dataclass
class RulesDetailsResponse(RulesData):
    action: list[Action | RateplanAction]
    notification: list[Notification]
    account_name: str | None = None
    logo_key: str | None = None
    logo_url: AnyHttpUrl | None = None


@dataclass
class RuleDetailList:
    result: list[RulesData]


@dataclass
class RuleDetailResponseList:
    result: list[RulesDetailsResponse]


@dataclass
class RuleCategoryDetails:
    id: int
    rule_type_id: int
    rule_type: str
    category: str
    rule_category_code: str


@dataclass
class RuleDefinitionDetails:
    id: int
    definition: str
    category: str
    rule_definition_code: str
    rule_category_code: str


@dataclass
class NotificationValueDetails(NotificationValue):
    id: int | None = None


@dataclass
class RuleStatus:
    uuid: UUID
    status: bool


@dataclass
class LockStatus:
    uuid: UUID
    lock: bool


@dataclass
class RuleDetailsRely:
    rule_definition_id: int
    data_volume: bool
    data_unit: bool
    sms_unit: bool
    voice_unit: bool
    threshold: bool
    data_percentage: bool
    view_deactivate_sim: bool
    required_deactivate_sim: bool
    view_rate_plan_change: bool
    required_rate_plan_change: bool
    add_any_rate_plan: bool
    is_monthly_pool: bool
    view_email: bool
    view_sms: bool
    view_push: bool
    voice_momt_percentage: bool
    voice_mo_percentage: bool
    voice_mt_percentage: bool
    sms_percentage: bool
    data: bool
    voice_mo: bool
    voice_mt: bool
    sms: bool


@dataclass
class RulesRely:
    data_volume: bool
    data_unit: bool
    sms_unit: bool
    voice_unit: bool
    threshold: bool
    data_percentage: bool
    voice_mo_percentage: bool
    voice_mt_percentage: bool
    voice_momt_percentage: bool
    sms_percentage: bool


@dataclass
class ActionRely:
    view_deactivate_sim: bool
    required_deactivate_sim: bool
    view_rate_plan_change: bool
    required_rate_plan_change: bool
    add_any_rate_plan: bool
    is_monthly_pool: bool
    data: bool
    voice_mo: bool
    voice_mt: bool
    sms: bool


@dataclass
class NotificationRely:
    view_email: bool
    view_sms: bool
    view_push: bool


@dataclass
class RuleResponseRely:
    id: int
    rules: RulesRely
    action: ActionRely
    notification: NotificationRely
