from datetime import datetime
from uuid import uuid4

from sqlalchemy import (
    BigInteger,
    Boolean,
    Column,
    DateTime,
    Enum,
    ForeignKey,
    Identity,
    Integer,
    String,
    Table,
    UniqueConstraint,
)
from sqlalchemy.dialects import postgresql
from sqlalchemy.orm import relationship

from automation.domain import model
from common.db import mapper_registry
from common.types import RULE_NAME, Unit

rule_type = Table(
    "rule_type",
    mapper_registry.metadata,
    Column(
        "id",
        Integer,
        Identity(),
        primary_key=True,
        autoincrement=True,
        nullable=False,
    ),
    Column("rule_type", String(), nullable=False),
    Column("rule_type_code", String(), nullable=False),
    UniqueConstraint("rule_type"),
)

rule_category = Table(
    "rule_category",
    mapper_registry.metadata,
    Column(
        "id",
        Integer,
        Identity(),
        primary_key=True,
        autoincrement=True,
        nullable=False,
    ),
    Column("rule_type_id", Integer, ForeignKey("rule_type.id"), nullable=False),
    Column("category", String(), nullable=False),
    Column("rule_category_code", String(), nullable=False),
    UniqueConstraint("category"),
)

rule_definition = Table(
    "rule_definition",
    mapper_registry.metadata,
    Column(
        "id",
        Integer,
        Identity(),
        primary_key=True,
        autoincrement=True,
        nullable=False,
    ),
    Column("rule_category_id", Integer, ForeignKey("rule_category.id"), nullable=False),
    Column("definition", String(), nullable=False),
    Column("rule_definition_code", String(), nullable=False),
)

actions = Table(
    "actions",
    mapper_registry.metadata,
    Column(
        "id",
        Integer,
        Identity(),
        primary_key=True,
        autoincrement=True,
        nullable=False,
    ),
    Column("action", String, nullable=False),
    UniqueConstraint("action"),
)

notifications = Table(
    "notifications",
    mapper_registry.metadata,
    Column(
        "id",
        Integer,
        Identity(),
        primary_key=True,
        autoincrement=True,
        nullable=False,
    ),
    Column("notification", String, nullable=False),
    UniqueConstraint("notification"),
)

rules = Table(
    "rules",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("uuid", postgresql.UUID(as_uuid=True), default=uuid4, nullable=False),
    Column("account_id", Integer, nullable=False),
    Column("rule_type_id", Integer, ForeignKey("rule_type.id"), nullable=False),
    Column("rule_category_id", Integer, ForeignKey("rule_category.id"), nullable=False),
    Column(
        "rule_definition_id", Integer, ForeignKey("rule_definition.id"), nullable=False
    ),
    Column("rule_name", String(length=RULE_NAME.max_length), nullable=False),
    Column("data_volume", BigInteger, nullable=False),
    Column("status", Boolean, nullable=False),
    Column("lock", Boolean, nullable=False),
    Column("created_by", String, nullable=False),
    Column("created_at", DateTime, default=datetime.utcnow, nullable=False),
    Column("ip_address", String, nullable=False),
    Column(
        "unit",
        Enum(Unit, name="unit"),
        nullable=False,
    ),
    UniqueConstraint(
        "account_id", "data_volume", "rule_type_id", "rule_category_id", "unit"
    ),
    UniqueConstraint("uuid"),
)

rules_action = Table(
    "rules_action",
    mapper_registry.metadata,
    Column(
        "id",
        Integer,
        Identity(),
        primary_key=True,
        autoincrement=True,
        nullable=False,
    ),
    Column(
        "rules_uuid",
        postgresql.UUID(as_uuid=True),
        ForeignKey("rules.uuid"),
        nullable=False,
    ),
    Column("actions_id", Integer, ForeignKey("actions.id"), nullable=False),
    Column(
        "action",
        Enum(model.RuleAction, name="rule_action"),
        nullable=False,
    ),
    Column(
        "action_value",
        String,
        nullable=False,
    ),
)

rules_notification = Table(
    "rules_notification",
    mapper_registry.metadata,
    Column(
        "id",
        Integer,
        Identity(),
        primary_key=True,
        autoincrement=True,
        nullable=False,
    ),
    Column(
        "rules_uuid",
        postgresql.UUID(as_uuid=True),
        ForeignKey("rules.uuid"),
        nullable=False,
    ),
    Column(
        "notifications_id",
        Integer,
        ForeignKey("notifications.id"),
        nullable=False,
    ),
    Column("notification", Boolean, nullable=False),
)

rules_action_mapping = Table(
    "rules_action_mapping",
    mapper_registry.metadata,
    Column(
        "id",
        Integer,
        Identity(),
        primary_key=True,
        autoincrement=True,
        nullable=False,
    ),
    Column(
        "rules_uuid",
        postgresql.UUID(as_uuid=True),
        ForeignKey("rules.uuid", ondelete="CASCADE"),
        nullable=False,
    ),
    Column(
        "source",
        Integer,
        nullable=True,
    ),
    Column(
        "target",
        Integer,
        nullable=False,
    ),
)

notification_value = Table(
    "notification_value",
    mapper_registry.metadata,
    Column(
        "id",
        Integer,
        Identity(),
        primary_key=True,
        autoincrement=True,
        nullable=False,
    ),
    Column(
        "rules_notification_id",
        Integer,
        ForeignKey("rules_notification.id"),
        nullable=False,
    ),
    Column("notification_value", String, nullable=False),
)

rule_details_rely = Table(
    "rule_details_rely",
    mapper_registry.metadata,
    Column(
        "id",
        Integer,
        Identity(),
        primary_key=True,
        autoincrement=True,
        nullable=False,
    ),
    Column(
        "rule_definition_id", Integer, ForeignKey("rule_definition.id"), nullable=False
    ),
    Column("data_volume", Boolean, nullable=False),
    Column("data_unit", Boolean, nullable=False),
    Column("sms_unit", Boolean, nullable=False),
    Column("voice_unit", Boolean, nullable=False),
    Column("threshold", Boolean, nullable=False),
    Column("data_percentage", Boolean, nullable=False),
    Column("view_deactivate_sim", Boolean, nullable=False),
    Column("required_deactivate_sim", Boolean, nullable=False),
    Column("view_rate_plan_change", Boolean, nullable=False),
    Column("required_rate_plan_change", Boolean, nullable=False),
    Column("add_any_rate_plan", Boolean, nullable=False),
    Column("is_monthly_pool", Boolean, nullable=False),
    Column("view_email", Boolean, nullable=False),
    Column("view_sms", Boolean, nullable=False),
    Column("view_push", Boolean, nullable=False),
    Column("voice_mo_percentage", Boolean, nullable=False),
    Column("sms_percentage", Boolean, nullable=False),
    Column("voice_momt_percentage", Boolean, nullable=False),
    Column("voice_mt_percentage", Boolean, nullable=False),
    Column("data", Boolean, nullable=False),
    Column("voice_mo", Boolean, nullable=False),
    Column("voice_mt", Boolean, nullable=False),
    Column("sms", Boolean, nullable=False),
    UniqueConstraint("rule_definition_id"),
)


def start_mappers() -> None:
    mapper_registry.map_imperatively(
        model.RuleType,
        rule_type,
        properties=dict(
            _rule_category=relationship(
                model.RuleCategory,
                back_populates="_rule_type",
                cascade="all, delete-orphan",
            ),
            _rules=relationship(
                model.Rules,
                back_populates="_rule_type",
            ),
        ),
    )

    mapper_registry.map_imperatively(
        model.RuleCategory,
        rule_category,
        properties=dict(
            _rule_type=relationship(model.RuleType, back_populates="_rule_category"),
            _rule_definition=relationship(
                model.RuleDefinition,
                back_populates="_rule_category",
                cascade="all, delete-orphan",
            ),
            _rules=relationship(
                model.Rules,
                back_populates="_rule_category",
            ),
        ),
    )

    mapper_registry.map_imperatively(
        model.RuleDefinition,
        rule_definition,
        properties=dict(
            _rule_category=relationship(
                model.RuleCategory, back_populates="_rule_definition"
            ),
            _rules=relationship(
                model.Rules,
                back_populates="_rule_definition",
            ),
            _rule_details_rely=relationship(
                model.RuleDetailsRely,
                back_populates="_rule_definition",
                cascade="all, delete-orphan",
            ),
        ),
    )

    mapper_registry.map_imperatively(
        model.Actions,
        actions,
        properties=dict(
            _rules_action=relationship(
                model.RulesAction,
                back_populates="_actions",
                cascade="all, delete-orphan",
            ),
        ),
    )

    mapper_registry.map_imperatively(
        model.Notifications,
        notifications,
        properties=dict(
            _rules_notification=relationship(
                model.RulesNotification,
                back_populates="_notifications",
                cascade="all, delete-orphan",
            ),
        ),
    )

    mapper_registry.map_imperatively(
        model.Rules,
        rules,
        properties=dict(
            _rule_type=relationship(model.RuleType, back_populates="_rules"),
            _rule_category=relationship(model.RuleCategory, back_populates="_rules"),
            _rule_definition=relationship(
                model.RuleDefinition, back_populates="_rules"
            ),
            _rules_action=relationship(
                model.RulesAction, back_populates="_rules", cascade="all, delete-orphan"
            ),
            _rules_notification=relationship(
                model.RulesNotification,
                back_populates="_rules",
                cascade="all, delete-orphan",
            ),
            _rules_action_mapping=relationship(
                model.RulesActionMapping,
                back_populates="_rules",
                cascade="all, delete-orphan",
            ),
        ),
    )

    mapper_registry.map_imperatively(
        model.RulesAction,
        rules_action,
        properties=dict(
            _rules=relationship(model.Rules, back_populates="_rules_action"),
            _actions=relationship(model.Actions, back_populates="_rules_action"),
        ),
    )

    mapper_registry.map_imperatively(
        model.RulesActionMapping,
        rules_action_mapping,
        properties=dict(
            _rules=relationship(model.Rules, back_populates="_rules_action_mapping"),
        ),
    )

    mapper_registry.map_imperatively(
        model.RulesNotification,
        rules_notification,
        properties=dict(
            _rules=relationship(
                model.Rules,
                back_populates="_rules_notification",
            ),
            _notifications=relationship(
                model.Notifications, back_populates="_rules_notification"
            ),
            _notification_value=relationship(
                model.NotificationValue,
                back_populates="_rules_notification",
                cascade="all, delete-orphan",
            ),
        ),
    )

    mapper_registry.map_imperatively(
        model.NotificationValue,
        notification_value,
        properties=dict(
            _rules_notification=relationship(
                model.RulesNotification,
                back_populates="_notification_value",
            ),
        ),
    )

    mapper_registry.map_imperatively(
        model.RuleDetailsRely,
        rule_details_rely,
        properties=dict(
            _rule_definition=relationship(
                model.RuleDefinition, back_populates="_rule_details_rely"
            ),
        ),
    )


def stop_mappers() -> None:
    mapper_registry.dispose()
