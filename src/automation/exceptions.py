class RuleError(Exception):
    ...


class RuleDetailsNotFound(Exception):
    ...


class NoRuleCategory(Exception):
    def __init__(self):
        super().__init__("No rule category found")


class NoRuleDefinition(Exception):
    def __init__(self, rule_category_id):
        super().__init__(
            f"No rule definition found for rule_category_id {rule_category_id}."
        )


class NoRuleTypeFound(Exception):
    def __init__(self):
        super().__init__("No Type found.")


class CreateRuleError(Exception):
    # def __init__(self):
    #     super().__init__()
    ...


class RuleAlreadyExist(RuleError):
    def __init__(self):
        super().__init__("Rule already exist.")


class RuleAccessError(RuleError):
    ...


class UpdateRuleError(Exception):
    ...


class DeleteRuleError(Exception):
    ...


class UpdateLockRuleError(Exception):
    ...


class RuleCategoryError(RuleError):
    ...


class RuleRelyError(Exception):
    ...


class RuleRelyConflictError(Exception):
    ...
