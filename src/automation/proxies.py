from typing import Iterator, List
from uuid import UUID

from app.config import logger
from auth.dto import AuthenticatedUser
from auth.exceptions import ForbiddenError
from auth.permissions import is_distributor_client, is_distributor_staff
from authorization.domain.ports import AbstractAuthorizationAPI
from automation.domain import model
from automation.exceptions import (
    DeleteRuleError,
    RuleError,
    UpdateLockRuleError,
    UpdateRuleError,
)
from automation.services import AbstractAutomationService
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from rate_plans.services import RatePlanService


class AutomationServiceProxy(AbstractAutomationService):
    def __init__(
        self,
        automation_service: AbstractAutomationService,
        user: AuthenticatedUser,
    ) -> None:
        self.automation_service = automation_service
        self.user = user

    def create_rule(
        self,
        rules_detail: list[model.Rules],
        scope: str,
        authorization: AbstractAuthorizationAPI,
    ) -> model.CreateRuleDetails:

        for rule_detail in rules_detail:
            rule_detail.created_by = self.user.email

        if is_distributor_staff(self.user):
            return self.automation_service.create_rule(
                rules_detail=rules_detail, scope=scope, authorization=authorization
            )
        elif is_distributor_client(self.user):
            if len(rules_detail) != 1:
                raise RuleError("Rule details must contain exact one item")
            if rules_detail[0].account_id != (
                self.user.organization.account.id  # type: ignore
            ):
                raise RuleError(
                    "You are not allowed to create rule for another account"
                )
            return self.automation_service.create_rule(
                rules_detail=rules_detail, scope=scope, authorization=authorization
            )
        else:
            raise ForbiddenError

    def update_rule(
        self,
        rules_uuid: UUID,
        rules_detail: model.Rules,
        scope: str,
        authorization: AbstractAuthorizationAPI,
    ) -> model.RuleDetails:

        rules_detail.created_by = self.user.email
        if is_distributor_staff(self.user):
            return self.automation_service.update_rule(
                rules_uuid=rules_uuid,
                rules_detail=rules_detail,
                scope=scope,
                authorization=authorization,
            )
        elif is_distributor_client(self.user):
            if rules_detail.account_id != (
                self.user.organization.account.id  # type: ignore
            ):
                raise UpdateRuleError(
                    "You are not allowed to update rule for another account"
                )
            return self.automation_service.update_rule(
                rules_uuid=rules_uuid,
                rules_detail=rules_detail,
                scope=scope,
                authorization=authorization,
            )
        else:
            raise ForbiddenError

    def get_rule_by_uuid(self, rule_uuid: UUID) -> model.RuleDetailsResponse:
        return self.automation_service.get_rule_by_uuid(rule_uuid)

    def get_rule(
        self,
        account_id: int | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
        pagination: Pagination | None = None,
    ) -> tuple[model.RuleDetailResponseList, int]:
        if is_distributor_staff(self.user):
            logger.info(f"Distributor Admin: {account_id}")
            return self.automation_service.get_rule(
                account_id=account_id,
                ordering=ordering,
                searching=searching,
                pagination=pagination,
            )
        elif is_distributor_client(self.user):
            return self.automation_service.get_rule(
                account_id=self.user.organization.account.id,  # type: ignore
                ordering=ordering,
                searching=searching,
                pagination=pagination,
            )
        else:
            raise ForbiddenError

    def get_rule_type(
        self,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.RuleType], int]:
        return self.automation_service.get_rule_type(
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )

    def get_rule_category(
        self,
        type_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.RuleCategoryDetails], int]:
        return self.automation_service.get_rule_category(
            type_id=type_id,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )

    def get_rule_definition(
        self,
        rule_category_id: int,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> tuple[Iterator[model.RuleDefinitionDetails], int]:
        return self.automation_service.get_rule_definition(
            rule_category_id=rule_category_id,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )

    def get_actions(
        self,
        action_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.Actions]:

        return self.automation_service.get_actions(
            action_id, pagination, ordering, searching
        )

    def get_actions_count(
        self, action_id: int | None = None, searching: Searching | None = None
    ) -> int:

        return self.automation_service.get_actions_count(action_id, searching)

    def get_notification(
        self,
        notification_id: int | None = None,
        pagination: Pagination | None = None,
        ordering: Ordering | None = None,
        searching: Searching | None = None,
    ) -> Iterator[model.Notifications]:
        logger.info(f"notification_id from proxies: {notification_id}")

        return self.automation_service.get_notification(
            notification_id, pagination, ordering, searching
        )

    def get_notification_count(
        self, notification_id: int | None = None, searching: Searching | None = None
    ) -> int:
        logger.info(f"notification_id from proxies: {notification_id}")

        return self.automation_service.get_notification_count(
            notification_id, searching
        )

    def update_rule_status_by_rule_uuid(
        self, rule_uuid: UUID, rule_status: bool
    ) -> model.RuleStatus:
        if is_distributor_staff(self.user):
            return self.automation_service.update_rule_status_by_rule_uuid(
                rule_uuid=rule_uuid, rule_status=rule_status
            )
        elif is_distributor_client(self.user):
            rule_details = self.automation_service.get_rule_by_uuid(rule_uuid=rule_uuid)
            if rule_details.account_id != (
                self.user.organization.account.id  # type: ignore
            ):
                raise UpdateRuleError(
                    "You are not allowed to update rule" " status for another account"
                )
            if rule_details.lock:
                raise UpdateLockRuleError(
                    "This rule has been locked by BT Administrator. You can’t edit."
                )
            return self.automation_service.update_rule_status_by_rule_uuid(
                rule_uuid=rule_uuid, rule_status=rule_status
            )
        else:
            raise ForbiddenError

    def delete_rule(
        self,
        rules_uuid: UUID,
    ) -> None:
        if is_distributor_staff(self.user):
            return self.automation_service.delete_rule(rules_uuid=rules_uuid)
        elif is_distributor_client(self.user):
            rule_details = self.automation_service.get_rule_by_uuid(
                rule_uuid=rules_uuid
            )
            if rule_details.account_id != (
                self.user.organization.account.id  # type: ignore
            ):
                raise DeleteRuleError(
                    "You are not allowed to delete rule for another account"
                )

            return self.automation_service.delete_rule(rules_uuid=rules_uuid)
        else:
            raise ForbiddenError

    def update_lock_status_by_rule_uuid(
        self, rule_uuid: UUID, lock_status: bool
    ) -> model.LockStatus:
        if is_distributor_staff(self.user):
            return self.automation_service.update_lock_status_by_rule_uuid(
                rule_uuid=rule_uuid, lock_status=lock_status
            )
        elif is_distributor_client(self.user):
            rule_details = self.automation_service.get_rule_by_uuid(rule_uuid=rule_uuid)
            if rule_details.account_id != (
                self.user.organization.account.id  # type: ignore
            ):
                raise UpdateRuleError(
                    "You are not allowed to update rule" " status for another account"
                )

            return self.automation_service.update_lock_status_by_rule_uuid(
                rule_uuid=rule_uuid, lock_status=lock_status
            )
        else:
            raise ForbiddenError

    def validate_rate_plan(
        self,
        rate_service: RatePlanService,
        rules_detail: list[model.Rules],
        validate_unit: bool = False,
    ) -> bool:
        return self.automation_service.validate_rate_plan(
            rate_service, rules_detail, validate_unit
        )

    def check_permission(
        self,
        scope: str,
        authorization: AbstractAuthorizationAPI,
    ) -> bool:
        return self.automation_service.check_permission(scope, authorization)

    def get_rule_rely(self, rule_definition_id: int) -> model.RuleResponseRely:
        return self.automation_service.get_rule_rely(
            rule_definition_id=rule_definition_id
        )

    def create_rule_rely(self, rule_rely: model.RuleDetailsRely) -> bool:
        return self.automation_service.create_rule_rely(rule_rely=rule_rely)

    def update_rule_rely(
        self,
        rely: model.RuleDetailsRely,
        rule_rely_id: int,
    ) -> bool:
        return self.automation_service.update_rule_rely(
            rule_rely_id=rule_rely_id, rely=rely
        )

    def get_service_by_definition(self, rule_definition_code: str) -> List[str]:
        return self.automation_service.get_service_by_definition(
            rule_definition_code=rule_definition_code
        )
