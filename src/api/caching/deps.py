from fastapi import Depends

from app.db.session import get_redis_session
from caching.adapters.repository import AbstractRedisRepository, RedisRepository
from caching.service import AbstractRedisService, RedisService


def redis_repository(
    session=Depends(get_redis_session),
) -> AbstractRedisRepository:
    return RedisRepository(session)


def get_redis_service(
    redis_repository: AbstractRedisRepository = Depends(redis_repository),
) -> AbstractRedisService:
    return RedisService(redis_repository)
