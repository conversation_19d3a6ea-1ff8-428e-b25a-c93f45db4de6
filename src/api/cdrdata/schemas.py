import uuid
from datetime import datetime

from pydantic import parse_obj_as

from api.cdrdata.examples import CDR_REQUEST
from api.schema_types import CamelBaseModel
from cdrdata.domain import model
from common.types import ICCID, IMSI, Carrier, Month


class CDRData(CamelBaseModel):
    """Push CDR data schema."""

    session_starttime: datetime
    session_endtime: datetime
    duration: int
    data_volume: int

    def to_model(self) -> model.CDRData:
        return model.CDRData(
            cdr_uuid=str(uuid.uuid4()),
            session_starttime=self.session_starttime,
            session_endtime=self.session_endtime,
            duration=self.duration,
            data_volume=self.data_volume,
        )


class CDRVoice(CamelBaseModel):
    """Push CDR data schema."""

    call_date: datetime
    call_number: str
    call_minutes: int

    def to_model(self) -> model.CDRVoice:
        return model.CDRVoice(
            cdr_uuid=str(uuid.uuid4()),
            call_date=self.call_date,
            call_number=self.call_number,
            call_minutes=self.call_minutes,
        )


class CDRVoiceMOMTIncamel(CamelBaseModel):
    """Push CDR data schema."""

    imsi: IMSI
    iccid: ICCID
    usage: int
    type: str
    month: Month

    def to_model(self) -> model.CDRVoiceMOMTIncamel:
        return model.CDRVoiceMOMTIncamel(
            imsi=self.imsi,
            iccid=self.iccid,
            usage=self.usage,
            type=self.type,
            month=self.month,
        )


class SMSUsageAggregate(CamelBaseModel):
    """Push CDR data schema."""

    imsi: IMSI
    iccid: ICCID
    total_sms: int
    month: Month

    def to_model(self) -> model.SMSUsageAggregate:
        return model.SMSUsageAggregate(
            cdr_uuid=str(uuid.uuid4()),
            imsi=self.imsi,
            iccid=self.iccid,
            total_sms=self.total_sms,
            month=self.month,
        )


class CDRSMSMOMT(CamelBaseModel):
    """Push CDR data schema."""

    imsi: IMSI
    iccid: ICCID
    usage: int
    type: str
    month: Month

    def to_model(self) -> model.CDRSMSMOMT:
        return model.CDRSMSMOMT(
            imsi=self.imsi,
            iccid=self.iccid,
            usage=self.usage,
            type=self.type,
            month=self.month,
        )


class CDRSMS(CamelBaseModel):
    """Push CDR data schema."""

    date_sent: datetime
    sent_from: str
    sent_to: str

    def to_model(self) -> model.CDRSMS:
        return model.CDRSMS(
            cdr_uuid=str(uuid.uuid4()),
            date_sent=self.date_sent,
            sent_from=self.sent_from,
            sent_to=self.sent_to,
        )


class DataUsageAggregate(CamelBaseModel):
    """Aggregation of Push CDR data schema."""

    imsi: IMSI
    iccid: ICCID
    usage: int
    month: Month

    def to_model(self) -> model.DataUsageAggregate:
        return model.DataUsageAggregate(
            cdr_uuid=str(uuid.uuid4()),
            imsi=self.imsi,
            iccid=self.iccid,
            usage=self.usage,
            month=self.month,
        )


class VoiceUsageAggregate(CamelBaseModel):
    """Aggregation of Push CDR voice schema."""

    imsi: IMSI
    iccid: ICCID
    voice_usage: int
    month: Month

    def to_model(self) -> model.VoiceUsageAggregate:
        return model.VoiceUsageAggregate(
            cdr_uuid=str(uuid.uuid4()),
            imsi=self.imsi,
            iccid=self.iccid,
            voice_usage=self.voice_usage,
            month=self.month,
        )


class CDR(CamelBaseModel):
    """Push CDR data schema."""

    imsi: IMSI
    iccid: ICCID
    type: str
    country: str
    country_name: str
    carrier: Carrier
    data: CDRData | None
    voice: CDRVoice | None
    sms: CDRSMS | None
    data_usage: DataUsageAggregate | None
    voice_usage: VoiceUsageAggregate | None
    sms_usage: SMSUsageAggregate | None
    voice_mo_mt_incamel: CDRVoiceMOMTIncamel | None
    sms_mo_mt: CDRSMSMOMT | None
    cdr_object_id: str | None

    def to_model(self) -> model.CDR:
        return model.CDR(
            uuid=uuid.uuid4(),
            imsi=self.imsi,
            iccid=self.iccid,
            type=self.type,
            country=self.country,
            country_name=self.country_name,
            carrier=self.carrier,
            data=self.data.to_model() if self.data else None,
            voice=self.voice.to_model() if self.voice else None,
            sms=self.sms.to_model() if self.sms else None,
            data_usage=self.data_usage.to_model() if self.data_usage else None,
            voice_usage=self.voice_usage.to_model() if self.voice_usage else None,
            sms_usage=self.sms_usage.to_model() if self.sms_usage else None,
            voice_mo_mt_incamel=self.voice_mo_mt_incamel.to_model()
            if self.voice_mo_mt_incamel
            else None,
            sms_mo_mt=self.sms_mo_mt.to_model() if self.sms_mo_mt else None,
            cdr_object_id=self.cdr_object_id,
        )

    class Config:
        schema_extra = {"example": CDR_REQUEST}
        orm_mode = True


parse_obj_as(CDR, CDR_REQUEST)
