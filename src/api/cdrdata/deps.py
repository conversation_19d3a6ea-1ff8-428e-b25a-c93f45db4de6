from fastapi import Depends
from sqlalchemy.orm import Session

from api.deps import get_db_session as _get_db_session
from cdrdata.adapters.externalapi import MarketShareAPI
from cdrdata.adapters.repository import AbstractCdrRepository, DatabaseCdrRepository
from cdrdata.domain.ports import AbstractMarketShare
from cdrdata.services import AbstractCdrService, CdrService
from sim.adapters.repository import AbstractSimRepository, DatabaseSimRepository


def _cdr_repository(
    session: Session = Depends(_get_db_session),
) -> AbstractCdrRepository:
    return DatabaseCdrRepository(session)


def get_sim_repository(
    session: Session = Depends(_get_db_session),
) -> AbstractSimRepository:
    return DatabaseSimRepository(session)


def _market_share_api() -> AbstractMarketShare:
    return MarketShareAPI()


def cdr_service(
    cdr_repository: AbstractCdrRepository = Depends(_cdr_repository),
    market_share: AbstractMarketShare = Depends(_market_share_api),
) -> AbstractCdrService:
    return CdrService(cdr_repository, market_share)
