import logging

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, Request, status

from api.decorators import validate_ip_address
from api.sim import deps
from api.sim.schemas import Notification
from app.config import log_activity, logger
from auth.exceptions import Unauthorized
from common.ip_utils import get_client_ip
from sim.domain import model
from sim.exceptions import PushNotificationError, SimCardsNotFound, WorkItemIdNotFound
from sim.notification_service import AbstractNotificationService

notification = APIRouter()
stream_logs = logging.getLogger(__name__)


@notification.post(
    "/notifications",
    status_code=status.HTTP_200_OK,
)
@log_activity(stream_logs, ignored_params=("push_notification_service", "request"))
@validate_ip_address
def add_sim_notification(
    request: Request,
    notification: Notification,
    push_notification_service: AbstractNotificationService = Depends(
        deps.push_notification_service
    ),
) -> bool:
    """Add SIM notification."""
    try:
        client_ip = get_client_ip(request)
        return push_notification_service.process_notification(
            model.RequestType(notification.request_type.title()),
            notification.to_model(),
            client_ip,
        )
    except Unauthorized as e:
        logger.error(f"Unauthorized.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access."
        )
    except (SimCardsNotFound, WorkItemIdNotFound) as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except (PushNotificationError, ValueError):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Invalid operation"
        )
