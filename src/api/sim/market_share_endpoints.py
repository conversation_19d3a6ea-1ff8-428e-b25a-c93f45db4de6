from datetime import date
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Path, Request, status
from requests.exceptions import ConnectionError

import api.deps as trace_id_deps
from api.authorization.deps import authorization_service
from api.decorators import check_permissions
from api.schema_types import IMSI
from api.sim import deps
from app.config import logger
from auth.exceptions import NotFound, Unauthorized
from authorization.domain.ports import AbstractAuthorizationAPI
from common.exceptions import AnalyticsAPIError, Unprocessable
from sim.domain import model
from sim.domain.model import MarketShareData, MarketSharePeriod
from sim.exceptions import IMSIDoesNotExit
from sim.services import AbstractSimService

marketShare = APIRouter(tags=["market"], prefix="/market")


@marketShare.get("/account/{account_id}")
@check_permissions
def get_market_share_by_account(
    request: Request,
    account_id: int = Path(ge=0),
    from_date: date = None,
    to_date: date = None,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> model.MarketShareCarrier:
    market_share_data = MarketShareData(
        account_id=account_id, from_date=from_date, to_date=to_date
    )
    try:
        logger.info(f"market_share_data {market_share_data}")
        result_data = sim_service.get_market_share_by_account(market_share_data)
        return result_data
    except Unprocessable as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
    except Unauthorized as e:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=str(e))
    except NotFound as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except (AnalyticsAPIError, ConnectionError) as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except AssertionError as e:
        logger.error(f"Assertion error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@marketShare.get("/accounts")
@check_permissions
def get_market_share(
    request: Request,
    from_date: date = None,
    to_date: date = None,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> model.MarketShareCarrier:
    period = MarketSharePeriod(from_date=from_date, to_date=to_date)
    try:
        logger.info(f"period {period}")
        result_data = sim_service.get_market_share(period)
        return result_data
    except Unprocessable as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
    except Unauthorized as e:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=str(e))
    except NotFound as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except (AnalyticsAPIError, ConnectionError) as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except AssertionError as e:
        logger.error(f"Assertion error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@marketShare.get("/accounts/imsi")
@check_permissions
def get_market_share_imsi(
    request: Request,
    imsi: IMSI,
    from_date: date = None,
    to_date: date = None,
    sim_service: AbstractSimService = Depends(deps.sim_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> model.MarketShareCarrier:
    period = MarketSharePeriod(from_date=from_date, to_date=to_date)
    try:
        logger.info(f"period {period}")
        result_data = sim_service.get_market_share_imsi(period, imsi)
        return result_data
    except Unprocessable as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
    except Unauthorized as e:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail=str(e))
    except NotFound as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except (AnalyticsAPIError, ConnectionError, IMSIDoesNotExit) as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
