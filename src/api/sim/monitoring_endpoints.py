import logging
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Request, status

import api.deps as trace_id_deps
from api.authorization.deps import authorization_service
from api.decorators import measure_execution_time
from api.sim import deps
from api.sim.schemas import InvalidSimMsisdnCountDetails
from app.config import logger
from auth.exceptions import ForbiddenError, NotFound
from authorization.domain.ports import AbstractAuthorizationAPI
from common.exceptions import Audit<PERSON>IError
from common.ip_utils import get_client_ip
from sim.monitoring_service import AbstractSimMonitoringService

router = APIRouter(tags=["monitoring"], prefix="/monitoring")
stream_logs = logging.getLogger(__name__)


@router.get(
    "/sim/msisdn/count",
    status_code=status.HTTP_200_OK,
    response_model=InvalidSimMsisdnCountDetails,
)
@measure_execution_time
def get_invalid_sim_and_msisdn_count(
    request: Request,
    sim_monitoring_service: AbstractSimMonitoringService = Depends(
        deps.sim_monitoring_service
    ),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> InvalidSimMsisdnCountDetails:

    try:
        client_ip = get_client_ip(request)
        logger.info(f"Client IP:- {client_ip}")
        invalid_sim_and_msisdn_count = (
            sim_monitoring_service.get_invalid_sim_and_msisdn_count()
        )
        return InvalidSimMsisdnCountDetails.from_response_model(
            invalid_sim_and_msisdn_count
        )
    except AuditAPIError as e:
        logger.error(
            f"Failed to get invalid sim msisdn count error occoured:- {str(e)}"
        )
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except ConnectionError as e:
        logger.info(f"Error while calling audit api. Error: {str(e)}")
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=str(e))
    except NotFound as e:
        logger.error(
            f"Failed to get invalid sim msisdn count error occoured:- {str(e)}"
        )
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except ForbiddenError as e:
        logger.error(
            f"Failed to get invalid sim msisdn count error occoured:- {str(e)}"
        )
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=str(e))
    except ValueError as e:
        logger.error(
            f"Failed to get invalid sim msisdn count error occoured:- {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e)
        )
    except Exception as e:
        logger.error(
            f"Failed to get invalid sim msisdn count error occoured - {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Couldn't process the request",
        )
