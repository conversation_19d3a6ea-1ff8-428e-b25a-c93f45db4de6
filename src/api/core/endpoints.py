from typing import Union
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Response, status
from fastapi.responses import JSONResponse

import api.deps as trace_id_deps
from api.core import deps
from api.core.schemas import PartitionsList
from app.config import logger
from core.domain import model
from core.services import AbstractCoreService

router = APIRouter(tags=["core"])


@router.post(
    "/partitions",
    status_code=status.HTTP_201_CREATED,
    response_model=model.CreatePartitionsResponse,
)
def create_partitions(
    partitions: PartitionsList,
    core_service: AbstractCoreService = Depends(deps.get_core_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> Union[model.CreatePartitionsResponse, Response]:
    try:
        response = core_service.create_partitions(partitions.to_model())
        logger.info(f"Partitions created: {response.created}")
        logger.info(f"Partitions skipped: {response.skipped}")
        logger.info(f"Number of partitions created: {len(response.created)}")
        logger.info(f"Number of Partitions skipped: {len(response.skipped)}")

        # return with status code 409 conflict
        if not response.created and response.skipped:
            return JSONResponse(
                content=response.to_dict(),
                status_code=status.HTTP_409_CONFLICT,
            )
        return response

    except ValueError as e:
        logger.error(f"Error creating partitions:{e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"{str(e)}",
        )
    except Exception as e:
        logger.error(f"Error creating partitions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"{str(e)}",
        )
