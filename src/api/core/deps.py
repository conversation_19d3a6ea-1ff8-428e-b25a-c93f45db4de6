from fastapi import Depends

from api.deps import get_db_session
from core.adapters.repository import AbstractCoreRepository, DataBaseCoreRepository
from core.services import CoreService


def get_core_repository(
    session=Depends(get_db_session),
) -> AbstractCoreRepository:
    return DataBaseCoreRepository(session)


def get_core_service(
    core_repository: AbstractCoreRepository = Depends(get_core_repository),
):
    return CoreService(
        core_repository,
    )
