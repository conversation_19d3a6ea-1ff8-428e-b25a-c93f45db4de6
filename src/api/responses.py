import os
import tempfile
from io import String<PERSON>

from pydantic import typing
from starlette.background import BackgroundTask, BackgroundTasks
from starlette.responses import FileResponse


class CSVFileResponse(FileResponse):
    media_type = "text/csv"

    def __init__(
        self,
        content: StringIO,
        status_code: int = 200,
        headers: typing.Optional[typing.Mapping[str, str]] = None,
        background: typing.Optional[BackgroundTask] = None,
        filename: typing.Optional[str] = None,
        stat_result: typing.Optional[os.stat_result] = None,
        method: typing.Optional[str] = None,
        content_disposition_type: str = "attachment",
    ):
        with tempfile.NamedTemporaryFile("w", delete=False) as f:
            f.write(content.read())
        tasks = BackgroundTasks()
        if background:
            tasks.add_task(background.func, *background.args, **background.kwargs)
        tasks.add_task(os.remove, f.name)
        super().__init__(
            f.name,
            status_code,
            headers,
            background=tasks,
            filename=filename,
            stat_result=stat_result,
            method=method,
            content_disposition_type=content_disposition_type,
        )
