from collections.abc import Sequence
from typing import Any, TypeVar

from common.minions import rangeofuse

RangeOfUseT = TypeVar("RangeOfUseT", bound=rangeofuse.RangeOfUse)


def check_ranges(v: Sequence[RangeOfUseT]) -> Sequence[RangeOfUseT]:
    if not rangeofuse.contiguous(v):
        raise ValueError("Ranges are not contiguous.")

    if not rangeofuse.is_complete(v):
        raise ValueError("The entire range is not complete.")
    return v


def check_range_to(v: int | None, values: dict[str, Any]) -> int | None:
    if v is None:
        return v
    if "range_from" not in values:
        return v

    return v
