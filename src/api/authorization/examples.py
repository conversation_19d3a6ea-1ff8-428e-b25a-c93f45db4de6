import uuid

from authorization.domain import model

GROUP_ROLE_RESPONSE = [
    model.GroupRole(
        id=uuid.UUID("8b376afc-9014-49ff-ad1f-4589e122deda"),
        name="admin",
        isDefault=False,
        description="admin",
        group="My Organization",
        permissions=0,
        userCount=0,
        createdBy="admin",
    )
]


GROUP_LIST = {
    "result": [
        {
            "id": "7247a128-78f5-4a81-abe7-3820f74210f3",
            "name": "Nextgen Clearing",
            "path": "/Nextgen Clearing",
            "subGroups": [
                {
                    "id": "b3d07d10-e372-45b1-b334-c1c01b6da1d5",
                    "name": "bt-push-cdr-account",
                    "path": "/Nextgen Clearing/bt-push-cdr-account",
                    "subGroups": [
                        {
                            "id": "c095050c-3f51-4d20-a266-bcb72f4d4f0d",
                            "name": "bt-push-cdr-account-nested",
                            "path": "/Nextgen Clearing/bt-push-cdr-acco\
                                unt/bt-push-cdr-account-nested",
                            "subGroups": [],
                        }
                    ],
                },
                {
                    "id": "72305eb7-e8bf-40b9-b8ca-c0ee4ea80a4d",
                    "name": "bt-push-cdr-account-2",
                    "path": "/Nextgen Clearing/bt-push-cdr-account-2",
                    "subGroups": [],
                },
            ],
        },
        {
            "id": "2a3a45b7-7edc-4b0e-9d31-ec39ab1f6760",
            "name": "single_user_account",
            "path": "/single_user_account",
            "subGroups": [],
        },
    ]
}


PERMISSION_LIST = model.PermissionsResponse(
    id=uuid.UUID("df279b03-0bcf-4ab5-9059-a72b2d2321a5"),
    name="ClientAdmin",
    roleGroup="My Organization",
    description="ClientAdmin description",
    permission=[
        "9cffe4ef-573a-4a03-8674-6b7d76265503",
        "d54db5ed-5c9e-416f-97f0-f178b01fe8d8",
    ],
)


PERMISSIONS = "PERMIT"
