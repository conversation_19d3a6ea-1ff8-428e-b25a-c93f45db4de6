from collections.abc import Generator
from functools import cache

from fastapi import Depends
from sqlalchemy.orm import Session

from accounts.domain.ports import AbstractAccountRepository
from api.deps import (
    _get_account_repository,
    get_auth_service,
    get_authenticated_user,
    get_db_session,
)
from app.platform import get_authorization_api_client
from auth.dto import AuthenticatedUser
from auth.services import (
    AbstractAuthService,
    AccountAuthService,
    FakeAuthService,
    TokenIntrospectionAuthService,
)
from authorization.adapters.repository import (
    AbstractAuthRepository,
    DatabaseAuthRepository,
)
from authorization.domain.ports import AbstractAuthorization<PERSON><PERSON>
from authorization.proxies import AuthorizationAuthProxy
from authorization.services import FakeAuthorization<PERSON><PERSON>, HTTPAuthorizationAPI


@cache
def fake_authorization_api_client() -> AbstractAuthorizationAPI:
    return FakeAuthorizationAPI()


def get_auth_repository(
    session: Session = Depends(get_db_session),
) -> AbstractAuthRepository:
    return DatabaseAuthRepository(session)


def get_authorization_client(
    authorization_service: AbstractAuthService = Depends(get_auth_service),
    account_repository: AbstractAccountRepository = Depends(_get_account_repository),
    auth_repository: AbstractAuthRepository = Depends(get_auth_repository),
) -> Generator[AbstractAuthorizationAPI, None, None]:
    auth_token: str | None
    match authorization_service:
        case TokenIntrospectionAuthService(token=_token):
            auth_token = _token
        case AccountAuthService(
            auth_service=TokenIntrospectionAuthService(token=_token)
        ):
            auth_token = _token
        case AccountAuthService(auth_service=FakeAuthService()) | FakeAuthService():
            auth_token = None
        case _:
            raise AssertionError(
                f"Unexpected auth service: {type(authorization_service)}"
            )

    if auth_token:
        with get_authorization_api_client(auth_token) as client:
            yield HTTPAuthorizationAPI(client, account_repository, auth_repository)
    else:
        yield fake_authorization_api_client()


def authorization_service(
    http_authorization: AbstractAuthorizationAPI = Depends(get_authorization_client),
    authenticated_user: AuthenticatedUser = Depends(get_authenticated_user),
) -> AbstractAuthorizationAPI:
    return AuthorizationAuthProxy(
        authorization=http_authorization,
        user=authenticated_user,
    )
