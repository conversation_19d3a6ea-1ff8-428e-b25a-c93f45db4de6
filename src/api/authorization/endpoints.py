from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Request, status

import api.deps as trace_id_deps
from api.authorization.deps import authorization_service
from api.decorators import check_permissions, measure_execution_time
from api.schema_types import PaginatedResponse
from app.config import logger
from auth.exceptions import ForbiddenError, NotFound
from authorization.domain import model
from authorization.domain.ports import AbstractAuthorizationAPI
from authorization.exceptions import (
    GroupNotFound,
    GroupRoleNotFound,
    MaxNumberInvalid,
    PolicyAlreadyExist,
    PolicyNotFound,
    RoleDeletionError,
    RoleNotFound,
)
from common.pagination import InvalidPage, Pagination
from common.searching import Searching

router = APIRouter(
    tags=["authorization"], prefix="/authorization", include_in_schema=True
)


def validate_pagination(page_size: int) -> int:
    if page_size % 10 != 0:
        raise MaxNumberInvalid
    return page_size


@router.get(
    "/role",
    status_code=status.HTTP_200_OK,
)
@check_permissions
def get_roles(
    request: Request,
    searching: Searching
    | None = Depends(
        Searching.build(
            fields={
                "role",
            }
        )
    ),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    pagination: Pagination = Depends(Pagination.query()),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> PaginatedResponse[model.GroupRole]:
    try:
        logger.info(f"Get role request: {searching}")
        pagination.page_size = validate_pagination(page_size=pagination.page_size)
        roles, total_count = authorization.get_roles(
            pagination=pagination, searching=searching
        )
        logger.info(f"get_roles_API-roles :-{str(roles)} ")
        logger.info(f"get_roles_API-total_count :-{str(total_count)} ")
        return PaginatedResponse.from_iterable(
            pagination=pagination, results=roles, total_count=total_count
        )
    except MaxNumberInvalid as e:
        logger.error(f"Error in get_roles: '{str(e)}'")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Page size should be multiple of 10.",
        )
    except GroupNotFound as e:
        logger.error(f"Group with id {str(e)} not found while getting group_roles.")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Group not found."
        )
    except ForbiddenError as e:
        logger.error(f"PlatforAPI error Forbidden.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Forbidden.",
        )
    except NotFound as e:
        logger.error(f"PlatforAPI error NotFound.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="NotFound",
        )
    except InvalidPage as e:
        logger.error(f"Invalid page error.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=f"Invalid page: {str(e)}"
        )
    except ValueError as e:
        logger.error(f"We couldn't process the request. {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process the request."
        )
    except Exception as e:
        logger.error(f"Couldn't process the request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request"
        )


@router.get(
    "/{role_id}/permission",
    response_model=model.PermissionsResponse,
    status_code=status.HTTP_200_OK,
)
@check_permissions
def get_permissions_role_id(
    request: Request,
    role_id: UUID,
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> model.PermissionsResponse:
    try:
        logger.info(f"Get permissions request: {role_id}")
        return authorization.get_permissions(role_id)
    except PolicyNotFound as e:
        logger.error(f"Policy not found error.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Policy Not Found"
        )
    except RoleNotFound as e:
        logger.error(f"Role not found error.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Role Not Found"
        )
    except ForbiddenError as e:
        logger.error(f"PlatforAPI error Forbidden.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Forbidden.",
        )
    except NotFound as e:
        logger.error(f"PlatforAPI error NotFound.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="NotFound",
        )
    except ValueError as e:
        logger.error(f"We couldn't process the request. {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process the request."
        )
    except Exception as e:
        logger.error(f"Couldn't process the request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request"
        )


@router.post(
    "/role",
    status_code=status.HTTP_201_CREATED,
    response_model=model.RoleResponse,
)
@check_permissions
@measure_execution_time
def create_role(
    request: Request,
    role_request: model.RoleRequest,
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> model.RoleResponse:
    try:
        """This Function creates Role & Policy"""
        logger.info(f"Create role request: {role_request}")
        role_data = authorization.create_role(role_request)
        logger.info(f"Create role response: {role_data}")

        return role_data
    except GroupRoleNotFound as e:
        logger.error(str(e))
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Group roles not found."
        )
    except GroupNotFound as e:
        logger.error(f"Group with id {str(e)} not found while getting group_roles.")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Group not found."
        )
    except PolicyAlreadyExist as e:
        logger.error(f"Role is created and policy already exist.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Role already exist.",
        )
    except ForbiddenError as e:
        logger.error(f"PlatforAPI error Forbidden.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Forbidden.",
        )
    except ValueError as e:
        logger.error(f"We coudn't process your request.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="We coudn't process your request.",
        )
    except NotFound as e:
        logger.error(f"PlatforAPI error NotFound.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="NotFound",
        )
    except Exception as e:
        logger.error(f"Coudn't process your request.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Coudn't process your request.",
        )


@router.delete(
    "/role",
    status_code=status.HTTP_204_NO_CONTENT,
)
@check_permissions
def delete_role(
    request: Request,
    role_uuid: UUID,
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    try:
        logger.info(f"Delete role request: {role_uuid}")
        return authorization.delete_role(role_uuid)
    except ForbiddenError as e:
        logger.error(f"PlatforAPI error Forbidden.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Forbidden.",
        )
    except NotFound as e:
        logger.error(f"PlatforAPI error NotFound.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="NotFound",
        )
    except RoleDeletionError as e:
        logger.error(f"Role {role_uuid} could not be deleted.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Role {role_uuid} could not be deleted.",
        )
    except ValueError as e:
        logger.error(f"We coudn't process your request.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="We coudn't process your request.",
        )
    except Exception as e:
        logger.error(f"Coudn't process your request.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Coudn't process your request.",
        )
