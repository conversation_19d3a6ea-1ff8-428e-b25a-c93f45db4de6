from typing import Any, Dict

BILLING_SETTINGS = {"simCharge": 12.35, "paymentTerms": 30}


ACCOUNT_1 = {
    "id": 1,
    "name": "Kyivstar",
    "logoUrl": "https://example.com/icon",
    "status": {"ref": "ACTIVE", "name": "Active"},
    "isBillable": True,
    "agreement_number": "25m",
    "currency": "GBP",
    "country": "UA",
    "industryVertical": {"ref": "TELECOMMUNICATIONS", "name": "Telecommunications"},
    "salesChannel": {"ref": "WHOLESALE", "name": "Wholesale"},
    "productTypes": [{"ref": "NATIONAL_ROAMING", "name": "National Roaming"}],
    "defaultRatePlan": "PAYG 1",
    "salesPerson": "Brooklyn Simmons",
    "contractEndDate": "2023-12-31",
    "usersTotal": 0,
    "simsTotal": 0,
    "activeSimsTotal": 0,
    "plansTotal": 0,
    "organizationId": 1,
    "thresholdCharge": 0.003,
    "warningThreshold": 90,
    **BILLING_SETTINGS,
    "simProfile": {"ref": "DATA_ONLY", "name": "DATA_ONLY"},
}
ACCOUNT_2 = {
    "id": 2,
    "name": "Tesla",
    "logoUrl": None,
    "status": {"ref": "SUSPENDED", "name": "Suspended"},
    "isBillable": False,
    "agreementNumber": "************",
    "currency": "GBP",
    "country": "US",
    "industryVertical": {"ref": "TRANSPORT", "name": "Transport"},
    "salesChannel": {"ref": "INDIRECT", "name": "Indirect"},
    "productTypes": [],
    "defaultRatePlan": "PAYG 1",
    "salesPerson": "Savannah Nguyen",
    "contractEndDate": "2023-12-31",
    "usersTotal": 0,
    "simsTotal": 0,
    "activeSimsTotal": 0,
    "plansTotal": 0,
    "organizationId": 1,
    "thresholdCharge": 0.003,
    "warningThreshold": 90,
    **BILLING_SETTINGS,
    "simProfile": {"ref": "VOICE_SMS_DATA", "name": "VOICE_SMS_DATA"},
}

USER_INVITE = {
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "role": "Admin",
}

SAF_EVENT = {
    "referenceId": "0bc786c6-1579-4160-91d0-85a63ff80318",
    "email": "<EMAIL>",
    "userId": "us-**************",
    "registrationTs": "**********",
    "event": {"eventCode": 1, "message": "Some message"},
}

ACCOUNT_NAMES = {"id": 1, "name": "Account name", "organization_id": 1}

TEST_ACCOUNT_USER_DETAILS = {
    "status": "ACTIVE",
    "industryVertical": "AEROSPACE",
    "salesChannel": "WHOLESALE",
    "simProfile": "DATA_ONLY",
}

TEST_ACCOUNT: Dict[str, Any] = {
    "name": "Test Account",
    "agreementNumber": "3a7fPKxR19LU",
    "logoKey": "/home/<USER>/tmp/monoglass/src/images/bt-logo.svg",
    "salesPerson": "Ved",
    "contractEndDate": "2999-08-30",
    "isBillable": True,
    "contactName": "Ved",
    "email": "<EMAIL>",
    "phone": "**********",
    "jobTitle": "RM",
    "country": "CA",
    "stateRegion": "LONDON",
    "city": "KITCHNER",
    "address1": "Lane46",
    "address2": "Lane53",
    "postcode": "852221",
    "thresholdCharge": 0.003,
    "warningThreshold": 90,
    **BILLING_SETTINGS,
    "simProfile": {"ref": "DATA_ONLY", "name": "DATA_ONLY"},
}
CREATE_ACCOUNT = {
    **TEST_ACCOUNT,
    **TEST_ACCOUNT_USER_DETAILS,
    "currency": "GBP",
    "productTypes": ["NATIONAL_ROAMING"],
}

UPDATE_ACCOUNT = {
    **TEST_ACCOUNT,
    **TEST_ACCOUNT_USER_DETAILS,
}

TEST_ACCOUNT_RESPONSE = {
    "id": 123,
    **TEST_ACCOUNT,
    "currency": "GBP",
    "logoUrl": "http://localhost:8000/home/<USER>/tmp/monoglass/src/images/bt-logo.svg",
    "status": {"ref": "ACTIVE", "name": "Active"},
    "industryVertical": {"ref": "AEROSPACE", "name": "Aerospace"},
    "salesChannel": {"ref": "WHOLESALE", "name": "Wholesale"},
    "productTypes": [{"ref": "NATIONAL_ROAMING", "name": "National Roaming"}],
    "organizationId": 1,
    "simProfile": {"ref": "DATA_ONLY", "name": "DATA_ONLY"},
}


SIM_ACCOUNT_INFO_DETAILS = {
    "imsi": "***************",
    "msisdn": "************",
    "iccid": "12345678901234600000",
    "sim_status": "Active",
    "account_id": 13,
    "account_name": "0wvryg4b17",
    "account_logo_url": "http://localhost:8000//home/<USER>/tmp",
}
SIM_INFO = {
    "result": [
        {
            "imsi": "***************",
            "iccid": "8944538532046590002",
            "msisdn": "***************",
            "sim_status": "READY_FOR_ACTIVATION",
            "account_id": 1,
            "account_name": "vedant",
            "account_logo_url": "http://localhost:8000/home/<USER>/"
            "tmp/monoglass/src/images/bt-logo.svg",
        }
    ]
}

SIM_INFO_LOGO_KEY_DETAILS = [
    {
        "imsi": "***************",
        "iccid": "12345678901234600000",
        "msisdn": "************",
        "sim_status": "Active",
        "account_id": 13,
        "account_name": "0wvryg4b17",
        "account_logo_key": "/home/<USER>/tmp",
    },
    {
        "imsi": "***************",
        "iccid": "9685538532046511001",
        "msisdn": "**********",
        "sim_status": "Deactivated",
        "account_id": 2,
        "account_name": 2,
        "account_logo_key": "home/PC/tmp",
    },
]

SIM_INFO_LOGO_KEY_DETAILS_RULE = [
    {
        "imsi": "***************",
        "iccid": "8944538531005850000",
        "msisdn": "************",
        "sim_status": "Ready for Activation",
        "account_id": 1,
        "account_name": "name 1",
        "account_logo_url": "http://localhost:8000//home/",
        "account_logo_key": "/home/<USER>/tmp/monoglass/images/bt-logo.svg",
        "rules": [
            {
                "rules_uuid_param": "acfa60de-d056-4b3e-b516-a2d5c18ab2da",
                "sim_usage_limit": 4,
                "unit": "KB",
                "size_in_bytes": 4096,
                "actions": [],
                "notifications": [
                    {
                        "name": "Send email",
                        "notification": True,
                        "email": [
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                        ],
                    }
                ],
            },
            {
                "rules_uuid_param": "9c9b24d5-9e9a-472a-895e-472da1c6c368",
                "sim_usage_limit": 5,
                "unit": "KB",
                "size_in_bytes": 5120,
                "actions": [],
                "notifications": [
                    {
                        "name": "Send email",
                        "notification": True,
                        "email": [
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                            "<EMAIL>",
                        ],
                    }
                ],
            },
        ],
    }
]


SIM_INFO_DB_RESPONSE = [
    {
        "rules_uuid_param": "acfa60de-d056-4b3e-b516-a2d5c18ab2da",
        "sim_usage_limit": 4,
        "unit": "KB",
        "imsi": "***************",
        "iccid": "8944538531005850000",
        "msisdn": "************",
        "sim_status": "Ready for Activation",
        "account_id": 1,
        "account_name": "name 1",
        "account_logo_key": "/home/<USER>/tmp/monoglass/images/bt-logo.svg",
    }
]
