from typing import Iterable
from uuid import UUID

from fastapi import (
    APIRouter,
    Depends,
    File,
    HTTPException,
    Request,
    Response,
    UploadFile,
    status,
)

import api.deps as trace_id_deps
from accounts.adapters.exceptions import AccountDeletionError
from accounts.domain import dto, model
from accounts.domain.exceptions import (
    AccountAlreadyExists,
    AccountDoesNotExist,
    MediaError,
    OrganizationDoesNotExist,
    SimAccountDataNotFound,
)
from accounts.domain.model import (
    AccountStatus,
    IndustryVertical,
    ProductType,
    SalesChannel,
    SimProfile,
)
from accounts.exceptions import InviteAlreadyAccepted, UserNotFound
from accounts.services import AbstractAccountService, AbstractUserService, MediaService
from api.accounts import deps
from api.accounts.schemas import (
    Account,
    AccountId,
    AccountNames,
    AccountSummary,
    BillingSettings,
    CreateAccount,
    GetAccount,
    SAFEvent,
    SimAccountInfoDetails,
    UpdateAccount,
    UploadAccountLogoResponse,
    UserInvite,
)
from api.authorization.deps import authorization_service
from api.decorators import check_permissions, measure_execution_time
from api.resolvers import Resolver
from api.schema_types import PaginatedResponse
from app.config import logger
from auth.exceptions import ForbiddenError
from authorization.domain.ports import AbstractAuthorizationAPI
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from common.types import ICCID, IMSI, MSISDN, EnumerationList

router = APIRouter(tags=["accounts"])


@router.post("/accounts", response_model=Account)
@check_permissions
@measure_execution_time
def create_account(
    request: Request,
    account: CreateAccount,
    account_service: AbstractAccountService = Depends(deps.get_account_service_proxy),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> Account:
    try:
        account_id = account_service.create_account(account)
    except MediaError as e:
        logger.error("Media error occoured.")
        raise HTTPException(status.HTTP_400_BAD_REQUEST, str(e))
    except AccountAlreadyExists:
        logger.error(
            f"Account with name {account.name} requested to create already exist."
        )
        raise HTTPException(status.HTTP_409_CONFLICT)
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )
    created_account = account_service.get(account_id)
    return Account.from_dto(created_account, 0, 0, 0, 0)


@router.get("/accounts/{account_id}", response_model=Account)
@check_permissions
@measure_execution_time
def view_account(
    request: Request,
    account_id: int,
    account_service: AbstractAccountService = Depends(deps.get_account_service_proxy),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    account_statistic_resolver: Resolver[dto.Account, dict[str, int]] = Depends(
        deps.account_statistic_resolver
    ),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> Account:
    try:
        account = account_service.get(account_id)
        get_statistic = account_statistic_resolver([account])
        statistic = get_statistic(account)
        return Account.from_dto(account, **statistic)
    except AccountDoesNotExist:
        raise HTTPException(status.HTTP_404_NOT_FOUND)


@router.put("/accounts/{account_id}", response_model=Account)
@check_permissions
def update_account(
    request: Request,
    account_id: int,
    account_data: UpdateAccount,
    account_service: AbstractAccountService = Depends(deps.get_account_service_proxy),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    account_statistic_resolver: Resolver[dto.Account, dict[str, int]] = Depends(
        deps.account_statistic_resolver
    ),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> Account:
    try:
        account = account_service.update(account_id, account_data)
        get_statistic = account_statistic_resolver([account])
        statistic = get_statistic(account)
        return Account.from_dto(account, **statistic)
    except MediaError as e:
        logger.error("Media error occured while updating account details.")
        raise HTTPException(status.HTTP_400_BAD_REQUEST, str(e))
    except AccountDoesNotExist:
        logger.error(f"Account with id {account_id} requested to update not found.")
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    except AccountAlreadyExists:
        logger.error(
            f"Account with id {account_id} requested to update details already exist."
        )
        raise HTTPException(status.HTTP_409_CONFLICT)
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


@router.delete(
    "/accounts/{account_id}",
    status_code=status.HTTP_202_ACCEPTED,
)
@check_permissions
def delete_account(
    request: Request,
    account_id: int,
    account_service: AbstractAccountService = Depends(deps.get_account_service_proxy),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> dict:
    try:
        account_service.delete(account_id)
        return {"message": "We have received your request."}
    except AccountDoesNotExist:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    except AccountDeletionError as e:
        logger.error(f"Account deletion error: {str(e)}")
        raise HTTPException(status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.get("/accounts", response_model=PaginatedResponse[GetAccount])
@check_permissions
@measure_execution_time
def list_accounts(
    request: Request,
    account_service: AbstractAccountService = Depends(deps.get_account_service_proxy),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    ordering: Ordering = Depends(
        Ordering.query(
            GetAccount,
            default_ordering="name",
            ordering_fields=(
                "name",
                "agreement_number",
                "currency",
                "industry_vertical",
                "sales_channel",
                "sales_person",
                "status",
            ),
        )
    ),
    searching: Searching
    | None = Depends(
        Searching.build(
            fields={
                "name",
            },
        )
    ),
    pagination: Pagination = Depends(Pagination.query()),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> PaginatedResponse[dto.GetAccount]:
    try:
        total_count = account_service.account_count(searching=searching)
        accounts = account_service.list(
            ordering=ordering, searching=searching, pagination=pagination
        )
        result = PaginatedResponse.from_iterable(
            pagination=pagination,
            results=accounts,
            total_count=total_count,
        )
        return result
    except Exception as error:
        logger.error(
            f"An Internal Server Error Occured while listing the \
                      accounts. With the Error:- '{error}'"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An Internal Server Error Occured while listing the \
                accounts. With the Error:- '{error}'",
        )


@router.post(
    "/accounts/logo",
    response_model=UploadAccountLogoResponse,
    status_code=status.HTTP_201_CREATED,
)
def upload_logo_image(
    file: UploadFile = File(...),
    media_service: MediaService = Depends(deps.get_media_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> UploadAccountLogoResponse:
    """Upload logo for account."""
    try:
        image_id = media_service.upload_image(file, "accounts/logo")
        return UploadAccountLogoResponse(
            image_key=image_id,
            image_url=media_service.get_file_url(image_id),
        )
    except MediaError as err:
        raise HTTPException(status.HTTP_400_BAD_REQUEST, detail=str(err))


@router.get("/accounts/{account_id}/billing-settings", response_model=BillingSettings)
def get_billing_settings(
    account_id: int,
    account_service: AbstractAccountService = Depends(deps.get_account_service_proxy),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    try:
        bl_settings = account_service.get_billing_settings(account_id)
        return bl_settings
    except AccountDoesNotExist:
        raise HTTPException(status.HTTP_404_NOT_FOUND)


@router.put("/accounts/{account_id}/billing-settings", response_model=BillingSettings)
def set_billing_settings(
    account_id: int,
    billing_settings: BillingSettings,
    account_service: AbstractAccountService = Depends(deps.get_account_service_proxy),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    try:
        bl_settings = account_service.set_billing_settings(account_id, billing_settings)
        return bl_settings
    except AccountDoesNotExist:
        raise HTTPException(status.HTTP_404_NOT_FOUND)


@router.post("/accounts/{account_id}/invites")
@check_permissions
def invite_user(
    request: Request,
    account_id: int,
    user_invite: UserInvite,
    account_service: AbstractAccountService = Depends(deps.get_account_service_proxy),
    user_service: AbstractUserService = Depends(deps.get_user_service_proxy),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> str:
    try:
        logger.info(f"Account_id : {account_id}")
        account = account_service.get(account_id)
        return user_service.invite_user(account, user_invite)
    except AccountDoesNotExist:
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


@router.post(
    "/accounts/saf-event",
    status_code=status.HTTP_204_NO_CONTENT,
    response_class=Response,
)
def process_saf_event(
    saf_event: SAFEvent,
    user_service: AbstractUserService = Depends(deps.get_user_service_proxy),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    try:
        logger.critical(f"**Endpoint EVENT passed details:- {saf_event} ")
        user_service.accept_user_invite(saf_event)
    except (AccountDoesNotExist, UserNotFound, InviteAlreadyAccepted):
        pass
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


@router.get("/product-types", response_model=EnumerationList)
def product_types(
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    return EnumerationList.from_enum(ProductType)


@router.get("/industry-verticals", response_model=EnumerationList)
def industry_verticals(
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    return EnumerationList.from_enum(IndustryVertical)


@router.get("/sales-channels", response_model=EnumerationList)
def sales_channels(
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    return EnumerationList.from_enum(SalesChannel)


@router.get("/account-statuses", response_model=EnumerationList)
def account_statuses(
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    return EnumerationList.from_enum(AccountStatus)


@router.get("/account-names", response_model=Iterable[AccountNames])
@check_permissions
def get_account_names(
    request: Request,
    account_service: AbstractAccountService = Depends(deps.get_account_service_proxy),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> Iterable[dto.AccountNames]:
    return account_service.get_account_names()


@router.get("/accounts/{organization_id}/info", response_model=AccountId)
def view_account_org(
    organization_id: int,
    account_service: AbstractAccountService = Depends(deps.get_account_service_proxy),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    try:
        account = account_service.get_account_by_organization_id(organization_id)
        return account
    except OrganizationDoesNotExist:
        raise HTTPException(status.HTTP_404_NOT_FOUND)


@router.get(
    "/accounts/sim/cards/info/{search}",
    status_code=status.HTTP_200_OK,
    response_model=SimAccountInfoDetails,
)
@check_permissions
def get_sim_account_info(
    request: Request,
    search: IMSI | ICCID | MSISDN,
    account_service: AbstractAccountService = Depends(deps.get_account_service_proxy),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> model.SimAccountInfoDetails:
    "Search is applicable on IMSI, ICCID and MSISDN"
    try:
        response = account_service.get_sim_account_info(search=search)
        return response
    except SimAccountDataNotFound as e:
        logger.error(f"No sim data found error occurred: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="No Sim data found."
        )
    except Exception as e:
        logger.error(f"Couldn't process the request: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )


@router.get("/sim-profile", response_model=EnumerationList)
def account_sim_profile(
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    return EnumerationList.from_enum(SimProfile)


@router.get(
    "/accounts/{account_id}/summary",
    status_code=status.HTTP_200_OK,
    response_model=AccountSummary,
)
def get_account_summary(
    request: Request,
    account_id: int,
    account_service: AbstractAccountService = Depends(deps.get_account_service_proxy),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> model.AccountSummary:
    try:
        response = account_service.get_account_summary(account_id=account_id)
        return response
    except ForbiddenError as e:
        logger.error(f"Forbidden: {str(e)}")
        raise HTTPException(status.HTTP_403_FORBIDDEN, detail="Forbidden.")
    except Exception as e:
        logger.error(f"Couldn't process the request: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request."
        )
