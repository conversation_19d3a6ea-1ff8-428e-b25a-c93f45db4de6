from collections.abc import Generator
from functools import cache

from fastapi import Depends
from sqlalchemy.orm import Session

from accounts.adapters.repository import (
    DatabaseAccountRepository,
    DatabaseUserRepository,
    HTTPDigitalIdentity,
    HTTPOrganizationRepository,
    InMemoryAccountRepository,
    InMemoryDigitalIdentity,
    InMemoryOrganizationRepository,
)
from accounts.domain import dto
from accounts.domain.ports import (
    AbstractAccountRepository,
    AbstractDigitalIdentity,
    AbstractOrganizationRepository,
    AbstractUserRepository,
)
from accounts.proxies import AccountServiceAuthProxy, UserServiceAuthProxy
from accounts.services import (
    AbstractAccountService,
    AbstractUserService,
    AccountService,
    MediaService,
    UserService,
)
from api import deps
from api.resolvers import AccountStatisticResolver, Resolver
from api.sim.deps import sim_service as get_sim_service
from app.config import settings
from app.digital_identity import get_digital_identity_client
from app.platform import get_organizations_api_client
from auth.dto import AuthenticatedUser, Service
from auth.services import (
    AbstractAuthService,
    AccountAuthService,
    FakeAuthService,
    TokenIntrospectionAuthService,
)
from automation.adapters.repository import (
    AbstractAutomationRepository,
    DatabaseAutomationRepository,
)
from common.file_storage import AbstractFileStorage
from rate_plans.adapters.rate_plan_repository import (
    AbstractRatePlanRepository,
    DatabaseRatePlanRepository,
)
from sim.services import SimService


@cache
def in_memory_organization_repository() -> AbstractOrganizationRepository:
    return InMemoryOrganizationRepository()


def get_organization_repository(
    authorization_service: AbstractAuthService = Depends(deps.get_auth_service),
) -> Generator[AbstractOrganizationRepository, None, None]:
    auth_token: str | None
    match authorization_service:
        case TokenIntrospectionAuthService(token=_token):
            auth_token = _token
        case AccountAuthService(
            auth_service=TokenIntrospectionAuthService(token=_token)
        ):
            auth_token = _token
        case AccountAuthService(auth_service=FakeAuthService()) | FakeAuthService():
            auth_token = None
        case _:
            raise AssertionError(
                f"Unexpected auth service: {type(authorization_service)}"
            )

    if auth_token:
        with get_organizations_api_client(auth_token) as client:
            yield HTTPOrganizationRepository(client)
    else:
        yield in_memory_organization_repository()


@cache
def in_memory_account_repository() -> AbstractAccountRepository:
    return InMemoryAccountRepository()


def database_account_repository(
    session: Session = Depends(deps.get_db_session),
) -> AbstractAccountRepository:
    return DatabaseAccountRepository(session)


def get_media_service(
    storage: AbstractFileStorage = Depends(
        deps.get_file_storage_object(settings.S3_MEDIA_BUCKET_NAME)
    ),
) -> MediaService:
    return MediaService(storage)


def get_automation_repository(
    session: Session = Depends(deps.get_db_session),
) -> AbstractAutomationRepository:
    return DatabaseAutomationRepository(session)


def get_account_service(
    organization_repository: AbstractOrganizationRepository = Depends(
        get_organization_repository
    ),
    account_repository: AbstractAccountRepository = Depends(
        database_account_repository
    ),
    media_service: MediaService = Depends(get_media_service),
    user: AuthenticatedUser = Depends(deps.get_authenticated_user),
    automation_repository: AbstractAutomationRepository = Depends(
        get_automation_repository
    ),
) -> AbstractAccountService:
    account_service = AccountService(
        user,
        account_repository,
        organization_repository,
        media_service,
        automation_repository,
    )
    return account_service


def get_account_service_proxy(
    user: AuthenticatedUser = Depends(deps.get_authenticated_user),
    account_service: AbstractAccountService = Depends(get_account_service),
) -> AbstractAccountService:
    return AccountServiceAuthProxy(account_service, user)


def rate_plan_repository(
    session: Session = Depends(deps.get_db_session),
) -> AbstractRatePlanRepository:
    return DatabaseRatePlanRepository(session)


def account_statistic_resolver(
    sim_service: SimService = Depends(get_sim_service),
    plans_total: AbstractRatePlanRepository = Depends(rate_plan_repository),
) -> Resolver[dto.Account, dict[str, int]]:
    return AccountStatisticResolver(sim_service, plans_total)


def get_user_repository(
    session: Session = Depends(deps.get_db_session),
) -> AbstractUserRepository:
    return DatabaseUserRepository(session)


def get_digital_identity_repository() -> AbstractDigitalIdentity:
    if settings.APPLICATION_ENV == "local":
        return InMemoryDigitalIdentity()
    else:
        client = get_digital_identity_client()
        return HTTPDigitalIdentity(client)


def get_user_service(
    account_repository: AbstractAccountRepository = Depends(
        database_account_repository
    ),
    organization_repository: AbstractOrganizationRepository = Depends(
        get_organization_repository
    ),
    user_repository=Depends(get_user_repository),
    digital_identity_repository=Depends(get_digital_identity_repository),
) -> AbstractUserService:
    return UserService(
        account_repository=account_repository,
        organization_repository=organization_repository,
        user_repository=user_repository,
        digital_identity=digital_identity_repository,
    )


def get_user_service_proxy(
    actor: AuthenticatedUser | Service = Depends(deps.get_authenticated_actor),
    user_service: AbstractUserService = Depends(get_user_service),
) -> UserServiceAuthProxy:
    return UserServiceAuthProxy(user_service, actor)
