from typing import Any

from pydantic import AnyHttpUrl, Extra, Field, parse_obj_as

from accounts.domain import dto
from api.accounts.examples import (
    ACCOUNT_1,
    ACCOUNT_2,
    ACCOUNT_NAMES,
    BILLING_SETTINGS,
    SAF_EVENT,
    USER_INVITE,
)
from api.schema_types import CamelBaseModel
from common.types import ICCID, IMSI, MSISDN, SimStatus


class RatePlan(CamelBaseModel):
    id: int
    name: str


class Account(CamelBaseModel, dto.Account):
    sims_total: int
    active_sims_total: int
    plans_total: int
    users_total: int
    default_rate_plan: str | None

    class Config:
        schema_extra = {
            "example": ACCOUNT_1,
            "examples": [ACCOUNT_1, ACCOUNT_2],
        }

    @classmethod
    def from_dto(
        cls,
        account: dto.Account,
        sims_total: int,
        plans_total: int,
        active_sims_total: int,
        users_total: int,
        default_rate_plan: int | None = None,
    ) -> "Account":
        return cls(
            **account.dict(),
            sims_total=sims_total,
            active_sims_total=active_sims_total,
            plans_total=plans_total,
            users_total=users_total,
            default_rate_plan=default_rate_plan,
        )


class GetAccount(CamelBaseModel, dto.GetAccount):
    ...

    class Config:
        schema_extra = {
            "example": ACCOUNT_1,
            "examples": [ACCOUNT_1, ACCOUNT_2],
        }

    @classmethod
    def from_dto(
        cls,
        account: dto.GetAccount,
    ) -> "GetAccount":
        return cls(
            **account.dict(),
        )


parse_obj_as(list[Account], [ACCOUNT_1, ACCOUNT_2])


class CreateAccount(CamelBaseModel, dto.CreateAccount):
    ...


class UpdateAccount(CamelBaseModel, dto.UpdateAccount):
    class Config:
        extra = Extra.forbid


class UploadAccountLogoResponse(CamelBaseModel):
    image_key: str
    image_url: AnyHttpUrl


class BillingSettings(CamelBaseModel, dto.BillingSettings):
    class Config:
        schema_extra = {"example": BILLING_SETTINGS}


class UserInvite(CamelBaseModel, dto.UserInvite):
    class Config:
        schema_extra = {"example": USER_INVITE}


parse_obj_as(UserInvite, USER_INVITE)


class SAFEvent(CamelBaseModel, dto.SAFEvent):
    event: dict[str, Any]

    class Config:
        schema_extra = {"example": SAF_EVENT}


parse_obj_as(SAFEvent, SAF_EVENT)


class AccountNames(CamelBaseModel):
    id: int
    name: str
    organization_id: int

    class Config:
        schema_extra = {"example": ACCOUNT_NAMES}


class AccountId(CamelBaseModel):
    id: int
    name: str


class SimAccountDetails(CamelBaseModel):
    imsi: IMSI
    iccid: ICCID
    msisdn: MSISDN
    sim_status: SimStatus
    account_id: int
    account_name: str | None


class SimAccountInfoDetails(SimAccountDetails):
    account_logo_key: str | None
    account_logo_url: str | None


class AccountSummary(CamelBaseModel):
    account_sim_count: int | None = Field(alias="simCount", default=0)
    account_rate_plan_count: int | None = Field(alias="ratePlanCount", default=0)
    account_automation_rule_count: int | None = Field(
        alias="automationRuleCount", default=0
    )
