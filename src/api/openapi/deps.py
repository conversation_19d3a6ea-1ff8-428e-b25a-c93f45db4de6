from fastapi import Depends

from accounts.domain.ports import AbstractOrganizationRepository
from api.accounts.deps import get_organization_repository
from api.authorization.deps import get_authorization_client
from api.deps import get_authenticated_user
from auth.dto import AuthenticatedUser
from authorization.domain.ports import AbstractAuthorizationAPI
from openapi.proxies import OpenApiServiceAuthProxy
from openapi.services import AbstractOpenApiAuthService, OpenApiAuthService


def openapi_auth_service(
    authorization: AbstractAuthorizationAPI = Depends(get_authorization_client),
    organization_repository: AbstractOrganizationRepository = Depends(
        get_organization_repository
    ),
    authenticated_user: AuthenticatedUser = Depends(get_authenticated_user),
) -> AbstractOpenApiAuthService:
    return OpenApiServiceAuthProxy(
        openapi_service=OpenApiAuthService(
            authorization=authorization,
            organization_repository=organization_repository,
        ),
        user=authenticated_user,
    )
