from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from platform_api_client import PlatformAPIError

import api.deps as trace_id_deps
from api.openapi import deps
from app.config import logger
from auth.exceptions import ForbiddenError
from openapi.domain import model
from openapi.exceptions import APIError, NotFound, OpenAPIUnauthorized
from openapi.services import OpenApiAuthService

router = APIRouter(tags=["openapi"], prefix="/v1")
token_router = APIRouter(tags=["openapi"])


@token_router.get(
    "/api/token",
    status_code=status.HTTP_200_OK,
    response_model=model.TokenResponse,
)
def generate_access_token(
    openapi_auth_service: OpenApiAuthService = Depends(deps.openapi_auth_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> model.TokenResponse:
    try:
        return openapi_auth_service.generate_access_token()
    except OpenAPIUnauthorized as e:
        logger.error(f"{e}")
        raise HTTPException(status.HTTP_401_UNAUTHORIZED, "Invalid user credentials.")
    except NotFound as e:
        logger.error(f"{e}")
        raise HTTPException(status.HTTP_404_NOT_FOUND, "Internal API Not Found Error.")
    except APIError as e:
        logger.error(f"{e}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, "Internal API Bad request error."
        )
    except PlatformAPIError as e:
        logger.error(f"{e}")
        raise HTTPException(status.HTTP_400_BAD_REQUEST, "External API error.")
    except (ValueError, Exception) as e:
        logger.error(f"{e}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, "We are unable to process your request."
        )
    except ForbiddenError as e:
        logger.error(f"No Scope Found.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Forbidden",
        )
