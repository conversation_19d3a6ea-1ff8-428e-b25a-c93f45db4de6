from pydantic import Field

from api.openapi.examples import GET_TOKEN_RESPONSE
from api.schema_types import CamelBaseModel


class TokenResponse(CamelBaseModel):
    access_token: str
    expires_in: str

    class Config:
        schema_extra = {"example": GET_TOKEN_RESPONSE}


class TokenRequest(CamelBaseModel):
    username: str
    password: str
    client_id: str | None = Field(readOnly=True)
    client_secret: str | None = Field(readOnly=True)
