from uuid import UUID

from pydantic import EmailStr

from automation.domain import model
from common.types import RULE_NAME, Unit

RULE_RESPONSE = {"uuid": "bdb5365e-40c1-4902-b8d2-55a13a72d3ea"}
RULE_ACTION = {
    "reactivateSim": {"value": "Next billing cycle"},
    "ratePlan": {
        "source": 12,
        "target": 15,
        "value": "Return to the previous rate plan",
    },
}

RULE_NOTIFICATION = {
    "id": 1,
    "notification": True,
    "notificationValue": ["<EMAIL>"],
}

RULE_REQUEST = {
    "accountId": [1],
    "ruleTypeId": 1,
    "ruleCategoryId": 1,
    "ruleName": "Rule1",
    "ruleDefinitionId": 1,
    "dataVolume": 40005,
    "unit": Unit.KB,
    "status": True,
    "lock": False,
    "action": RULE_ACTION,
    "notification": [RULE_NOTIFICATION],
}

RULES_MODEL = {
    "uuid": "3b994fa3-0037-43c6-90de-b35891e2b225",
    "account_id": 1,
    "rule_type_id": 1,
    "rule_category_id": 1,
    "rule_definition_id": 1,
    "rule_name": "Rule1",
    "data_volume": 40005,
    "unit": Unit.KB,
    "status": True,
    "id": None,
    "created_by": "<EMAIL>",
    "ip_address": "127.0.0.1",
}

RULE_ACTION_MODEL = {"actions_id": 1, "rule_action": "Deactivate"}
RULE_NOTIFICATION_MODEL = {"notifications_id": 1, "rule_notification": True}

ACTIONS = {
    "id": 1,
    "actionName": "Block",
}

NOTIFICATIONS = {
    "id": 1,
    "notificationName": "Email",
}


ACTION = {
    "name": "Deactivate SIM",
    "action": "Reactive Sim",
    "actionValue": "Next billing cycle",
}

NOTIFICATION = {"email": ["<EMAIL>"], "sms": [], "push": []}

RULE_DETAIL_1 = {
    "id": 1,
    "uuid": "bdb5365e-40c1-4902-b8d2-55a13a72d3ea",
    "account_id": 1,
    "rule_type": "Usage Monitoring",
    "rule_category": "Cycle to date data usage",
    "rule_name": "Rule1",
    "rule_definition": "Data usage exceeds a specified limit",
    "data_volume": 400000,
    "unit": "KB",
    "status": True,
    "lock": False,
}

RULE_DETAIL = {
    "uuid": "bdb5365e-40c1-4902-b8d2-55a13a72d3ea",
    "accountId": 5,
    "accountName": "BT_Test_Account",
    "logoKey": "accounts/logo/867a7acb4f25442dbbcf092c12e251e1.png",
    "logoUrl": "https://lr0oecnqlfha.compat.objectstorage.uk-london-1.oraclecloud.com",
    "ruleCategory": "Usage Monitoring",
    "ruleName": "Cycle to date data usage",
    "ruleDefinition": "Data usage exceeds a specified limit",
    "dataVolume": 400000,
    "ruleUnit": "KB",
    "status": True,
    "lock": False,
    "action": [ACTION],
    "notification": [NOTIFICATION],
}


ACTION_MODEL = model.Action(
    name="Deactivate SIM", action="Reactive Sim", action_value="Next billing cycle"
)
NOTIFICATION_MODEL = model.Notification(name="Send Email", notification=True)

NOTIFICATION_CHANNELS = model.NotificationChannels(
    email=[EmailStr("<EMAIL>")], sms=["**********"], push=[]
)

RULE_BY_UUID_RULE_TYPE = model.RulesTypeDetails(id=1, rule_type="Usage Monitoring")

RULE_BY_UUID_RULE_CATEGORY = model.RulesCategoryDetails(
    id=1, rule_category="Cycle To Date Data Usage"
)

RULE_BY_UUID_RULE_DEFINITIION = model.RulesDefinitionDetails(
    id=1, rule_definition="Data usage exceeds a specified limit"
)

RULE_BY_UUID_ACTION = {
    "name": "Deactivate SIM",
    "action": "Reactive Sim",
    "action_value": "Next billing cycle",
}

RULE_DETAIL_MODEL = model.RuleDetailsResponse(
    id=1,
    uuid=UUID("bdb5365e-40c1-4902-b8d2-55a13a72d3ea"),
    account_id=1,
    rule_name=RULE_NAME("Rule1"),
    data_volume=400000,
    unit=Unit.KB,
    status=True,
    lock=False,
    action=[ACTION_MODEL],
    notification=[NOTIFICATION_MODEL],
    rule_type=RULE_BY_UUID_RULE_TYPE,
    rule_category=RULE_BY_UUID_RULE_CATEGORY,
    rule_definition=RULE_BY_UUID_RULE_DEFINITIION,
)

RULE_TYPE = {
    "id": 1,
    "ruleCategory": "Usage Monitoring",
}

RULE_CATEGORY = {
    "id": 1,
    "ruleType": "Usage Monitoring",
    "category": " Cycle To Date Data Usage",
    "rule_type_id": 1,
}

RULE_DEFINITION = {
    "id": 1,
    "definition": "Data usage exceeds a specified limit",
    "category": "Cycle To Date Data Usage",
}

RULE_STATUS = {
    "uuid": "3b994fa3-0037-43c6-90de-b35891e2b225",
    "status": True,
}

RULE_BY_UUID = {
    "id": 1,
    "uuid": UUID("bdb5365e-40c1-4902-b8d2-55a13a72d3ea"),
    "account_id": 1,
    "rule_name": "Rule1",
    "data_volume": 400000,
    "unit": Unit.KB,
    "status": True,
    "lock": False,
    "rule_type": RULE_BY_UUID_RULE_TYPE,
    "rule_category": RULE_BY_UUID_RULE_CATEGORY,
    "rule_definition": RULE_BY_UUID_RULE_DEFINITIION,
}

LOCK = {
    "uuid": "e4e88d44-f534-4ee9-a31d-3284fee3b058",
    "status": True,
}


RULE_RELY = {
    "dataVolume": False,
    "dataUnit": False,
    "smsUnit": False,
    "voiceUnit": False,
    "threshold": True,
    "dataPercentage": False,
    "voiceMomtPercentage": False,
    "voiceMoPercentage": False,
    "voiceMtPercentage": False,
}
ACTION_RELY = {
    "viewDeactivateSim": True,
    "requiredDeactivateSim": False,
    "viewRatePlanChange": True,
    "requiredRatePlanChange": True,
}

NOTIFICATION_RELY = {"email": True, "sms": False, "push": False}

RULE_RESPONSE_RELY = {
    "id": 1,
    "rules": RULE_RELY,
    "action": ACTION_RELY,
    "notification": NOTIFICATION_RELY,
}

RULE_REQUEST_RELY = {
    "ruleDefinitionId": 1,
    "dataVolume": False,
    "dataUnit": False,
    "smsUnit": False,
    "voiceUnit": False,
    "threshold": True,
    "dataPercentage": True,
    "viewDeactivateSim": True,
    "requiredDeactivateSim": False,
    "viewRatePlanChange": True,
    "requiredRatePlanChange": True,
    "addAnyRatePlan": False,
    "isMonthlyPool": False,
    "viewEmail": True,
    "viewSms": False,
    "viewPush": False,
    "voiceMomtPercentage": False,
    "voiceMoPercentage": False,
    "voiceMtPercentage": False,
    "smsPercentage": False,
    "data": True,
    "voiceMo": False,
    "voiceMt": False,
    "sms": False,
}
