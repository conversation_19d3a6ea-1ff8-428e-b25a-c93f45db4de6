from fastapi import Depends
from sqlalchemy.orm import Session

from accounts.domain.dto import Account
from accounts.services import AccountService
from api.accounts.deps import get_account_service_proxy
from api.deps import get_authenticated_user
from api.deps import get_db_session as _get_db_session
from api.resolvers import AccountResolver, FakeAccountResolver, Resolver
from auth.dto import AuthenticatedUser
from automation.adapters.repository import (
    AbstractAutomationRepository,
    DatabaseAutomationRepository,
)
from automation.proxies import AutomationServiceProxy
from automation.services import AbstractAutomationService, AutomationService


def get_automation_repository(
    session: Session = Depends(_get_db_session),
) -> AbstractAutomationRepository:
    return DatabaseAutomationRepository(session)


def automation_service(
    automation_repository: AbstractAutomationRepository = Depends(
        get_automation_repository
    ),
    authenticated_user: AuthenticatedUser = Depends(get_authenticated_user),
) -> AbstractAutomationService:
    return AutomationServiceProxy(
        automation_service=AutomationService(
            automation_repository,
        ),
        user=authenticated_user,
    )


def account_resolver(
    account_service: AccountService = Depends(get_account_service_proxy),
) -> Resolver[int, Account]:
    return AccountResolver(account_service)


def fake_account_resolver() -> Resolver[int, Account]:
    return FakeAccountResolver()
