import logging
from collections.abc import Generator
from functools import cache

from fastapi import Depends
from sqlalchemy.orm import Session

from api.deps import get_auth_service, get_authenticated_user
from api.deps import get_db_session as _get_db_session
from app.platform import get_auditlog_api_client
from audit.proxies import AuditServiceAuthProxy
from audit.services import AbstractInternalAuditService, AuditService
from auth.dto import AuthenticatedUser
from auth.services import (
    AbstractAuthService,
    AccountAuthService,
    FakeAuthService,
    TokenIntrospectionAuthService,
)
from sim.adapters.externalapi import AuditServiceAPI, FakeAuditServiceAPI
from sim.adapters.repository import AbstractSimRepository, DatabaseSimRepository


def _sim_repository(
    session: Session = Depends(_get_db_session),
) -> AbstractSimRepository:
    return DatabaseSimRepository(session)


@cache
def fake_audit_service_api():
    return FakeAuditServiceAPI()


def get_audit_service_client(
    authorization_service: AbstractAuthService = Depends(get_auth_service),
) -> Generator[AuditServiceAPI, None, None]:
    logging.error("Re_allocation_auth service")
    auth_token: str | None
    match authorization_service:
        case TokenIntrospectionAuthService(token=_token):
            auth_token = _token
        case AccountAuthService(
            auth_service=TokenIntrospectionAuthService(token=_token)
        ):
            auth_token = _token
        case AccountAuthService(auth_service=FakeAuthService()) | FakeAuthService():
            auth_token = None
        case _:
            raise AssertionError(
                f"Unexpected auth service: {type(authorization_service)}"
            )
    logging.error(f"Re_allocation_auth token: {auth_token}")
    if auth_token:
        with get_auditlog_api_client(auth_token) as client:
            yield AuditServiceAPI(client)
    else:
        yield fake_audit_service_api()


def audit_service(
    sim_repository: AbstractSimRepository = Depends(_sim_repository),
    authenticated_user: AuthenticatedUser = Depends(get_authenticated_user),
    audit_service: AuditServiceAPI = Depends(get_audit_service_client),
) -> AbstractInternalAuditService:
    return AuditServiceAuthProxy(
        audit_service=AuditService(sim_repository, audit_service),
        user=authenticated_user,
    )
