from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Request, status

import api.deps as trace_id_deps
from api.audit import deps
from api.audit.schemas import SIMCardAccountAuditLogs
from api.authorization.deps import authorization_service
from api.decorators import check_permissions
from api.schema_types import PaginatedResponse
from app.config import logger
from audit.exceptions import NoAuditLogs
from audit.services import AbstractInternalAuditService
from auth.exceptions import Unauthorized
from authorization.domain.ports import AbstractAuthorizationAPI
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching

router = APIRouter(tags=["audit"])


@check_permissions
def system_audit_logs(
    request: Request,
    account_id: int,
    audit_service: AbstractInternalAuditService = Depends(deps.audit_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    pagination: Pagination = Depends(Pagination.query()),
    ordering: Ordering = Depends(
        Ordering.query(
            SIMCardAccountAuditLogs,
            default_ordering="-createdDate",
            ordering_fields=(
                "imsi",
                "iccid",
                "msisdn",
                "requestType",
                "priorValue",
                "newValue",
                "field",
                "action",
                "clientIp",
                "createdBy",
                "createdDate",
            ),
        )
    ),
    searching: Searching
    | None = Depends(
        Searching.build(
            fields={
                "imsi",
                "iccid",
                "msisdn",
                "request_type",
                "prior_value",
                "new_value",
                "field",
                "action",
                "client_ip",
                "created_by",
            },
        )
    ),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> PaginatedResponse[SIMCardAccountAuditLogs]:
    try:
        records, total_count = audit_service.system_audit(
            account_id=account_id,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        return PaginatedResponse.from_iterable(
            pagination=pagination,
            results=records,  # type: ignore
            total_count=total_count,
        )
    except Unauthorized as e:
        logger.error(f"Unauthorized.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access."
        )
    except NoAuditLogs as e:
        logger.error(f"No audit logs error occoured.:{str(e)} ")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="No audit logs found."
        )
    except ValueError as e:
        logger.error(f"value error occoured.:{str(e)} ")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Couldn't process your request",
        )
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


router.get(
    "/audit/{account_id}",
    status_code=status.HTTP_200_OK,
    response_model=PaginatedResponse[SIMCardAccountAuditLogs],
)(system_audit_logs)
