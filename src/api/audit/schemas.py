from api.schema_types import IMSI, CamelBaseModel
from audit.domain import model

# from common.types import ICCID, MSISDN


class SIMCardAccountAuditLogs(CamelBaseModel):
    id: str
    imsi: IMSI
    request_type: str
    field: str
    action: str
    iccid: str | None
    msisdn: str | None
    prior_value: str | None
    new_value: str | None
    client_ip: str | None = None
    created_by: str | None = None
    created_date: str | None = None

    @classmethod
    def from_model(
        cls, audit_logs: model.SIMCardAccountAuditLogs
    ) -> "SIMCardAccountAuditLogs":
        return cls(
            id=audit_logs.id,
            imsi=audit_logs.imsi,
            iccid=audit_logs.iccid,
            msisdn=audit_logs.msisdn,
            request_type=audit_logs.request_type,
            prior_value=audit_logs.prior_value,
            new_value=audit_logs.new_value,
            field=audit_logs.field,
            action=audit_logs.action,
            client_ip=audit_logs.client_ip,
            created_by=audit_logs.created_by,
            created_date=audit_logs.created_date,
        )
