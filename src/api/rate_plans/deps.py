import itertools
import random
import secrets
from functools import cache

from _decimal import Decimal
from fastapi import Depends
from sqlalchemy.orm import Session

from accounts.domain.dto import Account
from accounts.services import AccountService
from api import deps
from api.accounts.deps import get_account_service_proxy
from api.deps import get_authenticated_user, get_db_session
from api.resolvers import AccountResolver, FakeAccountResolver, Resolver
from app.config import settings
from auth.dto import AuthenticatedUser
from common.file_storage import AbstractFileStorage
from rate_plans.adapters.rate_plan_repository import (
    AbstractRatePlanRepository,
    DatabaseRatePlanRepository,
    InMemoryRatePlanRepository,
)
from rate_plans.proxies import RatePlanServiceAuthProxy
from rate_plans.services import (
    AbstractRatePlanService,
    FakeMediaService,
    MediaService,
    RatePlanService,
)
from sim.adapters.repository import AbstractSimRepository, DatabaseSimRepository


def get_rate_plan_repository(
    session: Session = Depends(get_db_session),
) -> AbstractRatePlanRepository:
    return DatabaseRatePlanRepository(session)


def get_sim_repository(
    session: Session = Depends(get_db_session),
) -> AbstractSimRepository:
    return DatabaseSimRepository(session)


def get_media_service(
    storage: AbstractFileStorage = Depends(
        deps.get_file_storage_object(settings.S3_MEDIA_BUCKET_NAME)
    ),
) -> MediaService:
    return MediaService(storage)


def rate_plan_service(
    rate_plan_repository: AbstractRatePlanRepository = Depends(
        get_rate_plan_repository
    ),
    media_service: MediaService = Depends(get_media_service),
    authenticated_user: AuthenticatedUser = Depends(get_authenticated_user),
    sim_repository: AbstractSimRepository = Depends(get_sim_repository),
) -> AbstractRatePlanService:
    return RatePlanServiceAuthProxy(
        rate_plan_service=RatePlanService(
            rate_plan_repository,
            media_service,
            sim_repository,
        ),
        user=authenticated_user,
    )


@cache
def _in_memory_rate_plan_repository() -> AbstractRatePlanRepository:
    """Generate data for working without DB"""
    from rate_plans.domain.model import RatePlan

    account_ids: list[int] = random.sample(list(range(1, 6)), k=5)  # nosec B311
    return InMemoryRatePlanRepository(
        [
            RatePlan(
                _id=id_,
                account_id=account_id,
                name=f"Rate Plan {id_}",
                sim_limit=0,
                is_default=False,
                access_fee=Decimal(secrets.randbelow(100) + 1 / 1000),
            )
            for id_, account_id in itertools.product(range(1, 6), account_ids)
        ]
    )


def account_resolver(
    account_service: AccountService = Depends(get_account_service_proxy),
) -> Resolver[int, Account]:
    return AccountResolver(account_service)


def fake_account_resolver() -> Resolver[int, Account]:
    return FakeAccountResolver()


def fake_media_service() -> FakeMediaService:
    return FakeMediaService()
