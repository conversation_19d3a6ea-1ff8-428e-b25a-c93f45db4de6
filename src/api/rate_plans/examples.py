"""Examples for OpenAPI schemas"""
from copy import deepcopy
from decimal import Decimal
from typing import Any

from common.constants import FIXED, FLEXI, INDI, PAYG

RATE_PLANS_BY_ACCOUNTS = [
    {
        "accountId": 1,
        "accountName": "Tesla",
        "accountLogoUrl": "https://example.com/icon",
        "ratePlans": [
            {
                "id": 1,
                "name": "PAYG",
                "accessFee": 2.33,
                "currency": "GBP",
                "isDefault": True,
            },
            {
                "id": 2,
                "name": "Plan B",
                "accessFee": 0.03,
                "currency": "GBP",
                "isDefault": False,
            },
            {
                "id": 3,
                "name": "Plan C",
                "accessFee": 0.02,
                "currency": "GBP",
                "isDefault": False,
            },
        ],
    },
    {
        "accountId": 2,
        "accountName": "Company 1",
        "accountLogoUrl": "https://example.com/icon",
        "ratePlans": [
            {
                "id": 5,
                "name": "Test Plan 1",
                "accessFee": 0.33,
                "currency": "GBP",
                "isDefault": True,
            },
            {
                "id": 6,
                "name": "Test Plan 2",
                "accessFee": 0.15,
                "currency": "GBP",
                "isDefault": False,
            },
            {
                "id": 7,
                "name": "Test Plan 3",
                "accessFee": 0.25,
                "currency": "GBP",
                "isDefault": False,
            },
            {
                "id": 8,
                "name": "Test Plan 4",
                "accessFee": 0.12,
                "currency": "GBP",
                "isDefault": False,
            },
        ],
    },
    {
        "accountId": 3,
        "accountName": "Kyivstar",
        "accountLogoUrl": "https://example.com/icon",
        "ratePlans": [
            {
                "id": 9,
                "name": "Plan A",
                "accessFee": 0.03,
                "currency": "GBP",
                "isDefault": True,
            }
        ],
    },
]

RATE_PLAN_DETAILS = [
    {
        "id": 1,
        "name": "PAYG",
        "access_fee": 2.33,
        "currency": "GBP",
        "is_default": True,
        "allowance_used": Decimal("5.09"),
    },
    {
        "id": 2,
        "name": "Plan B",
        "access_fee": 0.03,
        "currency": "GBP",
        "is_default": False,
        "allowance_used": Decimal("5.09"),
    },
    {
        "id": 3,
        "name": "Plan C",
        "access_fee": 0.02,
        "currency": "GBP",
        "is_default": False,
        "allowance_used": Decimal("5.09"),
    },
]

ACCOUNT_RATE_PLAN = {
    "account_id": 1,
    "account_name": "Tesla",
    "logo_url": "https://example.com/icon",
    "rate_plans": RATE_PLAN_DETAILS,
}

CREATE_RATE_PLAN_REQUEST = {
    "accountId": 1,
    "name": "Pay as You Go plan",
    "accessFee": 12.03,
    "currency": "GBP",
    "isDefault": False,
    "simLimit": 100,
    "allowanceUsed": 50,
    "originationGroups": [
        {
            "originationZones": ["UK"],
            "data": {
                "rateModel": 1,
                "rates": [
                    {
                        "rangeFrom": 0,
                        "rangeTo": 1024,
                        "value": 0.21,
                        "rangeUnit": "MB",
                        "priceUnit": "GB",
                        "overageFee": None,
                        "overageUnit": None,
                        "isoverage": False,
                        "overagePer": None,
                    },
                    {
                        "rangeFrom": 1024,
                        "rangeTo": None,
                        "value": 1.22,
                        "rangeUnit": "MB",
                        "priceUnit": "GB",
                        "overageFee": None,
                        "overageUnit": None,
                        "isoverage": False,
                        "overagePer": None,
                    },
                ],
            },
            "voiceMo": {
                "rateModel": 2,
                "rates": [
                    {
                        "rangeFrom": 0,
                        "rangeTo": 10240,
                        "value": 12.03,
                        "rangeUnit": "Min",
                        "priceUnit": "Min",
                        "overageFee": 5.26,
                        "overageUnit": "Min",
                        "isoverage": True,
                        "overagePer": 60,
                    }
                ],
            },
            "voiceMt": {
                "rateModel": 3,
                "rates": [
                    {
                        "rangeFrom": 0,
                        "rangeTo": 10241,
                        "value": 3.24,
                        "rangeUnit": "Min",
                        "priceUnit": "Min",
                        "overageFee": 6.27,
                        "overageUnit": "Min",
                        "isoverage": False,
                        "overagePer": 60,
                    }
                ],
            },
            "sms": {
                "rateModel": 4,
                "rates": [
                    {
                        "rangeFrom": 0,
                        "rangeTo": 10242,
                        "value": 12.03,
                        "rangeUnit": "SMS",
                        "priceUnit": "SMS",
                        "overageFee": 7.28,
                        "overageUnit": "SMS",
                        "isoverage": True,
                        "overagePer": 100,
                    }
                ],
            },
        }
    ],
}


RATE_PLAN_REQUEST: dict[str, Any] = {
    "id": 1,
    **deepcopy(CREATE_RATE_PLAN_REQUEST),
}


GET_RATE_PLAN_MODELS = [
    {"title": "Pay As You Go", "value": 1, "rate_model_code": PAYG},
    {"title": "Individual Plan", "value": 2, "rate_model_code": INDI},
    {"title": "Fixed Pool", "value": 3, "rate_model_code": FIXED},
    {"title": "Flexible Pool", "value": 4, "rate_model_code": FLEXI},
]

CREATE_UPDATE_RATE_PLAN_MODEL = {
    "title": "Individual Plan",
    "rate_model_code": INDI,
}

CREATE_UPDATE_RATE_MODEL = {**CREATE_UPDATE_RATE_PLAN_MODEL, "value": 6}  # type: ignore

RATE_PLAN_RESPONSE = {
    "accountId": 1,
    "name": "FIXED 202501",
    "accessFee": 12.03,
    "currency": "GBP",
    "isDefault": False,
    "simLimit": 100,
    "allowanceUsed": 0,
    "originationGroups": [
        {
            "originationZones": ["UK"],
            "data": {
                "rateModel": 3,
                "rates": [
                    {
                        "rangeFrom": 0,
                        "rangeTo": 500,
                        "value": 12.03,
                        "rangeUnit": "KB",
                        "priceUnit": "KB",
                        "overageFee": 5.26,
                        "overageUnit": "KB",
                        "isoverage": False,
                        "overagePer": 5,
                    }
                ],
            },
            "voiceMo": {
                "rateModel": 2,
                "rates": [
                    {
                        "rangeFrom": 0,
                        "rangeTo": 10240,
                        "value": 12.03,
                        "rangeUnit": "Min",
                        "priceUnit": "Min",
                        "overageFee": 5.26,
                        "overageUnit": "Min",
                        "isoverage": True,
                        "overagePer": 60,
                    }
                ],
            },
            "voiceMt": {
                "rateModel": 3,
                "rates": [
                    {
                        "rangeFrom": 0,
                        "rangeTo": 10241,
                        "value": 3.24,
                        "rangeUnit": "Min",
                        "priceUnit": "Min",
                        "overageFee": 6.27,
                        "overageUnit": "Min",
                        "isoverage": False,
                        "overagePer": 60,
                    }
                ],
            },
            "sms": {
                "rateModel": 4,
                "rates": [
                    {
                        "rangeFrom": 0,
                        "rangeTo": 10242,
                        "value": 12.03,
                        "rangeUnit": "SMS",
                        "priceUnit": "SMS",
                        "overageFee": 7.28,
                        "overageUnit": "SMS",
                        "isoverage": True,
                        "overagePer": 100,
                    }
                ],
            },
        }
    ],
    "id": 202,
}
