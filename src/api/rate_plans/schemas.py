import logging
from decimal import Decimal
from typing import Any, Iterable, Protocol

from pydantic import (
    AnyHttpUrl,
    ConstrainedStr,
    Field,
    NonNegativeInt,
    parse_obj_as,
    root_validator,
    validator,
)

from api import validators
from api.rate_plans.examples import (
    CREATE_RATE_PLAN_REQUEST,
    RATE_PLAN_REQUEST,
    RATE_PLANS_BY_ACCOUNTS,
)
from api.schema_types import CamelBaseModel, CountryCode, CurrencyCode
from common.constants import RATE_PLAN_FIELD
from common.types import Service
from rate_plans.domain import model


class CountryGroup(ConstrainedStr):
    min_length = 2
    max_length = 4
    to_upper = True

    @classmethod
    def __modify_schema__(cls, field_schema: dict[str, Any]) -> None:
        super().__modify_schema__(field_schema)
        # __modify_schema__ should mutate the dict it receives in place,
        # the returned value will be ignored
        field_schema.update(
            title="Country Group",
            examples=["UK", "EU27", "USA"],
        )


class RatePlanAccountView(CamelBaseModel):
    id: int
    name: str
    access_fee: Decimal = Field(decimal_places=2)
    currency: CurrencyCode
    is_default: bool
    allowance_used: Decimal | None = Field(default=Decimal("0.00"), decimal_places=2)
    sim_limit: int | None = None
    rate_plan_model: int | None = None

    @classmethod
    def from_domain(cls, rate_plan: model.RatePlans) -> "RatePlanAccountView":
        return cls(
            id=rate_plan.id,
            name=rate_plan.name,
            access_fee=rate_plan.access_fee,
            currency=CurrencyCode("GBP"),
            is_default=rate_plan.is_default,
            allowance_used=rate_plan.allowance_used,
            sim_limit=rate_plan.sim_limit,
            rate_plan_model=rate_plan.rate_plan_model,
        )


class Account(Protocol):
    id: int
    name: str
    logo_url: AnyHttpUrl | None


class Rate(model.Rate, CamelBaseModel):
    _check_range_to = validator("range_to", allow_reuse=True)(validators.check_range_to)


class RateGroup(CamelBaseModel):
    rate_model: int
    model_code: str | None = None
    allowance_used: Decimal = Field(default=Decimal("0.00"), decimal_places=2)
    rates: list[Rate] = Field(min_items=1)
    # _check_ranges = validator("rates", allow_reuse=True)(validators.check_ranges)

    @validator("rates")
    def check_rates_based_on_model(cls, v, values):
        rate_model = values.get("rate_model")

        # Only apply range validation if rate_model == 1
        if rate_model == 1:
            validators.check_ranges(v)  # Call your check_ranges function
            for ratemodel in v:
                if ratemodel.isoverage:
                    raise ValueError("isoverage must be False when rate_model is 1.")
                if ratemodel.value.as_tuple().exponent < -5:
                    raise ValueError(
                        "The 'value' field cannot have more than 5 "
                        f"decimal places: {ratemodel.value}"
                    )
        # elif rate_model != 1:
        else:
            if len(v) != 1:
                raise ValueError("The 'rates' field must contain exactly one rate.")
            for ratemodel in v:
                if ratemodel.overage_fee.as_tuple().exponent < -5:
                    raise ValueError(
                        "The 'overage_fee' field cannot have more than 5 "
                        f"decimal places: {ratemodel.overage_fee}"
                    )
        return v


class OriginationGroup(CamelBaseModel):
    origination_zones: set[CountryGroup | CountryCode] = Field(min_items=1)
    data: RateGroup
    voice_mo: RateGroup
    voice_mt: RateGroup
    sms: RateGroup


class RatePlanDetails(CamelBaseModel):
    account_id: int
    name: str = Field(max_length=60)
    access_fee: Decimal = Field(decimal_places=2)
    currency: CurrencyCode
    is_default: bool = False
    sim_limit: NonNegativeInt | None
    allowance_used: Decimal | None = Field(default=Decimal("0.00"), decimal_places=2)
    origination_groups: list[OriginationGroup] = Field(min_items=1)


class CreateRatePlanRequest(RatePlanDetails):
    @validator("name")
    def validate_name(cls, value):
        # Check if the name contains only spaces
        if value is None or value == "":
            raise ValueError("Rate plan name cannot be none or empty.")

        # Check if the name contains only spaces
        if value.isspace():
            raise ValueError("Rate plan name cannot consist of only spaces.")
        return value.strip()

    @root_validator(pre=True)
    def validate_values_equal_access_fee(cls, values):
        access_fee = values.get("accessFee")
        origination_groups = values.get("originationGroups", [])
        if any(
            group.get(field, {}).get("rateModel") == 3
            and access_fee in [0, Decimal("0.00")]
            for group in origination_groups
            for field in RATE_PLAN_FIELD
        ):
            return values

        for group in origination_groups:
            for field in RATE_PLAN_FIELD:
                # Get the rate group if it exists and isn't using rateModel 1 or 3
                rate_group = group.get(field)
                if rate_group and rate_group.get("rateModel") not in [1, 3]:
                    rates = rate_group.get("rates", [])
                    # Check each rate's value directly
                    for rate in rates:
                        if rate.get("value") != access_fee:
                            raise ValueError(
                                f"Value in {field} rates value must be "
                                "equal to access_fee."
                            )
        return values

    def to_model(self) -> model.RatePlan:
        rate_plan = model.RatePlan(
            account_id=self.account_id,
            name=self.name,
            access_fee=self.access_fee,
            is_default=self.is_default,
            sim_limit=self.sim_limit,
        )
        og = self.origination_groups[0]

        rate_plan.voice_mo = og.voice_mo  # type: ignore
        rate_plan.voice_mt = og.voice_mt  # type: ignore
        rate_plan.sms_mo = og.sms  # type: ignore
        rate_plan.data = og.data  # type: ignore
        return rate_plan

    class Config:
        schema_extra = {"example": CREATE_RATE_PLAN_REQUEST}


parse_obj_as(CreateRatePlanRequest, CREATE_RATE_PLAN_REQUEST)


class CreateRatePlanResponse(CreateRatePlanRequest):
    id: int
    is_default: bool = False


class UpdateRatePlanRequest(CreateRatePlanResponse):
    def to_model(self) -> model.RatePlan:
        rate_plan = super().to_model()
        rate_plan.id = self.id
        return rate_plan

    class Config:
        schema_extra = {"example": RATE_PLAN_REQUEST}


class RatePlanResponse(RatePlanDetails):
    id: int


class RatePlanView(RatePlanResponse):
    account_name: str
    # rate_plan_model: int | None = None

    @classmethod
    def from_model(
        cls, rate_plan: model.RatePlan, is_default: bool = False
    ) -> "RatePlanView":
        def get_rate_model_id(service_type):
            for rate_group in rate_plan._rate_groups:
                if service_type in rate_group.services:
                    return rate_group.rate_model_id
            return None

        og = OriginationGroup(
            origination_zones={CountryGroup("UK")},
            data=RateGroup(
                rate_model=get_rate_model_id(Service.DATA), rates=rate_plan.data
            ),
            voice_mo=RateGroup(
                rate_model=get_rate_model_id(Service.VOICE_MO), rates=rate_plan.voice_mo
            ),
            voice_mt=RateGroup(
                rate_model=get_rate_model_id(Service.VOICE_MT), rates=rate_plan.voice_mt
            ),
            sms=RateGroup(
                rate_model=get_rate_model_id(Service.SMS_MO), rates=rate_plan.sms_mo
            ),
        )
        return cls(
            id=rate_plan.id,
            account_id=rate_plan.account_id,
            name=rate_plan.name,
            access_fee=rate_plan.access_fee,
            currency=CurrencyCode("GBP"),
            sim_limit=rate_plan.sim_limit,
            origination_groups=[og],
            is_default=rate_plan.is_default,
            account_name=f"account-{rate_plan.account_id}",
        )

    @classmethod
    def from_model_rate_plan(cls, rate_plan: model.RatePlans) -> "RatePlanView":
        logging.info(f"*****Rate_plan_id: {rate_plan.id}")

        def get_rate_model_id(service_type):
            for rate_group in rate_plan._rate_groups:
                if service_type in rate_group.services:
                    return (
                        rate_group.rate_model_id,
                        rate_group.rate_model_code,
                        rate_group.allowance_used,
                    )
            return None

        def unpack(service_type, rates):
            rate_model, model_code, allowance_used = get_rate_model_id(service_type)
            return RateGroup(
                rate_model=rate_model,
                model_code=model_code,
                rates=rates,
                allowance_used=allowance_used,
            )

        og = OriginationGroup(
            origination_zones={CountryGroup("UK")},
            data=unpack(Service.DATA, rate_plan.data),
            voice_mo=unpack(Service.VOICE_MO, rate_plan.voice_mo),
            voice_mt=unpack(Service.VOICE_MT, rate_plan.voice_mt),
            sms=unpack(Service.SMS_MO, rate_plan.sms_mo),
        )
        return cls(
            id=rate_plan.id,
            account_id=rate_plan.account_id,
            name=rate_plan.name,
            access_fee=rate_plan.access_fee,
            currency=CurrencyCode("GBP"),
            sim_limit=rate_plan.sim_limit,
            allowance_used=rate_plan.allowance_used,
            origination_groups=[og],
            is_default=rate_plan.is_default,
            account_name=f"account-{rate_plan.account_id}",
            # rate_plan_model=rate_plan.rate_plan_model,
        )


class AccountRatePlan(CamelBaseModel):
    id: int
    name: str
    access_fee: Decimal = Field(decimal_places=2)
    currency: CurrencyCode
    is_default: bool
    allowance_used: Decimal = Field(default=Decimal("0.00"), decimal_places=2)
    rate_plan_model: int | None = None

    @classmethod
    def from_model(cls, account_rate_plan: model.AccountRatePlan) -> "AccountRatePlan":
        return cls(
            id=account_rate_plan.id,
            name=account_rate_plan.name,
            access_fee=account_rate_plan.access_fee,
            currency=account_rate_plan.currency,
            is_default=account_rate_plan.is_default,
            allowance_used=account_rate_plan.allowance_used,
            rate_plan_model=account_rate_plan.rate_plan_model,
        )


class AccountRatePlanDetails(CamelBaseModel):
    account_id: int
    account_name: str
    account_logo_url: AnyHttpUrl | None
    rate_plans: list[AccountRatePlan]

    class Config:
        schema_extra = {"example": RATE_PLANS_BY_ACCOUNTS}

    @classmethod
    def from_model(
        cls, rate_plan: model.AccountRatePlanDetails
    ) -> "AccountRatePlanDetails":
        return cls(
            account_id=rate_plan.account_id,
            account_name=rate_plan.account_name,
            account_logo_url=rate_plan.logo_url,
            rate_plans=[
                AccountRatePlan.from_model(account_rate_plan)
                for account_rate_plan in rate_plan.rate_plans
            ],
        )


class AccountRatePlanResponse(CamelBaseModel):
    result: list[AccountRatePlanDetails]

    @classmethod
    def from_model(
        cls, rate_plan: Iterable[model.AccountRatePlanDetails]
    ) -> "AccountRatePlanResponse":
        return cls(
            result=[
                AccountRatePlanDetails.from_model(rate_plan_data)
                for rate_plan_data in rate_plan
            ],
        )


class RatePlanModel(CamelBaseModel):
    title: str
    rate_model_code: str

    @validator("rate_model_code")
    def validate_rate_model_code(cls, value):
        # Check if the name contains only spaces
        if value is None or value == "":
            raise ValueError("Rate model rate_model_code cannot be none or empty.")

        # Check if the name contains only spaces
        if value.isspace():
            raise ValueError("Rate plan rate_model_code cannot consist of only spaces.")
        return value.strip()

    @validator("title")
    def validate_title(cls, value):
        # Check if the name contains only spaces
        if value is None or value == "":
            raise ValueError("Rate model title cannot be none or empty.")

        # Check if the name contains only spaces
        if value.isspace():
            raise ValueError("Rate plan title cannot consist of only spaces.")
        return value.strip()

    def to_model(self) -> model.RatePlanModel:
        return model.RatePlanModel(
            title=self.title,
            rate_model_code=self.rate_model_code,
        )


class RateModel(RatePlanModel):
    value: int

    @classmethod
    def from_model(cls, rate_model: model.RateModel) -> "RateModel":
        return cls(
            title=rate_model.title,
            value=rate_model.value,
            rate_model_code=rate_model.rate_model_code,
        )


class RatePlanModels(CamelBaseModel):
    result: list[RateModel]

    @classmethod
    def from_model(cls, rate_plan_models: list[model.RateModel]) -> "RatePlanModels":
        return cls(
            result=[
                RateModel.from_model(rate_model) for rate_model in rate_plan_models
            ],
        )


class RatePlanViewDetails(RatePlanView):
    sim_count: int = 0

    @classmethod
    def from_model_rate_plan_details(
        cls, rate_plan: model.RatePlan, sim_count: int, is_default: bool = False
    ) -> "RatePlanView":
        def get_rate_model_id(service_type):
            for rate_group in rate_plan._rate_groups:
                if service_type in rate_group.services:
                    return rate_group.rate_model_id
            return None

        og = OriginationGroup(
            origination_zones={CountryGroup("UK")},
            data=RateGroup(
                rate_model=get_rate_model_id(Service.DATA), rates=rate_plan.data
            ),
            voice_mo=RateGroup(
                rate_model=get_rate_model_id(Service.VOICE_MO), rates=rate_plan.voice_mo
            ),
            voice_mt=RateGroup(
                rate_model=get_rate_model_id(Service.VOICE_MT), rates=rate_plan.voice_mt
            ),
            sms=RateGroup(
                rate_model=get_rate_model_id(Service.SMS_MO), rates=rate_plan.sms_mo
            ),
        )
        return cls(
            id=rate_plan.id,
            account_id=rate_plan.account_id,
            name=rate_plan.name,
            access_fee=rate_plan.access_fee,
            currency=CurrencyCode("GBP"),
            sim_limit=rate_plan.sim_limit,
            origination_groups=[og],
            is_default=rate_plan.is_default,
            account_name=f"account-{rate_plan.account_id}",
            sim_count=sim_count,
        )


class AccountRatePlans(CamelBaseModel):
    account_id: int
    account_name: str
    account_logo_url: AnyHttpUrl | None
    rate_plans: list[RatePlanView]

    @classmethod
    def from_domain(
        cls, account: Account, rate_plans: Iterable[model.RatePlans]
    ) -> "AccountRatePlans":
        return cls(
            account_id=account.id,
            account_name=account.name,
            account_logo_url=account.logo_url,
            rate_plans=[RatePlanView.from_model_rate_plan(p) for p in rate_plans],
        )
