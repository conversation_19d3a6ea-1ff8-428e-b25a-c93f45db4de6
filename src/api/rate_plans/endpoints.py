from uuid import UUID

from fastapi import (
    APIRout<PERSON>,
    BackgroundTasks,
    Depends,
    HTTPException,
    Path,
    Request,
    Response,
    status,
)

import api.deps as trace_id_deps
from api.authorization.deps import authorization_service
from api.decorators import check_permissions, measure_execution_time
from api.deps import distributor_access_level, redis_service
from api.rate_plans import deps
from api.rate_plans.schemas import (
    Account,
    AccountRatePlanDetails,
    AccountRatePlanResponse,
    AccountRatePlans,
    CreateRatePlanRequest,
    CreateRatePlanResponse,
    RateModel,
    RatePlanModel,
    RatePlanModels,
    RatePlanView,
    RatePlanViewDetails,
    UpdateRatePlanRequest,
)
from api.resolvers import ResolveError, Resolver
from api.schema_types import PaginatedResponse
from app.config import logger
from auth.exceptions import ForbiddenError
from authorization.domain.ports import AbstractAuthorizationAPI
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from rate_plans.adapters.exceptions import RatePlanCreationError, RatePlanDeletionError
from rate_plans.exceptions import (
    AccountRatePlanNotFound,
    AlreadyDefaultRatePlan,
    DefaultRatePlanError,
    MediaError,
    RatePlanDoesNotExist,
    RatePlanModelDeletionError,
    RatePlanModelNotFound,
    ReadOnlyFieldsModified,
    SimLimitCountError,
)
from rate_plans.services import AbstractRatePlanService
from sync_redis.service import RedisService

router = APIRouter(tags=["rate-plans"])


@router.get(
    "/rate-plans/models",
    response_model=RatePlanModels,
)
def get_rate_plans_models(
    rate_plan_service: AbstractRatePlanService = Depends(deps.rate_plan_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> RatePlanModels:
    try:
        response = rate_plan_service.get_rate_plans_models()
        return RatePlanModels.from_model(response.result)
    except RatePlanModelNotFound as e:
        logger.error(f"Rateplan model not found error occoured :- {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Rateplan models not found.",
        )
    except Exception as e:
        logger.error(f"Rateplan model error occoured :- {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="We couldn't process you request.",
        )


@router.get(
    "/rate-plans/models/{id}",
    response_model=RateModel,
)
def get_rate_plans_model_by_id(
    id: int,
    rate_plan_service: AbstractRatePlanService = Depends(deps.rate_plan_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> RateModel:
    try:
        response = rate_plan_service.get_rate_plans_model_by_id(id=id)
        return RateModel.from_model(response)
    except RatePlanModelNotFound as e:
        logger.error(f"Rateplan model not found error occoured :- {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requested rateplan model not found.",
        )
    except Exception as e:
        logger.error(f"Rateplan model error occoured :- {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="We couldn't process you request.",
        )


@router.put(
    "/rate-plans/models/{id}",
    response_model=RateModel,
)
def update_rate_plans_model_by_id(
    id: int,
    model: RatePlanModel,
    rate_plan_service: AbstractRatePlanService = Depends(deps.rate_plan_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> RateModel:
    try:
        ratemodel = model.to_model()
        response = rate_plan_service.update_rate_plans_model_by_id(
            id=id, model=ratemodel
        )
        return RateModel.from_model(response)
    except RatePlanModelNotFound as e:
        logger.error(f"Rateplan model not found error occoured :- {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requested rateplan model not found.",
        )
    except Exception as e:
        logger.error(f"Rateplan model error occoured :- {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="We couldn't process you request.",
        )


@router.delete(
    "/rate-plans/models/{id}",
    response_model=None,
    status_code=status.HTTP_204_NO_CONTENT,
)
def delete_rate_plan_model(
    id: int,
    rate_plan_service: AbstractRatePlanService = Depends(deps.rate_plan_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> None:
    try:
        response = rate_plan_service.delete_rate_plan_model(rate_plan_model_id=id)
        return response
    except RatePlanModelNotFound as e:
        logger.error(f"Rateplan model not found error occoured :- {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requested rateplan model not found.",
        )
    except RatePlanModelDeletionError as e:
        logger.error(f"Rateplan model deletion error occoured :- {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Unable to delete rateplan model.",
        )
    except Exception as e:
        logger.error(f"Rateplan model error occoured :- {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="We couldn't process you request.",
        )


@router.post(
    "/rate-plans/models",
    response_model=RateModel,
    status_code=status.HTTP_201_CREATED,
)
def create_rate_plan_model(
    model: RatePlanModel,
    rate_plan_service: AbstractRatePlanService = Depends(deps.rate_plan_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> RateModel:
    try:
        ratemodel = model.to_model()
        response = rate_plan_service.create_rate_plan_model(model=ratemodel)
        return RateModel.from_model(rate_model=response)
    except RatePlanModelNotFound as e:
        logger.error(f"Rateplan model not found error occoured :- {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Requested rateplan model not found.",
        )
    except Exception as e:
        logger.error(f"Rateplan model error occoured :- {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="We couldn't process you request.",
        )


@router.get(
    "/rate-plans/by-accounts",
    response_model=PaginatedResponse[AccountRatePlanDetails],
    dependencies=[Depends(distributor_access_level())],
)
@check_permissions
def rate_plans_by_accounts(
    request: Request,
    rate_plan_service: AbstractRatePlanService = Depends(deps.rate_plan_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    searching: Searching
    | None = Depends(
        Searching.build(
            fields={
                "name",
                "account_name",
                "access_fee",
            },
        )
    ),
    ordering: Ordering = Depends(
        Ordering.query(
            AccountRatePlanDetails,
            default_ordering="account_id",
            ordering_fields=(
                "account_id",
                "account_name",
                "id",
                "name",
                "access_fee",
                "allowance_used",
            ),
        )
    ),
    pagination: Pagination = Depends(Pagination.query()),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> PaginatedResponse[AccountRatePlanDetails]:
    """Rate plans grouped by accounts."""
    try:
        response, total_count = rate_plan_service.rate_plans_by_accounts(
            searching=searching, ordering=ordering, pagination=pagination
        )
        result = AccountRatePlanResponse.from_model(response)
        return PaginatedResponse.from_iterable(
            pagination=pagination,
            results=result.result,
            total_count=total_count,
        )
    except MediaError as e:
        logger.error("Media error occured while getting rate-palns.")
        raise HTTPException(status.HTTP_400_BAD_REQUEST, str(e))
    except ConnectionError:
        raise HTTPException(status_code=status.HTTP_408_REQUEST_TIMEOUT)


@router.get(
    "/rate-plans/by-accounts/{account_id}",
    response_model=AccountRatePlans,
)
@check_permissions
def account_rate_plans(
    request: Request,
    rate_plan_service: AbstractRatePlanService = Depends(deps.rate_plan_service),
    account_resolver: Resolver[int, Account] = Depends(deps.account_resolver),
    account_id: int = Path(gt=0),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> AccountRatePlans:
    """Rate plans of the account."""
    get_account = account_resolver([account_id])
    try:
        account = get_account(account_id)
    except ResolveError:
        logger.error(f"Account with id {account_id} requested to update not found.")
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    except (Exception, AssertionError) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )

    rate_plans = rate_plan_service.list([account_id])

    return AccountRatePlans.from_domain(account, rate_plans)


@router.get("/rate-plans/{id}", response_model=RatePlanViewDetails)
@check_permissions
def rate_plan_details(
    request: Request,
    rate_plan_id: int = Path(ge=1, alias="id"),
    rate_plan_service: AbstractRatePlanService = Depends(deps.rate_plan_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    """Return rate plan by id"""
    try:
        rate_plan = rate_plan_service.get(rate_plan_id)
        sim_count = rate_plan_service.get_allocation_count_by_rate_plan(rate_plan_id)
        return RatePlanViewDetails.from_model_rate_plan_details(
            rate_plan=rate_plan, sim_count=sim_count
        )
    except RatePlanDoesNotExist:
        logger.error(f"Rate plan with id {rate_plan_id} not found.")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Rate plan not found"
        )
    except ValueError as e:
        logger.error(f"Rate plans vlaue error.: {str(e)}")
        raise HTTPException(status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e))


@router.put(
    "/rate-plans/{id}",
    response_model=RatePlanView,
)
@check_permissions
def update_rate_plan(
    request: Request,
    rate_plan_update: UpdateRatePlanRequest,
    background_tasks: BackgroundTasks,
    rate_plan_id: int = Path(ge=1, alias="id"),
    redis_service: RedisService = Depends(redis_service),
    rate_plan_service: AbstractRatePlanService = Depends(deps.rate_plan_service),
    account_resolver: Resolver[int, Account] = Depends(deps.account_resolver),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):

    _account_id = rate_plan_update.account_id
    get_account = account_resolver([_account_id])

    try:
        get_account(_account_id)
        updated_rate_plan = rate_plan_update.to_model()
        rate_plan_service.update(rate_plan_id, updated_rate_plan)
        background_tasks.add_task(
            redis_service.update_redis_rate_plan,
            rate_plan_id,
            rate_plan_update,
            rate_plan_service,
        )
    except DefaultRatePlanError as e:
        logger.error(f"update rateplan error occoured.: {e}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            detail=("An account should have minimum one " "default rate plan."),
        )
    except ResolveError:
        logger.error(f"Account with id {_account_id} requested to update not found.")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND,
            detail=f"Could not find account id {_account_id} to update"
            f" the rateplan {rate_plan_id}",
        )
    except RatePlanDoesNotExist:
        logger.error(f"Rate plan with id {rate_plan_id} requested does not exist.")
        raise HTTPException(status.HTTP_404_NOT_FOUND)
    except ReadOnlyFieldsModified:
        logger.error("Attempt to change read-only fields while updating rate plans.")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, "Attempt to change read-only fields"
        )
    except SimLimitCountError as e:
        logger.error(f"Sim Count Error while updating rate_plan. {str(e)}")
        raise HTTPException(status.HTTP_400_BAD_REQUEST, detail=f"{str(e)}")
    except ValueError as e:
        logger.error(f"We couldn't process the request. {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process the request."
        )
    except Exception as e:
        logger.error(f"Couldn't process the request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request"
        )

    return RatePlanView.from_model(
        rate_plan=updated_rate_plan,
        is_default=rate_plan_update.is_default,
    )


@router.delete(
    "/rate-plans/{id}",
    status_code=status.HTTP_202_ACCEPTED,
)
@check_permissions
def remove_rate_plan(
    request: Request,
    background_tasks: BackgroundTasks,
    rate_plan_id: int = Path(ge=1, alias="id"),
    redis_service: RedisService = Depends(redis_service),
    rate_plan_service: AbstractRatePlanService = Depends(deps.rate_plan_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> dict:
    try:
        rate_plan_service.delete(rate_plan_id)
        background_tasks.add_task(redis_service.delete_redis_rate_plan, rate_plan_id)
        return {"message": "We have received your request."}
    except DefaultRatePlanError as e:
        logger.error(f"delete rateplan error occoured.: {e}")
        raise HTTPException(status.HTTP_400_BAD_REQUEST, detail=f"{str(e)}")
    except RatePlanDoesNotExist as e:
        logger.error(f"Rate plan with id {rate_plan_id} requested does not exist.")
        raise HTTPException(status.HTTP_404_NOT_FOUND, detail=str(e))
    except RatePlanDeletionError as e:
        logger.error(f"Rate plan with id {rate_plan_id} deletion conflict.")
        raise HTTPException(status.HTTP_409_CONFLICT, detail=str(e))


@router.get(
    "/rate-plans",
    response_model=list[RatePlanView],
)
@check_permissions
@measure_execution_time
def rate_plans(
    request: Request,
    rate_plan_service: AbstractRatePlanService = Depends(deps.rate_plan_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> list[RatePlanView]:
    try:
        rate_plans = rate_plan_service.list()
        rate_plans_view = [
            RatePlanView.from_model_rate_plan(rate_plan=rate_plan)
            for rate_plan in rate_plans
        ]
        return rate_plans_view
    except ValueError as e:
        logger.error(f"Rate plans vlaue error.: {str(e)}")
        raise HTTPException(status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e))
    except AssertionError as e:
        logger.error(f"Assertion error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )
    except Exception as e:
        logger.error(f"Rate plans error.: {str(e)}")
        raise HTTPException(status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.post(
    "/rate-plans",
    response_model=CreateRatePlanResponse,
    status_code=status.HTTP_201_CREATED,
)
@check_permissions
def create_rate_plan(
    request: Request,
    rate_plan_in: CreateRatePlanRequest,
    background_tasks: BackgroundTasks,
    response: Response,
    redis_service: RedisService = Depends(redis_service),
    rate_plan_service: AbstractRatePlanService = Depends(deps.rate_plan_service),
    account_resolver: Resolver[int, Account] = Depends(deps.account_resolver),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> CreateRatePlanResponse:
    """Create rate plan for account."""
    _account_id = rate_plan_in.account_id
    get_account = account_resolver([_account_id])

    try:
        get_account(_account_id)
        rate_plan = rate_plan_in.to_model()
        rate_plan_id = rate_plan_service.create(rate_plan)
        response.headers["location"] = request.url_for(
            rate_plan_details.__name__, id=rate_plan_id
        )
        background_tasks.add_task(
            redis_service.create_redis_rate_plan,
            rate_plan_id,
            rate_plan_in,
            rate_plan_service,
        )
        return CreateRatePlanResponse(id=rate_plan_id, **rate_plan_in.dict())
    except DefaultRatePlanError as e:
        logger.error(f"Create rateplan error occoured.: {e}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            detail=("An account should have minimum one " "default rate plan."),
        )
    except RatePlanCreationError as e:
        logger.error(f"Create rateplan error occoured.: {e}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Invalid rateplan model."
        )
    except ResolveError:
        logger.error(f"Account with id {_account_id} not found.")
        raise HTTPException(
            status.HTTP_404_NOT_FOUND,
            detail="Account id not found while creating rateplan.",
        )
    except ValueError as e:
        logger.error(f"We couldn't process the request. {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="We couldn't process the request."
        )
    except Exception as e:
        logger.error(f"Couldn't process the request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request"
        )


@router.patch("/rate-plans/{id}/account/{account_id}")
@check_permissions
def set_default_rate_plan(
    request: Request,
    id: int = Path(ge=1, alias="id"),
    account_id: int = Path(ge=1, alias="account_id"),
    rate_plan_service: AbstractRatePlanService = Depends(deps.rate_plan_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    """Return rate plan by id"""
    try:
        rate_plan_service.set_default_rate_plan(account_id=account_id, id=id)
        return {"message": "Default Rate Plan was changed succesfully!"}
    except ForbiddenError as e:
        logger.error(f"Setting Default rate plan error.{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=("You are tring to " "access restricted area."),
        )
    except RatePlanDoesNotExist:
        logger.error(f"Rate plan with id {id} not found.")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Rate plan not found"
        )
    except AccountRatePlanNotFound:
        logger.error(
            f"The requested rate plan with id {id} is not linked to the "
            f"specified account id {account_id}."
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=(
                "The requested rate plan is" " not linked to the specified account."
            ),
        )
    except AlreadyDefaultRatePlan:
        logger.error(f"Rate plan with id {id} is already set default.")
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail="Rate plan is " "already set default.",
        )
    except ValueError as e:
        logger.error(f"Default Rate plan vlaue error.: {str(e)}")
        raise HTTPException(status.HTTP_422_UNPROCESSABLE_ENTITY, detail=str(e))

    except Exception as e:
        logger.error(f"Couldn't process the request.: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST, detail="Couldn't process the request"
        )
