from uuid import UUID

from fastapi import APIRouter, Depends, File, HTTPException, Response, UploadFile
from starlette import status

import api.deps as trace_id_deps
from api.decorators import convert_response_to_csv_paginated
from api.rating import deps
from api.rating.schemas import (
    AppVersion,
    AppVersionView,
    FilePath,
    OperatorUsage,
    OperatorUsageExport,
    UsageCSVRequest,
)
from api.renders import make_csv_renderer
from api.responses import CSVFileResponse
from api.schema_types import PaginatedResponse
from common.exceptions import VersionNotFound
from common.pagination import Pagination
from common.types import Month
from rating.calculation import AbstractAppService
from rating.domain import model
from rating.exceptions import NoData
from rating.factory import UsageCSVFormat, generate_usages_csv
from rating.parser import ManxCSVParser
from rating.storage_sync import CDRStorage
from rating.usage import UsageService

router = APIRouter()


@router.post("/monthly-usage/documents", status_code=status.HTTP_201_CREATED)
def monthly_usage_documents(
    file: UploadFile = File(...),
    usage_service: UsageService = Depends(deps.usage_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    """Upload monthly usage records from csv file to the database."""
    records = ManxCSVParser(file.file)
    usage_service.upload(records)


@router.post("/monthly-usage/import", status_code=status.HTTP_201_CREATED)
def monthly_usage_import(
    path: FilePath,
    cdr_storage: CDRStorage = Depends(deps.cdr_storage),
    usage_service: UsageService = Depends(deps.usage_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    """Upload monthly usage records from csv file to the database."""
    records = cdr_storage.extract_new_records(path.prefix)
    usage_service.upload(records)


@router.post("/monthly-usage/documents/test", response_class=CSVFileResponse)
def generate_test_monthly_usage(
    usage_request: UsageCSVRequest,
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
):
    records = generate_usages_csv(**usage_request.dict())
    render = make_csv_renderer(UsageCSVFormat, delimiter=",")
    return CSVFileResponse(
        content=render(list(records)),
        filename=(
            f"MonthlyUsage_{usage_request.month}_{usage_request.country}_"
            f"{usage_request.imsi_first}_{usage_request.length}.csv"
        ),
    )


@router.delete(
    "/monthly-usage/{month}",
    status_code=status.HTTP_204_NO_CONTENT,
    response_class=Response,
)
def remove_monthly_usage(
    month: Month,
    usage_service: UsageService = Depends(deps.usage_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> None:
    """Remove all usage records for a given month."""
    usage_service.remove_monthly_usage(month)


@router.post("/app-version", status_code=status.HTTP_201_CREATED)
def create(
    app: AppVersion,
    app_service: AbstractAppService = Depends(deps.app_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> bool:
    """Create release version"""
    version = app_service.create(app.to_model())
    if not version:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT, detail="Version already exist."
        )
    return version


@router.get("/version", status_code=status.HTTP_200_OK, response_model=AppVersionView)
def get(
    app_service: AbstractAppService = Depends(deps.app_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> model.AppVersionView | None:
    """Get latest release version"""
    try:
        return app_service.get()
    except VersionNotFound as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))


def get_monthly_usage(
    month: Month,
    usage_service: UsageService = Depends(deps.usage_service),
    pagination: Pagination = Depends(Pagination.query()),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> PaginatedResponse[OperatorUsage]:
    try:
        records, total = usage_service.get_monthly_usage(
            month=month, pagination=pagination
        )
        return PaginatedResponse.from_iterable(
            pagination=pagination,
            results=list(map(OperatorUsage.from_model, records)),
            total_count=total,
        )
    except NoData as e:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


router.get(
    "/monthly-usage/{month}/export",
    summary="Export Imsi Data Usage",
    status_code=status.HTTP_200_OK,
    response_model=PaginatedResponse[OperatorUsage],
)(
    convert_response_to_csv_paginated(OperatorUsageExport, "SimVoiceCDRHistory")(
        get_monthly_usage
    )
)
