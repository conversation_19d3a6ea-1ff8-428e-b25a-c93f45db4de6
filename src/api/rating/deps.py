import itertools
import random
import secrets
from functools import cache

from fastapi import Depends
from sqlalchemy.orm import Session

from api.deps import get_db_session, get_file_storage_object
from app.config import settings
from common.file_storage import AbstractFileStorage
from common.types import IMSI, Month, Service
from rating.adapters.usage_repository import (
    AbstractAppRepository,
    AbstractUsageRepository,
    DatabaseAppRepository,
    DatabaseUsageRepository,
    InMemoryUsageRepository,
)
from rating.calculation import AppService
from rating.domain.model import MonthlyUsageRecords
from rating.storage_sync import CDRStorage
from rating.usage import UsageService


@cache
def _in_memory_usage_repository() -> AbstractUsageRepository:
    # TODO: move to a usage generator(SPOG-207)

    choices: list[int] = random.sample(list(range(10000)), k=1000)  # nosec B311
    months = [Month(2022, 10, 1), Month(2022, 11, 1), Month(2022, 12, 1)]
    return InMemoryUsageRepository(
        [
            MonthlyUsageRecords(
                imsi=IMSI(f"{i:0>15}"),
                service=service,
                volume=secrets.choice(choices),
                month=month,
                operator="DUMMY",
            )
            for i, service, month in itertools.product(range(100), Service, months)
        ]
    )


def _db_usage_repository(
    session: Session = Depends(get_db_session),
) -> AbstractUsageRepository:
    return DatabaseUsageRepository(session)


def db_app_repository(
    session: Session = Depends(get_db_session),
) -> AbstractAppRepository:
    return DatabaseAppRepository(session)


def usage_service(
    usage_repo: AbstractUsageRepository = Depends(_db_usage_repository),
) -> UsageService:
    return UsageService(usage_repo)


def cdr_storage(
    file_storage: AbstractFileStorage = Depends(
        get_file_storage_object(settings.S3_CDR_BUCKET_NAME)
    ),
) -> CDRStorage:
    return CDRStorage(file_storage)


def app_service(
    app_repository: AbstractAppRepository = Depends(db_app_repository),
) -> AppService:
    return AppService(app_repository)


def custom_sim_storage(
    file_storage: AbstractFileStorage = Depends(
        get_file_storage_object(settings.S3_CUSTOM_ALLOCATION_BUCKET_NAME)
    ),
) -> CDRStorage:
    return CDRStorage(file_storage)


def error_cdr_storage(
    file_storage: AbstractFileStorage = Depends(
        get_file_storage_object(settings.S3_ERROR_CDR_FOLDER_NAME)
    ),
) -> CDRStorage:
    return CDRStorage(file_storage)


def duplicate_cdr_storage(
    file_storage: AbstractFileStorage = Depends(
        get_file_storage_object(settings.S3_DUPLICATE_CDR_FOLDER_NAME)
    ),
) -> CDRStorage:
    return CDRStorage(file_storage)


def msisdn_pool_storage(
    file_storage: AbstractFileStorage = Depends(
        get_file_storage_object(settings.S3_MSISDN_UPLOAD_BUCKET_NAME)
    ),
) -> CDRStorage:
    return CDRStorage(file_storage)


def sim_msisdn_bulk_update_storage(
    file_storage: AbstractFileStorage = Depends(
        get_file_storage_object(settings.S3_BULK_MSISDN_UPLOAD_BUCKET_NAME)
    ),
) -> CDRStorage:
    return CDRStorage(file_storage)
