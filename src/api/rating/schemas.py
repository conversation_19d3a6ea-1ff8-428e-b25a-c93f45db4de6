import pathlib
from datetime import date
from typing import Sequence, TypedDict

from pydantic import (
    BaseModel,
    ConfigDict,
    Extra,
    Field,
    NonNegativeInt,
    PositiveInt,
    validator,
)
from pydantic.dataclasses import dataclass

from api import validators
from api.schema_types import IMSI, CamelBaseModel
from common.minions import usagegroup
from common.types import Month, Service, Version
from rating.domain import model
from rating.factory import Carrier, CountryCode


@dataclass
class Rate(model.Rate):
    _check_range_to = validator("range_to", allow_reuse=True)(validators.check_range_to)


@dataclass
class RateGroup(model.RateGroup):
    rates: list[Rate] = Field(min_items=1)
    _check_ranges = validator("rates", allow_reuse=True)(validators.check_ranges)


class RatePlan(model.RatePlan):
    rate_groups: list[RateGroup] = Field(min_items=1)

    @validator("rate_groups")
    def check_usage_groups_are_disjoint(cls, v: Sequence[RateGroup]):
        if not usagegroup.disjoint(v):
            raise ValueError("Rate groups have intersections.")
        return v


RATE_PLAN_EXAMPLE = {
    "access_fee": 2.2,
    "rate_groups": [
        {
            "services": ["DATA"],
            "rates": [
                {"range_from": 0, "range_to": 512, "value": 3.50},
                {"range_from": 512, "range_to": 1024, "value": 2.5000001},
                {"range_from": 1024, "range_to": None, "value": 1.25},
            ],
        },
        {
            "services": ["VOICE_MT", "VOICE_MO"],
            "rates": [
                {"range_from": 0, "range_to": 50, "value": 0.25},
                {"range_from": 50, "range_to": None, "value": 0.0005},
            ],
        },
        {
            "services": ["SMS_MT"],
            "rates": [
                {"range_from": 0, "range_to": 50, "value": 0.25},
                {"range_from": 50, "range_to": None, "value": 0.0005},
            ],
        },
    ],
}
RATE_PLAN_CONFIG: ConfigDict = ConfigDict(
    extra=Extra.forbid,
    schema_extra={"examples": [RATE_PLAN_EXAMPLE]},
)
RatePlan = dataclass(config=RATE_PLAN_CONFIG)(RatePlan)  # type: ignore

# Self validation
RatePlan(**RATE_PLAN_EXAMPLE)  # type: ignore


class RatingParameters(BaseModel):
    rate_plan: RatePlan
    imsi_list: list[IMSI] | None = Field(None, description="List of IMSI numbers.")


class MonthlyCharge(TypedDict):
    imsi: IMSI
    volume: int | None
    service: Service
    month: date
    charge: str


class UsageCSVRequest(CamelBaseModel):
    imsi_first: IMSI
    length: PositiveInt
    country: CountryCode
    month: Month
    carriers: list[Carrier]


class FilePath(CamelBaseModel):
    prefix: pathlib.Path | None

    class Config:
        schema_extra = {"example": {"prefix": "monthly/"}}


class AppVersion(CamelBaseModel):
    name: str = Field(min_length=1, max_length=15)
    version: Version

    def to_model(self) -> model.AppVersion:
        return model.AppVersion(name=self.name, version=self.version)

    class Config:
        schema_extra = {"example": {"name": "SPOG UI", "version": "1.0.1"}}


class AppVersionView(CamelBaseModel):
    id: int
    name: str
    version: Version

    def to_model(self) -> model.AppVersionView:
        return model.AppVersionView(id=self.id, name=self.name, version=self.version)

    class Config:
        schema_extra = {"example": {"id": 1, "name": "SPOG UI", "version": "1.0.1"}}


class OperatorUsage(CamelBaseModel):
    IMSI: IMSI
    Carrier: str
    Month: str
    SMSMO: NonNegativeInt | None = None
    SMSMT: NonNegativeInt | None = None
    TotalBytes: NonNegativeInt | None = None
    VoiceMOSeconds: NonNegativeInt | None = None
    VoiceMTSeconds: NonNegativeInt | None = None

    @classmethod
    def from_model(cls, usage: model.OperatorUsage) -> "OperatorUsage":
        return cls(
            IMSI=usage.IMSI,
            Carrier=usage.Carrier,
            Month=usage.Month,
            SMSMO=usage.SMSMO,
            SMSMT=usage.SMSMT,
            TotalBytes=usage.TotalBytes,
            VoiceMOSeconds=usage.VoiceMOSeconds,
            VoiceMTSeconds=usage.VoiceMTSeconds,
        )


class OperatorUsageExport(BaseModel):
    IMSI: IMSI
    Carrier: str
    Month: str
    SMSMO: NonNegativeInt | None = None
    SMSMT: NonNegativeInt | None = None
    TotalBytes: NonNegativeInt | None = None
    VoiceMOSeconds: NonNegativeInt | None = None
    VoiceMTSeconds: NonNegativeInt | None = None

    @classmethod
    def from_response_model(cls, usage: model.OperatorUsage) -> "OperatorUsageExport":
        return cls(
            IMSI=usage.IMSI,
            Carrier=usage.Carrier,
            Month=usage.Month,
            SMSMO=usage.SMSMO,
            SMSMT=usage.SMSMT,
            TotalBytes=usage.TotalBytes,
            VoiceMOSeconds=usage.VoiceMOSeconds,
            VoiceMTSeconds=usage.VoiceMTSeconds,
        )
