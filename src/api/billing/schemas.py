from datetime import date
from decimal import Decimal
from typing import Iterable

from pydantic import AnyHttpUrl, Field, NonNegativeInt, PositiveInt, parse_obj_as

from accounts.domain.dto import Account
from accounts.domain.model import IndustryVertical
from accounts.services import MediaService
from api.billing.examples import (
    BILLING_CYCLES,
    CREATE_INVOICE_ADJUSTMENT,
    INVOICE_ADJUSTMENT,
    INVOICE_EXAMPLE,
    SIM_CARD_DETAILS,
    UPDATE_INVOICE_ADJUSTMENT,
)
from api.schema_types import IMSI, CamelBaseModel, CurrencyCode, HumanReadableBaseModel
from billing.domain import model
from billing.domain.model import AdjustmentType, SubscriptionSIM
from common.pagination import PaginatedResponse, Pagination
from common.types import Enumeration, KeyEnumeration, Month
from rating.domain.model import Service
from sim.domain.model import ICCID, MSISDN, SimStatus


class BillingAccount(CamelBaseModel):
    id: int
    name: str
    logo_url: AnyHttpUrl | None
    is_active: bool
    is_billable: bool
    industry_vertical: Enumeration
    currency: CurrencyCode
    contract_end_date: date

    class Config:
        orm_mode = True


class ServiceUsage(CamelBaseModel):
    service: Service
    volume: NonNegativeInt
    sub_charge: Decimal
    total_overage_charge: Decimal
    bulk_overage_charge: Decimal
    charge: Decimal

    class Config:
        orm_mode = True


class Subscription(CamelBaseModel):
    rate_plan_id: int
    name: str
    access_fee: Decimal

    sims_total: NonNegativeInt
    sims_active: NonNegativeInt
    charge: Decimal
    sim_charge: Decimal

    class Config:
        orm_mode = True


class CreateAdjustment(CamelBaseModel):
    date: date
    type: KeyEnumeration.generate(AdjustmentType)  # type: ignore[valid-type]
    amount: Decimal

    class Config:
        schema_extra = {"example": CREATE_INVOICE_ADJUSTMENT}

    def to_model(self, id: int | None = None) -> model.Adjustment:
        return model.Adjustment(
            id=id,
            date=self.date,
            type=AdjustmentType[self.type] if self.type else None,
            amount=self.amount,
        )


class UpdateAdjustment(CreateAdjustment):
    id: int

    class Config:
        schema_extra = {"example": UPDATE_INVOICE_ADJUSTMENT}


class Adjustment(UpdateAdjustment):
    type: Enumeration | None

    class Config:
        schema_extra = {"example": INVOICE_ADJUSTMENT}

    @classmethod
    def from_model(cls, adjustment: model.Adjustment) -> "Adjustment":
        return cls(
            id=adjustment.id,
            date=adjustment.date,
            type=Enumeration.from_enum_value(adjustment.type)
            if adjustment.type
            else None,
            amount=adjustment.amount,
        )


parse_obj_as(Adjustment, INVOICE_ADJUSTMENT)


class Invoice(CamelBaseModel):
    id: PositiveInt
    account: BillingAccount

    billing_cycle: Month
    invoice_date: date | None
    due_date: date | None
    is_published: bool
    rating_state: model.InvoiceRatingState
    new_sims: int
    total_account_charge: Decimal
    sim_charge: Decimal

    adjustments: list[Adjustment]
    subscriptions: list[Subscription]
    service_usage: list[ServiceUsage] = Field(
        min_items=len(Service), max_items=len(Service)
    )

    class Config:
        schema_extra = {"example": INVOICE_EXAMPLE}

    @classmethod
    def from_model(cls, invoice: model.Invoice, account: Account) -> "Invoice":
        sim_charge = Decimal(0)
        subscriptions = list(map(Subscription.from_orm, invoice.subscriptions))
        if subscriptions:
            sim_charge = subscriptions[0].sim_charge
        return cls(
            id=invoice.id,
            adjustments=list(map(Adjustment.from_model, invoice.adjustments)),
            account=BillingAccount.from_orm(account),
            billing_cycle=invoice.billing_cycle.month,
            invoice_date=invoice.invoice_date,
            due_date=invoice.due_date,
            is_published=invoice.is_published,
            rating_state=invoice.rating_state,
            new_sims=invoice.new_sims,
            total_account_charge=invoice.total_account_charge,
            sim_charge=sim_charge,
            subscriptions=subscriptions,
            service_usage=list(map(ServiceUsage.from_orm, invoice.usages)),
        )


parse_obj_as(Invoice, INVOICE_EXAMPLE)


class BillingCycle(CamelBaseModel):
    month: Month
    invoices: list[Invoice] = []

    class Config:
        schema_extra = {"example": BILLING_CYCLES[0]}


parse_obj_as(list[BillingCycle], BILLING_CYCLES)


class SIMCardUsage(CamelBaseModel):
    id: int
    iccid: ICCID
    msisdn: MSISDN
    imsi: IMSI

    rate_plan_id: PositiveInt | None
    status: str
    currency: CurrencyCode
    sim_fee_charge: Decimal

    service_usage: list[ServiceUsage] = Field(
        min_items=len(Service), max_items=len(Service)
    )

    class Config:
        schema_extra = {"example": SIM_CARD_DETAILS}

    @classmethod
    def from_model(cls, sim: SubscriptionSIM) -> "SIMCardUsage":
        return cls(
            id=sim.sim_id,
            rate_plan_id=sim.rate_plan_id,
            iccid=sim.iccid,
            msisdn=sim.msisdn,
            imsi=sim.imsi,
            currency=CurrencyCode("GBP"),
            status=SimStatus.ACTIVE,
            service_usage=list(map(ServiceUsage.from_orm, sim.usage_summary)),
            sim_fee_charge=sim.sim_fee_charge,
        )

    @property
    def voice_mo(self) -> ServiceUsage:
        return self._get_service_usage(Service.VOICE_MO)

    @property
    def voice_mt(self) -> ServiceUsage:
        return self._get_service_usage(Service.VOICE_MT)

    @property
    def data(self) -> ServiceUsage:
        return self._get_service_usage(Service.DATA)

    @property
    def sms_mo(self) -> ServiceUsage:
        return self._get_service_usage(Service.SMS_MO)

    @property
    def sms_mt(self) -> ServiceUsage:
        return self._get_service_usage(Service.SMS_MT)

    def _get_service_usage(self, service: Service) -> ServiceUsage:
        return [usage for usage in self.service_usage if usage.service == service][0]


parse_obj_as(SIMCardUsage, SIM_CARD_DETAILS)


class SIMDetailsExport(HumanReadableBaseModel):
    iccid: ICCID = Field(alias="ICCID")
    msisdn: MSISDN = Field(alias="MSISDN")
    imsi: IMSI = Field(alias="IMSI")

    sim_id: NonNegativeInt = Field(alias="SIM ID")
    rate_plan: NonNegativeInt  # Should be the name, not id
    imsi_status: str = Field(alias="IMSI Status")
    sim_fee_charge: Decimal = Field(alias="SIM Fee Charge")

    plan_data_charge: Decimal = Decimal("0.0")
    roaming_data_charge: Decimal = Decimal("0.0")

    data_charge: Decimal
    voice_charge: Decimal
    sms_charge: Decimal = Field(alias="SMS Charge")

    plan_data_volume: NonNegativeInt | None = None
    roaming_data_volume: NonNegativeInt | None = None

    data_volume: NonNegativeInt
    sms_volume: NonNegativeInt = Field(alias="SMS Volume")
    sms_mo_volume: NonNegativeInt = Field(alias="SMS MO Volume")
    sms_mt_volume: NonNegativeInt = Field(alias="SMS MT Volume")
    voice_volume: NonNegativeInt
    voice_mo_volume: NonNegativeInt = Field(alias="Voice MO Volume")
    voice_mt_volume: NonNegativeInt = Field(alias="Voice MT Volume")

    @classmethod
    def from_response_model(cls, sim_usage: SIMCardUsage) -> "SIMDetailsExport":
        def get_usage_charge(service_name):
            """Helper function to retrieve charge for a specific service."""
            usage_item = next(
                (u for u in sim_usage.usage if u.service.name == service_name), None
            )
            return getattr(usage_item, "charge", 0) or 0

        def get_usage_volume(service_name):
            """Helper function to retrieve volume for a specific service."""
            usage_item = next(
                (u for u in sim_usage.usage if u.service.name == service_name), None
            )
            return getattr(usage_item, "volume", 0) or 0

        return cls(
            iccid=sim_usage.iccid,
            msisdn=sim_usage.msisdn,
            imsi=sim_usage.imsi,
            sim_id=sim_usage.sim_id,  # type: ignore
            rate_plan=sim_usage.subscription.rate_plan_id,  # type: ignore
            imsi_status=sim_usage.sim_status.name,  # type: ignore
            sim_fee_charge=sim_usage.subscription.access_fee,  # type: ignore
            data_charge=get_usage_charge("DATA"),
            data_volume=get_usage_volume("DATA"),
            voice_charge=(get_usage_charge("VOICE_MO") + get_usage_charge("VOICE_MT")),
            voice_volume=(get_usage_volume("VOICE_MO") + get_usage_volume("VOICE_MT")),
            voice_mo_volume=get_usage_volume("VOICE_MO"),
            voice_mt_volume=get_usage_volume("VOICE_MT"),
            sms_charge=(get_usage_charge("SMS_MO") + get_usage_charge("SMS_MT")),
            sms_volume=(get_usage_volume("SMS_MO") + get_usage_volume("SMS_MT")),
            sms_mo_volume=get_usage_volume("SMS_MO"),
            sms_mt_volume=get_usage_volume("SMS_MT"),
        )


class SIMCardsTotalUsage(CamelBaseModel):
    service_usage: list[ServiceUsage]
    subscription_charge: Decimal


class SIMDetails(CamelBaseModel, PaginatedResponse[SIMCardUsage]):
    total_usage: SIMCardsTotalUsage

    @classmethod
    def build(
        cls,
        pagination: Pagination,
        results: Iterable[SIMCardUsage],
        total_count: NonNegativeInt,
        total_usage: SIMCardsTotalUsage,
    ) -> "SIMDetails":
        return super().from_iterable(
            pagination, results, total_count, total_usage=total_usage
        )


class Reconciliation(CamelBaseModel):
    result: list[model.ReconciliationDetails] | str


class BillingAccountNew(CamelBaseModel):
    id: int
    name: str
    logo_url: AnyHttpUrl | None
    is_active: bool
    is_billable: bool
    industry_vertical: Enumeration
    currency: CurrencyCode
    contract_end_date: date

    @classmethod
    def from_model(
        cls, account: model.BillingAccount, media_service: MediaService
    ) -> "BillingAccountNew":
        return cls(
            id=account.id,
            name=account.name,
            logo_url=media_service.get_file_url(account.logo_key)
            if account.logo_key
            else None,
            is_active=True if account.status == model.AccountStatus.ACTIVE else False,
            is_billable=account.is_billable,
            industry_vertical=Enumeration.from_enum_value(
                IndustryVertical[account.industry_vertical]
            ),
            currency=CurrencyCode(account.currency),
            contract_end_date=account.contract_end_date,
        )


class ServiceUsageNew(CamelBaseModel):
    service: Service
    volume: NonNegativeInt
    sub_charge: Decimal
    total_overage_charge: Decimal
    bulk_overage_charge: Decimal
    charge: Decimal

    @classmethod
    def from_model(cls, service_usage: model.ServiceUsage) -> "ServiceUsageNew":
        return cls(
            service=service_usage.service,
            volume=service_usage.volume,
            sub_charge=service_usage.sub_charge,
            total_overage_charge=service_usage.total_overage_charge,
            bulk_overage_charge=service_usage.bulk_overage_charge,
            charge=service_usage.charge,
        )


class SubscriptionNew(CamelBaseModel):
    rate_plan_id: int
    name: str
    access_fee: Decimal
    sims_total: NonNegativeInt
    sims_active: NonNegativeInt
    charge: Decimal
    sim_charge: Decimal

    @classmethod
    def from_orm(cls, subscription: model.Subscription) -> "SubscriptionNew":
        return cls(
            rate_plan_id=subscription.rate_plan_id,
            name=subscription.name,
            access_fee=subscription.access_fee,
            sims_total=subscription.sims_total,
            sims_active=subscription.sims_active,
            charge=subscription.charge,
            sim_charge=subscription.sim_charge,
        )


class AdjustmentNew(CamelBaseModel):
    id: int
    date: date
    type: Enumeration | None
    amount: Decimal

    @classmethod
    def from_model(cls, adjustment: model.Adjustment) -> "AdjustmentNew":
        return cls(
            id=adjustment.id,
            date=adjustment.date,
            type=Enumeration.from_enum_value(adjustment.type)
            if adjustment.type
            else None,
            amount=adjustment.amount,
        )


class InvoiceNew(CamelBaseModel):
    id: PositiveInt
    account: BillingAccountNew
    billing_cycle: Month
    invoice_date: date | None
    due_date: date | None
    is_published: bool
    rating_state: model.InvoiceRatingState
    new_sims: int
    total_account_charge: Decimal
    sim_charge: Decimal
    adjustments: list[AdjustmentNew]
    subscriptions: list[SubscriptionNew]
    service_usage: list[ServiceUsageNew]

    @classmethod
    def from_model(
        cls, invoice: model.Invoice, media_service: MediaService
    ) -> "InvoiceNew":
        subscriptions = list(map(Subscription.from_orm, invoice.subscriptions))
        sim_charge = subscriptions[0].sim_charge if subscriptions else Decimal(0)
        return cls(
            id=invoice.id,
            account=BillingAccountNew.from_model(
                invoice._account_info, media_service  # type: ignore
            ),
            billing_cycle=invoice.billing_cycle.month,
            invoice_date=invoice.invoice_date,
            due_date=invoice.due_date,
            is_published=invoice.is_published,
            rating_state=invoice.rating_state,
            new_sims=invoice.new_sims,
            total_account_charge=invoice.total_account_charge,
            sim_charge=sim_charge,
            adjustments=list(map(AdjustmentNew.from_model, invoice.adjustments)),
            subscriptions=subscriptions,
            service_usage=list(map(ServiceUsageNew.from_model, invoice.usages)),
        )


class GetInvoices(CamelBaseModel):
    invoices: list[InvoiceNew]

    @classmethod
    def from_model(cls, invoices: list, media_service: MediaService) -> "GetInvoices":
        result = cls(
            invoices=[
                InvoiceNew.from_model(invoice, media_service) for invoice in invoices
            ]
        )
        return result
