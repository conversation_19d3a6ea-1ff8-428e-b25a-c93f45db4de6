from fastapi import Depends
from sqlalchemy.orm import Session

from accounts.domain.dto import Account
from accounts.services import AccountService
from api.accounts.deps import database_account_repository, get_account_service
from api.deps import get_authenticated_user
from api.deps import get_db_session as _get_db_session
from api.resolvers import AccountResolver, FakeAccountResolver, Resolver
from api.sim.deps import get_audit_service_client, get_sim_repository
from auth.dto import AuthenticatedUser
from billing.adapters.repository import (
    AbstractAccountRepository,
    AbstractBillingCycleRepository,
    DatabaseBillingCycleRepository,
)
from billing.proxies import BillingServiceAuthProxy
from billing.services import (
    AbstractBillingService,
    AbstractSimUsageService,
    BillingService,
    SqlSimUsageService,
)
from rate_plans.adapters.rate_plan_repository import (
    AbstractRatePlanRepository,
    DatabaseRatePlanRepository,
)
from sim.adapters.repository import AbstractSimRepository
from sim.domain.ports import AbstractAuditService


def _billing_cycle_repository(
    session: Session = Depends(_get_db_session),
) -> AbstractBillingCycleRepository:
    return DatabaseBillingCycleRepository(session)


def _rate_plan_repository(
    session: Session = Depends(_get_db_session),
) -> AbstractRatePlanRepository:
    return DatabaseRatePlanRepository(session)


def _sim_usage_service(
    session: Session = Depends(_get_db_session),
) -> AbstractSimUsageService:
    return SqlSimUsageService(session)


def _get_billing_service(
    billing_cycle_repository: AbstractBillingCycleRepository = Depends(
        _billing_cycle_repository
    ),
    account_repository: AbstractAccountRepository = Depends(
        database_account_repository
    ),
    sim_repository: AbstractSimRepository = Depends(get_sim_repository),
    rate_plan_repository: AbstractRatePlanRepository = Depends(_rate_plan_repository),
    sim_usage_service: AbstractSimUsageService = Depends(_sim_usage_service),
    audit_service: AbstractAuditService = Depends(get_audit_service_client),
) -> AbstractBillingService:
    return BillingService(
        account_repository,
        billing_cycle_repository,
        rate_plan_repository,
        sim_repository,
        sim_usage_service,
        audit_service,
    )


def billing_service(
    authenticated_user: AuthenticatedUser = Depends(get_authenticated_user),
    _billing_service: AbstractBillingService = Depends(_get_billing_service),
) -> AbstractBillingService:

    return BillingServiceAuthProxy(
        billing_service=_billing_service,
        user=authenticated_user,
    )


def account_resolver(
    account_service: AccountService = Depends(get_account_service),
) -> Resolver[int, Account]:
    return AccountResolver(account_service)


def fake_account_resolver() -> Resolver[int, Account]:
    return FakeAccountResolver()
