from typing import Any

_KYIVSTAR_BILLING_ACCOUNT = {
    "id": 1,
    "name": "Kyivstar",
    "logoUrl": "https://example.com/icon",
    "isActive": True,
    "isBillable": True,
    "industryVertical": {"ref": "TELECOMMUNICATIONS", "name": "Telecommunications"},
    "currency": "GBP",
    "contractEndDate": "2023-12-01",
}
_TESLA_BILLING_ACCOUNT = {
    "id": 2,
    "name": "Tesla",
    "logoUrl": "https://example.com/icon",
    "isActive": True,
    "isBillable": True,
    "industryVertical": {"ref": "TRANSPORT", "name": "Transport"},
    "currency": "GBP",
    "contractEndDate": "2023-12-01",
}
_TESLA_INVOICE_22_11 = {
    "id": 101,
    "account": _TESLA_BILLING_ACCOUNT,
    "adjustments": [
        {
            "id": 1,
            "date": "2022-12-31",
            "type": {"ref": "OTHER_CHARGE_ONE_TIME", "name": "Other Charge - One Time"},
            "description": "Adjustment 1",
            "amount": 2.51,
        }
    ],
    "billingCycle": "2022-11",
    "invoiceDate": "2022-11-05",
    "dueDate": "2022-12-05",
    "isPublished": False,
    "ratingState": "GENERATED",
    "newSims": 3,
    "totalAccountCharge": 0,
    "simCharge": 0,
    "subscriptions": [
        {
            "ratePlanId": 4,
            "name": "PAYG+",
            "access_fee": 0.09,
            "simsTotal": 2_000,
            "simsActive": 3_000,
            "charge": 2.53,
            "sim_charge": 0.09,
        },
        {
            "ratePlanId": 6,
            "name": "PAYG X",
            "access_fee": 0.003,
            "simsTotal": 3_000,
            "simsActive": 2_000,
            "charge": 6.53,
            "sim_charge": 0.09,
        },
    ],
    "serviceUsage": [
        {
            "service": "DATA",
            "volume": 3_000_000,
            "sub_charge": 0,
            "total_overage_charge": 0,
            "bulk_overage_charge": 0,
            "charge": 0,
        },
        {
            "service": "SMS_MO",
            "volume": 10,
            "sub_charge": 0,
            "total_overage_charge": 0,
            "bulk_overage_charge": 0,
            "charge": 0,
        },
        {
            "service": "SMS_MT",
            "volume": 10,
            "sub_charge": 0,
            "total_overage_charge": 0,
            "bulk_overage_charge": 0,
            "charge": 0,
        },
        {
            "service": "VOICE_MO",
            "volume": 60015,
            "sub_charge": 0,
            "total_overage_charge": 0,
            "bulk_overage_charge": 0,
            "charge": 0,
        },
        {
            "service": "VOICE_MT",
            "volume": 60015,
            "sub_charge": 0,
            "total_overage_charge": 0,
            "bulk_overage_charge": 0,
            "charge": 0,
        },
    ],
}
_KYIVSTAR_INVOICE_22_11: dict[str, Any] = {
    "id": 100,
    "account": _KYIVSTAR_BILLING_ACCOUNT,
    "adjustments": [
        {
            "id": 101,
            "date": "2022-12-31",
            "type": {"ref": "OTHER_CHARGE_ONE_TIME", "name": "Other Charge - One Time"},
            "description": "Additional charge",
            "amount": 12.37,
        }
    ],
    "billingCycle": "2022-11",
    "invoiceDate": "2022-11-05",
    "dueDate": "2022-12-05",
    "isPublished": False,
    "ratingState": "GENERATED",
    "newSims": 2,
    "simCharge": 0,
    "totalAccountCharge": 0,
    "subscriptions": [
        {
            "ratePlanId": 3,
            "name": "PAYG",
            "access_fee": 0.01,
            "simsTotal": 20_000,
            "simsActive": 19_000,
            "charge": 2.53,
            "sim_charge": 0.09,
        }
    ],
    "serviceUsage": [
        {
            "service": "DATA",
            "volume": 2_000_000,
            "sub_charge": 0,
            "total_overage_charge": 0,
            "bulk_overage_charge": 0,
            "charge": 0,
        },
        {
            "service": "SMS_MO",
            "volume": 0,
            "sub_charge": 0,
            "total_overage_charge": 0,
            "bulk_overage_charge": 0,
            "charge": 0,
        },
        {
            "service": "SMS_MT",
            "volume": 10,
            "sub_charge": 0,
            "total_overage_charge": 0,
            "bulk_overage_charge": 0,
            "charge": 0,
        },
        {
            "service": "VOICE_MO",
            "volume": 15,
            "sub_charge": 0,
            "total_overage_charge": 0,
            "bulk_overage_charge": 0,
            "charge": 0,
        },
        {
            "service": "VOICE_MT",
            "volume": 60015,
            "sub_charge": 0,
            "total_overage_charge": 0,
            "bulk_overage_charge": 0,
            "charge": 0,
        },
    ],
}
_KYIVSTAR_INVOICE_22_12 = {
    "id": 200,
    "account": _KYIVSTAR_BILLING_ACCOUNT,
    "adjustments": [
        {
            "id": 201,
            "type": {"ref": "OTHER_CHARGE_ONE_TIME", "name": "Other Charge - One Time"},
            "date": "2023-01-03",
            "description": "Additional charge 2",
            "amount": -2.15,
        }
    ],
    "billing_cycle": "2022-12",
    "invoiceDate": "2022-12-05",
    "dueDate": "2023-01-05",
    "isPublished": False,
    "ratingState": "GENERATED",
    "newSims": 0,
    "totalAccountCharge": 0,
    "simCharge": 0,
    "subscriptions": [
        {
            "ratePlanId": 1,
            "name": "PAYG",
            "access_fee": 0.01,
            "simsTotal": 20_000,
            "simsActive": 19_000,
            "charge": 2.53,
            "sim_charge": 0.09,
        }
    ],
    "serviceUsage": [
        {
            "service": "DATA",
            "volume": 5_000_000,
            "sub_charge": 0,
            "total_overage_charge": 0,
            "bulk_overage_charge": 0,
            "charge": 0,
        },
        {
            "service": "SMS_MO",
            "volume": 3,
            "sub_charge": 0,
            "total_overage_charge": 0,
            "bulk_overage_charge": 0,
            "charge": 0,
        },
        {
            "service": "SMS_MT",
            "volume": 12,
            "sub_charge": 0,
            "total_overage_charge": 0,
            "bulk_overage_charge": 0,
            "charge": 0,
        },
        {
            "service": "VOICE_MO",
            "volume": 25,
            "sub_charge": 0,
            "total_overage_charge": 0,
            "bulk_overage_charge": 0,
            "charge": 0,
        },
        {
            "service": "VOICE_MT",
            "volume": 15,
            "sub_charge": 0,
            "total_overage_charge": 0,
            "bulk_overage_charge": 0,
            "charge": 0,
        },
    ],
}
_BILLING_CYCLE_2022_11 = {
    "month": "2022-11",
    "invoices": [
        _KYIVSTAR_INVOICE_22_11,
        _TESLA_INVOICE_22_11,
    ],
}
_BILLING_CYCLE_2022_12 = {
    "month": "2022-12",
    "invoices": [_KYIVSTAR_INVOICE_22_12],
}

BILLING_CYCLES = [
    _BILLING_CYCLE_2022_11,
    _BILLING_CYCLE_2022_12,
]

INVOICE_EXAMPLE = _KYIVSTAR_INVOICE_22_11

CREATE_INVOICE_ADJUSTMENT = {
    "date": "2023-01-12",
    "type": "OTHER_CHARGE_ONE_TIME",
    "amount": 70.5,
}

UPDATE_INVOICE_ADJUSTMENT = CREATE_INVOICE_ADJUSTMENT | {"id": 101}

INVOICE_ADJUSTMENT = UPDATE_INVOICE_ADJUSTMENT | {
    "type": {"ref": "OTHER_CHARGE_ONE_TIME", "name": "Other Charge - One Time"}
}

CREATE_SIM_PLAN = {"imsi": "126546789672975", "ratePlanId": 1}

SIM_PLAN = {"id": 100, "imsi": "123456789123456", "ratePlanId": 120}

SIM_CARD_DETAILS: dict[str, Any] = {
    "id": 15,
    "rate_plan_id": 12,
    "iccid": "8944538531005853110",
    "msisdn": "447452380011",
    "imsi": "234588560667871",
    "currency": "GBP",
    "status": "Unknown",
    "simFeeCharge": 0.23,
    "serviceUsage": [
        {
            "service": "DATA",
            "volume": 799_000,
            "sub_charge": 0,
            "total_overage_charge": 0,
            "bulk_overage_charge": 0,
            "charge": 2.25,
        },
        {
            "service": "SMS_MO",
            "volume": 0,
            "sub_charge": 0,
            "total_overage_charge": 0,
            "bulk_overage_charge": 0,
            "charge": 0,
        },
        {
            "service": "SMS_MT",
            "volume": 15,
            "sub_charge": 0,
            "total_overage_charge": 0,
            "bulk_overage_charge": 0,
            "charge": 2,
        },
        {
            "service": "VOICE_MO",
            "volume": 15,
            "sub_charge": 0,
            "total_overage_charge": 0,
            "bulk_overage_charge": 0,
            "charge": 3.45,
        },
        {
            "service": "VOICE_MT",
            "volume": 60015,
            "sub_charge": 0,
            "total_overage_charge": 0,
            "bulk_overage_charge": 0,
            "charge": 0,
        },
    ],
}


RECONCILIATION = [
    {
        "simImsi": "234588570010011",
        "cdrImsi": "234588570010011",
        "dataDetails": None,
        "voiceDetails": {"simUsage": 70, "cdrUsage": 5, "variance": 65},
        "smsDetails": {"simUsage": 178, "cdrUsage": 0, "variance": 178},
    },
    {
        "simImsi": "234588570010012",
        "cdrImsi": None,
        "dataDetails": {"simUsage": 20008, "cdrUsage": 0, "variance": 20008},
        "voiceDetails": {"simUsage": 74, "cdrUsage": 0, "variance": 74},
        "smsDetails": {"simUsage": 182, "cdrUsage": 0, "variance": 182},
    },
    {
        "simImsi": None,
        "cdrImsi": "234588560667862",
        "dataDetails": None,
        "voiceDetails": {"simUsage": 0, "cdrUsage": 950, "variance": -950},
        "smsDetails": None,
    },
    {
        "simImsi": None,
        "cdrImsi": "234588560667861",
        "dataDetails": {"simUsage": 0, "cdrUsage": 5000, "variance": -5000},
        "voiceDetails": {"simUsage": 0, "cdrUsage": 5850, "variance": -5850},
        "smsDetails": None,
    },
    {
        "simImsi": "234588560667860",
        "cdrImsi": None,
        "dataDetails": {"simUsage": 20000, "cdrUsage": 0, "variance": 20000},
        "voiceDetails": {"simUsage": 58, "cdrUsage": 0, "variance": 58},
        "smsDetails": {"simUsage": 166, "cdrUsage": 0, "variance": 166},
    },
    {
        "simImsi": "234588570010009",
        "cdrImsi": None,
        "dataDetails": {"simUsage": 20002, "cdrUsage": 0, "variance": 20002},
        "voiceDetails": {"simUsage": 62, "cdrUsage": 0, "variance": 62},
        "smsDetails": {"simUsage": 170, "cdrUsage": 0, "variance": 170},
    },
]


RECONCILIATION_RESPONSE = {"result": RECONCILIATION}


SIM_DATA_1 = {
    "service": "VOICE",
    "simImsi": None,
    "cdrImsi": "234588570010011",
    "simUsage": 70,
    "cdrUsage": 5,
    "variance": 65,
}

SIM_DATA_2 = {
    "service": "DATA",
    "simImsi": None,
    "cdrImsi": "234588570010011",
    "simUsage": 0,
    "cdrUsage": 5000,
    "variance": 5000,
}

SIM_DATA_3 = {
    "service": "DATA",
    "simImsi": "234588570010012",
    "cdrImsi": None,
    "simUsage": 20008,
    "cdrUsage": 0,
    "variance": 20008,
}

SIM_DATA_4 = {
    "service": "SMS",
    "simImsi": "234588570010012",
    "cdrImsi": None,
    "simUsage": 20,
    "cdrUsage": 0,
    "variance": 20,
}

SIM_DATA_5 = {
    "service": "DATA",
    "simImsi": None,
    "cdrImsi": "234588560667860",
    "simUsage": 0,
    "cdrUsage": 5000,
    "variance": 5000,
}

SIM_DATA_6 = {
    "service": "SMS",
    "simImsi": None,
    "cdrImsi": "234588560667860",
    "simUsage": 166,
    "cdrUsage": 0,
    "variance": 166,
}

SIM_DATA_7 = {
    "service": "VOICE",
    "simImsi": None,
    "cdrImsi": "234588560667860",
    "simUsage": 0,
    "cdrUsage": 5000,
    "variance": 5000,
}

SIM_DATA_8 = {
    "service": "SMS",
    "simImsi": None,
    "cdrImsi": "234588560667861",
    "simUsage": 0,
    "cdrUsage": 500,
    "variance": 500,
}
