import csv
import ipaddress
import logging
import os
import tempfile
import time
from datetime import datetime
from functools import wraps
from typing import Type

import pandas as pd
from fastapi import HTTPException, Request, status
from starlette.background import BackgroundTask
from starlette.responses import FileResponse, StreamingResponse

from api.renders import RT, make_csv_renderer
from api.schema_types import PaginatedResponse
from app.config import settings
from auth.exceptions import ForbiddenError
from authorization.domain.ports import AbstractAuthorizationAPI
from common.pagination import Pagination


def convert_response_to_csv(csv_model: Type[RT], prefix_name: str):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs) -> StreamingResponse:
            start_decorator = datetime.now()
            start_endpoint = datetime.now()
            response_data = func(*args, **kwargs)
            finish_endpoint = datetime.now()

            print(f"ENDPOINT TIME:======> {finish_endpoint - start_endpoint}")

            def generate_csv_data():
                with tempfile.NamedTemporaryFile(
                    mode="w+", delete=False, newline=""
                ) as f:
                    csv_writer = csv.writer(f)
                    try:
                        first_item = next(response_data)
                    except StopIteration:
                        raise HTTPException(
                            status_code=status.HTTP_404_NOT_FOUND,
                            detail="No Data to Export",
                        )

                    first_item_dict = first_item.dict(by_alias=True)
                    csv_writer.writerow(first_item_dict.keys())

                    csv_writer.writerow(first_item.__dict__.values())
                    for item in response_data:
                        csv_writer.writerow(item.__dict__.values())

                    f.seek(0)

                    content = f.read()

                    f.close()
                    BackgroundTask(os.remove, f.name)

                    yield content

            finish_decorator = datetime.now()
            print(f"DECORATOR TIME:=========> {finish_decorator-start_decorator}")
            return StreamingResponse(
                generate_csv_data(),
                media_type="text/csv",
                headers={
                    "Content-Disposition": (
                        "attachment; "
                        f'filename="{prefix_name}_{datetime.now().timestamp()}.csv"'
                    )
                },
            )

        return wrapper

    return decorator


def convert_response_to_csv_with_panda(csv_model: Type, prefix_name: str):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs) -> StreamingResponse:
            response_data = func(*args, **kwargs)
            if not response_data:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="No Data to Export",
                )

            df = pd.DataFrame(response_data)

            with tempfile.NamedTemporaryFile(mode="w+", delete=False) as f:
                df.to_csv(f.name, index=False)
                f.seek(0)
                csv_content = f.read()

            return StreamingResponse(
                iter([csv_content]),
                media_type="text/csv",
                headers={
                    "Content-Disposition": (
                        "attachment; "
                        f'filename="{prefix_name}_{datetime.now().timestamp()}.csv"'
                    )
                },
            )

        return wrapper

    return decorator


def convert_response_to_csv_paginated(csv_model: Type[RT], prefix_name: str):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs) -> FileResponse:
            page = 1
            write_headers = True
            render = make_csv_renderer(csv_model)

            with tempfile.NamedTemporaryFile("w", delete=False) as f:
                while True:
                    kwargs["pagination"] = Pagination(page=page, page_size=50)
                    paginated_result: PaginatedResponse = func(*args, **kwargs)
                    content = render(
                        list(
                            map(
                                csv_model.from_response_model,  # type: ignore
                                paginated_result.results,
                            )
                        ),
                        write_headers,
                    )
                    f.write(content.read())
                    if paginated_result.last_page == page:
                        break
                    write_headers = False
                    page += 1

            task = BackgroundTask(os.remove, f.name)
            return FileResponse(
                path=f.name,
                filename=f"{prefix_name}_{datetime.now().timestamp()}.csv",
                media_type="text/csv",
                background=task,
                content_disposition_type="attachment",
            )

        return wrapper

    return decorator


def validate_ip_address(func):
    @wraps(func)
    def wrapper(request: Request, *args, **kwargs):
        whitelisted_ips = []
        if settings.WHITELISTED_IPS:
            whitelisted_ips = [
                ipaddress.ip_network(ip.strip())
                for ip in settings.WHITELISTED_IPS.split(",")
            ]
        client_ip = request.client.host if request.client else None
        if client_ip and any(
            ipaddress.ip_address(client_ip) in ip for ip in whitelisted_ips
        ):
            return func(request, *args, **kwargs)
        else:
            logging.error(f"IP {client_ip} tried to access the resources.")
            raise ForbiddenError()

    return wrapper


GREEN, RED, RESET = "\033[92m", "\033[91m", "\033[0m"


def check_permissions(func):
    @wraps(func)
    def wrapper(request: Request, *args, **kwargs):
        authorization = None
        for value in [*args, *kwargs.values()]:
            if isinstance(value, AbstractAuthorizationAPI):
                authorization = value
                break
        if authorization is None:
            raise ValueError("authorization not found in dependencies")
        route_params = request.path_params
        original_route = request.url_for(
            func.__name__, **{param: f"{{{param}}}" for param in route_params}
        )
        path_route = original_route.replace(
            request.base_url.scheme + "://" + request.base_url.netloc, request.method
        )
        status = authorization.is_authorized(path_route)
        if status != "PERMIT":
            logging.info(f"{RED}Access denied for {path_route}.{RESET}")
            raise ForbiddenError()
        logging.info(f"{GREEN}Access granted for {path_route}.{RESET}")
        return func(request, *args, **kwargs)

    return wrapper


def measure_execution_time(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        logging.info(
            f"Function '{func.__name__}' took {execution_time:.4f} seconds to complete."
        )
        return result

    return wrapper
