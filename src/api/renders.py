import csv
from io import <PERSON><PERSON>
from typing import Callable, TypeVar

from fastapi.encoders import jsonable_encoder
from mypy_extensions import <PERSON><PERSON>ult<PERSON>rg
from pydantic import BaseModel

RT = TypeVar("RT", bound=BaseModel)


def make_csv_renderer(
    model: type[RT], delimiter: str = ";"
) -> Callable[[list[RT], DefaultArg(bool)], StringIO]:
    """
    Create callable that renders list of model objects into CSV-like string.
    Order of columns corresponds to order in Model.__fields__.
    Column names in header taken from Field.alias(by default is the Field.name).
    """
    fieldnames = [f.alias for f in model.__fields__.values()]

    def csv_renderer(records: list[RT], write_headers: bool = True) -> StringIO:
        f = StringIO()
        writer = csv.DictWriter(f, fieldnames=fieldnames, delimiter=delimiter)
        if write_headers:
            writer.writeheader()
        for record in records:
            row_dict = jsonable_encoder(record, by_alias=True, exclude_none=True)
            writer.writerow(row_dict)
        f.seek(0)
        return f

    return csv_renderer
