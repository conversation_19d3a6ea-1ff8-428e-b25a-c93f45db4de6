from abc import ABC, abstractmethod
from datetime import date
from operator import attrgetter
from typing import Callable, Iterable, Protocol, TypeVar

from accounts.domain import dto, model
from accounts.domain.model import (
    AccountStatus,
    IndustryVertical,
    PaymentTerms,
    ProductType,
    SalesChannel,
    SimProfile,
)
from accounts.services import AccountService
from api.schema_types import CountryCode, CurrencyCode
from rate_plans.adapters.rate_plan_repository import AbstractRatePlanRepository
from sim.services import SimService


class ResolveError(Exception):
    ...


_KT = TypeVar("_KT", contravariant=True)
_VT = TypeVar("_VT", covariant=True)


class Resolver(Protocol[_KT, _VT]):
    def __call__(
        self,
        keys: Iterable[_KT],
    ) -> Callable[[_KT], _VT]:
        ...


class AbstractResolver(Resolver[_KT, _VT], ABC):
    key_func = attrgetter("id")

    def __call__(
        self,
        keys: Iterable[_KT],
    ) -> Callable[[_KT], _VT]:
        value_items = self.get_collection(keys)
        values_map = {self.key_func(i): i for i in value_items}

        def resolve(k):
            item = values_map.get(k, None)
            if not item:
                raise ResolveError(k)
            return item

        return resolve

    @abstractmethod
    def get_collection(self, keys: Iterable[_KT]) -> Iterable[_VT]:
        ...


class AccountResolver(AbstractResolver):
    def __init__(self, account_service: AccountService):
        self.account_service = account_service

    def get_collection(self, keys: Iterable[int]) -> Iterable[dto.Account]:
        accounts = self.account_service.list(list(keys))
        return accounts


class AccountStatisticResolver(Resolver):
    def __init__(
        self, sim_service: SimService, rate_plan_repository: AbstractRatePlanRepository
    ):
        self.sim_service = sim_service
        self.rate_plan_repository = rate_plan_repository

    def __call__(
        self, keys: Iterable[dto.Account]
    ) -> Callable[[dto.Account], dict[str, int | str | None]]:
        return self.get_statistic

    def get_statistic(self, account: dto.Account) -> dict[str, int | str | None]:
        rate_plan = self.rate_plan_repository.query([account.id])
        try:
            plan_data = next(rate_plan)
            plan_name = plan_data.name
        except StopIteration:
            plan_name = None

        return {
            "sims_total": self.sim_service.get_sim_count(account.id, active_only=False),
            "active_sims_total": self.sim_service.get_sim_count(
                account.id, active_only=True
            ),
            "plans_total": self.rate_plan_repository.count_for_account(account.id),
            "users_total": 0,
            "default_rate_plan": plan_name,
        }


class FakeAccountResolver(AbstractResolver):
    def get_collection(self, keys: Iterable[int]) -> Iterable[dto.Account]:
        return map(self._get_account, keys)

    @staticmethod
    def _get_account(account_id: int) -> dto.Account:
        account = model.Account(
            _id=account_id,
            name=model.OrganizationName(f"account-{account_id}"),
            agreement_number="42",
            status=AccountStatus.ACTIVE,
            is_billable=True,
            industry_vertical=IndustryVertical.TELECOMMUNICATIONS,
            currency=CurrencyCode("GBP"),
            sales_channel=SalesChannel.WHOLESALE,
            product_types=[ProductType.NATIONAL_ROAMING],
            sales_person="Brooklyn Simmons",
            country=CountryCode("UK"),
            contract_end_date=date(2023, 12, 31),
            organization_id=-1,
            payment_terms=PaymentTerms(30),
            sim_profile=SimProfile.DATA_ONLY,
        )
        return dto.Account.from_model(account, logo_url=None)
