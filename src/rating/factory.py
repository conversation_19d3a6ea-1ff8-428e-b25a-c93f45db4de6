from typing import Any, Iterable, Iterator

from pydantic import BaseModel, ConstrainedInt, ConstrainedStr, root_validator
from pydantic_factories import ModelFactory

from api.schema_types import IMSI
from common.types import Month


class CountryCode(ConstrainedStr):
    min_length = 3
    max_length = 3
    to_upper = True


class Carrier(ConstrainedStr):
    min_length = 5
    max_length = 5
    to_upper = True


class BytesValue(ConstrainedInt):
    ge = 0
    le = 999999999


class VoiceValue(ConstrainedInt):
    ge = 0
    le = 500


class SMSValue(ConstrainedInt):
    ge = 0
    le = 30


class UsageCSVFormat(BaseModel):
    IMSI: IMSI
    Month: str
    Country: CountryCode
    Carrier: Carrier
    TotalBytes: BytesValue | None
    VoiceMOSeconds: VoiceValue | None
    VoiceMTSeconds: VoiceValue | None
    VoiceSeconds: int | None
    SMSMT: SMSValue | None
    SMSMO: SMSValue | None
    SMS: int | None

    @staticmethod
    def _calculate_total(mo_value: int | None, mt_value: int | None):
        if mo_value is None and mt_value is None:
            return None
        mo_value = mo_value or 0
        mt_value = mt_value or 0
        return mo_value + mt_value

    @root_validator
    def compute_totals(cls, values: dict[str, Any]) -> dict:
        values["VoiceSeconds"] = cls._calculate_total(
            values["VoiceMOSeconds"], values["VoiceMTSeconds"]
        )
        values["SMS"] = cls._calculate_total(values["SMSMO"], values["SMSMT"])

        return values


class UsageFactory(ModelFactory):
    __model__ = UsageCSVFormat


def generate_usages_csv(
    imsi_first: IMSI, length: int, month: Month, country: str, carriers: Iterable[str]
) -> Iterator[UsageCSVFormat]:
    for imsi in range(int(imsi_first), int(imsi_first) + length):
        for carrier in carriers:
            yield UsageFactory.build(
                IMSI=imsi,
                Month=f"{month:%Y%m}",
                Country=country,
                Carrier=carrier,
            )
