from decimal import Decimal
from typing import Sequence

from pydantic import NonNegativeInt
from pydantic.dataclasses import dataclass

from common.types import (
    IMSI,
    DataService,
    Month,
    RangeOfUse,
    Service,
    SMSService,
    UsageGroup,
    Version,
    VoiceService,
)


@dataclass
class Rate(RangeOfUse):
    """Range-based price that can be applied to the amount of usage."""

    range_from: NonNegativeInt
    range_to: NonNegativeInt | None
    value: Decimal


@dataclass
class RateGroup(UsageGroup):
    """Rates for certain GSM services and usage parameters."""

    services: set[VoiceService] | set[SMSService] | set[DataService]
    rates: Sequence[Rate]


@dataclass
class RatePlan:
    """Tariff plan that determines prices for GSM services."""

    access_fee: Decimal
    rate_groups: Sequence[RateGroup]


@dataclass
class MonthlyUsageRecords:
    """Monthly information about call detail records"""

    imsi: IMSI
    service: Service
    operator: str
    month: Month
    volume: NonNegativeInt


@dataclass
class AppVersion:
    name: str
    version: Version


@dataclass
class AppVersionView:
    id: int
    name: str
    version: Version


@dataclass
class UsageModel:
    imsi: IMSI
    operator: str
    service: Service
    volume: NonNegativeInt
    month: Month


@dataclass
class OperatorUsage:
    IMSI: IMSI
    Carrier: str
    Month: str
    SMSMO: NonNegativeInt | None = None
    SMSMT: NonNegativeInt | None = None
    TotalBytes: NonNegativeInt | None = None
    VoiceMOSeconds: NonNegativeInt | None = None
    VoiceMTSeconds: NonNegativeInt | None = None
