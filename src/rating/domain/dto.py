from datetime import date
from decimal import Decimal
from typing import TypedDict

from common.types import IMSI, Service


class MonthlyUsageAggregate(TypedDict):
    """Aggregated usage for a given IMSI, service and month."""

    imsi: IMSI
    volume: int | None
    service: Service
    month: date


class MonthlyChargeAggregate(MonthlyUsageAggregate):
    """Total charge for a given monthly usage record."""

    charge: Decimal
