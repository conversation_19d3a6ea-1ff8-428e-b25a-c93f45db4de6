import logging
from abc import ABC, abstractmethod
from decimal import Decimal
from typing import Final, Protocol, Sequence, TypeVar

from typing_extensions import assert_never

from common.exceptions import VersionNotFound
from common.minions import rangeofuse, usagegroup
from common.minions.rangeofuse import VolumePosition
from common.types import RangeOfUse, UsageGroup
from rating.adapters.usage_repository import AbstractAppRepository
from rating.domain import model
from rating.domain.dto import MonthlyChargeAggregate, MonthlyUsageAggregate


class Rate(RangeOfUse, Protocol):
    value: Decimal
    overage_fee: Decimal | None = None
    overage_per: int | None = None


RateT = TypeVar("RateT", bound=Rate)


class RateGroup(UsageGroup, Protocol[RateT]):
    rates: Sequence[RateT]
    rate_model_id: int


class RatingCalculator:
    DEFAULT_PRECISION: Final[int] = 6

    def __init__(
        self,
        rates: Sequence[Rate],
        price_unit_ratio: int,
        overage_unit_ratio: int,
        rounding_precision: int | None = None,
    ):
        self.rates = rates
        self.price_unit_ratio = price_unit_ratio
        self.overage_unit_ratio = overage_unit_ratio
        precision = (
            rounding_precision
            if rounding_precision is not None
            else self.DEFAULT_PRECISION
        )
        self.exp = Decimal((0, (0,), -precision))

    def apply_rates(
        self, volume: int | None, rate_model: int | None, service: str
    ) -> Decimal:
        if volume is None:
            return Decimal("0")
        charge = Decimal("0")
        if rate_model == 2:
            charge += self._overage_charge(
                self.rates,
                volume,
                self.overage_unit_ratio,
                self.rates[0].overage_per,  # type: ignore
                service,
            )
        elif rate_model == 1:
            charge = self._volume_charge(self.rates, volume, rate_model)
            charge /= self.price_unit_ratio
        else:
            charge
        charge = charge.quantize(self.exp)
        return charge

    @classmethod
    def _overage_charge(
        cls,
        rates: Sequence[Rate],
        volume: int,
        overage_unit_ratio: int,
        overage_per: int,
        service: str,
    ) -> Decimal:
        """Return the overage charge for volume beyond the defined ranges."""
        overage_charge = Decimal(0)
        for rate in rates:
            if volume > rate.range_to:  # type: ignore
                extra_volume = volume - rate.range_to  # type: ignore
                if rate.isoverage:  # type: ignore
                    block_size = (
                        overage_per * overage_unit_ratio
                    )  # 50 MB in bytes need to dynamic
                    blocks = (extra_volume + block_size - 1) // block_size
                    overage_charge += Decimal(blocks) * rate.overage_fee  # type: ignore
                else:
                    try:
                        if service == "DATA":
                            extra_volume_convert = extra_volume / Decimal(1024)
                        elif service == "VOICE_MO" or service == "VOICE_MT":
                            extra_volume_convert = extra_volume / Decimal(60)
                        elif service == "SMS_MO" or service == "SMS_MT":
                            extra_volume_convert = extra_volume / Decimal(1)
                        divisor = usagegroup.overage_ratio_divisor(
                            rate.overage_unit  # type: ignore
                        )
                        if divisor == 0:
                            raise ValueError("Divisor cannot be zero.")
                        if rate.overage_fee is not None and divisor is not None:
                            overage_charge += (
                                (rate.overage_fee / overage_per) / Decimal(divisor)
                            ) * extra_volume_convert
                        else:
                            logging.info(f"Invalid fee or divisor for rate: {rate}.")
                    except ValueError as e:
                        logging.info(
                            f"Error calculating overage charge: {rate}. Error: {e}"
                        )
                    except Exception as e:
                        logging.info(f"Unexpected error with rate: {rate}. Error: {e}")

        return overage_charge

    @classmethod
    def _volume_charge(
        cls, rates: Sequence[Rate], volume: int | None, rate_model: int | None
    ) -> Decimal:
        """Return charge for the volume according to rates."""

        charge = Decimal(0)

        if volume is None or volume == 0:
            return charge

        for rate in rates:
            if rate_model != 1:
                charge += rate.value
            else:
                charge += cls._rate_volume(rate, volume) * rate.value
        return charge

    @classmethod
    def _rate_volume(cls, r: Rate, volume: int) -> int:
        """Return the volume slice covered by the rate's range."""

        volume_position = rangeofuse.volume_position(r, volume)
        match volume_position:
            case VolumePosition.BELOW:
                return 0
            case VolumePosition.INSIDE:
                return volume - r.range_from
            case VolumePosition.ABOVE:
                return rangeofuse.coverage(r)
            case _:
                assert_never(volume_position)

    @classmethod
    def from_rate_group(
        cls, rate_group: RateGroup, rounding_precision: int | None = None
    ) -> "RatingCalculator":
        range_ratio = usagegroup.range_ratio(rate_group)
        rates = rangeofuse.adjust_units(rate_group.rates, range_ratio)
        price_ratio = usagegroup.price_ratio(rate_group)
        if rate_group.rate_model_id == 2:
            overage_ratio = usagegroup.overage_ratio(rate_group)
        else:
            overage_ratio = 1
        return cls(rates, price_ratio, overage_ratio, rounding_precision)


class AbstractAppService(ABC):
    @abstractmethod
    def create(self, version: model.AppVersion) -> bool:
        ...

    @abstractmethod
    def get(self) -> model.AppVersionView | None:
        ...


class AppService(AbstractAppService):
    def __init__(
        self,
        app_repository: AbstractAppRepository,
    ):
        self.app_repository = app_repository

    def create(self, version: model.AppVersion) -> bool:
        return self.app_repository.create(version)

    def get(self) -> model.AppVersionView | None:
        version = self.app_repository.get()
        if not version:
            raise VersionNotFound("App version not found")
        return version


def _with_charge(
    usage_record: MonthlyUsageAggregate, charge: Decimal
) -> MonthlyChargeAggregate:
    return MonthlyChargeAggregate(
        imsi=usage_record["imsi"],
        service=usage_record["service"],
        volume=usage_record["volume"],
        month=usage_record["month"],
        charge=charge,
    )
