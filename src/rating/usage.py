from dataclasses import dataclass
from typing import Iterable

from common.pagination import Pagination
from common.types import Month, Service
from rating import exceptions
from rating.adapters.usage_repository import AbstractUsageRepository
from rating.domain.model import MonthlyUsageRecords, OperatorUsage


@dataclass
class UsageService:
    usage_repository: AbstractUsageRepository

    def upload(self, records: Iterable[MonthlyUsageRecords]) -> None:
        self.usage_repository.bulk_insert(records)

    def remove_monthly_usage(self, month: Month) -> None:
        return self.usage_repository.remove_by_month(month)

    def get_monthly_usage(
        self, month: Month, pagination: Pagination
    ) -> tuple[list[OperatorUsage], int]:
        records = self.usage_repository.get_monthly_usage(month=month)
        monthly_usage_dict = {}  # type:ignore
        for usage in records:
            imsi = usage.imsi
            operator = usage.operator
            service_name = usage.service.name
            volume = usage.volume
            month = usage.month
            if imsi not in monthly_usage_dict:
                monthly_usage_dict[imsi] = {}
            if operator not in monthly_usage_dict[imsi]:
                monthly_usage_dict[imsi][operator] = {
                    "imsi": imsi,
                    "operator": operator,
                    "month": f"{month.year}{month.month}",
                }
            if service_name not in monthly_usage_dict[imsi][operator]:
                monthly_usage_dict[imsi][operator][service_name] = 0
            monthly_usage_dict[imsi][operator][service_name] += volume
        monthly_usage_list = []
        for imsi_data in monthly_usage_dict.values():
            for operator_data in imsi_data.values():
                operator_usage = OperatorUsage(
                    IMSI=operator_data.get("imsi"),
                    Carrier=operator_data.get("operator"),
                    SMSMO=operator_data.get(Service.SMS_MO.name),
                    SMSMT=operator_data.get(Service.SMS_MT.name),
                    TotalBytes=operator_data.get(Service.DATA.name),
                    VoiceMOSeconds=operator_data.get(Service.VOICE_MO.name),
                    VoiceMTSeconds=operator_data.get(Service.VOICE_MT.name),
                    Month=operator_data.get("month"),
                )
                monthly_usage_list.append(operator_usage)
        monthly_usage_count = len(monthly_usage_list)
        paginated_list = monthly_usage_list[
            pagination.offset : pagination.offset + pagination.page_size
        ]
        if not monthly_usage_list:
            raise exceptions.NoData()
        return paginated_list, monthly_usage_count
