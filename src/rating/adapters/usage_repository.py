import itertools
from abc import ABC, abstractmethod
from collections.abc import Generator, Set
from operator import and_, attrgetter
from typing import Iterable, Iterator

from sqlalchemy import delete, desc, func, select, true
from sqlalchemy.orm import Session
from sqlalchemy.sql import Select

from common.types import IMSI, Month, Service
from rating.adapters import orm
from rating.domain import model
from rating.domain.dto import MonthlyUsageAggregate
from rating.domain.model import MonthlyUsageRecords, UsageModel


class AbstractUsageRepository(ABC):
    @abstractmethod
    def bulk_insert(self, usage_records: Iterable[model.MonthlyUsageRecords]):
        ...

    @abstractmethod
    def query_month(self, month: Month) -> Iterable[model.MonthlyUsageRecords]:
        ...

    @abstractmethod
    def aggregate_month(
        self,
        month: Month,
        services: Set[Service] = None,
        imsi_list: list[IMSI] = None,
    ) -> Iterable[MonthlyUsageAggregate]:
        """Return aggregated usage for a given month."""

    @abstractmethod
    def aggregate_month_fill_missing(
        self,
        month: Month,
        services: Set[Service] = None,
        imsi_list: list[IMSI] = None,
    ) -> Iterable[MonthlyUsageAggregate]:
        """
        Same as `aggregate_month` but with None volume for missing usage.
        Result will contain entries for all requested IMSIs/services.
        """

    @abstractmethod
    def remove_by_month(self, month: Month) -> None:
        """Remove all usage records for a given month."""

    @abstractmethod
    def get_monthly_usage(
        self,
        month: Month,
    ) -> Iterator[UsageModel]:
        ...


class InMemoryUsageRepository(AbstractUsageRepository):
    def __init__(self, records: list[MonthlyUsageRecords] = None):

        self._records: dict[Month, list[MonthlyUsageRecords]] = {}
        self._aggregates: dict[tuple[IMSI, Month, Service], MonthlyUsageAggregate] = {}
        self._add_records(records or [])

    def query_month(self, month: Month) -> Iterable[model.MonthlyUsageRecords]:
        return self._records.get(month, [])

    def remove_by_month(self, month: Month) -> None:
        del self._records[month]

    def bulk_insert(self, usage_records: Iterable[model.MonthlyUsageRecords]):
        self._add_records(usage_records)

    def aggregate_month(
        self,
        month: Month,
        services: Set[Service] = None,
        imsi_list: list[IMSI] = None,
    ) -> Iterable[MonthlyUsageAggregate]:
        yield from self._aggregate_month(month, services, imsi_list, fill_missing=False)

    def _add_records(self, records: Iterable[MonthlyUsageRecords]):
        for r in list(records):
            self._records.setdefault(r.month, []).append(r)
            agg = self._aggregates.setdefault(
                (r.imsi, r.month, r.service),
                MonthlyUsageAggregate(
                    imsi=r.imsi, volume=0, service=r.service, month=r.month
                ),
            )
            agg["volume"] = (agg["volume"] or 0) + r.volume
        self._imsi_list = set(map(attrgetter("imsi"), records))

    def aggregate_month_fill_missing(
        self,
        month: Month,
        services: Iterable[Service] = None,
        imsi_list: list[IMSI] = None,
    ) -> Iterable[MonthlyUsageAggregate]:
        yield from self._aggregate_month(month, services, imsi_list, fill_missing=True)

    def _aggregate_month(
        self,
        month: Month,
        services: Iterable[Service] = None,
        imsi_list: list[IMSI] = None,
        *,
        fill_missing: bool,
    ) -> Iterable[MonthlyUsageAggregate]:
        services = services or list(Service)
        _imsi_list = self._imsi_list
        if imsi_list is not None:
            _imsi_list = self._imsi_list.intersection(imsi_list)
        for imsi, service in itertools.product(_imsi_list, services):
            record = self._aggregates.get((imsi, month, service))
            if record is not None:
                yield record
            elif fill_missing:
                yield self._no_usage_record(imsi, month, service)

    @classmethod
    def _no_usage_record(
        cls, imsi: IMSI, month: Month, service: Service
    ) -> MonthlyUsageAggregate:
        return MonthlyUsageAggregate(
            imsi=imsi, service=service, month=month, volume=None
        )

    def get_monthly_usage(
        self,
        month: Month,
    ) -> Iterator[UsageModel]:
        ...


class DatabaseUsageRepository(AbstractUsageRepository):
    def __init__(self, session: Session):
        self.session = session

    def bulk_insert(self, usage_records: Iterable[model.MonthlyUsageRecords]):
        self.session.bulk_save_objects(list(usage_records))
        self.session.commit()

    def query_month(self, month: Month) -> Iterable[model.MonthlyUsageRecords]:
        query = select(model.MonthlyUsageRecords).where(
            orm.monthly_usage_records.c.month == month
        )
        yield from self.session.execute(
            query, execution_options={"yield_per": 100}
        ).scalars()

    def remove_by_month(self, month: Month) -> None:
        query = delete(model.MonthlyUsageRecords).filter_by(month=month)
        self.session.execute(query)
        self.session.commit()

    def aggregate_month(
        self,
        month: Month,
        services: Set[Service] = None,
        imsi_list: list[IMSI] = None,
    ) -> Iterable[MonthlyUsageAggregate]:
        table = orm.monthly_usage_records

        filter_criteria = table.c.month == month
        if services is not None:
            filter_criteria &= table.c.service.in_(services)
        if imsi_list is not None:
            filter_criteria &= table.c.imsi.in_(imsi_list)

        query = (
            select(
                table.c.imsi,
                table.c.service,
                func.sum(table.c.volume).label("volume"),
            )
            .filter(filter_criteria)
            .group_by(table.c.imsi, table.c.service)
            .order_by(table.c.imsi, table.c.service)
        )
        yield from self._iter_monthly_aggregate(query, month)

    def aggregate_month_fill_missing(
        self,
        month: Month,
        services: Set[Service] = None,
        imsi_list: list[IMSI] = None,
    ):
        table = orm.monthly_usage_records

        imsi_stmt = select(table.c.imsi).distinct()
        if imsi_list is not None:
            imsi_stmt = imsi_stmt.filter(table.c.imsi.in_(imsi_list))
        imsi_cte = imsi_stmt.cte("imsi_list")

        record_cte = (
            select(
                table.c.imsi,
                table.c.volume,
                table.c.service,
            )
            .where(table.c.month == month)
            .cte("monthly_records")
        )
        services = services or set(Service)
        service_stmt = func.unnest(list(services)).table_valued("service")
        query = (
            select(
                imsi_cte.c.imsi,
                service_stmt.c.service,
                func.sum(record_cte.c.volume).label("volume"),
            )
            .select_from(
                imsi_cte.join(service_stmt, true()).outerjoin(
                    record_cte,
                    and_(
                        record_cte.c.imsi == imsi_cte.c.imsi,
                        record_cte.c.service == service_stmt.c.service,
                    ),
                )
            )
            .group_by(imsi_cte.c.imsi, service_stmt.c.service)
            .order_by(imsi_cte.c.imsi, service_stmt.c.service)
        )
        yield from self._iter_monthly_aggregate(query, month)

    def _iter_monthly_aggregate(
        self, query: Select, month: Month, yield_per: int = 100
    ) -> Generator[MonthlyUsageAggregate, None, None]:
        """Yield query rows as MonthlyUsageAggregate."""

        result = self.session.execute(query, execution_options={"yield_per": yield_per})
        for row in result:
            yield MonthlyUsageAggregate(
                imsi=row.imsi,
                service=Service(row.service),
                # Convert from Decimal, because in postgresql: sum ( bigint ) → numeric
                # https://www.postgresql.org/docs/current/functions-aggregate.html
                volume=int(row.volume) if row.volume else row.volume,
                month=month,
            )

    def get_monthly_usage(
        self,
        month: Month,
    ) -> Iterator[UsageModel]:
        table = orm.monthly_usage_records
        query = select(
            table.c.imsi,
            table.c.operator,
            table.c.service,
            table.c.volume,
            table.c.month,
        ).where(table.c.month == month)
        for row in self.session.execute(query).mappings():
            yield UsageModel(**row)


class AbstractAppRepository(ABC):
    @abstractmethod
    def create(self, version: model.AppVersion) -> bool:
        ...

    @abstractmethod
    def get(self) -> model.AppVersionView | None:
        ...


class InMemoryAppRepository(AbstractAppRepository):
    def __init__(self):
        self.app_version = []

    def create(self, version: model.AppVersion) -> bool:
        self.app_version = version
        return True

    def get(self) -> model.AppVersionView | None:
        if not self.app_version:
            return None
        else:
            return model.AppVersionView(
                id=1, name=self.app_version.name, version=self.app_version.version
            )


class DatabaseAppRepository(AbstractAppRepository):
    def __init__(self, session: Session):
        self.session = session

    def create(self, version: model.AppVersion) -> bool:
        existing_version = (
            self.session.query(model.AppVersion)
            .filter_by(name=version.name)
            .filter_by(version=version.version)
            .first()
        )
        if existing_version is None:
            self.session.add(version)
            self.session.commit()
            return True
        return False

    def get(self) -> model.AppVersionView | None:
        source = orm.app_version
        query = select(source.c.id, source.c.name, source.c.version).order_by(
            desc(source.c.id)
        )
        version_info = self.session.execute(query).first()
        if not version_info:
            return None
        return model.AppVersionView(
            id=version_info.id, name=version_info.name, version=version_info.version
        )
