from datetime import datetime

from sqlalchemy import (
    CHAR,
    BigInteger,
    Column,
    Date,
    DateTime,
    Enum,
    Identity,
    Integer,
    String,
    Table,
)

from common.db import mapper_registry
from common.types import IMSI, Service
from rating.domain import model

monthly_usage_records = Table(
    "monthly_usage_records",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    Column("imsi", String(length=IMSI.max_length), nullable=False),
    <PERSON>umn("service", Enum(Service, name="service"), nullable=False),
    <PERSON>umn("operator", CHAR(length=5), nullable=False),
    Column("month", Date, nullable=False),
    Column("volume", BigInteger, nullable=False),
)


app_version = Table(
    "app_version",
    mapper_registry.metadata,
    Column("id", Integer, Identity(), primary_key=True, autoincrement=True),
    <PERSON>umn("name", String(length=15), nullable=False),
    <PERSON>umn("version", String(), nullable=False),
    <PERSON>umn("created_at", DateTime, default=datetime.utcnow, nullable=False),
)


def start_mappers() -> None:
    mapper_registry.map_imperatively(model.MonthlyUsageRecords, monthly_usage_records)
    mapper_registry.map_imperatively(model.AppVersion, app_version)


def stop_mappers() -> None:
    mapper_registry.dispose()
