import os
import pathlib
import tempfile
import uuid
from datetime import datetime
from enum import Enum
from typing import Iterator

from app.config import logger
from common.file_storage import AbstractFileStorage
from common.parser import ParsingError
from rating.domain.model import MonthlyUsageRecords
from rating.parser import ManxCSVParser


class CDRFolder(str, Enum):
    COMPLETED = "Completed"
    FAILED = "Failed"


class CDRStorage:
    parser = ManxCSVParser

    def __init__(self, file_storage: AbstractFileStorage):
        self.file_storage = file_storage

    @staticmethod
    def _generate_target_path(
        file_key: str, folder: CDRFolder, prefix_path: pathlib.Path | None
    ) -> str:
        if not prefix_path:
            return f"{folder}/{file_key}"
        file_path = pathlib.Path(file_key)
        return str(
            pathlib.Path(prefix_path)
            / folder.value
            / os.path.relpath(file_path, prefix_path)
        )

    @staticmethod
    def _check_already_parsed_files(
        file_key: str, prefix_path: pathlib.Path | None
    ) -> bool:
        if not prefix_path:
            return file_key.startswith((CDRFolder.COMPLETED, CDRFolder.FAILED))

        return file_key.startswith(
            (
                str(prefix_path / CDRFolder.COMPLETED.value),
                str(prefix_path / CDRFolder.FAILED.value),
            )
        )

    def extract_new_records(
        self, prefix_path: pathlib.Path | None
    ) -> Iterator[MonthlyUsageRecords]:
        for file_key in self.file_storage.files_list(prefix_path):
            if self._check_already_parsed_files(file_key, prefix_path):
                continue

            with tempfile.NamedTemporaryFile("w+b") as temp_file:
                self.file_storage.download_file(file_key, temp_file)
                temp_file.seek(0)
                try:
                    yield from self.parser(temp_file)
                except ParsingError:
                    folder = CDRFolder.FAILED
                else:
                    folder = CDRFolder.COMPLETED
                target_path = self._generate_target_path(file_key, folder, prefix_path)
                self.file_storage.move_file(file_key, target_path)

    def real_time_usage_file(self, xml_string: str) -> str:
        """Upload realtime usage records file to the s3 bucket."""
        current_date = datetime.now()
        formatted_date = current_date.strftime("%Y-%m-%d")
        file_newname = f"{formatted_date}_{uuid.uuid4()}.xml"
        logger.info(f"CDR file name : {file_newname}")

        with open(file_newname, "w") as f:
            f.write(xml_string)

        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            with open(file_newname, "rb") as f:
                temp_file.write(f.read())
            file_path = temp_file.name
            os.remove(file_path)

        application_path = os.path.abspath(file_newname)
        return self.file_storage.upload_file(file_newname, file_path=application_path)

    def custom_sim_file(self, file_newname: str, file_obj: bytes) -> str:
        file_newname = f"{file_newname.split('.')[0]}_{uuid.uuid4()}.csv"
        return self.file_storage.upload_file(file_newname, file_obj=file_obj)

    def error_upload_file(
        self, file_newname: str, file_obj: bytes, file_path: str
    ) -> str:
        return self.file_storage.upload_file(
            file_newname, file_obj=file_obj, file_path=file_path
        )

    def error_cdr_file_storage(self, xml_string: str) -> str:
        """Upload realtime usage records file to the s3 bucket."""
        current_date = datetime.now()
        formatted_date = current_date.strftime("%Y-%m-%d")
        file_newname = f"{formatted_date}_{uuid.uuid4()}.xml"
        logger.info(f"Error CDR file name : {file_newname}")

        with open(file_newname, "w") as f:
            f.write(xml_string)

        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            with open(file_newname, "rb") as f:
                temp_file.write(f.read())
            file_path = temp_file.name
            os.remove(file_path)

        application_path = os.path.abspath(file_newname)
        return self.file_storage.upload_file(file_newname, file_path=application_path)

    def duplicate_cdr_file_storage(
        self, xml_string: str, session_endtime_elem: str
    ) -> str:
        """Upload duplicate cdr records file to the s3 bucket."""
        current_date = datetime.strptime(session_endtime_elem, "%Y-%m-%d %H:%M:%S")
        formatted_date = current_date.strftime("%Y-%m-%d")
        file_newname = f"{formatted_date}_{uuid.uuid4()}.xml"

        with open(file_newname, "w") as f:
            f.write(xml_string)

        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            with open(file_newname, "rb") as f:
                temp_file.write(f.read())
            file_path = temp_file.name
            os.remove(file_path)

        application_path = os.path.abspath(file_newname)
        self.file_storage.upload_file(file_newname, file_path=application_path)
        return file_newname

    def download_file_to_local(self, filename: str) -> str | None:
        return self.file_storage.download_file_to_local(filename)
