from datetime import datetime

from typing_extensions import assert_never

from common.parser import BaseCSVParserRowInList, ParsingError
from common.types import IMSI, Service
from rating.domain.model import MonthlyUsageRecords


class ManxCSVParser(BaseCSVParserRowInList[MonthlyUsageRecords]):
    REQUIRED_FIELDNAMES: tuple = (
        "IMSI",
        "Month",
        "Carrier",
        "VoiceMOSeconds",
        "VoiceMTSeconds",
        "SMSMO",
        "SMSMT",
        "TotalBytes",
    )

    @staticmethod
    def _parse_month(value: str):
        try:
            return datetime.strptime(value, "%Y%m").date()
        except ValueError:
            raise ParsingError(f"Invalid date format for `Month` column: {value}")

    @staticmethod
    def _match_service_value(service: Service) -> str:
        """Return column name for the volume value according to service."""
        match service:
            case Service.VOICE_MO:
                return "VoiceMOSeconds"
            case Service.VOICE_MT:
                return "VoiceMTSeconds"
            case Service.SMS_MO:
                return "SMSMO"
            case Service.SMS_MT:
                return "SMSMT"
            case Service.DATA:
                return "TotalBytes"
            case _:
                assert_never(service)

    def _get_and_validate_volume(self, service: Service, row: dict[str, str]) -> int:
        """Return volume value after validation."""
        column_name = self._match_service_value(service)
        volume = row[column_name]
        if volume.upper() in ("", "NULL"):
            return -1
        elif volume.isdigit():
            return int(volume)
        else:
            raise ParsingError(
                f"Invalid line: {row}, value for column `{column_name}` "
                f"should be integer, but we have `{volume}`"
            )

    def _parse_row(self, row: dict[str, str]) -> list[MonthlyUsageRecords]:
        mur_objects = []
        for service in Service:
            if (volume := self._get_and_validate_volume(service, row)) < 0:
                continue
            mur_objects.append(
                MonthlyUsageRecords(
                    imsi=IMSI(row["IMSI"]),
                    service=service,
                    operator=row["Carrier"],
                    month=self._parse_month(row["Month"]),
                    volume=volume,
                )
            )
        return mur_objects
