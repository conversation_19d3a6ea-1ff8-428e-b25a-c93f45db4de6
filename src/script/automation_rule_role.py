import logging
from urllib.parse import urlparse

import requests
from fastapi import HTT<PERSON><PERSON>x<PERSON>, status
from pydantic import EmailStr
from sqlalchemy.orm import Session

from app.config import logger, settings
from app.db.session import make_session
from app.security import get_service_access_token
from authorization.domain import model


def get_db_session() -> Session:
    session = make_session()
    try:
        return session
    finally:
        session.close()


class AutomationScriptError(Exception):
    ...


class DatabaseAuthRepository:
    def __init__(self, session: Session) -> None:
        self.session = session
        self.condition_true = True

    def create_role(
        self,
        role_request: model.UserRole,
    ) -> None:
        self.session.add(role_request)
        self.session.flush()
        self.session.commit()


class AuthService:
    def __init__(
        self,
    ):
        ...

    def build_url(self, path: str) -> str:
        if not settings.APP_BASE_URL:
            logging.error("APP_BASE_URL is missing.")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="APP_BASE_URL is missing.",
            )
        url = settings.APP_BASE_URL + path
        return url

    def get_kc_roles(self, role_name: str):
        try:
            logger.info(f"OIDC url : {settings.OIDC_AUTHORIZATION_URL}")
            parsed_url = urlparse(settings.OIDC_AUTHORIZATION_URL)
            logger.info(f"parsed url : {parsed_url}")
            KEYCLOAK_URL = f"{parsed_url.scheme}://{parsed_url.netloc}"  # type: ignore
            logger.info(f"Keycloak url: {KEYCLOAK_URL}")
            path_parts = parsed_url.path.split("/")  # type: ignore
            REALM = path_parts[3]
            BASE_ADMIN_URL = f"{KEYCLOAK_URL}/auth/admin/realms/{REALM}"  # type: ignore
            url = f"{BASE_ADMIN_URL}/roles?search={role_name}"

            token = get_service_access_token()
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}",
            }
            kc_roles = requests.get(url=url, headers=headers, timeout=settings.TIMEOUT)
            if kc_roles.status_code == 200:
                return kc_roles.json()
            else:
                raise AutomationScriptError("Keycloak Roles not found")
        except requests.exceptions.Timeout as e:
            logger.error(
                f"Request timed out while fetching roles from Keycloak: {str(e)}"
            )
            raise AutomationScriptError("Request timeout occurred.")


if __name__ == "__main__":
    session = get_db_session()
    auth_repository = DatabaseAuthRepository(session)
    http_auth = AuthService()
    try:
        # Getting AutomationRule from keycloak
        kc_role = http_auth.get_kc_roles(role_name="AutomationRule")

        # Create role in DB
        create_role_details = model.UserRole(
            role="AutomationRule",
            rolegroup=model.RoleGroup.MY_ORGANIZATION.name,
            is_default=True,
            created_by=EmailStr("<EMAIL>"),
            role_uuid=kc_role[0]["id"],
        )
        auth_repository.create_role(role_request=create_role_details)

        logger.info("Roles created successfully.")
    except AutomationScriptError as e:
        raise e

"""python -m script.automation_rule_role"""
