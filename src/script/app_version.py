import argparse
import logging

from fastapi import Depends
from pydantic import ValidationError
from sqlalchemy.orm import Session

from app.db.session import make_session
from common.types import Version
from rating.adapters.usage_repository import DatabaseAppRepository
from rating.domain.model import AppVersion


class InvalidVersionError(BaseException):
    ...


def get_db_session() -> Session:
    session = make_session()
    try:
        return session
    finally:
        session.close()


def db_app_repository(
    session: Session = Depends(get_db_session),
) -> DatabaseAppRepository:
    return DatabaseAppRepository(session)


class AppVersionService:
    def __init__(
        self,
    ):
        ...

    def create_app_version(
        self,
        name: str,
        version: Version,
        app_service: DatabaseAppRepository = db_app_repository(
            session=get_db_session()
        ),
    ) -> bool:
        try:
            version_obj = AppVersion(name=name, version=version)
            result = app_service.create(version_obj)
            logging.info(f"App version created. name: {name}, version: {version}")
            return result
        except ValidationError as e:
            raise InvalidVersionError(str(e))


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Create an app version")
    parser.add_argument(
        "--name", type=str, default=False, required=True, help="Name of the app"
    )
    parser.add_argument(
        "--version", type=Version, required=True, help="Version of the app"
    )

    args = parser.parse_args()

    version = AppVersionService()
    version.create_app_version(args.name, args.version)


"""python app_version.py --name BUNDLE --version 1.0.1"""
