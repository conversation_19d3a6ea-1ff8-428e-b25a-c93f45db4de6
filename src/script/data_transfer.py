from functools import cache

from pymongo import MongoClient
from pymongo.database import Database
from sqlalchemy import func
from sqlalchemy.orm import Session

from app.config import logger, settings
from app.db.session import make_session
from sim.adapters import orm


def get_mongodb_url() -> str:
    if not settings.MONGODB_DATABASE_URI:
        raise RuntimeError(
            "No MongoDB configuration provided, set DATABASE_URI variable"
        )

    return settings.MONGODB_DATABASE_URI


@cache
def get_client_mongodb() -> MongoClient:
    return MongoClient(get_mongodb_url())


def get_mongodb_database(client: MongoClient) -> Database:
    return client.get_database()


def make_session_mongodb() -> Database:
    client = get_client_mongodb()
    return get_mongodb_database(client)


def get_mongodb_session() -> Database:
    return make_session_mongodb()


def get_db_session() -> Session:
    session = make_session()
    try:
        return session
    finally:
        session.close()


class ScriptError(Exception):
    ...


class DatabaseSIMAuditRepository:
    def __init__(self, session, mongodb_session) -> None:
        self.session = session
        self.mongodb_session = mongodb_session
        self.condition_true = True

    @staticmethod
    def collection_exists(db, collection_name):
        return collection_name in db.list_collection_names()

    @staticmethod
    def _get_activity_collection(mongodb_session):
        return mongodb_session["sim_activity_log"]

    @staticmethod
    def _get_provider_collection(mongodb_session):
        return mongodb_session["sim_provider_log"]

    def data_transfer(self, batch_size=1000):

        sim_activity = orm.sim_activity_log
        sim_provider = orm.sim_provider_log

        total_records_activity = self.session.query(
            func.count(sim_activity.c.uuid)
        ).scalar()
        total_records_provider = self.session.query(
            func.count(sim_provider.c.id)
        ).scalar()
        logger.info(
            f"Total records to transfer from Activity: {total_records_activity}"
        )
        logger.info(
            f"Total records to transfer from Provider: {total_records_provider}"
        )

        activity_collection = self._get_activity_collection(self.mongodb_session)
        if not self.collection_exists(self.mongodb_session, "sim_activity_log"):
            self.mongodb_session.create_collection("sim_activity_log")
            logger.info("Activity collection created")

        provider_collection = self._get_provider_collection(self.mongodb_session)
        if not self.collection_exists(self.mongodb_session, "sim_provider_log"):
            self.mongodb_session.create_collection("sim_provider_log")
            logger.info("Provider collection created")

        total_transferred = 0
        for offset in range(0, total_records_activity, batch_size):
            _activity = (
                self.session.query(
                    sim_activity.c.uuid,
                    sim_activity.c.imsi,
                    sim_activity.c.iccid,
                    sim_activity.c.msisdn,
                    sim_activity.c.request_type,
                    sim_activity.c.prior_value,
                    sim_activity.c.new_value,
                    sim_activity.c.client_ip,
                    sim_activity.c.created_at,
                    sim_activity.c.created_by,
                )
                .offset(offset)
                .limit(batch_size)
            )
            result = self.session.execute(_activity).all()

            documents = [
                {
                    "uuid": str(row.uuid),
                    "imsi": row.imsi,
                    "iccid": row.iccid,
                    "msisdn": row.msisdn,
                    "request_type": row.request_type,
                    "prior_value": row.prior_value,
                    "new_value": row.new_value,
                    "field": "Status",
                    "action": "Updated",
                    "client_ip": row.client_ip,
                    "created_date": row.created_at,
                    "created_by": row.created_by,
                }
                for row in result
            ]

            if documents:
                activity_collection.insert_many(documents)
                total_transferred += len(documents)
                logger.info(
                    f"Transferred {total_transferred}/{total_records_activity} \
                        records to MongoDB"
                )

        logger.info(f"Total records transferred to MongoDB: {total_transferred}")

        # Verify the number of records in MongoDB
        mongo_count = activity_collection.count_documents({})
        logger.info(
            f"Total records in MongoDB collection 'sim_activity_log': {mongo_count}"
        )

        total_transferred_provider = 0
        for offset in range(0, total_records_provider, batch_size):
            _provider = (
                self.session.query(
                    sim_provider.c.audit_date,
                    sim_provider.c.message,
                    sim_provider.c.status,
                    sim_provider.c.work_id,
                    sim_provider.c.prior_status,
                    sim_provider.c.sim_activity_log_uuid,
                )
                .offset(offset)
                .limit(batch_size)
            )
            result_provider = self.session.execute(_provider).all()

            documents_provider = [
                {
                    "audit_date": row.audit_date,
                    "message": row.message,
                    "status": row.status,
                    "work_id": row.work_id,
                    "prior_status": row.prior_status,
                    "sim_activity_log_uuid": str(row.sim_activity_log_uuid),
                }
                for row in result_provider
            ]

            if documents_provider:
                provider_collection.insert_many(documents_provider)
                total_transferred_provider += len(documents_provider)
                logger.info(
                    f"Transferred {total_transferred_provider}/ \
                        {total_records_provider} records to MongoDB"
                )

        logger.info(
            f"Total records transferred to MongoDB: {total_transferred_provider}"
        )

        # Verify the number of records in MongoDB
        mongo_count_provider = provider_collection.count_documents({})
        logger.info(
            f"Total records in MongoDB collection \
                'sim_provider_log': {mongo_count_provider}"
        )

        return None


if __name__ == "__main__":
    session = get_db_session()
    mongodb_session = get_mongodb_session()
    audit_repository = DatabaseSIMAuditRepository(session, mongodb_session)
    try:
        logger.info("Data transfer start.")
        audit_repository.data_transfer()
        logger.info("Data transfer successfully.")
    except ScriptError as e:
        raise e

"""python -m script.data_transfer"""
