from uuid import uuid4

from sqlalchemy import null, update
from sqlalchemy.orm import Session

from app.config import logger
from app.db.session import make_session
from cdrdata.adapters import orm

try:
    from common.utils import trace_id_var

    trace_id_var.set(f"script-run-{uuid4()}")
except ImportError:
    pass  # Optional fallback if not using trace_id_var


def get_db_session() -> Session:
    session = make_session()
    try:
        return session
    finally:
        session.close()


class ScriptError(Exception):
    ...


class DatabaseCdrRepository:
    def __init__(self, session: Session) -> None:
        self.session = session

    def update_cdr_types(self):

        cdr = orm.sim_cdr
        cdr_data = orm.sim_cdr_data
        cdr_voice = orm.sim_cdr_voice
        cdr_sms = orm.sim_cdr_sms

        try:
            # Update 'gsm-data' type from cdr_data
            stmt_data = (
                update(cdr)
                .values(type="gsm-data")
                .where(cdr.c.uuid == cdr_data.c.cdr_uuid)
            )
            result_data = self.session.execute(stmt_data)
            logger.info(f"'gsm-data' updated rows: {result_data.rowcount}")

            # Update 'out' type from cdr_voice
            stmt_voice = (
                update(cdr).values(type="out").where(cdr.c.uuid == cdr_voice.c.cdr_uuid)
            )
            result_voice = self.session.execute(stmt_voice)
            logger.info(f"'out' updated rows: {result_voice.rowcount}")

            # Update 'sms' type from cdr_sms
            stmt_sms = (
                update(cdr).values(type="sms").where(cdr.c.uuid == cdr_sms.c.cdr_uuid)
            )
            result_sms = self.session.execute(stmt_sms)
            logger.info(f"'sms' updated rows: {result_sms.rowcount}")

            # Update remaining null types to 'unknown'
            stmt_unknown = (
                update(cdr).values(type="unknown").where(cdr.c.type.is_(null()))
            )
            result_unknown = self.session.execute(stmt_unknown)
            logger.info(f"'unknown' updated rows: {result_unknown.rowcount}")

            self.session.commit()
            logger.info("CDR type fields updated successfully.")
        except Exception as e:
            self.session.rollback()
            logger.error(f"Failed to update CDR types: {e}")
            raise ScriptError("Update failed") from e


if __name__ == "__main__":
    session = get_db_session()
    updater = DatabaseCdrRepository(session)
    try:
        updater.update_cdr_types()
    except ScriptError as e:
        raise e


"""python -m script.30_05_2025_cdr_type_add"""
