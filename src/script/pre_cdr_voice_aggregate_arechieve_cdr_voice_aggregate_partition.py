import logging
from datetime import date, datetime, timedelta

from dateutil.relativedelta import relativedelta
from sqlalchemy import text
from sqlalchemy.orm import Session

from app.db.session import make_session


def get_db_session() -> Session:
    session = make_session()
    try:
        return session
    finally:
        session.close()


class DatabaseCDRRepository:
    def __init__(self, session) -> None:
        self.session = session
        self.condition_true = True

    def transfer_data_to_cdr_voice_aggregate_partitions(
        self, start_date, end_date
    ) -> None:
        start_time = datetime.now()
        logging.info(f"Script started at: {start_time}")

        # Define the batch size
        batch_size = 10000

        partition_date = start_date

        # Loop through each month from start_date to end_date
        while partition_date < end_date:
            partition_name = f'cdr_voice_aggregate_{partition_date.strftime("%Y_%m")}'
            v_offset = 0

            if not partition_name.isidentifier():
                raise ValueError("Invalid partition name")

            next_part_date = (partition_date + relativedelta(months=1)).replace(day=1)

            while True:
                batch_running_start_time = datetime.now()
                sql = text(
                    """
                        WITH ins AS (
                            INSERT INTO :partition_name
                            SELECT *
                            FROM ONLY cdr_voice_aggregate_archieve
                            WHERE month >= :partition_date
                            AND month < :next_month_date
                            ORDER BY id, month
                            LIMIT :batch_size OFFSET :v_offset
                            RETURNING 1
                        )
                        SELECT count(*) FROM ins
                    """
                )
                transferred_records = self.session.execute(
                    sql,
                    {
                        "partition_name": partition_name,
                        "partition_date": partition_date,
                        "next_month_date": next_part_date,
                        "batch_size": batch_size,
                        "v_offset": v_offset,
                    },
                ).scalar()

                if transferred_records == 0:
                    break  # Exit the loop if no more records are transferred

                v_offset += batch_size
                self.session.commit()

                logging.info(
                    f"Transferred {transferred_records} records to"
                    f" partition {partition_name} in this batch"
                )

                batch_running_end_time = datetime.now()
                execution_time = batch_running_end_time - batch_running_start_time
                logging.info(
                    f"Batch Execution time: {execution_time} per"
                    f" {transferred_records} records."
                )

            # After all batches for the current month, print a summary
            logging.info(f"Completed data transfer for partition {partition_name}")

            # Move to the next month
            partition_date = next_part_date

        end_time = datetime.now()
        logging.info(f"Script ended at: {end_time}")

        execution_time = end_time - start_time
        logging.info(f"Total execution time: {execution_time}")


class ScriptError(Exception):
    ...


if __name__ == "__main__":
    session = get_db_session()
    cdr_repository = DatabaseCDRRepository(session)
    try:
        logging.info("Data transfer CDR_VOICE_AGGREGATE table started.")

        # Get the start date from the cdr_sms_archieve table
        """
        This will handle the last 3 month date and will set the date even
        the year handleling will also be done if the last 3rd month is in
        the previous year
        """

        min_date = session.execute(
            text(
                """SELECT date_trunc('month', MIN(month))::DATE
                FROM cdr_voice_aggregate_archieve"""
            )
        ).scalar()

        start_date = date.today().replace(day=1) - relativedelta(months=2)
        logging.info(f"Start Date details: {start_date}")

        if min_date is None:
            raise ScriptError("No data found!!!!!")

        if start_date < min_date:
            start_date = min_date

        # Get the end date
        end_date = date.today() + timedelta(days=1)

        logging.info(f"End Date details: {end_date}")

        # Starting the data transfer
        cdr_repository.transfer_data_to_cdr_voice_aggregate_partitions(
            start_date=start_date, end_date=end_date
        )
        logging.info("Data transfered successfully.")
    except ScriptError as e:
        raise e

"""python -m script.pre_cdr_voice_aggregate_arechieve_cdr_voice_aggregate_partition"""
