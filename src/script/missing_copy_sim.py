import argparse

import pandas as pd  # type: ignore
from fastapi import Depends, status
from sqlalchemy.orm import Session

from app.config import logger, settings
from app.db.session import make_session
from common.types import IMSI, Month
from sim.adapters.repository import DatabaseSimRepository


def get_db_session() -> Session:
    session = make_session()
    try:
        return session
    finally:
        session.close()


def _sim_repository(
    session: Session = Depends(get_db_session),
) -> DatabaseSimRepository:
    return DatabaseSimRepository(session)


class SimNotFoundError(Exception):
    def __init__(self, detail: str, status_code: int = 404):
        self.detail = detail
        self.status_code = status_code


class DatabaseError(Exception):
    def __init__(self, detail: str, status_code: int = 400):
        self.detail = detail
        self.status_code = status_code


class SimDetails:
    def __init__(
        self,
    ):
        ...

    def get_csv_data(self, path: str):
        sheet1 = pd.read_csv(path)
        return sheet1

    def add_missing_sim_data(
        self,
        imsi: IMSI,
        is_first_active: bool,
        copy_month: Month,
        sim_repository: DatabaseSimRepository = _sim_repository(
            session=get_db_session()
        ),
    ) -> bool:

        try:
            sim_response = sim_repository.add_missing_monthly_data(
                imsi, is_first_active, copy_month=copy_month
            )
            return sim_response
        except DatabaseError as e:
            logger.info(f"Database error: {str(e)}")
            raise DatabaseError(
                f"Database error: {str(e)}", status.HTTP_400_BAD_REQUEST
            )
        except ValueError as e:
            logger.info(f"Invalid input values: {str(e)}")
            raise ValueError(
                f"Invalid input values: {str(e)}", status.HTTP_404_NOT_FOUND
            )
        except PermissionError as e:
            logger.info(f"Permission error: {str(e)}")
            raise PermissionError(
                f"Permission error: {str(e)}", status.HTTP_401_UNAUTHORIZED
            )


if __name__ == "__main__":
    path = f"{settings.ADD_MONTHLY_DATA}"
    logger.info(f"File_URL:- {path}")
    parser = argparse.ArgumentParser(description="Add monthly data from CSV records")
    parser.add_argument(
        "--copy-month", type=str, default=False, required=True, help="month to copy"
    )
    args = parser.parse_args()
    copy_month = Month.from_str(args.copy_month)

    obj = SimDetails()
    csv_data = obj.get_csv_data(path=path)
    imsis_activations = csv_data[["IMSI", "is_first_activation"]].values.tolist()

    logger.info(f"Trying to update data for IMSIs: {imsis_activations}")
    for data in imsis_activations:
        sim_response = obj.add_missing_sim_data(data[0], data[1], copy_month)
        logger.info(f"Record added for {data[0]} : {sim_response}")

# python -m script.missing_copy_sim --copy-month '2023-01'
