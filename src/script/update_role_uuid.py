from urllib.parse import urlparse

import requests
from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.orm import Session

from app.config import logger, settings
from app.db.session import make_session
from app.security import get_service_access_token
from authorization.adapters import orm
from authorization.domain import model


def get_db_session() -> Session:
    session = make_session()
    try:
        return session
    finally:
        session.close()


class ScriptError(Exception):
    ...


class DBRoleResponse(BaseModel):
    role: str
    rolegroup: model.RoleGroup
    created_by: str
    is_default: bool = False


class DatabaseAuthRepository:
    def __init__(self, session: Session) -> None:
        self.session = session
        self.condition_true = True

    def get_roles(self) -> list[DBRoleResponse]:
        role = orm.user_role
        NONE = None
        query = select(
            role.c.role, role.c.rolegroup, role.c.is_default, role.c.created_by
        ).filter(
            role.c.role_uuid == NONE,
        )
        result = self.session.execute(query).all()
        return [DBRoleResponse(**role) for role in result]  # type: ignore

    def update_role_uuid(self, role_info: list) -> None:
        user_role = orm.user_role
        for role in role_info:
            self.session.query(user_role).filter(
                user_role.c.role == role["role"]
            ).update({user_role.c.role_uuid: role["role_uuid"]})
        self.session.commit()


class AuthService:
    def __init__(
        self,
    ):
        ...

    def get_kc_roles(self):
        try:
            logger.info(f"OIDC url : {settings.OIDC_AUTHORIZATION_URL}")
            parsed_url = urlparse(settings.OIDC_AUTHORIZATION_URL)
            logger.info(f"parsed url : {parsed_url}")
            KEYCLOAK_URL = f"{parsed_url.scheme}://{parsed_url.netloc}"
            logger.info(f"Keycloak url: {KEYCLOAK_URL}")
            path_parts = parsed_url.path.split("/")
            REALM = path_parts[3]
            BASE_ADMIN_URL = f"{KEYCLOAK_URL}/auth/admin/realms/{REALM}"
            url = f"{BASE_ADMIN_URL}/roles"

            token = get_service_access_token()
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}",
            }
            kc_roles = requests.get(url=url, headers=headers, timeout=settings.TIMEOUT)
            if kc_roles.status_code == 200:
                return kc_roles.json()
            else:
                raise ScriptError("KC Roles not found")
        except requests.exceptions.Timeout as e:
            logger.error(
                f"Request timed out while updating roles from Keycloak: {str(e)}"
            )
            raise ScriptError("Request timeout occurred.")


if __name__ == "__main__":
    session = get_db_session()
    auth_repository = DatabaseAuthRepository(session)
    http_auth = AuthService()
    try:
        db_roles = auth_repository.get_roles()
        logger.info(f"Total database roles with null uuid : {len(db_roles)}")
        db_roles_dict = {role.role: role for role in db_roles}
        kc_roles = http_auth.get_kc_roles()

        result = []
        for kc_role in kc_roles:
            if kc_role["name"] in db_roles_dict:
                result.append({"role": kc_role["name"], "role_uuid": kc_role["id"]})
        logger.info(f"Total common role in db and keycloak : {len(result)}")
        auth_repository.update_role_uuid(result)
        logger.info("Roles updated successfully.")
    except ScriptError as e:
        raise e

"""python -m script.update_role_uuid"""
