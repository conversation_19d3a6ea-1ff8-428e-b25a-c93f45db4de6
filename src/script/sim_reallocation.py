import argparse

import pandas as pd  # type: ignore
from fastapi import Depends, status
from sqlalchemy.orm import Session

from app.config import logger, settings
from app.db.session import make_session
from common.types import IMSI
from sim.adapters.repository import DatabaseSimRepository


def get_db_session() -> Session:
    session = make_session()
    try:
        return session
    finally:
        session.close()


def _sim_repository(
    session: Session = Depends(get_db_session),
) -> DatabaseSimRepository:
    return DatabaseSimRepository(session)


class SimNotFoundError(Exception):
    def __init__(self, detail: str, status_code: int = 404):
        self.detail = detail
        self.status_code = status_code


class DatabaseError(Exception):
    def __init__(self, detail: str, status_code: int = 400):
        self.detail = detail
        self.status_code = status_code


class SimDetails:
    def __init__(
        self,
    ):
        ...

    def get_excel_data(self, path: str):
        sheet1 = pd.read_excel(path, sheet_name=0)
        return sheet1

    def sim_reallocation(
        self,
        imsis: list[IMSI],
        account_id: int,
        rate_plan_id: int,
        sim_db_service: DatabaseSimRepository = _sim_repository(
            session=get_db_session()
        ),
    ) -> str:

        try:
            reallocation_response = sim_db_service.imsi_reallocation_func(
                imsis=imsis, account_id=account_id, rate_plan_id=rate_plan_id
            )
            return reallocation_response
        except DatabaseError as e:
            logger.info(f"Database error: {str(e)}")
            raise DatabaseError(
                f"Database error: {str(e)}", status.HTTP_400_BAD_REQUEST
            )
        except ValueError as e:
            logger.info(f"Invalid input values: {str(e)}")
            raise ValueError(
                f"Invalid input values: {str(e)}", status.HTTP_404_NOT_FOUND
            )
        except PermissionError as e:
            logger.info(f"Permission error: {str(e)}")
            raise PermissionError(
                f"Permission error: {str(e)}", status.HTTP_401_UNAUTHORIZED
            )


if __name__ == "__main__":
    path = f"{settings.IMSI_REALLOCATION_PATH}"
    logger.info(f"File_URL:- {path}")

    sim_details_instance = SimDetails()
    excel_data = sim_details_instance.get_excel_data(path=path)
    newData = excel_data[["IMSI"]]

    imsi_list = list(map(lambda row: IMSI(row), newData["IMSI"]))

    parser = argparse.ArgumentParser(description="Sim Reallocation check.")
    parser.add_argument(
        "--account_id", type=int, default=False, required=True, help="Account Id"
    )
    parser.add_argument(
        "--rate_plan_id", type=int, default=False, required=True, help="Rate plan Id"
    )
    args = parser.parse_args()

    reallocation_response = sim_details_instance.sim_reallocation(
        imsis=imsi_list, account_id=args.account_id, rate_plan_id=args.rate_plan_id
    )
    logger.info(f"Reallocation details:- {reallocation_response}")
    """python sim_reallocation.py --account_id 1 --rate_plan_id 1"""
    """IMSI_REALLOCATION_PATH=alembic/data/IMSI.xlsx"""
