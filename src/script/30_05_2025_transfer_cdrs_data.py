import concurrent.futures
import logging
import shlex
import subprocess  # nosec
from datetime import datetime

start_time = datetime.now()
logging.info(f"*****POST Script started at: {start_time}")

# List of scripts to run
scripts = [
    "python -m script.28_05_2025_cdr_sms_aggregate_data_transfer",
    "python -m script.28_05_2025_post_cdr_sms_mo_mt_data_transfer",
    "python -m script.28_05_2025_post_cdr_voice_mo_mt_data_transfer",
    "python -m script.28_05_2025_cdr_type_add",
]


# Function to run a script
def run_script(command):
    args = shlex.split(command)
    subprocess.run(args, check=True)  # nosec


# Execute all scripts in parallel
with concurrent.futures.ThreadPoolExecutor() as executor:
    executor.map(run_script, scripts)

end_time = datetime.now()
logging.info(f"*****POST Script ended at: {end_time}")

execution_time = end_time - start_time
logging.info(f"*****POST Total execution time: {execution_time}")

"""python -m script.30_05_2025_transfer_cdrs_data"""
