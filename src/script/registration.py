import argparse
import json

import requests

from app.config import logger, settings
from script import exceptions


class ScriptError(Exception):
    ...


class RegisterURL:
    def __init__(self):

        self.data = []

    def _register_notification_ppl(self) -> str:
        headers = {"Content-Type": "application/json"}
        request = {
            "URL": settings.SPOG_NOTIFICATION_URI,
            "UserName": settings.PPL_USERNAME,
            "Password": settings.PPL_PASSWORD,
        }
        return json.dumps({"headers": headers, "json": request})

    def register_url(self, isUpdate):
        try:
            if isUpdate:
                register = settings.PPL_REGISTER_UPDATE
            else:
                register = settings.PPL_REGISTER

            request = self._register_notification_ppl()
            response_dict = json.loads(request)

            headers = response_dict["headers"]
            json_data = response_dict["json"]

            response = requests.post(
                f"{settings.PPL_URI}{register}",
                headers=headers,
                json=json_data,
                timeout=settings.TIMEOUT,
            )

            register_url = response.json()

            if not register_url["IsSuccess"]:
                raise exceptions.PplAuthError("PPL auth failed.")

            return register_url
        except requests.exceptions.Timeout as e:
            logger.error(f"Request timed out while registering the URL: {str(e)}")
            raise ScriptError("Request timeout occurred.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--is-update",
        type=bool,
        default=False,
        help="to update URL set isUpdate=True, default register new URL.",
    )
    args = parser.parse_args()

    register_url_instance = RegisterURL()
    register_url_instance.register_url(isUpdate=args.is_update)
