import pandas as pd  # type: ignore
from fastapi import Depends, status
from sqlalchemy.orm import Session

from app.config import logger, settings
from app.db.session import make_session
from common.types import IMSI, MSISDN
from sim.adapters.repository import DatabaseSimRepository


def get_db_session() -> Session:
    session = make_session()
    try:
        return session
    finally:
        session.close()


def _sim_repository(
    session: Session = Depends(get_db_session),
) -> DatabaseSimRepository:
    return DatabaseSimRepository(session)


class SimNotFoundError(Exception):
    def __init__(self, detail: str, status_code: int = 404):
        self.detail = detail
        self.status_code = status_code


class DatabaseError(Exception):
    def __init__(self, detail: str, status_code: int = 400):
        self.detail = detail
        self.status_code = status_code


class SimDetails:
    def __init__(
        self,
    ):
        ...

    def get_excel_data(self, path: str):
        sheet1 = pd.read_excel(path, sheet_name=0)
        return sheet1

    def update_sim_msisdn(
        self,
        imsi: IMSI,
        msisdn: MSISDN,
        sim_service: DatabaseSimRepository = _sim_repository(session=get_db_session()),
    ) -> bool:

        try:
            update_response = sim_service.update_sim_msisdn_by_imsi(
                imsi=imsi, msisdn=msisdn
            )
            return update_response
        except DatabaseError as e:
            logger.info(f"Database error: {str(e)}")
            raise DatabaseError(
                f"Database error: {str(e)}", status.HTTP_400_BAD_REQUEST
            )
        except ValueError as e:
            logger.info(f"Invalid input values: {str(e)}")
            raise ValueError(
                f"Invalid input values: {str(e)}", status.HTTP_404_NOT_FOUND
            )
        except PermissionError as e:
            logger.info(f"Permission error: {str(e)}")
            raise PermissionError(
                f"Permission error: {str(e)}", status.HTTP_401_UNAUTHORIZED
            )


if __name__ == "__main__":
    path = f"{settings.MSISDN_UPDATE_FILE}"
    logger.info(f"File_URL:- {path}")

    sim_details_instance = SimDetails()
    excel_data = sim_details_instance.get_excel_data(path=path)
    newData = excel_data[["MSISDN", "IMSI"]]

    sim_data_update = [
        sim_details_instance.update_sim_msisdn(
            imsi=IMSI(row["IMSI"]), msisdn=MSISDN(row["MSISDN"])
        )
        for index, row in newData.iterrows()
    ]
