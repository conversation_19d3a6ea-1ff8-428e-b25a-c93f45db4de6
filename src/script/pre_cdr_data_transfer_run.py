import concurrent.futures
import logging
import shlex
import subprocess  # nosec
from datetime import datetime

start_time = datetime.now()
logging.info(f"*****PRE Script started at: {start_time}")

# List of scripts to run
scripts = [
    "python -m script.pre_cdr_voice_aggregate_arechieve_cdr_voice_aggregate_partition",
    "python -m script.pre_cdr_data_aggregate_arechieve_cdr_data_aggregate_partition",
    "python -m script.pre_cdr_voice_archieve_cdr_voice_partition",
    "python -m script.pre_cdr_sms_archieve_cdr_sms_partition",
    "python -m script.pre_cdr_data_archieve_cdr_data_partition",
    "python -m script.pre_cdr_archieve_cdr_partition",
]


# Function to run a script
def run_script(command):
    args = shlex.split(command)
    subprocess.run(args, check=True)  # nosec


# Execute all scripts in parallel
with concurrent.futures.ThreadPoolExecutor() as executor:
    executor.map(run_script, scripts)

end_time = datetime.now()
logging.info(f"*****PRE Script ended at: {end_time}")

execution_time = end_time - start_time
logging.info(f"*****PRE Total execution time: {execution_time}")

"""python -m script.pre_cdr_data_transfer_run"""
