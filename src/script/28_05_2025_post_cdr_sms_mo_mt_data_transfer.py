import logging
from datetime import datetime

from sqlalchemy import text
from sqlalchemy.orm import Session

from app.db.session import make_session


def get_db_session() -> Session:
    session = make_session()
    try:
        return session
    finally:
        session.close()


class DatabaseCDRRepository:
    def __init__(self, session) -> None:
        self.session = session

    def insert_cdr_sms_mo_mts(self) -> None:
        start_time = datetime.now()
        logging.info(f"Insert script started at: {start_time}")

        insert_sql = text(
            """
            INSERT INTO cdr_sms_mo_mt (
                imsi,
                iccid,
                usage,
                type,
                month
            )
            SELECT
                cd.imsi,
                cd.iccid,
                COUNT(*) AS usage,
                'sms' AS type,
                TO_DATE(
                    TO_CHAR(cs.date_sent, 'YYYY-MM') || '-01',
                    'YYYY-MM-DD'
                ) AS month_start
            FROM cdr_sms cs
            JOIN cdr cd ON cs.cdr_uuid = cd.uuid
            GROUP BY
                cd.imsi,
                cd.iccid,
                TO_CHAR(cs.date_sent, 'YYYY-MM')
            ORDER BY
                cd.imsi,
                cd.iccid,
                month_start
            ON CONFLICT (iccid, month, type)
            DO NOTHING;
            """
        )

        try:
            self.session.execute(insert_sql)
            self.session.commit()
            logging.info("Data inserted into cdr_sms_mo_mt successfully.")
        except Exception as e:
            self.session.rollback()
            logging.error(f"Failed to insert data: {e}")
            raise
        finally:
            end_time = datetime.now()
            logging.info(f"Script ended at: {end_time}")
            logging.info(f"Total execution time: {end_time - start_time}")


class ScriptError(Exception):
    ...


if __name__ == "__main__":
    session = get_db_session()
    cdr_repository = DatabaseCDRRepository(session)
    try:
        logging.info(
            "Inserting aggregated CDR SMS data into cdr_sms_mo_mt " "partitioned table."
        )
        cdr_repository.insert_cdr_sms_mo_mts()
    except ScriptError as e:
        raise e

"""python -m script.28_05_2025_post_cdr_sms_mo_mt_data_transfer"""
