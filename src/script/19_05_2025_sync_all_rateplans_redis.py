import logging

import requests
from fastapi import status
from fastapi.encoders import jsonable_encoder
from platform_api_client import PlatformAPIError
from sqlalchemy.orm import Session

from api.rate_plans.schemas import RatePlanViewDetails
from app.config import settings
from app.db.session import make_session
from app.security import get_service_access_token
from auth.exceptions import ForbiddenError, Unauthorized
from rate_plans.adapters.rate_plan_repository import DatabaseRatePlanRepository
from sync_redis.domain import model as redis_model
from sync_redis.exception import RedisAPIError


def get_db_session() -> Session:
    session = make_session()
    try:
        return session
    finally:
        session.close()


class AutomationScriptError(Exception):
    ...


class DatabaseRARepository:
    def __init__(self, session: Session) -> None:
        self.session = session
        self.condition_true = True


class AuthService:
    def __init__(
        self,
    ):
        self.CREATE_REDIS_RATE_PLAN = "/v1/analytics/rateplan"

        self.token = get_service_access_token()
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}",
        }

    def sync_rateplan_redis(self, data: redis_model.RatePlanInfo):
        try:
            url = f"{settings.APP_BASE_URL}{self.CREATE_REDIS_RATE_PLAN}"
            logging.debug(f"Syncing Fixed Flexi plans: {url}")

            response = requests.post(
                url=url,
                headers=self.headers,
                json=jsonable_encoder(data),
                timeout=settings.TIMEOUT,
            )
            if response.status_code == 201:
                return response.json()
            else:
                raise RedisAPIError
        except PlatformAPIError as e:
            logging.error(f"Error in sync_rateplan_redis. Error: {str(e)}")
            match e.status_code:
                case status.HTTP_401_UNAUTHORIZED:
                    raise Unauthorized("Unauthorized access.")
                case status.HTTP_403_FORBIDDEN:
                    raise ForbiddenError()
                case _:
                    logging.error(f"Error in sync_rateplan_redis. Error: {str(e)}")
        except ConnectionError:
            raise ConnectionError("Failed to connect with the Redis API")
        except requests.exceptions.Timeout as e:
            logging.error(
                f"Request timed out while syncing rateplans in redis: {str(e)}"
            )
            raise AutomationScriptError("Request timeout occurred.")


if __name__ == "__main__":
    session = get_db_session()
    repository = DatabaseRARepository(session)
    auth_service = AuthService()
    rateplan_repository = DatabaseRatePlanRepository(session)
    try:
        rateplans = rateplan_repository.query()
        for rateplan in rateplans:
            if rateplan is None:
                logging.error(f"Rate Plan with id {id} not found.")
                continue
            data = RatePlanViewDetails.from_model_rate_plan(rateplan)
            response = redis_model.RatePlanInfo(
                accountId=data.account_id,
                name=data.name,
                accessFee=data.access_fee,
                currency=data.currency,
                isDefault=data.is_default,
                simLimit=data.sim_limit,
                allowanceUsed=data.allowance_used,
                originationGroups=data.origination_groups,
                ratePlanId=data.id,
            )
            auth_service.sync_rateplan_redis(response)

        logging.info("Fixed Flexi plans synced successfully in redis.")
    except AutomationScriptError as e:
        logging.error(f"Error in syncing Fixed Flexi plans in redis. Error: {str(e)}")
        raise e

"""python -m script.19_05_2025_sync_all_rateplans_redis"""
