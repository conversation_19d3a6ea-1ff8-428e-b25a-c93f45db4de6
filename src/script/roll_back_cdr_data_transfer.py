import logging
from datetime import datetime

from sqlalchemy import text
from sqlalchemy.orm import Session

from app.db.session import make_session


def get_db_session() -> Session:
    session = make_session()
    try:
        return session
    finally:
        session.close()


class DatabaseCDRRepository:
    def __init__(self, session) -> None:
        self.session = session
        self.condition_true = True

    def transfer_data_from_cdr_new_to_cdr(self) -> None:
        # CDR_NEW to CDR
        start_time = datetime.now()
        logging.info(f"To cdr data transfer started at: {start_time}")

        sql = text(
            """
                INSERT INTO cdr (
                    uuid, iccid, country, carrier, created_at,
                    imsi, country_name, cdr_object_id
                )
                SELECT uuid, iccid, country, carrier, created_at, imsi,
                country_name, cdr_object_id
                FROM cdr_new cn
                WHERE NOT EXISTS (
                    SELECT 1
                    FROM cdr c
                    WHERE c.uuid = cn.uuid
                );
            """
        )

        self.session.execute(sql)

        # self.session.commit()

        end_time = datetime.now()
        logging.info(f"To cdr data transfer ended at: {end_time}")

        execution_time = end_time - start_time
        logging.info(f"Total execution time for cdr: {execution_time}")

    def transfer_data_from_cdr_data_new_to_cdr_data(self) -> None:
        # CDR_DATA_NEW to CDR_DATA
        start_time = datetime.now()
        logging.info(f"To cdr_data data transfer started at: {start_time}")

        sql = text(
            """
                INSERT INTO cdr_data (
                    cdr_uuid, session_starttime,
                    session_endtime, duration, data_volume
                )
                SELECT cdr_uuid, session_starttime, session_endtime,
                duration,data_volume
                FROM cdr_data_new cdn
                WHERE NOT EXISTS (
                    SELECT 1
                    FROM cdr_data cd
                    WHERE cd.cdr_uuid = cdn.cdr_uuid
                );
            """
        )

        self.session.execute(sql)

        # self.session.commit()

        end_time = datetime.now()
        logging.info(f"To cdr_data data transfer ended at: {end_time}")

        execution_time = end_time - start_time
        logging.info(f"Total execution time for cdr_data: {execution_time}")

    def transfer_data_from_cdr_voice_new_to_cdr_voice(self) -> None:
        # CDR_VOICE_NEW to CDR_VOICE
        start_time = datetime.now()
        logging.info(f"To cdr_voice data transfer started at: {start_time}")

        sql = text(
            """
                INSERT INTO cdr_voice (cdr_uuid, call_date, call_number, call_minutes)
                SELECT cdr_uuid, call_date, call_number, call_minutes
                FROM cdr_voice_new cvn
                WHERE NOT EXISTS (
                    SELECT 1
                    FROM cdr_voice cv
                    WHERE cv.cdr_uuid = cvn.cdr_uuid
                );
            """
        )

        self.session.execute(sql)

        # self.session.commit()

        end_time = datetime.now()
        logging.info(f"To cdr_voice data transfer ended at: {end_time}")

        execution_time = end_time - start_time
        logging.info(f"Total execution time for cdr_voice: {execution_time}")

    def transfer_data_from_cdr_sms_new_to_cdr_sms(self) -> None:
        # CDR_SMS_NEW to CDR_SMS
        start_time = datetime.now()
        logging.info(f"To cdr_sms data transfer started at: {start_time}")

        sql = text(
            """
                INSERT INTO cdr_sms (cdr_uuid, sent_from, sent_to, date_sent)
                SELECT cdr_uuid, sent_from, sent_to, date_sent
                FROM cdr_sms_new csn
                WHERE NOT EXISTS (
                    SELECT 1
                    FROM cdr_sms cs
                    WHERE cs.cdr_uuid = csn.cdr_uuid
                );
            """
        )

        self.session.execute(sql)

        # self.session.commit()

        end_time = datetime.now()
        logging.info(f"To cdr_sms data transfer ended at: {end_time}")

        execution_time = end_time - start_time
        logging.info(f"Total execution time for cdr_sms: {execution_time}")

    def transfer_data_from_cdr_data_agg_new_to_cdr_data_agg(self) -> None:
        # CDR_DATA_AGGREGATE_NEW to CDR_DATA_AGGREGATE
        start_time = datetime.now()
        logging.info(f"To cdr_data_aggregate data transfer started at: {start_time}")

        sql = text(
            """
                INSERT INTO cdr_data_aggregate (cdr_uuid, iccid, usage, month, imsi)
                SELECT cdr_uuid, iccid, usage, month, imsi
                FROM cdr_data_aggregate_new csn
                WHERE NOT EXISTS (
                    SELECT 1
                    FROM cdr_data_aggregate cs
                    WHERE cs.cdr_uuid = csn.cdr_uuid
                );
            """
        )

        self.session.execute(sql)

        # self.session.commit()

        end_time = datetime.now()
        logging.info(f"To cdr_data_aggregate data transfer ended at: {end_time}")

        execution_time = end_time - start_time
        logging.info(f"Total execution time for cdr_data_aggregate: {execution_time}")

    def transfer_data_from_cdr_voice_agg_new_to_cdr_voice_agg(self) -> None:
        # CDR_VOICE_AGGREGATE_NEW to CDR_VOICE_AGGREGATE
        start_time = datetime.now()
        logging.info(f"To cdr_voice_aggregate data transfer started at: {start_time}")

        sql = text(
            """
                INSERT INTO cdr_voice_aggregate (cdr_uuid, iccid,
                voice_usage, month, imsi)
                SELECT cdr_uuid, iccid, voice_usage, month, imsi
                FROM cdr_voice_aggregate_new csn
                WHERE NOT EXISTS (
                    SELECT 1
                    FROM cdr_voice_aggregate cs
                    WHERE cs.cdr_uuid = csn.cdr_uuid
                );
            """
        )

        self.session.execute(sql)

        # self.session.commit()

        end_time = datetime.now()
        logging.info(f"To cdr_voice_aggregate data transfer ended at: {end_time}")

        execution_time = end_time - start_time
        logging.info(f"Total execution time for cdr_voice_aggregate: {execution_time}")

    def reverting_forigen_keys(self) -> None:
        # Giving back forigen keys to the below tables
        start_time = datetime.now()
        logging.info(
            "Reverting back forigen keys to cdr_refernces"
            "cdr_data_aggregate &"
            f"cdr_voice_aggregate started at: {start_time}"
        )

        # FK constraint for cdr_references table

        cdr_references_fk_sql = text(
            """
                DO $$
                BEGIN
                    IF NOT EXISTS (
                        SELECT 1
                        FROM information_schema.table_constraints
                        WHERE constraint_type = 'FOREIGN KEY'
                        AND table_name = 'cdr_references'
                        AND constraint_name = 'fk_cdr_references_cdr_uuid'
                    ) THEN
                        ALTER TABLE cdr_references
                        ADD CONSTRAINT fk_cdr_references_cdr_uuid
                        FOREIGN KEY (cdr_uuid) REFERENCES cdr(uuid);
                    END IF;
                END $$;

            """
        )
        self.session.execute(cdr_references_fk_sql)

        # FK constraint for cdr_data_aggregate table
        cdr_data_aggregate_fk_sql = text(
            """
                DO $$
                BEGIN
                    IF NOT EXISTS (
                        SELECT 1
                        FROM information_schema.table_constraints
                        WHERE constraint_type = 'FOREIGN KEY'
                        AND table_name = 'cdr_data_aggregate'
                        AND constraint_name = 'fk_cdr_uuid'
                    ) THEN
                        ALTER TABLE cdr_data_aggregate
                        ADD CONSTRAINT fk_cdr_uuid
                        FOREIGN KEY (cdr_uuid) REFERENCES cdr(uuid);
                    END IF;
                END $$;
            """
        )
        self.session.execute(cdr_data_aggregate_fk_sql)

        # FK constraint for cdr_voice_aggregate table
        cdr_voice_aggregate_fk_sql = text(
            """
                DO $$
                BEGIN
                    IF NOT EXISTS (
                        SELECT 1
                        FROM information_schema.table_constraints
                        WHERE constraint_type = 'FOREIGN KEY'
                        AND table_name = 'cdr_voice_aggregate'
                        AND constraint_name = 'fk_cdr_uuid'
                    ) THEN
                        ALTER TABLE cdr_voice_aggregate
                        ADD CONSTRAINT fk_cdr_uuid
                        FOREIGN KEY (cdr_uuid) REFERENCES cdr(uuid);
                    END IF;
                END $$;

            """
        )
        self.session.execute(cdr_voice_aggregate_fk_sql)

        self.session.commit()

        end_time = datetime.now()
        logging.info(f"To cdr_sms data transfer ended at: {end_time}")

        execution_time = end_time - start_time
        logging.info(f"Total execution time for cdr_sms: {execution_time}")


class ScriptError(Exception):
    ...


if __name__ == "__main__":
    session = get_db_session()
    cdr_repository = DatabaseCDRRepository(session)
    try:
        start_time = datetime.now()
        logging.info(f"Process started at: {start_time}")

        # Starting the data transfer
        cdr_repository.transfer_data_from_cdr_new_to_cdr()
        cdr_repository.transfer_data_from_cdr_data_new_to_cdr_data()
        cdr_repository.transfer_data_from_cdr_voice_new_to_cdr_voice()
        cdr_repository.transfer_data_from_cdr_sms_new_to_cdr_sms()
        cdr_repository.transfer_data_from_cdr_data_agg_new_to_cdr_data_agg()
        cdr_repository.transfer_data_from_cdr_voice_agg_new_to_cdr_voice_agg()
        cdr_repository.reverting_forigen_keys()

        end_time = datetime.now()
        logging.info(f"Process ended at: {end_time}")

        execution_time = end_time - start_time
        logging.info(f"Total script execution time: {execution_time}")
        logging.info("Data transfered successfully.")
    except ScriptError as e:
        raise e

"""python -m script.roll_back_cdr_data_transfer"""
