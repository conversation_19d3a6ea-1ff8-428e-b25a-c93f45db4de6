from uuid import uuid4

from sqlalchemy import insert, select
from sqlalchemy.orm import Session

from app.config import logger
from app.db.session import make_session
from sim.adapters import orm


def get_db_session() -> Session:
    session = make_session()
    try:
        return session
    finally:
        session.close()


class ScriptError(Exception):
    ...


class DatabaseSIMRepository:
    def __init__(self, session: Session) -> None:
        self.session = session
        self.condition_true = True

    def update_pending_sim_status(self):
        IMSIs = [
            "234588570014823",
            "234588570014820",
            "234588570014821",
            "234588570014822",
            "234588570014819",
            "234588570024704",
            "234588570024705",
            "234588570024706",
            "234588570024707",
            "234588570024708",
        ]
        sim_card = orm.sim_card
        sim_activity = orm.sim_activity_log

        self.session.query(sim_card).filter(sim_card.c.imsi.in_(IMSIs)).update(
            {sim_card.c.sim_status: "READY_FOR_ACTIVATION"}
        )
        for sim in IMSIs:
            activity_detail = (
                select("*")
                .select_from(sim_activity)
                .filter(sim_activity.c.imsi == sim)
                .order_by(sim_activity.c.id.desc())
                .limit(1)
            )

            response = self.session.execute(activity_detail).first()

            ins = insert(sim_activity).values(
                uuid=uuid4(),
                imsi=response.imsi,
                iccid=response.iccid,
                msisdn=response.msisdn,
                request_type="PROVIDE",  # Replace with the actual request type
                prior_value=response.new_value,
                new_value=response.prior_value,
                client_ip=response.client_ip,  # Replace with the actual client IP
                created_by="sysuser",  # Replace with the actual creator
            )

            self.session.execute(ins)
        self.session.commit()
        return None


if __name__ == "__main__":
    session = get_db_session()
    auth_repository = DatabaseSIMRepository(session)
    try:
        auth_repository.update_pending_sim_status()

        logger.info("SIM status updated successfully.")
    except ScriptError as e:
        raise e

"""python -m script.update_pending_sim_status"""
