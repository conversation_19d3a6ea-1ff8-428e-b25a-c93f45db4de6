version: 1
disable_existing_loggers: no

formatters:
  default:
    format: "%(asctime)s %(levelname)s [%(name)s] [%(process)d] [%(filename)s:%(lineno)d] - %(message)s"
    datefmt: "%Y-%m-%d %H:%M:%S"
    class: uvicorn.logging.DefaultFormatter
  default_with_otel:
    format: "%(asctime)s %(levelname)s [%(name)s] [%(process)d] [%(filename)s:%(lineno)d] [ trace_id=%(otelTraceID)s span_id=%(otelSpanID)s resource.service.name=%(otelServiceName)s ] - %(message)s"
    datefmt: "%Y-%m-%d %H:%M:%S"
    class: uvicorn.logging.DefaultFormatter

loggers:
  root:
    level: INFO
    handlers: [console]
  gunicorn.error:
    level: INFO
    handlers: [console]
    propagate: no
    qualname: gunicorn.error
  gunicorn.access:
    level: INFO
    handlers: [console]
    propagate: no
    qualname: gunicorn.access
  uvicorn.error:
    level: INFO
    handlers: [console]
    propagate: no
    qualname: uvicorn.error
  uvicorn.access:
    level: INFO
    handlers: [console]
    propagate: no
    qualname: uvicorn.access
  faker.factory:
    level: INFO

handlers:
  console:
    class: logging.StreamHandler
    formatter: default
    stream: ext://sys.stdout
