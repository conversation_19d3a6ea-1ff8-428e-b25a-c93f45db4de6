import datetime
from abc import ABC, abstractmethod
from uuid import UUID, uuid4

import anyio

from app.config import logger
from app.socket_server import sim_events
from common.types import ICCID, IMSI, Month
from sim import exceptions
from sim.adapters.repository import AbstractSimRepository
from sim.domain import model
from sim.domain.model import (
    MSISDN,
    NotificationStatus,
    RequestType,
    SIMMonthlyStatus,
    SIMProviderLog,
    SimStatus,
)
from sim.domain.ports import AbstractAuditService


class AbstractNotificationService(ABC):
    @abstractmethod
    def process_notification(
        self,
        request_type: RequestType,
        sim_provider_log: SIMProviderLog,
        client_ip: str | None = None,
    ) -> bool:
        ...


class SimNotification(AbstractNotificationService):
    def __init__(
        self,
        sim_repository: AbstractSimRepository,
        audit_service: AbstractAuditService,
    ):
        self.sim_repository = sim_repository
        self.audit_service = audit_service

    def _add_sim_monthly_status(
        self, sim_monthly_status: SIMMonthlyStatus, sim_status: SimStatus
    ):
        is_sim_fee_applicable = self.sim_repository.check_sim_monthly_status(
            sim_monthly_status.sim_card_id, None
        )
        if is_sim_fee_applicable:
            if not self.sim_repository.check_sim_monthly_status(
                sim_monthly_status.sim_card_id, sim_monthly_status.month
            ):
                sim_monthly_status.is_first_activation = False
                self.sim_repository.add_sim_monthly_status(sim_monthly_status)
            else:
                self.sim_repository.update_sim_monthly_status(
                    sim_monthly_status.sim_card_id,
                    sim_monthly_status.month,
                    SimStatus(sim_status),
                )
        else:
            if sim_status == model.SimStatus.ACTIVE:
                self.sim_repository.add_sim_monthly_status(sim_monthly_status)

    def _get_sim_card(self, imsi: IMSI):
        """Get SIM card object of single sim"""
        sim_cards = self.sim_repository.get_sim_cards(imsi_list=[imsi])
        try:
            sim_card = next(sim_cards)
        except StopIteration:
            raise exceptions.SimCardsNotFound("Requested reference not found.")
        return sim_card

    def get_sim_card(self, imsi: IMSI):
        """Get SIM card object of single sim"""
        sim_cards = self.sim_repository.get_sim_cards(imsi_list=[imsi])
        try:
            sim_card = next(sim_cards)
            if sim_card.allocation_id is None:
                raise exceptions.AllocationError("IMSI allocation not found.")
        except StopIteration:
            raise exceptions.SimCardsNotFound("Requested IMSI not found.")
        return sim_card

    def _get_sim_monthly_status(self, reference: IMSI, notification: SIMProviderLog):
        sim_card = self._get_sim_card(IMSI(reference))
        year = notification.audit_date.year
        month = notification.audit_date.month
        sim_status = SimStatus(notification.status)
        sim_monthly_status = SIMMonthlyStatus(
            sim_card_id=sim_card.id,
            month=Month(year=year, month=month, day=1),
            sim_status=sim_status,
            is_first_activation=True,
        )
        return sim_monthly_status

    def update_sim_card_based_on_response(
        self,
        sim_provider_log: SIMProviderLog,
        provider_log,
        sim_status: SimStatus,
        given_status: NotificationStatus,
        given_request_type: RequestType,
        client_ip: str | None = None,
    ) -> bool:
        if (given_status == NotificationStatus.SUCCESS) and (
            provider_log.requestType.upper() == given_request_type.name
        ):
            self.sim_repository.update_sim_card(
                IMSI(provider_log.imsi), SimStatus(sim_status)
            )
            sim_provider_log.status = SimStatus(sim_status)
            self._add_sim_monthly_status(
                self._get_sim_monthly_status(IMSI(provider_log.imsi), sim_provider_log),
                SimStatus(sim_status),
            )
            anyio.from_thread.run(
                lambda: sim_events.sim_details_update(
                    [provider_log.imsi], sim_status.value
                )
            )
        elif (given_status == NotificationStatus.FAILURE) and (
            provider_log.requestType.upper() == given_request_type.name
        ):
            self.sim_repository.update_sim_card(
                IMSI(provider_log.imsi), SimStatus(provider_log.priorStatus)
            )
            sim_card = self.get_sim_card(IMSI(provider_log.imsi))
            iccid = sim_card.iccid
            msisdn = sim_card.msisdn

            audit_detail = model.SIMCardAudit(
                uuid4(),
                IMSI(provider_log.imsi),
                ICCID(iccid),
                MSISDN(msisdn),
                given_request_type.name,
                SimStatus(sim_status),
                SimStatus(provider_log.priorStatus),
                field="Status",
                action="Updated",
                client_ip=client_ip,
                created_by="<EMAIL>",
                audit_date=datetime.datetime.today(),
            )
            self.audit_service.add_sim_audit_api([audit_detail])
            anyio.from_thread.run(
                lambda: sim_events.sim_details_update(
                    [provider_log.imsi], provider_log.priorStatus
                )
            )
        return True

    def process_notification(
        self,
        request_type: RequestType,
        sim_provider_log: SIMProviderLog,
        client_ip: str | None = None,
    ) -> bool:
        notification_status = {
            RequestType.CEASE: SimStatus.DEACTIVATED,
            RequestType.PROVIDE: SimStatus.ACTIVE,
        }.get(request_type)
        logger.info(f"process notification: {notification_status}")
        if not self.audit_service.validate_notification(
            sim_provider_log, SimStatus(notification_status)
        ):
            return True

        logger.info("process notification")
        provider_log = self.audit_service.get_sim_provider_log_audit_api(
            sim_provider_log.work_id
        )
        logger.info(f"provider_log: {provider_log}")
        if provider_log is None:
            raise exceptions.WorkItemIdNotFound(
                f"Work item id not found - {sim_provider_log.work_id}"
            )
        sim_provider_log.activity_id = provider_log.activityId
        sim_provider_log.sim_activity_log_uuid = provider_log.simActivityLogUuid
        sim_provider_log.prior_status = provider_log.status

        audit_provider_detail = model.SIMCardProviderAudit(
            activity_id=str(sim_provider_log.activity_id),
            sim_activity_log_uuid=UUID(sim_provider_log.sim_activity_log_uuid),
            audit_date=sim_provider_log.audit_date,
            message=sim_provider_log.message,
            status=sim_provider_log.prior_status,
            work_id=sim_provider_log.work_id,
            prior_status=sim_provider_log.status,
        )
        self.audit_service.add_sim_provider_audit_api(audit_provider_detail)

        return self.update_sim_card_based_on_response(
            sim_provider_log,
            provider_log,
            SimStatus(notification_status),
            NotificationStatus(sim_provider_log.status),
            request_type,
            client_ip,
        )
