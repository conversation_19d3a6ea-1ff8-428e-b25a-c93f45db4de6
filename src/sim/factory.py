from typing import Iterator

from pydantic import BaseModel
from pydantic_factories import ModelFactory

from api.schema_types import IMSI
from sim.domain.model import ICCID, MSISDN, PIN, PUK


class SIMCardCSVFormat(BaseModel):
    ICCID: ICCID
    IMSI: IMSI
    MSISDN: MSISDN
    PIN1: PIN
    PIN2: PIN
    PUK1: PUK
    PUK2: PUK


class SIMCardFactory(ModelFactory):
    __model__ = SIMCardCSVFormat


def generate_sim_cards_csv(imsi_first: IMSI, length: int) -> Iterator[SIMCardCSVFormat]:
    for imsi in range(int(imsi_first), int(imsi_first) + length):
        yield SIMCardFactory.build(IMSI=imsi)
