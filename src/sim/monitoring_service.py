from abc import ABC, abstractmethod

from sim.adapters.repository import AbstractSimRepository
from sim.domain import model
from sim.domain.ports import AbstractAuditService


class AbstractSimMonitoringService(ABC):
    @abstractmethod
    def get_invalid_sim_and_msisdn_count(self) -> model.InvalidSimMsisdnCountDetails:
        ...


class SimMonitoringService(AbstractSimMonitoringService):
    def __init__(
        self,
        sim_repository: AbstractSimRepository,
        audit_service: AbstractAuditService,
    ):
        self.sim_repository = sim_repository
        self.audit_service = audit_service

    def get_invalid_sim_and_msisdn_count(self) -> model.InvalidSimMsisdnCountDetails:
        response = self.sim_repository.get_invalid_sim_and_msisdn_count()
        sim_msisdn_update_log = self.audit_service.get_sim_msisdn_update_log_audit_api()
        response.update_sim_msisdn_count = sim_msisdn_update_log
        return response
