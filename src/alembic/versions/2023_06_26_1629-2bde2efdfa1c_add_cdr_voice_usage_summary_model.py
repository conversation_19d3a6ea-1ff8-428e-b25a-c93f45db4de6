"""add cdr voice usage summary model

Revision ID: 2bde2efdfa1c
Revises: 8a18c3e27575
Create Date: 2023-06-26 16:29:38.905504

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "2bde2efdfa1c"
down_revision = "8a18c3e27575"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "cdr_voice_aggregate",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False, start=1, increment=1),
            nullable=False,
        ),
        sa.Column("cdr_uuid", postgresql.UUID(), nullable=False),
        sa.Column("imsi", sa.String(length=15), nullable=False),
        sa.Column("iccid", sa.String(length=20), nullable=False),
        sa.Column("voice_usage", sa.<PERSON>, nullable=False),
        sa.Column("month", sa.Date(), nullable=False),
        sa.ForeignKeyConstraint(
            ["cdr_uuid"],
            ["cdr.uuid"],
            name=op.f("fk_cdr_uuid"),
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("iccid", "month"),
    )


def downgrade() -> None:
    op.drop_table("cdr_voice_aggregate")
