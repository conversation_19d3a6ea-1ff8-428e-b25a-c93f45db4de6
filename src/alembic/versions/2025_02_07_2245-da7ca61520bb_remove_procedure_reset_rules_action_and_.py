"""remove procedure reset_rules_action_and_notification

Revision ID: da7ca61520bb
Revises: d66207348887
Create Date: 2025-02-07 22:45:46.395876

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "da7ca61520bb"
down_revision = "d66207348887"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
            DROP FUNCTION IF EXISTS public.reset_rules_action_and_notification(uuid);
    """
    )


def downgrade() -> None:
    op.execute(
        """
            CREATE OR
            REPLACE FUNCTION reset_rules_action_and_notification(uuid_param uuid)
            RETURNS void AS $$
            BEGIN
                DELETE FROM notification_value
                WHERE rules_notification_id IN (
                    SELECT id FROM rules_notification WHERE rules_uuid = uuid_param
                );

                DELETE FROM rules_notification
                WHERE rules_uuid = uuid_param;

                DELETE FROM rules_action
                WHERE rules_uuid = uuid_param;

            END;
            $$ LANGUAGE plpgsql;
        """
    )
