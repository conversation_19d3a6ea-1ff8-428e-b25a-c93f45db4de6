"""add PERCENTAGE unit type

Revision ID: 198c1811405f
Revises: da7ca61520bb
Create Date: 2025-02-10 12:30:40.388370

"""
from alembic import op
from sqlalchemy import text

from common.types import Unit

# revision identifiers, used by Alembic.
revision = "198c1811405f"
down_revision = "da7ca61520bb"
branch_labels = None
depends_on = None


def upgrade() -> None:

    conn = op.get_bind()
    query = text(
        """
        DO $$
        BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM pg_enum
                WHERE enumtypid = 'unit'::regtype
                AND enumlabel = :percentage_value
            ) THEN
                ALTER TYPE unit ADD VALUE :percentage_value;
            END IF;
        END $$;
    """
    )

    conn.execute(query, {"percentage_value": Unit.PERCENTAGE})


def downgrade() -> None:
    pass
