"""re-configure get all rule details function for data_volume ordering

Revision ID: 122c2b98898e
Revises: d9169e1cc79b
Create Date: 2024-12-22 12:38:11.229321

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "122c2b98898e"
down_revision = "d9169e1cc79b"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS get_all_rule_details(text,integer, text,
        text, integer, integer);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_all_rule_details(
            search_term TEXT DEFAULT NULL,
            account_id_param INT DEFAULT NULL,
            order_by_column TEXT DEFAULT 'id',
            order_direction TEXT DEFAULT 'ASC',
            page_offset INT DEFAULT NULL,
            page_size INT DEFAULT NULL
        )
        RETURNS TABLE (
            id INT,
            uuid UUID,
            account_id INT,
            rule_name character varying,
            rule_type character varying,
            rule_definition character varying,
            rule_category character varying,
            data_volume bigint,
            unit unit,
            status bool,
            account_name character varying
        ) AS $$
        DECLARE
            final_order_by_column TEXT := COALESCE(order_by_column, 'id');
            final_order_direction TEXT := COALESCE(order_direction, 'DESC');
            pagination_clause TEXT := '';
            order_clause TEXT;
        BEGIN
            -- Pagination check
            IF page_size IS NOT NULL AND page_offset IS NOT NULL THEN
                pagination_clause := ' LIMIT ' || page_size ||
                ' OFFSET ' || page_offset;
            END IF;

            -- Determine the order clause based on the ordering column
            IF final_order_by_column = 'data_volume' THEN
                order_clause := 'ORDER BY convert_unit(unit, data_volume) '
                || final_order_direction;
            ELSE
                order_clause := 'ORDER BY ' || quote_ident(final_order_by_column)
                || ' ' || final_order_direction;
            END IF;

            RETURN QUERY EXECUTE
                'SELECT
                    r.id,
                    r.uuid,
                    r.account_id,
                    r.rule_name,
                    rt.rule_type,
                    rd.definition AS rule_definition,
                    rc.category AS rule_category,
                    r.data_volume,
                    r.unit,
                    r.status,
                    a.name AS account_name
                FROM rules r
                JOIN rule_type rt ON rt.id = r.rule_type_id
                JOIN rule_category rc ON rc.id = r.rule_category_id
                JOIN rule_definition rd ON rd.id = r.rule_definition_id
                JOIN account a ON a.id = r.account_id
                WHERE
                    ($1 IS NULL OR r.account_id = $1)
                    AND
                    (
                        $2 IS NULL OR $2 = '''' OR
                        EXISTS (
                            SELECT 1 FROM (
                                VALUES
                                    (r.rule_name),
                                    (rd.definition),
                                    (rt.rule_type),
                                    (rc.category),
                                    (a.name),
                                    (r.data_volume::text),
                                    (r.unit::text)
                            ) AS search_columns(col)
                            WHERE col ILIKE ''%'' || $2 || ''%''
                        )
                    )
                ' || order_clause || ' ' || pagination_clause
            USING account_id_param, search_term;
        END;
        $$ LANGUAGE plpgsql;

        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS get_all_rule_details(text,integer, text,
        text, integer, integer);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_all_rule_details(
            search_term TEXT DEFAULT NULL,
            account_id_param INT DEFAULT NULL,
            order_by_column TEXT DEFAULT 'id',
            order_direction TEXT DEFAULT 'ASC',
            page_offset INT DEFAULT NULL,
            page_size INT DEFAULT NULL
        )
        RETURNS TABLE (
            id INT,
            uuid UUID,
            account_id INT,
            rule_name character varying,
            rule_type character varying,
            rule_definition character varying,
            rule_category character varying,
            data_volume bigint,
            unit unit,
            status bool,
            account_name character varying
        ) AS $$
        DECLARE
            final_order_by_column TEXT := COALESCE(order_by_column, 'id');
            final_order_direction TEXT := COALESCE(order_direction, 'DESC');
            pagination_clause TEXT := '';
        BEGIN
            -- Pagination check
            IF page_size IS NOT NULL AND page_offset IS NOT NULL THEN
                pagination_clause := ' LIMIT ' || page_size ||
                ' OFFSET ' || page_offset;
            END IF;

            RETURN QUERY EXECUTE
                'SELECT
                    r.id,
                    r.uuid,
                    r.account_id,
                    r.rule_name,
                    rt.rule_type,
                    rd.definition AS rule_definition,
                    rc.category AS rule_category,
                    r.data_volume,
                    r.unit,
                    r.status,
                    a.name AS account_name
                FROM rules r
                JOIN rule_type rt ON rt.id = r.rule_type_id
                JOIN rule_category rc ON rc.id = r.rule_category_id
                JOIN rule_definition rd ON rd.id = r.rule_definition_id
                JOIN account a ON a.id = r.account_id
                WHERE
                    ($1 IS NULL OR r.account_id = $1)
                    AND
                    (
                        $2 IS NULL OR $2 = '''' OR
                        EXISTS (
                            SELECT 1 FROM (
                                VALUES
                                    (r.rule_name),
                                    (rd.definition),
                                    (rt.rule_type),
                                    (rc.category),
                                    (a.name),
                                    (r.data_volume::text),
                                    (r.unit::text)
                            ) AS search_columns(col)
                            WHERE col ILIKE ''%'' || $2 || ''%''
                        )
                    )
                ORDER BY ' || quote_ident(final_order_by_column) || ' ' ||
                final_order_direction || pagination_clause
            USING account_id_param, search_term;
        END;
        $$ LANGUAGE plpgsql;

        """
    )
