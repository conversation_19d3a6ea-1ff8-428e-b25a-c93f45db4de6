"""rename_cdr_data_aggregate_to_cdr_data_aggregate_archieve

Revision ID: 0e10df2e0892
Revises: 157f4b0f8318
Create Date: 2025-01-01 13:09:05.831899

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "0e10df2e0892"
down_revision = "157f4b0f8318"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.rename_table("cdr_data_aggregate", "cdr_data_aggregate_archieve")
    op.execute(
        """
            ALTER TABLE cdr_data_aggregate_archieve RENAME CONSTRAINT
            uq_cdr_data_aggregate_iccid TO uq_cdr_data_aggregate_archieve_iccid;
        """
    )
    op.rename_table("cdr_data_aggregate_new", "cdr_data_aggregate")
    op.execute(
        """
        DO $$
        DECLARE
            partition_name TEXT;         -- Current partition name
            new_partition_name TEXT;     -- New partition name after replacement
        BEGIN
            FOR partition_name IN
                SELECT inhrelid::regclass::text
                FROM pg_inherits
                WHERE inhparent = 'cdr_data_aggregate'::regclass
            LOOP
                -- Generate the new partition name
                new_partition_name := replace(
                partition_name,
                'cdr_data_aggregate_new',
                'cdr_data_aggregate');

                -- Log the renaming action
                RAISE NOTICE 'Renaming partition % to %',
                partition_name, new_partition_name;

                -- Rename the partition
                EXECUTE format('ALTER TABLE %I RENAME TO %I;',
                            partition_name,
                            new_partition_name);
            END LOOP;

            -- Log completion
            RAISE NOTICE 'All partitions renamed successfully.';
        END $$;


        """
    )


def downgrade() -> None:
    op.rename_table("cdr_data_aggregate", "cdr_data_aggregate_new")
    op.execute(
        """
        DO $$
        DECLARE
            partition_name TEXT;         -- Current partition name
            new_partition_name TEXT;     -- New partition name after replacement
        BEGIN
            FOR partition_name IN
                SELECT inhrelid::regclass::text
                FROM pg_inherits
                WHERE inhparent = 'cdr_data_aggregate_new'::regclass
            LOOP
                -- Generate the new partition name
                new_partition_name := replace(
                partition_name,
                'cdr_data_aggregate',
                'cdr_data_aggregate_new');

                -- Log the renaming action
                RAISE NOTICE 'Renaming partition % to %',
                partition_name, new_partition_name;

                -- Rename the partition
                EXECUTE format('ALTER TABLE %I RENAME TO %I;',
                            partition_name,
                            new_partition_name);
            END LOOP;

            -- Log completion
            RAISE NOTICE 'All partitions renamed successfully.';
        END $$;


        """
    )
    op.rename_table("cdr_data_aggregate_archieve", "cdr_data_aggregate")
    op.execute(
        """
        ALTER TABLE cdr_data_aggregate RENAME
        CONSTRAINT uq_cdr_data_aggregate_archieve_iccid TO uq_cdr_data_aggregate_iccid;
    """
    )
