"""Add logo_key field for Account table

Revision ID: 8d49cfd1b588
Revises: 20ecb1cf750e
Create Date: 2023-04-10 12:36:16.212470

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "8d49cfd1b588"
down_revision = "20ecb1cf750e"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("account", sa.Column("logo_key", sa.String(length=64), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("account", "logo_key")
    # ### end Alembic commands ###
