"""add type column to cdr table

Revision ID: 009508ddfdb1
Revises: cd9a34359a4d
Create Date: 2025-05-30 12:43:05.794633

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "009508ddfdb1"
down_revision = "cd9a34359a4d"
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        "cdr",
        sa.Column(
            "type", sa.String(length=15), nullable=False, server_default="unknown"
        ),
    )

    op.create_index(
        "ix_cdr_type",
        "cdr",
        ["type"],
        unique=False,
    )


def downgrade():
    op.drop_column("cdr", "type")
