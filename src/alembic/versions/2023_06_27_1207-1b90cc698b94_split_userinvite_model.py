"""Split UserInvite model

Revision ID: 1b90cc698b94
Revises: 2bde2efdfa1c
Create Date: 2023-06-27 12:07:29.647103

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "1b90cc698b94"
down_revision = "2bde2efdfa1c"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "user",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("email", sa.String(), nullable=False),
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("role", sa.String(length=255), nullable=False),
        sa.Column("saf_id", sa.String(), nullable=True),
        sa.Column("saf_registered_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["account_id"], ["account.id"], name=op.f("fk_user_account_id_account")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_user")),
        sa.UniqueConstraint("email", name=op.f("uq_user_email")),
    )
    op.create_table(
        "saf_invite",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("saf_id", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("accepted_at", sa.DateTime(), nullable=True),
        sa.Column("user_id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.ForeignKeyConstraint(
            ["user_id"], ["user.id"], name=op.f("fk_saf_invite_user_id_user")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_saf_invite")),
    )
    op.drop_table("user_invite")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "user_invite",
        sa.Column(
            "id",
            sa.INTEGER(),
            sa.Identity(
                always=False,
                start=1,
                increment=1,
                minvalue=1,
                maxvalue=**********,
                cycle=False,
                cache=1,
            ),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("account_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.Column("email", sa.VARCHAR(), autoincrement=False, nullable=False),
        sa.Column("user_id", postgresql.UUID(), autoincrement=False, nullable=False),
        sa.Column(
            "created_at", postgresql.TIMESTAMP(), autoincrement=False, nullable=False
        ),
        sa.Column("external_id", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column("external_user_id", sa.VARCHAR(), autoincrement=False, nullable=True),
        sa.Column(
            "external_registered_at",
            postgresql.TIMESTAMP(),
            autoincrement=False,
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["account_id"], ["account.id"], name="fk_user_invite_account_id_account"
        ),
        sa.PrimaryKeyConstraint("id", name="pk_user_invite"),
        sa.UniqueConstraint("email", name="uq_user_invite_email"),
    )
    op.drop_table("saf_invite")
    op.drop_table("user")
    # ### end Alembic commands ###
