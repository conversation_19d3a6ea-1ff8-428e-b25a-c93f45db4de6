"""set full profile on data only imsis

Revision ID: 8e335d80dceb
Revises: 16feb6d224e3
Create Date: 2025-04-01 17:47:39.190451

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "8e335d80dceb"
down_revision = "16feb6d224e3"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        UPDATE msisdn_pool
        SET sim_profile = 'VOICE_SMS_DATA'
        FROM sim_card
        JOIN allocation ON allocation.imsi = sim_card.imsi
        WHERE msisdn_pool.msisdn = sim_card.msisdn
        AND msisdn_pool.sim_profile = 'DATA_ONLY'
        AND msisdn_pool.msisdn_factor = 'NATIONAL';
    """
    )


def downgrade() -> None:
    op.execute(
        """
        UPDATE msisdn_pool
        SET sim_profile = 'DATA_ONLY'
        FROM sim_card
        JOIN allocation ON allocation.imsi = sim_card.imsi
        WHERE msisdn_pool.msisdn = sim_card.msisdn
        AND msisdn_pool.sim_profile = 'VOICE_SMS_DATA'
        AND msisdn_pool.msisdn_factor = 'NATIONAL';
    """
    )
