"""Add type field for Adjustment model

Revision ID: dc66683d55cf
Revises: f18a9a5218f7
Create Date: 2023-02-27 14:06:30.353820

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "dc66683d55cf"
down_revision = "f18a9a5218f7"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "adjustment",
        sa.Column(
            "type",
            sa.Enum(
                "ACCOUNT_SETUP",
                "ACTIVATION_ADJUSTMENT",
                "DATA_CHARGE_ADJUSTMENT",
                "MONTHLY_ACCOUNT_MINIMUM",
                "OTHER_CHARGE_ONE_TIME",
                "PREMIER_SUPPORT",
                "SIM_FEE",
                "SMS_CHARGE_ADJUSTMENT",
                "SUBSCRIPTION_CHARGE_ADJUSTMENT",
                "SUSPENDED_FEE",
                "TRAINING",
                name="adjustment_type",
                native_enum=False,
                create_constraint=True,
            ),
            nullable=True,
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("adjustment", "type")
    # ### end Alembic commands ###
