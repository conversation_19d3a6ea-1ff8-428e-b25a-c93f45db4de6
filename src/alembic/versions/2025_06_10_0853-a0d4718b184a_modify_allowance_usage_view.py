"""modify allowance_usage_view

Revision ID: a0d4718b184a
Revises: 926075062b5b
Create Date: 2025-05-30 08:53:21.453235

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "a0d4718b184a"
down_revision = "926075062b5b"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        DROP VIEW IF EXISTS allowance_usage_view;
        """
    )
    op.execute(
        """
        CREATE OR REPLACE VIEW allowance_usage_view AS
        WITH cte_usage AS (
        SELECT
            al.account_id,
            COALESCE(sc.rate_plan_id, al.rate_plan_id) AS rate_plan_id,
            r.range_to,
            r.range_unit,
            convert_unit(r.range_unit, r.range_to) AS total_allowance,
            rgs.service,
            rm.rate_model_code,
            al.imsi
        FROM
            allocation AS al
        INNER JOIN sim_card AS sc
            ON sc.allocation_id = al.id
        INNER JOIN rate_plan AS rp
            ON rp.id = COALESCE(sc.rate_plan_id, al.rate_plan_id)
        INNER JOIN rate_group AS rg
            ON rg.rate_plan_id = rp.id
        INNER JOIN rate_group_service AS rgs
            ON rgs.rate_group_id = rg.id
        INNER JOIN rate_model AS rm
            ON rm.id = rg.rate_model_id
        INNER JOIN rate AS r
            ON rgs.rate_group_id = r.rate_group_id
        WHERE rm.rate_model_code in ('FIXED', 'FLEXI')
        GROUP BY
            al.account_id,
            sc.rate_plan_id,
            al.rate_plan_id,
            r.range_to,
            r.range_unit,
            rgs.service,
            rm.rate_model_code,
            al.imsi
        )
        SELECT
            ct.account_id AS account_id,
            ct.rate_plan_id,
            ct.total_allowance,
            ct.service,
            ct.rate_model_code,
            ct.imsi
        FROM
            cte_usage AS ct;
    """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP VIEW IF EXISTS allowance_usage_view;
        """
    )
    op.execute(
        """
        CREATE OR REPLACE VIEW allowance_usage_view AS
        WITH cte_usage AS (
            SELECT
                al.account_id,
                COALESCE(sc.rate_plan_id, al.rate_plan_id) AS rate_plan_id,
                r.range_to,
                r.range_unit,
                convert_unit(r.range_unit, r.range_to) AS total_allowance,
                rgs.service,
                rm.rate_model_code,
                al.imsi
            FROM
                allocation AS al
            INNER JOIN sim_card AS sc
                ON sc.allocation_id = al.id
            INNER JOIN rate_plan AS rp
                ON rp.id = COALESCE(sc.rate_plan_id, al.rate_plan_id)
            INNER JOIN rate_group AS rg
                ON rg.rate_plan_id = rp.id
            INNER JOIN rate_group_service AS rgs
                ON rgs.rate_group_id = rg.id
            INNER JOIN rate_model AS rm
                ON rm.id = rg.rate_model_id
            INNER JOIN rate AS r
                ON rgs.rate_group_id = r.rate_group_id
            GROUP BY
                al.account_id,
                sc.rate_plan_id,
                al.rate_plan_id,
                r.range_to,
                r.range_unit,
                rgs.service,
                rm.rate_model_code,
                al.imsi
        )
        SELECT
            ct.account_id AS account_id,
            ct.rate_plan_id,
            ct.total_allowance,
            ct.service,
            ct.rate_model_code,
            ct.imsi
        FROM
            cte_usage AS ct;

        """
    )
