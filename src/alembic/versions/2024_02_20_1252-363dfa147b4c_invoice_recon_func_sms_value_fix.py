"""invoice_recon_func_sms_value_fix

Revision ID: 363dfa147b4c
Revises: a342aabf4c62
Create Date: 2024-02-20 12:52:52.219585

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "363dfa147b4c"
down_revision = "a342aabf4c62"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
            DROP FUNCTION IF EXISTS public.invoice_recon(date, date, integer);
    """
    )
    op.execute(
        """
            CREATE OR REPLACE FUNCTION public.invoice_recon(
                start_date_param date,
                end_date_param date,
                billing_cycle_id_param integer)
                RETURNS TABLE(
                    service text,
                    su_imsi character varying(15),
                    cdr_imsi character varying(15),
                    su_usage bigint,
                    cdr_usage bigint,
                    su_cdr_variance bigint)
                LANGUAGE 'plpgsql'
                COST 100
                VOLATILE PARALLEL UNSAFE
                ROWS 1000

            AS $BODY$
            BEGIN

                FOR service, su_imsi, cdr_imsi, su_usage, cdr_usage, su_cdr_variance IN

                WITH cdr_sms_agg AS (
                        SELECT COUNT(cs.id) AS volume, cd.imsi
                        FROM cdr_sms AS cs
                        INNER JOIN cdr AS cd ON cs.cdr_uuid = cd.uuid
                        WHERE cs.date_sent BETWEEN start_date_param AND end_date_param
                        GROUP BY cd.imsi
                    )
                    SELECT 'DATA' AS service,
                    su.imsi AS su_imsi,
                    cdr.imsi AS cdr_imsi,
                    COALESCE(SUM(su.volume), 0) AS su_usage,
                    COALESCE(cdr.usage, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(su.volume),0) - COALESCE(cdr.usage,0))
                    AS su_cdr_variance
                    FROM sim_usage AS su
                    LEFT JOIN cdr_data_aggregate AS cdr ON su.imsi = cdr.imsi
                    AND cdr.month = start_date_param
                    WHERE su.billing_cycle_id = billing_cycle_id_param
                    AND su.service = 'DATA'
                    GROUP BY su.imsi,cdr.imsi, cdr.usage

                    UNION

                    SELECT 'DATA' AS service,su.imsi AS su_imsi, cdr.imsi AS cdr_imsi,
                    COALESCE(SUM(su.volume), 0) AS su_usage,
                    COALESCE(cdr.usage, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(su.volume),0) - COALESCE(cdr.usage,0))
                    AS su_cdr_variance
                    FROM cdr_data_aggregate AS cdr
                    LEFT JOIN sim_usage AS su ON su.imsi = cdr.imsi
                    AND su.billing_cycle_id = billing_cycle_id_param
                    AND su.service = 'DATA'
                    WHERE cdr.month = start_date_param
                    GROUP BY su.imsi,cdr.imsi, cdr.usage

                    UNION

                    SELECT 'VOICE' AS service,su.imsi AS su_imsi,cdr.imsi AS cdr_imsi,
                    COALESCE(SUM(su.volume),0) AS su_usage,
                    COALESCE(cdr.voice_usage, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(su.volume),0) - COALESCE(cdr.voice_usage,0))
                    AS su_cdr_variance
                    FROM sim_usage AS su
                    LEFT JOIN cdr_voice_aggregate AS cdr ON su.imsi=cdr.imsi
                    AND cdr.month=start_date_param
                    WHERE su.billing_cycle_id=billing_cycle_id_param
                    AND (su.service='VOICE_MO' OR su.service='VOICE_MT')
                    GROUP BY su.imsi,cdr.imsi, cdr.voice_usage

                    UNION

                    SELECT 'VOICE' AS service,su.imsi AS su_imsi,cdr.imsi AS cdr_imsi,
                    COALESCE(SUM(su.volume),0) AS su_usage,
                    COALESCE(cdr.voice_usage, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(su.volume),0) - COALESCE(cdr.voice_usage,0))
                    AS su_cdr_variance
                    FROM cdr_voice_aggregate AS cdr
                    LEFT JOIN sim_usage AS su ON su.imsi=cdr.imsi
                    AND su.billing_cycle_id=billing_cycle_id_param
                    AND (su.service='VOICE_MO' OR su.service='VOICE_MT')
                    WHERE cdr.month=start_date_param
                    GROUP BY su.imsi,cdr.imsi, cdr.voice_usage



                    UNION

                    SELECT 'SMS' AS service,su.imsi AS su_imsi,cds.imsi AS cdr_imsi,
                    COALESCE(SUM(su.volume),0) AS su_usage,
                    COALESCE(cds.volume, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(su.volume),0) - COALESCE(cds.volume,0))
                    AS su_cdr_variance
                    FROM sim_usage AS su
                    LEFT JOIN cdr_sms_agg AS cds ON su.imsi=cds.imsi
                    WHERE su.billing_cycle_id=billing_cycle_id_param
                    AND (su.service='SMS_MO' OR su.service='SMS_MT')
                    GROUP BY su.imsi,cds.imsi, cds.volume

                    UNION

                    SELECT 'SMS' AS service,su.imsi AS su_imsi,cds.imsi AS cdr_imsi,
                    COALESCE(SUM(su.volume),0) AS su_usage,
                    COALESCE(cds.volume, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(su.volume),0) - COALESCE(cds.volume,0))
                    AS su_cdr_variance
                    FROM cdr_sms_agg AS cds
                    LEFT JOIN sim_usage AS su
                    ON su.imsi=cds.imsi and su.billing_cycle_id=billing_cycle_id_param
                    AND (su.service='SMS_MO' OR su.service='SMS_MT')
                    GROUP BY su.imsi,cds.imsi, cds.volume
                LOOP
                    -- Return the fetched values
                    IF su_cdr_variance = 0  THEN
                        IF (cdr_imsi IS NULL OR su_imsi IS NULL)
                        AND su_cdr_variance!=0 THEN
                            RETURN NEXT;
                        END IF;
                    ELSEIF su_cdr_variance != 0  THEN
                        RETURN NEXT;
                    END IF;
                END LOOP;

                RETURN;
            END;
            $BODY$;
        """
    )


def downgrade() -> None:
    op.execute(
        """
            DROP FUNCTION IF EXISTS public.invoice_recon(date, date, integer);
    """
    )

    op.execute(
        """
            CREATE OR REPLACE FUNCTION public.invoice_recon(
                start_date_param date,
                end_date_param date,
                billing_cycle_id_param integer)
                RETURNS TABLE(
                    service text,
                    su_imsi character varying(15),
                    cdr_imsi character varying(15),
                    su_usage bigint,
                    cdr_usage bigint,
                    su_cdr_variance bigint)
                LANGUAGE 'plpgsql'
                COST 100
                VOLATILE PARALLEL UNSAFE
                ROWS 1000

            AS $BODY$
            BEGIN

                FOR service, su_imsi, cdr_imsi, su_usage, cdr_usage, su_cdr_variance IN

                WITH cdr_sms_agg AS (
                        SELECT COUNT(cs.id) AS volume, cd.imsi
                        FROM cdr_sms AS cs
                        INNER JOIN cdr AS cd ON cs.cdr_uuid = cd.uuid
                        WHERE cs.date_sent BETWEEN start_date_param AND end_date_param
                        GROUP BY cd.imsi
                    )
                    SELECT 'DATA' AS service,
                    su.imsi AS su_imsi,
                    cdr.imsi AS cdr_imsi,
                    COALESCE(SUM(su.volume), 0) AS su_usage,
                    COALESCE(cdr.usage, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(su.volume),0) - COALESCE(cdr.usage,0))
                    AS su_cdr_variance
                    FROM sim_usage AS su
                    LEFT JOIN cdr_data_aggregate AS cdr ON su.imsi = cdr.imsi
                    AND cdr.month = start_date_param
                    WHERE su.billing_cycle_id = billing_cycle_id_param
                    AND su.service = 'DATA'
                    GROUP BY su.imsi,cdr.imsi, cdr.usage

                    UNION

                    SELECT 'DATA' AS service,su.imsi AS su_imsi, cdr.imsi AS cdr_imsi,
                    COALESCE(SUM(su.volume), 0) AS su_usage,
                    COALESCE(cdr.usage, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(su.volume),0) - COALESCE(cdr.usage,0))
                    AS su_cdr_variance
                    FROM cdr_data_aggregate AS cdr
                    LEFT JOIN sim_usage AS su ON su.imsi = cdr.imsi
                    AND su.billing_cycle_id = billing_cycle_id_param
                    AND su.service = 'DATA'
                    WHERE cdr.month = start_date_param
                    GROUP BY su.imsi,cdr.imsi, cdr.usage

                    UNION

                    SELECT 'VOICE' AS service,su.imsi AS su_imsi,cdr.imsi AS cdr_imsi,
                    COALESCE(SUM(su.volume),0) AS su_usage,
                    COALESCE(cdr.voice_usage, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(su.volume),0) - COALESCE(cdr.voice_usage,0))
                    AS su_cdr_variance
                    FROM sim_usage AS su
                    LEFT JOIN cdr_voice_aggregate AS cdr ON su.imsi=cdr.imsi
                    AND cdr.month=start_date_param
                    WHERE su.billing_cycle_id=billing_cycle_id_param
                    AND (su.service='VOICE_MO' OR su.service='VOICE_MT')
                    GROUP BY su.imsi,cdr.imsi, cdr.voice_usage

                    UNION

                    SELECT 'VOICE' AS service,su.imsi AS su_imsi,cdr.imsi AS cdr_imsi,
                    COALESCE(SUM(su.volume),0) AS su_usage,
                    COALESCE(cdr.voice_usage, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(su.volume),0) - COALESCE(cdr.voice_usage,0))
                    AS su_cdr_variance
                    FROM cdr_voice_aggregate AS cdr
                    LEFT JOIN sim_usage AS su ON su.imsi=cdr.imsi
                    AND su.billing_cycle_id=billing_cycle_id_param
                    AND (su.service='VOICE_MO' OR su.service='VOICE_MT')
                    WHERE cdr.month=start_date_param
                    GROUP BY su.imsi,cdr.imsi, cdr.voice_usage



                    UNION

                    SELECT 'SMS' AS service,su.imsi AS su_imsi,cds.imsi AS cdr_imsi,
                    COALESCE(SUM(su.volume),0) AS su_usage,
                    COALESCE(cds.volume, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(su.volume),0) - COALESCE(cds.volume,0))
                    AS su_cdr_variance
                    FROM sim_usage AS su
                    LEFT JOIN cdr_sms_agg AS cds ON su.imsi=cds.imsi
                    WHERE su.billing_cycle_id=billing_cycle_id_param
                    AND (su.service='SMS_MO' OR su.service='SMS_MT')
                    GROUP BY su.imsi,cds.imsi, cds.volume

                    UNION

                    SELECT 'SMS' AS service,su.imsi AS su_imsi,cds.imsi AS cdr_imsi,
                    COALESCE(SUM(su.volume),0) AS su_usage,
                    COALESCE(cds.volume, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(su.volume),0) - COALESCE(cds.volume,0))
                    AS su_cdr_variance
                    FROM cdr_sms_agg AS cds
                    LEFT JOIN sim_usage AS su
                    ON su.imsi=cds.imsi and su.billing_cycle_id=billing_cycle_id_param
                    AND (su.service='SMS_MO' OR su.service='SMS_MT')
                    GROUP BY su.imsi,cds.imsi, cds.volume
                LOOP
                    -- Return the fetched values
                    IF su_cdr_variance = 0  THEN
                        IF cdr_imsi IS NULL OR su_imsi IS NULL THEN
                            RETURN NEXT;
                        END IF;
                    ELSEIF su_cdr_variance != 0  THEN
                        RETURN NEXT;
                    END IF;
                END LOOP;

                RETURN;
            END;
            $BODY$;
        """
    )
