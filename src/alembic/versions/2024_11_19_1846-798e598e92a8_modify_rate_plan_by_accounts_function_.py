"""modify rate_plan_by_accounts function modified search term

Revision ID: 798e598e92a8
Revises: b0ca50b4a884
Create Date: 2024-11-19 18:46:03.810718

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "798e598e92a8"
down_revision = "b0ca50b4a884"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS rate_plan_by_accounts(varchar);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION rate_plan_by_accounts(
            search_term varchar DEFAULT NULL
        )
        RETURNS TABLE (
            account_id integer,
            account_name character varying,
            logo_key character varying,
            id integer,
            name character varying,
            access_fee numeric,
            currency character,
            is_default character varying,
            allowance_used numeric
        )
        LANGUAGE 'plpgsql'
        COST 100
        VOLATILE
        PARALLEL UNSAFE
        ROWS 1000
        AS $BODY$
        DECLARE
            start_of_month date := date_trunc('month', CURRENT_DATE);
            end_of_month date := (date_trunc('month',
            CURRENT_DATE) + interval '1 month')::date;
        BEGIN
            RETURN QUERY
                WITH RankedRatePlans AS (
                    SELECT
                        a.id AS account_id,
                        a.name AS account_name,
                        a.logo_key,
                        r.id,
                        r.name,
                        r.access_fee,
                        a.currency,
                        ROW_NUMBER() OVER (PARTITION BY a.id ORDER BY r.id) AS row_num
                    FROM
                        rate_plan r
                    JOIN
                        account a ON r.account_id = a.id
                    WHERE
                        search_term IS NULL OR (
                            a.name ILIKE '%' || search_term || '%' OR
                            r.name ILIKE '%' || search_term || '%' OR
                            r.access_fee::text ILIKE '%' || search_term || '%'
                        )
                    GROUP BY
                        a.id, r.id
                ),
                AllowancesData AS (
                    SELECT
                        gau.account_id,
                        gau.rate_plan_id,
                        gau.allowance_used
                    FROM
                        get_allowance_usage(start_of_month, end_of_month) AS gau
                )
            SELECT
                rp.account_id,
                rp.account_name,
                rp.logo_key,
                rp.id,
                rp.name,
                rp.access_fee,
                rp.currency,
                (CASE WHEN rp.row_num = 1 THEN 'True' ELSE 'False' END
                )::character varying AS is_default,
                COALESCE(ad.allowance_used, 0) AS allowance_used
            FROM
                RankedRatePlans rp
            LEFT JOIN
                AllowancesData ad ON ad.account_id = rp.account_id
                AND ad.rate_plan_id = rp.id
            ORDER BY
                rp.account_name ASC, rp.id ASC;
        END;
        $BODY$;


        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS rate_plan_by_accounts(varchar);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION rate_plan_by_accounts(
            search_term varchar DEFAULT NULL
        )
        RETURNS TABLE (
            account_id integer,
            account_name character varying,
            logo_key character varying,
            id integer,
            name character varying,
            access_fee numeric,
            currency character varying,
            is_default character varying
        )
        LANGUAGE 'plpgsql'
        COST 100
        VOLATILE
        PARALLEL UNSAFE
        ROWS 1000
        AS $BODY$
        BEGIN
            RETURN QUERY
                WITH RankedRatePlans AS (
                SELECT
                    a.id AS account_id,
                    a.name AS account_name,
                    a.logo_key AS logo_key,
                    r.id AS id,
                    r.name AS name,
                    r.access_fee AS access_fee,
                    'GBP'::character varying AS currency,
                    ROW_NUMBER() OVER (PARTITION BY a.id ORDER BY r.id) AS row_num
                FROM
                    rate_plan r
                JOIN
                    account a ON r.account_id = a.id
                WHERE
                    search_term IS NULL OR (
                        a.name LIKE '%' || search_term || '%' OR
                        r.name LIKE '%' || search_term || '%' OR
                        r.access_fee::text LIKE '%' || search_term || '%'
                    )
                GROUP BY
                    a.id, r.id

            )
            SELECT
            rp.account_id,
            rp.account_name,
            rp.logo_key,
            rp.id,
            rp.name,
            rp.access_fee,
            rp.currency,
            (CASE
            WHEN rp.row_num = 1 THEN 'True'
            ELSE 'False'
            END)::character varying AS is_default
            FROM
                RankedRatePlans rp
            ORDER BY
                rp.account_name ASC, rp.id ASC;

        END;
        $BODY$;


        """
    )
