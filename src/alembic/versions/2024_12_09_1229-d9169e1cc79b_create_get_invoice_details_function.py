"""create get_invoice_details function

Revision ID: d9169e1cc79b
Revises: 3d43adf4053f
Create Date: 2024-12-09 12:29:39.243461

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "d9169e1cc79b"
down_revision = "3d43adf4053f"
branch_labels = None
depends_on = None


def upgrade() -> None:

    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_invoice_details(invoice_id_param BIGINT)
        RETURNS TABLE (
            iccid character varying(20),
            msisdn character varying(15),
            imsi character varying(15),
            sim_id integer,
            rate_plan_name character varying,
            sim_fee_charge NUMERIC,
            plan_data_charge NUMERIC,
            roaming_data_charge NUMERIC,
            data_charge NUMERIC,
            voice_charge NUMERIC,
            sms_charge NUMERIC,
            plan_data_volume NUMERIC,
            roaming_data_volume NUMERIC,
            data_volume NUMERIC,
            sms_volume NUMERIC,
            sms_mo_volume NUMERIC,
            sms_mt_volume NUMERIC,
            voice_volume NUMERIC,
            voice_mo_volume NUMERIC,
            voice_mt_volume NUMERIC
        )
        AS $$
        BEGIN
            RETURN QUERY
            SELECT
                subs.iccid,
                subs.msisdn,
                subs.imsi,
                subs.sim_id,
                rp.name AS rate_plan_name,
                sub.access_fee AS sim_fee_charge,
                0.00 AS plan_data_charge,
                0.00 AS roaming_data_charge,
                ROUND(SUM(CASE WHEN su.service = 'DATA'
                THEN su.charge ELSE 0 END),2) AS data_charge,
                ROUND(SUM(CASE WHEN su.service IN ('VOICE_MO', 'VOICE_MT')
                THEN su.charge ELSE 0 END), 2) AS voice_charge,
                ROUND(SUM(CASE WHEN su.service IN ('SMS_MO', 'SMS_MT')
                THEN su.charge ELSE 0 END), 2) AS sms_charge,
                0.00 AS plan_data_volume,
                0.00 AS roaming_data_volume,
                ROUND(SUM(CASE WHEN su.service = 'DATA'
                THEN reverse_convert_unit('MB', su.volume) ELSE 0 END), 2)
                AS data_volume,
                ROUND(SUM(CASE WHEN su.service IN ('SMS_MO', 'SMS_MT')
                THEN su.volume ELSE 0 END), 0) AS sms_volume,
                ROUND(SUM(CASE WHEN su.service = 'SMS_MO' THEN su.volume ELSE 0 END), 0)
                AS sms_mo_volume,
                ROUND(SUM(CASE WHEN su.service = 'SMS_MT' THEN su.volume ELSE 0 END), 0)
                AS sms_mt_volume,
                ROUND(SUM(CASE WHEN su.service IN ('VOICE_MO', 'VOICE_MT')
                THEN reverse_convert_unit('Min', su.volume) ELSE 0 END), 0)
                AS voice_volume,
                ROUND(SUM(CASE WHEN su.service = 'VOICE_MO'
                THEN reverse_convert_unit('Min', su.volume) ELSE 0 END), 0)
                AS voice_mo_volume,
                ROUND(SUM(CASE WHEN su.service = 'VOICE_MT'
                THEN reverse_convert_unit('Min', su.volume) ELSE 0 END), 0)
                AS voice_mt_volume
            FROM
                invoice AS inv
            INNER JOIN
                subscription AS sub ON sub.invoice_id = inv.id
            INNER JOIN
                subscription_sim AS subs ON subs.subscription_id = sub.id
            INNER JOIN
                rate_plan AS rp ON rp.id = sub.rate_plan_id
            LEFT JOIN
                sim_usage AS su ON su.imsi = subs.imsi
                AND inv.billing_cycle_id = su.billing_cycle_id
            WHERE
                inv.id = invoice_id_param
                AND sub.sims_total > 0
            GROUP BY
                subs.iccid, subs.msisdn, subs.imsi, subs.sim_id, rp.name, sub.access_fee
            ORDER BY
                subs.imsi;
        END;
        $$ LANGUAGE plpgsql;
        """
    )


def downgrade() -> None:

    op.execute(
        """
        DROP FUNCTION IF EXISTS get_invoice_details(BIGINT);
        """
    )
