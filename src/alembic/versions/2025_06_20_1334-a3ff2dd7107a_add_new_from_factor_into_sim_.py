"""add_new_from_factor_into_sim_reallocation

Revision ID: a3ff2dd7107a
Revises: da9fd94b3ad4
Create Date: 2025-06-16 13:34:32.133358

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "a3ff2dd7107a"
down_revision = "da9fd94b3ad4"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
            DROP FUNCTION IF EXISTS sim_reallocation(text[], int, int);
    """
    )

    op.execute(
        """
            CREATE OR REPLACE FUNCTION public.sim_reallocation(
                input_imsis text[],
                input_account_id int,
                input_rate_plan_id int
            )
            RETURNS text
            LANGUAGE 'plpgsql'
            COST 100
            VOLATILE PARALLEL UNSAFE
            AS $BODY$
            DECLARE
                total_count INTEGER;
                account_count INTEGER;
                check_rate_plan INTEGER;
                same_account INTEGER;
                form_factor_text TEXT;
                imsi_record RECORD; -- Define imsi_record as a record type
                imsi_array TEXT[];
            BEGIN

                -- Check if input IMSIs exist in allocation
                SELECT COUNT(*) INTO total_count
                FROM allocation
                WHERE imsi = ANY(input_imsis);

                IF total_count <> cardinality(input_imsis) THEN
                    RETURN 'Input IMSIs not found in allocation.';
                END IF;

                -- Check if only one account is associated with the input IMSIs
                SELECT COUNT(DISTINCT account_id) INTO account_count
                FROM allocation
                WHERE imsi = ANY(input_imsis);

                IF account_count <> 1 THEN
                    RETURN 'Multiple accounts found.';
                END IF;

                -- Check if the specified rate plan exists for the input account
                SELECT COUNT(*) INTO check_rate_plan
                FROM rate_plan
                WHERE account_id = input_account_id AND id = input_rate_plan_id;

                IF check_rate_plan <> 1 THEN
                    RETURN 'Rateplan against account not found.';
                END IF;

                FOR form_factor_text IN
                    SELECT DISTINCT r.form_factor
                    FROM allocation AS al
                    INNER JOIN range AS r ON r.id = al.range_id
                    WHERE al.imsi = ANY(input_imsis)
                LOOP
                    imsi_array := array_agg(al.imsi)
                    FROM allocation AS al
                    INNER JOIN range AS r ON r.id = al.range_id
                    WHERE al.imsi = ANY(input_imsis)
                    AND r.form_factor = form_factor_text;


                    -- Perform updates within a transaction
                    -- Update allocation_details
                    UPDATE allocation_details AS ald
                    SET total_sim = total_sim - subquery.used_sim_count
                    FROM (
                        SELECT al.allocation_details_id,
                        COUNT(al.imsi) AS used_sim_count
                        FROM allocation AS al
                        WHERE al.imsi = ANY(imsi_array)
                        GROUP BY al.allocation_details_id
                    ) AS subquery
                    WHERE ald.id = subquery.allocation_details_id;

                    -- Insert new allocation_details and update allocation
                    WITH inserted_rows AS (
                        INSERT INTO allocation_details (file_name, total_sim, error_sim)
                        SELECT
                            CASE form_factor_text
                                WHEN 'STANDARD' THEN CONCAT(ac.name, '_2FF')
                                WHEN 'MICRO' THEN CONCAT(ac.name,'_3FF')
                                WHEN 'NANO' THEN CONCAT(ac.name,'_4FF')
                                WHEN 'eSIM_MFF2' THEN CONCAT(ac.name,'_eSIM_MFF2')
                                WHEN 'eSIM_MFF2_eUICC' THEN
                                CONCAT(ac.name, '_eSIM_MFF2_eUICC')
                                ELSE ''
                            END AS file_name,
                            COUNT(al.imsi) AS total_sim,
                            0 AS error_sim
                        FROM allocation AS al
                        INNER JOIN range AS r ON r.id = al.range_id
                        INNER JOIN account AS ac ON ac.id = input_account_id
                        WHERE al.imsi = ANY(imsi_array)
                        GROUP BY ac.name
                        RETURNING id, file_name
                    )
                    -- Use the inserted file names to update the allocation table
                    UPDATE allocation AS al
                    SET
                        allocation_details_id = inserted_rows.id,
                        title = inserted_rows.file_name,
                        account_id = input_account_id,
                        rate_plan_id = input_rate_plan_id
                    FROM inserted_rows
                    WHERE al.imsi = ANY(imsi_array);

                    -- Update sim_card.rate_plan_id where imsi matches imsi_array
                    UPDATE sim_card
                    SET rate_plan_id = input_rate_plan_id
                    WHERE imsi = ANY(imsi_array);
                END LOOP;


                RETURN 'Success';
            EXCEPTION
                WHEN OTHERS THEN
                    RETURN 'Error occurred: ' || SQLERRM;
            END;
            $BODY$;
        """
    )


def downgrade() -> None:
    op.execute(
        """
            DROP FUNCTION IF EXISTS sim_reallocation(text[], int, int);
    """
    )

    op.execute(
        """
            CREATE OR REPLACE FUNCTION public.sim_reallocation(
                input_imsis text[],
                input_account_id int,
                input_rate_plan_id int
            )
            RETURNS text
            LANGUAGE 'plpgsql'
            COST 100
            VOLATILE PARALLEL UNSAFE
            AS $BODY$
            DECLARE
                total_count INTEGER;
                account_count INTEGER;
                check_rate_plan INTEGER;
                same_account INTEGER;
                form_factor_text TEXT;
                imsi_record RECORD; -- Define imsi_record as a record type
                imsi_array TEXT[];
            BEGIN

                -- Check if input IMSIs exist in allocation
                SELECT COUNT(*) INTO total_count
                FROM allocation
                WHERE imsi = ANY(input_imsis);

                IF total_count <> cardinality(input_imsis) THEN
                    RETURN 'Input IMSIs not found in allocation.';
                END IF;

                -- Check if only one account is associated with the input IMSIs
                SELECT COUNT(DISTINCT account_id) INTO account_count
                FROM allocation
                WHERE imsi = ANY(input_imsis);

                IF account_count <> 1 THEN
                    RETURN 'Multiple accounts found.';
                END IF;

                -- Check if the specified rate plan exists for the input account
                SELECT COUNT(*) INTO check_rate_plan
                FROM rate_plan
                WHERE account_id = input_account_id AND id = input_rate_plan_id;

                IF check_rate_plan <> 1 THEN
                    RETURN 'Rateplan against account not found.';
                END IF;

                FOR form_factor_text IN
                    SELECT DISTINCT r.form_factor
                    FROM allocation AS al
                    INNER JOIN range AS r ON r.id = al.range_id
                    WHERE al.imsi = ANY(input_imsis)
                LOOP
                    imsi_array := array_agg(al.imsi)
                    FROM allocation AS al
                    INNER JOIN range AS r ON r.id = al.range_id
                    WHERE al.imsi = ANY(input_imsis)
                    AND r.form_factor = form_factor_text;


                    -- Perform updates within a transaction
                    -- Update allocation_details
                    UPDATE allocation_details AS ald
                    SET total_sim = total_sim - subquery.used_sim_count
                    FROM (
                        SELECT al.allocation_details_id,
                        COUNT(al.imsi) AS used_sim_count
                        FROM allocation AS al
                        WHERE al.imsi = ANY(imsi_array)
                        GROUP BY al.allocation_details_id
                    ) AS subquery
                    WHERE ald.id = subquery.allocation_details_id;

                    -- Insert new allocation_details and update allocation
                    WITH inserted_rows AS (
                        INSERT INTO allocation_details (file_name, total_sim, error_sim)
                        SELECT
                            CASE form_factor_text
                                WHEN 'STANDARD' THEN CONCAT(ac.name, '_2FF')
                                WHEN 'MICRO' THEN CONCAT(ac.name,'_3FF')
                                WHEN 'NANO' THEN CONCAT(ac.name,'_4FF')
                                ELSE ''
                            END AS file_name,
                            COUNT(al.imsi) AS total_sim,
                            0 AS error_sim
                        FROM allocation AS al
                        INNER JOIN range AS r ON r.id = al.range_id
                        INNER JOIN account AS ac ON ac.id = input_account_id
                        WHERE al.imsi = ANY(imsi_array)
                        GROUP BY ac.name
                        RETURNING id, file_name
                    )
                    -- Use the inserted file names to update the allocation table
                    UPDATE allocation AS al
                    SET
                        allocation_details_id = inserted_rows.id,
                        title = inserted_rows.file_name,
                        account_id = input_account_id,
                        rate_plan_id = input_rate_plan_id
                    FROM inserted_rows
                    WHERE al.imsi = ANY(imsi_array);

                    -- Update sim_card.rate_plan_id where imsi matches imsi_array
                    UPDATE sim_card
                    SET rate_plan_id = input_rate_plan_id
                    WHERE imsi = ANY(imsi_array);
                END LOOP;


                RETURN 'Success';
            EXCEPTION
                WHEN OTHERS THEN
                    RETURN 'Error occurred: ' || SQLERRM;
            END;
            $BODY$;
        """
    )
