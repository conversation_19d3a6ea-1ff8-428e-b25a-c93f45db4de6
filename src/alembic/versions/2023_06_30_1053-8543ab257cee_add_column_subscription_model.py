"""add column subscription model

Revision ID: 8543ab257cee
Revises: 1b90cc698b94
Create Date: 2023-06-30 10:53:29.413689

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "8543ab257cee"
down_revision = "1b90cc698b94"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column(
        "subscription",
        sa.Column("sim_charge", sa.Numeric(), server_default="0", nullable=False),
    )

    op.execute(
        """UPDATE subscription AS s
        SET sim_charge = ac.sim_charge
        FROM invoice AS i
        INNER JOIN account AS ac ON i.account_id = ac.id
        WHERE s.invoice_id = i.id;"""
    )


def downgrade() -> None:
    op.drop_column("subscription", "sim_charge")
