"""add seed data rule rely

Revision ID: db03fd62475b
Revises: 7302eac86a13
Create Date: 2025-02-25 11:58:51.385352

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "db03fd62475b"
down_revision = "7302eac86a13"
branch_labels = None
depends_on = None


def insert_rule_details(conn, category_name, data):
    """Insert rule details for a given category name."""
    result = conn.execute(
        sa.text("SELECT id FROM rule_category WHERE category = :name"),
        {"name": category_name},
    )
    category_id = result.scalar()

    if category_id:
        data["rule_category_id"] = category_id
        conn.execute(
            sa.text(
                """
                INSERT INTO rule_details_rely (
                    rule_category_id, data_volume, data_unit, sms_unit, voice_unit,
                    threshold, percentage_unit, view_deactivate_sim,
                    required_deactivate_sim, view_rate_plan_change,
                    required_rate_plan_change, add_any_rate_plan,
                    is_monthly_pool, view_email, view_sms, view_push
                ) VALUES (
                    :rule_category_id, :data_volume, :data_unit, :sms_unit,
                    :voice_unit, :threshold, :percentage_unit, :view_deactivate_sim,
                    :required_deactivate_sim, :view_rate_plan_change,
                    :required_rate_plan_change, :add_any_rate_plan,
                    :is_monthly_pool, :view_email, :view_sms, :view_push
                )
            """
            ),
            data,
        )


def upgrade():
    conn = op.get_bind()

    insert_rule_details(
        conn,
        "Cycle To Date Data Usage",
        {
            "data_volume": True,
            "data_unit": True,
            "sms_unit": False,
            "voice_unit": False,
            "threshold": False,
            "percentage_unit": False,
            "view_deactivate_sim": True,
            "required_deactivate_sim": False,
            "view_rate_plan_change": True,
            "required_rate_plan_change": False,
            "add_any_rate_plan": True,
            "is_monthly_pool": False,
            "view_email": True,
            "view_sms": False,
            "view_push": False,
        },
    )

    insert_rule_details(
        conn,
        "Monthly Pooled Data Usage",
        {
            "data_volume": False,
            "data_unit": False,
            "sms_unit": False,
            "voice_unit": False,
            "threshold": True,
            "percentage_unit": True,
            "view_deactivate_sim": True,
            "required_deactivate_sim": False,
            "view_rate_plan_change": True,
            "required_rate_plan_change": True,
            "add_any_rate_plan": False,
            "is_monthly_pool": True,
            "view_email": True,
            "view_sms": False,
            "view_push": False,
        },
    )


def downgrade():
    op.execute(
        "DELETE FROM rule_details_rely WHERE rule_category_id IN "
        "(SELECT id FROM rule_category WHERE category IN "
        "('Cycle To Date Data Usage', 'Monthly Pooled Data Usage'))"
    )
