"""add lock column in rules table

Revision ID: c3bdb3d9d587
Revises: c2caa3456cc3
Create Date: 2025-01-23 18:50:09.168744

"""
import sqlalchemy as sa
from alembic import op

revision = "c3bdb3d9d587"
down_revision = "c2caa3456cc3"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column(
        "rules",
        sa.Column(
            "lock", sa.<PERSON>(), server_default=sa.text("false"), nullable=False
        ),
    )


def downgrade() -> None:
    op.drop_column("rules", "lock")
