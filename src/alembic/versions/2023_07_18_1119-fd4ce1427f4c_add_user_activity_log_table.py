"""add user activity log table

Revision ID: fd4ce1427f4c
Revises: ac1bc09951f2
Create Date: 2023-07-18 11:19:13.664722

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

from sim.domain.model import SimStatus

# revision identifiers, used by Alembic.
revision = "fd4ce1427f4c"
down_revision = "ac1bc09951f2"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    sim_status = postgresql.ENUM(SimStatus, name="sim_status", create_type=False)
    op.create_table(
        "user_activity_log",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False, start=1, increment=1),
            nullable=False,
        ),
        sa.Column("provider_log_id", sa.Integer(), nullable=False),
        sa.Column("audit_uuid", postgresql.UUID(), nullable=False),
        sa.Column("prior_value", sim_status, nullable=False),
        sa.Column("new_value", sim_status, nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("user_activity_log")
    # ### end Alembic commands ###
