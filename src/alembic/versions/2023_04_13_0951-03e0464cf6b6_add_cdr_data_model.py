"""add_cdr_data_model

Revision ID: 03e0464cf6b6
Revises: ad2f29f5445c
Create Date: 2023-04-13 09:51:11.381113

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "03e0464cf6b6"
down_revision = "ad2f29f5445c"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "cdr_data",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False, start=1, increment=1),
            nullable=False,
        ),
        sa.Column("cdr_uuid", postgresql.UUID(), nullable=False),
        sa.Column("session_starttime", sa.DateTime(), nullable=False),
        sa.Column("session_endtime", sa.DateTime, nullable=False),
        sa.Column("duration", sa.Integer, nullable=False),
        sa.<PERSON>umn("data_volume", sa.Integer, nullable=False),
        sa.ForeignKeyConstraint(
            ["cdr_uuid"],
            ["cdr.uuid"],
            name=op.f("fk_cdr_uuid"),
        ),
        sa.PrimaryKeyConstraint("id"),
    )


def downgrade() -> None:
    op.drop_table("cdr_data")
