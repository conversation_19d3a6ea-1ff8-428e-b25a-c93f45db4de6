"""Add is_billable field to Account

Revision ID: 2a666b543763
Revises: d8ea075810e7
Create Date: 2023-01-19 16:58:16.592758

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "2a666b543763"
down_revision = "d8ea075810e7"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "account",
        sa.Column(
            "is_billable", sa.<PERSON>(), server_default=sa.text("false"), nullable=False
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("account", "is_billable")
    # ### end Alembic commands ###
