"""create invoice_account_charge_details function

Revision ID: 21c6c1984366
Revises: f56aa36a5873
Create Date: 2024-11-04 16:36:01.514303

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "21c6c1984366"
down_revision = "f56aa36a5873"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        CREATE OR REPLACE FUNCTION public.invoice_account_charge_details(
            account_id_param integer,
            invoice_id_param integer,
            month_param date)
            RETURNS void
            LANGUAGE 'plpgsql'
            COST 100
            VOLATILE PARALLEL UNSAFE
        AS $BODY$
            BEGIN
                INSERT INTO invoice_account_charge
                (invoice_id, rate_plan_id, account_charge)
                SELECT invoice_id_param, rp.id,r.value
                FROM allocation AS al
                INNER JOIN sim_card AS sc
                ON sc.allocation_id = al.id and al.account_id = account_id_param
                INNER JOIN sim_monthly_statistic AS sms ON sms.sim_card_id = sc.id
                AND month=month_param AND sms.sim_status != 'READY_FOR_ACTIVATION'
                INNER JOIN rate_plan AS rp
                ON rp.id = COALESCE(sc.rate_plan_id, al.rate_plan_id)
                INNER JOIN rate_group AS rg
                ON rg.rate_plan_id = rp.id AND rg.rate_model_id = 3
                INNER JOIN rate_group_service AS rgs ON rgs.rate_group_id = rg.id
                INNER JOIN rate AS r ON r.rate_group_id = rg.id
                WHERE al.account_id = account_id_param
                AND month=month_param
                AND sms.sim_status != 'READY_FOR_ACTIVATION'
                AND rg.rate_model_id = 3
                GROUP BY rp.id,r.value;
            END;
        $BODY$;

        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS invoice_account_charge_details(integer, integer, date);
        """
    )
