# flake8: noqa

"""Add get_account_details function

Revision ID: be825380e5f9
Revises: 8e335d80dceb
Create Date: 2025-04-08 10:27:52.476590

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "be825380e5f9"
down_revision = "8e335d80dceb"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        """
        CREATE OR REPLACE VIEW account_details_view AS
            SELECT
                a.id,
                a.name,
                a.logo_key,
                a.status::account_status,
                a.is_billable,
                a.agreement_number,
                a.currency,
                a.industry_vertical::industry_vertical,
                a.sales_channel::sales_channel,
                a.sales_person,
                a.contract_end_date,
                a.contact_name,
                a.email,
                a.phone,
                a.job_title,
                a.country,
                a.state_region,
                a.city,
                a.address1,
                a.address2,
                a.postcode,
                a.sim_charge,
                a.payment_terms,
                a.organization_id,
                a.sim_profile::sim_profile,
                att.carrier_name,
                COALESCE(att.warning_threshold, 0) AS warning_threshold,
                COALESCE(atc.threshold_charge, 0) AS threshold_charge,
                COALESCE(sc.sims_total, 0)::INT AS sims_total,
                COALESCE(sc.active_sims_total, 0)::INT AS active_sims_total,
                COALESCE(rp.plans_total, 0)::INT AS plans_total,
                rp.default_rate_plan::VARCHAR AS default_rate_plan,
                COALESCE(pt.product_types, '[]'::json) AS product_types,
                0 AS users_total
            FROM
                account a
            LEFT JOIN
                account_traffic_thresholds att ON a.id = att.account_id
            LEFT JOIN
                account_traffic_charges atc ON a.id = atc.account_id
            LEFT JOIN (
                SELECT
                    account_id,
                    count(*)::INT AS sims_total,
                    count(*) FILTER (WHERE sc.sim_status = 'ACTIVE') AS active_sims_total
                FROM allocation
                JOIN sim_card sc ON sc.imsi = allocation.imsi
                GROUP BY account_id
            ) sc ON a.id = sc.account_id
            LEFT JOIN (
                SELECT
                    account_id,
                    count(*)::INT AS plans_total,
                    max(name) FILTER (WHERE is_default) AS default_rate_plan
                FROM rate_plan
                GROUP BY account_id
            ) rp ON a.id = rp.account_id
            LEFT JOIN (
                SELECT
                    apt.account_id,
                    COALESCE(json_agg(apt.product_type), '[]'::json) AS product_types
                FROM account_product_type apt
                GROUP BY apt.account_id
            ) pt ON a.id = pt.account_id;
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_account_details(
            search_term TEXT DEFAULT NULL,
            account_id_param INT[] DEFAULT NULL,
            order_by_column TEXT DEFAULT 'id',
            order_direction TEXT DEFAULT 'ASC',
            page_offset INT DEFAULT NULL,
            page_size INT DEFAULT NULL
        )
        RETURNS TABLE (
            id INT,
            name VARCHAR,
            logo_key VARCHAR,
            status account_status,
            is_billable BOOLEAN,
            agreement_number VARCHAR,
            currency CHAR(3),
            industry_vertical industry_vertical,
            sales_channel sales_channel,
            sales_person VARCHAR,
            contract_end_date DATE,
            contact_name VARCHAR,
            email VARCHAR,
            phone VARCHAR,
            job_title VARCHAR,
            country CHAR(3),
            state_region VARCHAR,
            city VARCHAR,
            address1 VARCHAR,
            address2 VARCHAR,
            postcode VARCHAR,
            sim_charge NUMERIC,
            payment_terms INT,
            organization_id INT,
            sim_profile sim_profile,
            carrier_name VARCHAR,
            warning_threshold INT,
            threshold_charge NUMERIC,
            sims_total INT,
            active_sims_total INT,
            plans_total INT,
            default_rate_plan VARCHAR,
            product_types JSON,
            users_total INT
        ) AS $$
        DECLARE
            final_order_by_column TEXT := COALESCE(order_by_column, 'id');
            final_order_direction TEXT := COALESCE(order_direction, 'ASC');
            pagination_clause TEXT := '';
            order_clause TEXT;
        BEGIN
            IF page_size IS NOT NULL AND page_offset IS NOT NULL THEN
                pagination_clause := ' LIMIT ' || page_size || ' OFFSET ' || page_offset;
            END IF;
            order_clause := 'ORDER BY ' || quote_ident(final_order_by_column) || ' ' || final_order_direction;
            RETURN QUERY EXECUTE
                '
                SELECT *
                FROM account_details_view
                WHERE 
                    ($1 IS NULL OR id = ANY($1)) AND
                    (
                        $2 IS NULL OR $2 = '''' OR EXISTS (
                            SELECT 1 FROM (
                                VALUES
                                    (name)
                            ) AS search_columns(col)
                            WHERE col ILIKE ''%'' || $2 || ''%''
                        )
                    )
                ' || order_clause || pagination_clause
            USING account_id_param, search_term;
        END;
        $$ LANGUAGE plpgsql;
        """
    )


def downgrade():
    op.execute(
        """
        DROP FUNCTION IF EXISTS get_account_details(
            TEXT, INT[], TEXT, TEXT, INT, INT
        );
        """
    )
    op.execute(
        """
        DROP VIEW IF EXISTS account_details_view;
        """
    )
