"""modify get_allowance_usage function with view

Revision ID: 5191ce1ad280
Revises: 21aeec828c28
Create Date: 2025-02-04 19:00:19.545091

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "5191ce1ad280"
down_revision = "21aeec828c28"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS get_allowance_usage(integer[]);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_allowance_usage(
            account_ids integer[] DEFAULT NULL,
            rate_model_codes VARCHAR[] DEFAULT NULL,
            rate_plan_service service DEFAULT 'DATA'::service
        )
        RETURNS TABLE (
            account_id INT,
            rate_plan_id INT,
            allowance_used NUMERIC
        ) AS $$
        DECLARE
            partition_name TEXT;
            sql_query TEXT;
        BEGIN
            -- Construct the partition name dynamically for the current month
            partition_name := 'cdr_data_aggregate_' || to_char(current_date, 'YYYY_MM');

            -- Construct the SQL query dynamically
            sql_query := '
                SELECT
                    auv.account_id,
                    auv.rate_plan_id,
                    ROUND((COALESCE(SUM(cda.usage), 0) * 100) / GREATEST(
                    auv.total_allowance, 1), 2) AS allowance_used
                FROM
                    allowance_usage_view auv
                INNER JOIN ' || partition_name || ' AS cda
                    ON cda.imsi = auv.imsi
                WHERE
                    ($1 IS NULL OR auv.account_id = ANY($1))
                    AND ($2 IS NULL OR auv.rate_model_code != ALL($2))
                    AND ($3 IS NULL OR auv.service = $3)
                GROUP BY
                    auv.account_id,
                    auv.rate_plan_id,
                    auv.total_allowance,
                    auv.service,
                    auv.rate_model_code,
                    auv.imsi;
            ';

            -- Execute the dynamically constructed query and return the result
            RETURN QUERY EXECUTE sql_query USING account_ids, rate_model_codes,
            rate_plan_service;
        END;
        $$ LANGUAGE plpgsql;
        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS get_allowance_usage(integer[], varchar[], service);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_allowance_usage(
            account_ids integer[] DEFAULT NULL
        )
        RETURNS TABLE (
            account_id INT,
            rate_plan_id INT,
            -- allowances INT,
            -- range_unit TEXT,
            -- total_allowances INT,
            -- final_rate_plan_id INT,
            -- data_usage NUMERIC,
            allowance_used NUMERIC
        ) AS $$
        DECLARE
            partition_name TEXT;
            sql_query TEXT;
        BEGIN
            -- Dynamically construct the partition name for the current month
            partition_name := 'cdr_data_aggregate_' || to_char(current_date, 'YYYY_MM');

            -- Construct the dynamic SQL query
            sql_query :=
                'WITH cte_usage AS (
                    SELECT
                        al.account_id,
                        COALESCE(sc.rate_plan_id, al.rate_plan_id) AS rate_plan_id,
                        r.range_to,
                        r.range_unit,
                        COALESCE(SUM(cda.usage), 0) AS total_data_volume,
                        convert_unit(r.range_unit, r.range_to) AS total_allowance
                    FROM
                        allocation AS al
                    INNER JOIN sim_card AS sc
                        ON sc.allocation_id = al.id
                    INNER JOIN rate_plan AS rp
                        ON rp.id = COALESCE(sc.rate_plan_id, al.rate_plan_id)
                    INNER JOIN rate_group AS rg
                        ON rg.rate_plan_id = rp.id
                    INNER JOIN rate_group_service AS rgs
                        ON rgs.rate_group_id = rg.id
                        AND rgs.service = ''DATA''
                        AND rg.rate_model_id NOT IN (1, 2)
                    INNER JOIN rate AS r
                        ON rgs.rate_group_id = r.rate_group_id
                    INNER JOIN ' || partition_name || ' AS cda
                        ON cda.imsi = al.imsi
                    WHERE
                        ($1 IS NULL OR al.account_id = ANY($1))
                    GROUP BY
                        al.account_id,
                        sc.rate_plan_id,
                        al.rate_plan_id,
                        r.range_to,
                        r.range_unit
                )
                SELECT
                    ct.account_id AS account_id,
                    ct.rate_plan_id,
                    ROUND((ct.total_data_volume * 100) / GREATEST(
                    ct.total_allowance, 1), 2) AS allowance_used
                FROM
                    cte_usage AS ct';

            -- Execute the dynamically constructed query with parameters
            RETURN QUERY EXECUTE sql_query USING account_ids;
        END;
        $$ LANGUAGE plpgsql;

        """
    )
