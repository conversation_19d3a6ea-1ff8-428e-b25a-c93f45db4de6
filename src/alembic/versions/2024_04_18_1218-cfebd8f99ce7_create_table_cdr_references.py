"""create table cdr_references

Revision ID: cfebd8f99ce7
Revises: dcfcd597792c
Create Date: 2024-04-18 12:18:50.574382

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "cfebd8f99ce7"
down_revision = "dcfcd597792c"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "cdr_references",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("cdr_uuid", postgresql.UUID(), nullable=False),
        sa.Column("file_key", sa.String(), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_cdr_references")),
        sa.ForeignKeyConstraint(
            ["cdr_uuid"],
            ["cdr.uuid"],
            name=op.f("fk_cdr_references_cdr_uuid"),
        ),
    )


def downgrade() -> None:
    op.drop_table("cdr_references")
