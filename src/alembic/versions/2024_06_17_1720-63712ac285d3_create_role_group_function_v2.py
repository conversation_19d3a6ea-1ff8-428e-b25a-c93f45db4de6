"""create role_group function v2

Revision ID: 63712ac285d3
Revises: 550ff65896f1
Create Date: 2024-06-09 17:20:27.661721

"""
# import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "63712ac285d3"
down_revision = "550ff65896f1"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
            DROP FUNCTION IF EXISTS role_group_function(
                INT, UUID
                );
        """
    )
    op.execute(
        """
            CREATE OR REPLACE FUNCTION role_group_function_v2(
                input_role_id INT)
                RETURNS void
                LANGUAGE 'plpgsql'
                COST 100
                VOLATILE PARALLEL UNSAFE
            AS $BODY$
                        BEGIN
                            INSERT INTO role_group (role_id, organization_id)
                            SELECT input_role_id, id FROM account;
                        END;

            $BODY$;
"""
    )


def downgrade() -> None:
    op.execute(
        """
            DROP FUNCTION IF EXISTS role_group_function_v2(
                INT, UUID
                );
        """
    )

    op.execute(
        """
            CREATE OR REPLACE FUNCTION role_group_function(
                input_role_id INT,
                input_role_uuid UUID)
                RETURNS void
                LANGUAGE 'plpgsql'
                COST 100
                VOLATILE PARALLEL UNSAFE
            AS $BODY$
                        BEGIN
                            INSERT INTO role_group (role_id, organization_id, role_uuid)
                            SELECT input_role_id, id, input_role_uuid FROM account;
                        END;

            $BODY$;
"""
    )
