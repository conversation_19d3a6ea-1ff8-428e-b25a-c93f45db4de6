"""copy data in sim activity table

Revision ID: 7d77c994a6d4
Revises: 4c6fd4177885
Create Date: 2023-08-08 13:27:34.334664

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "7d77c994a6d4"
down_revision = "4c6fd4177885"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        -- Drop min_data table if it exists
        DROP TABLE IF EXISTS min_data;
        -- Create temp table min_data
        CREATE TEMP TABLE min_data AS
        SELECT min(sp.id) as id, sp.sim_audit_log_uuid
        FROM sim_provider_log as sp
        GROUP BY sp.sim_audit_log_uuid;
        -- Drop max_data table if it exists
        DROP TABLE IF EXISTS max_data;
        -- Create temp table max_data
        CREATE TEMP TABLE max_data AS
        SELECT max(sp.id) as id, sp.sim_audit_log_uuid
        FROM sim_provider_log as sp
        GROUP BY sp.sim_audit_log_uuid;

        -- Insert data into sim_activity_log
        INSERT INTO sim_activity_log
        (uuid, imsi, iccid, msisdn, request_type, prior_value, new_value, \
               client_ip, created_at, created_by)
        SELECT mi.sim_audit_log_uuid, sa.reference, sc.iccid, sc.msisdn, \
               sa.request_type, sp.prior_status,
        (CASE sp1.status WHEN 'Deactivated' THEN 'DEACTIVATED'::sim_status \
               WHEN 'Active' THEN 'ACTIVE'::sim_status ELSE 'ACTIVE' END) AS status,
        sp.client_ip, sa.created_at, sa.created_by
        FROM min_data AS mi
        INNER JOIN sim_audit_log AS sa ON mi.sim_audit_log_uuid = sa.uuid
        INNER JOIN sim_provider_log AS sp ON mi.id = sp.id
        INNER JOIN sim_card AS sc ON sa.reference = sc.imsi
        INNER JOIN max_data AS mx ON sa.uuid = mx.sim_audit_log_uuid
        INNER JOIN sim_provider_log AS sp1 ON mx.id = sp1.id
        ORDER BY sa.created_at;
    """
    )

    op.add_column(
        "sim_provider_log",
        sa.Column(
            "sim_activity_log_uuid", postgresql.UUID(as_uuid=True), nullable=True
        ),
    )

    op.execute(
        "UPDATE sim_provider_log\
        SET sim_activity_log_uuid =sim_audit_log_uuid;"
    )

    # op.create_foreign_key(
    #     op.f("fk_sim_activity_log_uuid"),
    #     "sim_provider_log",
    #     "sim_activity_log",
    #     ["sim_activity_log_uuid"],
    #     ["uuid"],
    # )


def downgrade() -> None:

    op.execute(
        "UPDATE sim_provider_log\
        SET sim_audit_log_uuid =sim_activity_log_uuid;"
    )

    # op.drop_constraint(
    #     op.f("fk_sim_activity_log_uuid"), "sim_provider_log", type_="foreignkey"
    # )

    op.drop_column("sim_provider_log", "sim_activity_log_uuid")
