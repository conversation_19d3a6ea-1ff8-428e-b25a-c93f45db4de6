"""create_rule_definition_table

Revision ID: 2028bb4f380f
Revises: ef8fa30e9662
Create Date: 2024-07-18 15:09:38.200154

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.sql import column, table, text

# revision identifiers, used by Alembic.
revision = "2028bb4f380f"
down_revision = "ef8fa30e9662"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "rule_definition",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("rule_category_id", sa.Integer(), nullable=False),
        sa.Column("definition", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["rule_category_id"],
            ["rule_category.id"],
            name=op.f("fk_rule_definition_rule_category_id_rule_category"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_rule_definition")),
    )

    # Insert data to the rule_definition table

    rule_category = table(
        "rule_category",
        column("id", sa.Integer),
        column("rule_type_id", sa.Integer),
        column("category", sa.String),
    )

    conn = op.get_bind()
    rule_category_query = sa.select([rule_category.c.id]).where(
        rule_category.c.category == "Cycle To Date Data Usage"
    )

    rule_category_id = conn.execute(rule_category_query).scalar_one()

    query = text(
        """
            INSERT INTO rule_definition (rule_category_id, definition)
            VALUES (:rule_category_id, 'Data usage exceeds a specified limit')
            """
    )

    conn.execute(query, {"rule_category_id": rule_category_id})


def downgrade() -> None:
    op.drop_table("rule_definition")
