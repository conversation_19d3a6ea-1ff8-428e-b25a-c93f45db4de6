"""create table role_group

Revision ID: cd735b595a0d
Revises: 962a6790dcce
Create Date: 2024-02-27 12:18:50.383722

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "cd735b595a0d"
down_revision = "962a6790dcce"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "role_group",
        sa.<PERSON>umn(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.<PERSON>umn("role_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("organization_id", sa.Integer(), nullable=False),
        sa.Column("role_uuid", postgresql.UUID(), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_role_group")),
        sa.ForeignKeyConstraint(
            ["role_id"],
            ["user_role.id"],
            name=op.f("fk_user_role_id"),
        ),
    )


def downgrade() -> None:
    op.drop_table("role_group")
