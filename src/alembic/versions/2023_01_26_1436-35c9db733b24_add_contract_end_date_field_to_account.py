"""Add contract_end_date field to Account

Revision ID: 35c9db733b24
Revises: ************
Create Date: 2023-01-26 14:36:55.665349

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "35c9db733b24"
down_revision = "************"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "account",
        sa.Column(
            "contract_end_date",
            sa.Date(),
            server_default=sa.text("'2023-12-31'"),
            nullable=False,
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("account", "contract_end_date")
    # ### end Alembic commands ###
