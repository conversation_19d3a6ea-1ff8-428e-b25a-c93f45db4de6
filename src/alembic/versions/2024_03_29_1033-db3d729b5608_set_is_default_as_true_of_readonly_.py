"""set is_default as true of ReadOnly roles (revert)

Revision ID: db3d729b5608
Revises: ccf31db298dd
Create Date: 2024-03-29 10:33:43.479666

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "db3d729b5608"
down_revision = "ccf31db298dd"
branch_labels = None
depends_on = None

# This migration is written to revert the changes made in ccf31db298dd
# As per discussion with product


def upgrade() -> None:
    # Update existing rows to set is_default = true
    op.execute(
        "UPDATE user_role SET is_default = true "
        "WHERE role IN ('Distributor_ReadOnly', 'Client_ReadOnly');"
    )


def downgrade() -> None:
    # Update existing rows to set is_default = false
    op.execute(
        "UPDATE user_role SET is_default = false "
        "WHERE role IN ('Distributor_ReadOnly', 'Client_ReadOnly');"
    )
