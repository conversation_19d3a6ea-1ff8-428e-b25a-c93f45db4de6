"""keys added in search_info

Revision ID: 5669731cad9e
Revises: 83236848b37a
Create Date: 2024-07-18 09:44:56.481178

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "5669731cad9e"
down_revision = "83236848b37a"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
            CREATE OR REPLACE FUNCTION search_info(
            search VARCHAR
        ) RETURNS TABLE (
            rules_uuid_param uuid,
            simusagelimit bigint,
            status boolean,
            unit unit,
            imsi character varying,
            iccid character varying,
            msisdn character varying,
            sim_status sim_status,
            account_id integer,
            account_name character varying,
            account_logo_key character varying
        )
        LANGUAGE 'plpgsql'
        COST 100
        VOLATILE
        PARALLEL UNSAFE
        ROWS 1000
        AS $BODY$
        BEGIN
            RETURN QUERY
            SELECT
                r.uuid AS rules_uuid_param,
                r.data_volume AS simusagelimit,
                r.status as status,
                r.unit,
                s.imsi,
                s.iccid,
                s.msisdn,
                s.sim_status,
                ac.id AS account_id,
                ac.name AS account_name,
                ac.logo_key AS account_logo_key
            FROM
                sim_card s
            JOIN
                allocation a ON s.allocation_id = a.id
            JOIN
                account ac ON a.account_id = ac.id
            LEFT JOIN
                rules r ON ac.id = r.account_id
            WHERE
                s.imsi = search OR s.iccid = search OR s.msisdn = search;
        END;
        $BODY$;

    """
    )


def downgrade() -> None:
    op.execute(
        """
            DROP FUNCTION IF EXISTS search_info(search VARCHAR);
    """
    )
