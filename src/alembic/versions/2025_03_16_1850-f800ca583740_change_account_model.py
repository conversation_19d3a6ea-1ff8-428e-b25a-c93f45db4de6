"""change account model

Revision ID: f800ca583740
Revises: 00d84d47aded
Create Date: 2025-03-16 18:50:02.508337

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

from common.types import SimProfile

# revision identifiers, used by Alembic.
revision = "f800ca583740"
down_revision = "00d84d47aded"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create ENUM type if it doesn't exist
    sim_profile_enum = postgresql.ENUM(
        SimProfile, name="sim_profile", create_type=False
    )
    sim_profile_enum.create(op.get_bind(), checkfirst=True)

    # Add the column with the ENUM type
    op.add_column(
        "account",
        sa.Column(
            "sim_profile",
            sim_profile_enum,
            server_default=SimProfile.DATA_ONLY.name,
            nullable=False,
        ),
    )

    # Remove default constraint after migration
    op.alter_column("account", "sim_profile", server_default=None)

    conn = op.get_bind()

    # Get the account ID for "BT Group Severn Trent - 3077240"
    result = conn.execute(
        sa.text("SELECT id FROM account WHERE name = :name"),
        {"name": "BT Group Severn Trent - 3077240"},
    )
    account_id = result.scalar()

    if account_id:
        # Update the required_rate_plan_change value to false
        conn.execute(
            sa.text(
                """
                UPDATE account
                SET sim_profile = CAST(:VOICE_SMS_DATA AS sim_profile)
                WHERE id = :account_id
                """
            ),
            {
                "account_id": account_id,
                "VOICE_SMS_DATA": SimProfile.VOICE_SMS_DATA.name,
            },
        )


def downgrade() -> None:
    # Drop the column
    op.drop_column("account", "sim_profile")
