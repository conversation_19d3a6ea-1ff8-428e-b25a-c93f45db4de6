"""add created_by column in allocation

Revision ID: 02d650933705
Revises: 148c36af70c6
Create Date: 2024-04-03 19:55:55.734939

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "02d650933705"
down_revision = "148c36af70c6"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "allocation", sa.Column("created_by", sa.String(length=128), nullable=True)
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("allocation", "created_by")
    # ### end Alembic commands ###
