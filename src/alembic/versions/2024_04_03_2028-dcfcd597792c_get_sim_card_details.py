"""get_sim_info_function

Revision ID: dcfcd597792c
Revises: 02d650933705
Create Date: 2024-04-03 20:28:45.049126

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "dcfcd597792c"
down_revision = "02d650933705"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
            CREATE OR REPLACE FUNCTION get_sim_card_details(imsi_param VARCHAR)
                RETURNS TABLE (
                    account_id INT,
                    account_name VARCHAR,
                    account_logo_key VARCHAR,
                    imsi VARCHAR,
                    iccid VARCHAR,
                    msisdn VARCHAR,
                    sim_status sim_status
                )
                AS $$
                BEGIN
                    RETURN QUERY
                    SELECT
                        acc.id,
                        acc.name,
                        acc.logo_key,
                        sc.imsi,
                        sc.iccid,
                        sc.msisdn,
                        sc.sim_status
                    FROM sim_card AS sc
                        INNER JOIN allocation AS al ON al.id = sc.allocation_id
                        INNER JOIN account AS acc ON acc.id = al.account_id
                    WHERE (sc.imsi = imsi_param OR imsi_param = '')
                        OR (sc.msisdn = imsi_param OR imsi_param = '')
                        OR (sc.iccid = imsi_param OR imsi_param = '')

                    LIMIT 1;
                END;
            $$ LANGUAGE plpgsql;
"""
    )


def downgrade() -> None:
    op.execute(
        """
            DROP FUNCTION IF EXISTS get_sim_card_details(imsi_param VARCHAR);
        """
    )
