"""add manx voice sms data update model

Revision ID: 3dc820681d03
Revises: 177eeff1e1ed
Create Date: 2023-07-10 17:40:35.765223

"""
import pandas as pd  # type: ignore
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "3dc820681d03"
down_revision = "177eeff1e1ed"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "sim_card_voice_sms_msisdn",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("iccid", sa.String(length=20), nullable=False),
        sa.Column("imsi", sa.String(length=15), nullable=False),
        sa.Column("msisdn", sa.String(length=15), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_sim_card_voice_sms_msisdn")),
    )

    metadata = sa.MetaData(bind=op.get_bind())
    table = sa.Table(
        "sim_card_voice_sms_msisdn",
        metadata,
        sa.Column("iccid", sa.String(length=20)),
        sa.Column("imsi", sa.String(length=15)),
        sa.Column("msisdn", sa.String(length=15)),
    )
    file_path = "alembic/data/MSISDN_update.xlsx"
    df = pd.read_excel(file_path)

    if not df.empty and len(df.columns) > 0:
        data = df.to_dict(orient="records")
        conn = op.get_bind()
        conn.execute(table.insert().values(data))

        op.execute(
            """UPDATE sim_card
            SET msisdn = st.msisdn
            FROM sim_card_voice_sms_msisdn AS st
            WHERE sim_card.imsi = st.imsi AND sim_card.iccid = st.iccid;"""
        )


def downgrade() -> None:
    op.drop_table("sim_card_voice_sms_msisdn")
