"""create allocation details table

Revision ID: 54ba29f7f2e0
Revises: 7748bea360bd
Create Date: 2024-01-02 15:25:43.553593

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "54ba29f7f2e0"
down_revision = "7748bea360bd"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "allocation_details",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("file_name", sa.String(), nullable=True),
        sa.Column("total_sim", sa.Integer(), nullable=False),
        sa.Column("error_sim", sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_allocation_details")),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("allocation_details")
    # ### end Alembic commands ###
