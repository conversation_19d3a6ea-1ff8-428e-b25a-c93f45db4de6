"""add voice unit types

Revision ID: 620c6126db4e
Revises: b1730a12d6c5
Create Date: 2025-05-30 12:10:34.408981

"""
import sqlalchemy as sa
from alembic import op

from common.types import Unit

# revision identifiers, used by Alembic.
revision = "620c6126db4e"
down_revision = "b1730a12d6c5"
branch_labels = None
depends_on = None


def upgrade() -> None:

    conn = op.get_bind()
    query = sa.text(
        """
        DO $$
        BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM pg_enum
                WHERE enumtypid = 'unit'::regtype
                AND enumlabel = :percentage_value
            ) THEN
                ALTER TYPE unit ADD VALUE :percentage_value;
            END IF;
        END $$;
    """
    )

    conn.execute(query, {"percentage_value": Unit.Mins_MO.value})
    conn.execute(query, {"percentage_value": Unit.Mins_MT.value})
    conn.execute(query, {"percentage_value": Unit.Mins_MO_MT.value})


def downgrade() -> None:
    pass
