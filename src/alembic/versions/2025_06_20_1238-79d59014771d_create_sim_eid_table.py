"""create_sim_eid_table

Revision ID: 79d59014771d
Revises: 56d27a8069a4
Create Date: 2025-06-10 12:38:35.729684

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "79d59014771d"
down_revision = "56d27a8069a4"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "sim_eid",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("eid", sa.String(length=32), nullable=False),
        sa.Column("sim_card_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["sim_card_id"], ["sim_card.id"], name=op.f("fk_sim_card_id")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_sim_eid")),
    )
    op.create_index("ix_sim_eid", "sim_eid", ["eid"])


def downgrade() -> None:
    op.drop_table("sim_eid")
