"""add sim_status column in subscription_sim table

Revision ID: 09a33a7b939c
Revises: 195704919f6c
Create Date: 2024-01-24 15:51:00.392680

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

from sim.domain.model import SimStatus

# revision identifiers, used by Alembic.
revision = "09a33a7b939c"
down_revision = "195704919f6c"
branch_labels = None
depends_on = None


def upgrade() -> None:
    sim_status = postgresql.ENUM(SimStatus, name="sim_status", create_type=False)
    op.add_column(
        "subscription_sim",
        sa.Column("sim_status", sim_status, nullable=True),
    )


def downgrade() -> None:
    op.drop_column("subscription_sim", "sim_status")
