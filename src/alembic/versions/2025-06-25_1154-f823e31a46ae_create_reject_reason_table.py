"""create_reject_reason_table

Revision ID: f823e31a46ae
Revises: 74cb8db400eb
Create Date: 2025-06-25 11:54:35.645625

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects.postgresql import UUID

# revision identifiers, used by Alembic.
revision = "f823e31a46ae"
down_revision = "74cb8db400eb"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "reject_reason",
        sa.Column("id", sa.Integer, primary_key=True, autoincrement=True),
        sa.Column("uuid", UUID(as_uuid=True), nullable=False),
        sa.Column("comment", sa.Text(), nullable=True),
        sa.Column("order_id", UUID, sa.ForeignKey("orders.orders.uuid")),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
        schema="orders",
    )


def downgrade() -> None:
    op.drop_table("reject_reason", schema="orders")
