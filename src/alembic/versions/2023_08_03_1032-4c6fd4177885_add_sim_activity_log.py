"""add_sim_activity_log

Revision ID: 4c6fd4177885
Revises: 653baffead01
Create Date: 2023-08-03 10:32:07.645413

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

from sim.domain.model import SimStatus

# revision identifiers, used by Alembic.
revision = "4c6fd4177885"
down_revision = "653baffead01"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # need to drop sim_auidt_log and few column from provder log also.
    sim_status = postgresql.ENUM(SimStatus, name="sim_status", create_type=False)
    op.create_table(
        "sim_activity_log",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False, start=1, increment=1),
            nullable=False,
        ),
        sa.Column("uuid", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("imsi", sa.String(length=15), nullable=False),
        sa.Column("iccid", sa.String(length=20), nullable=False),
        sa.Column("msisdn", sa.String(length=15), nullable=False),
        sa.Column("request_type", sa.String(), nullable=False),
        sa.Column("prior_value", sim_status, nullable=False),
        sa.Column("new_value", sim_status, nullable=False),
        sa.Column("client_ip", sa.String(), nullable=True),
        sa.Column(
            "created_at",
            sa.DateTime(),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("created_by", sa.String(length=128), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
    )


def downgrade() -> None:
    op.drop_table("sim_activity_log")
