"""monthly_recon_func_sms_value_fix

Revision ID: a342aabf4c62
Revises: a17cf477c9e6
Create Date: 2024-02-20 12:52:24.282944

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "a342aabf4c62"
down_revision = "a17cf477c9e6"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
            DROP FUNCTION IF EXISTS public.monthly_recon(date, date);
    """
    )

    op.execute(
        """
            CREATE OR REPLACE FUNCTION public.monthly_recon(
                start_date_param date,
                end_date_param date)
                RETURNS TABLE(
                    service text,
                    mu_imsi character varying(15),
                    cdr_imsi character varying(15),
                    mu_usage bigint,
                    cdr_usage bigint,
                    mu_cdr_variance bigint)
                LANGUAGE 'plpgsql'
                COST 100
                VOLATILE PARALLEL UNSAFE
                ROWS 1000

            AS $BODY$
            BEGIN

                FOR service, mu_imsi, cdr_imsi, mu_usage, cdr_usage, mu_cdr_variance IN

                WITH cdr_sms_agg AS (
                        SELECT COUNT(cs.id) AS volume, cd.imsi
                        FROM cdr_sms AS cs
                        INNER JOIN cdr AS cd ON cs.cdr_uuid = cd.uuid
                        WHERE cs.date_sent BETWEEN start_date_param AND end_date_param
                        GROUP BY cd.imsi
                    )

                    SELECT 'DATA' AS service,
                    mu.imsi AS mu_imsi,
                    cdr.imsi AS cdr_imsi,
                    COALESCE(SUM(mu.volume), 0) AS mu_usage,
                    COALESCE(cdr.usage, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(mu.volume),0) - COALESCE(cdr.usage,0))
                    AS mu_cdr_variance
                    FROM monthly_usage_records AS mu
                    LEFT JOIN cdr_data_aggregate AS cdr ON mu.imsi = cdr.imsi
                    AND cdr.month = start_date_param
                    WHERE mu.month = start_date_param
                    AND mu.service = 'DATA'
                    GROUP BY mu.imsi,cdr.imsi, cdr.usage

                    UNION

                    SELECT 'DATA' AS service,mu.imsi AS mu_imsi, cdr.imsi AS cdr_imsi,
                    COALESCE(SUM(mu.volume), 0) AS mu_usage,
                    COALESCE(cdr.usage, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(mu.volume),0) - COALESCE(cdr.usage,0))
                    AS mu_cdr_variance
                    FROM cdr_data_aggregate AS cdr
                    LEFT JOIN monthly_usage_records AS mu ON mu.imsi = cdr.imsi
                    AND mu.month = start_date_param
                    AND mu.service = 'DATA'
                    WHERE cdr.month = start_date_param
                    GROUP BY mu.imsi,cdr.imsi, cdr.usage

                    UNION

                    SELECT 'VOICE' AS service,mu.imsi AS mu_imsi,cdr.imsi AS cdr_imsi,
                    COALESCE(SUM(mu.volume),0) AS mu_usage,
                    COALESCE(cdr.voice_usage, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(mu.volume),0) - COALESCE(cdr.voice_usage,0))
                    AS mu_cdr_variance
                    FROM monthly_usage_records AS mu
                    LEFT JOIN cdr_voice_aggregate AS cdr ON mu.imsi=cdr.imsi
                    AND cdr.month=start_date_param
                    WHERE mu.month=start_date_param
                    AND (mu.service='VOICE_MO' OR mu.service='VOICE_MT')
                    GROUP BY mu.imsi,cdr.imsi, cdr.voice_usage

                    UNION

                    SELECT 'VOICE' AS service,mu.imsi AS mu_imsi,cdr.imsi AS cdr_imsi,
                    COALESCE(SUM(mu.volume),0) AS mu_usage,
                    COALESCE(cdr.voice_usage, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(mu.volume),0) - COALESCE(cdr.voice_usage,0))
                    AS mu_cdr_variance
                    FROM cdr_voice_aggregate AS cdr
                    LEFT JOIN monthly_usage_records AS mu ON mu.imsi=cdr.imsi
                    AND mu.month=start_date_param
                    AND (mu.service='VOICE_MO' OR mu.service='VOICE_MT')
                    WHERE cdr.month=start_date_param
                    GROUP BY mu.imsi,cdr.imsi, cdr.voice_usage

                    UNION

                    SELECT 'SMS' AS service,mu.imsi AS mu_imsi,cds.imsi AS cdr_imsi,
                    COALESCE(SUM(mu.volume),0) AS mu_usage,
                    COALESCE(cds.volume, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(mu.volume),0) - COALESCE(cds.volume,0))
                    AS mu_cdr_variance
                    FROM monthly_usage_records AS mu
                    LEFT JOIN cdr_sms_agg AS cds ON mu.imsi=cds.imsi
                    WHERE mu.month=start_date_param
                    AND (mu.service='SMS_MO' OR mu.service='SMS_MT')
                    GROUP BY mu.imsi,cds.imsi, cds.volume

                    UNION

                    SELECT 'SMS' AS service,mu.imsi AS mu_imsi,cds.imsi AS cdr_imsi,
                    COALESCE(SUM(mu.volume),0) AS mu_usage,
                    COALESCE(cds.volume, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(mu.volume),0) - COALESCE(cds.volume,0))
                    AS mu_cdr_variance
                    FROM cdr_sms_agg AS cds
                    LEFT JOIN monthly_usage_records AS mu
                    ON mu.imsi=cds.imsi and mu.month=start_date_param
                    AND (mu.service='SMS_MO' OR mu.service='SMS_MT')
                    GROUP BY mu.imsi,cds.imsi, cds.volume
                LOOP
                    -- Return the fetched values
                    IF mu_cdr_variance = 0  THEN
                        IF (cdr_imsi IS NULL OR mu_imsi IS NULL)
                        AND mu_cdr_variance!=0 THEN
                            RETURN NEXT;
                        END IF;
                    ELSEIF mu_cdr_variance != 0  THEN
                        RETURN NEXT;
                    END IF;
                END LOOP;

                RETURN;
            END;
            $BODY$;
        """
    )


def downgrade() -> None:
    op.execute(
        """
            DROP FUNCTION IF EXISTS public.monthly_recon(date, date);
    """
    )

    op.execute(
        """
            CREATE OR REPLACE FUNCTION public.monthly_recon(
                start_date_param date,
                end_date_param date)
                RETURNS TABLE(
                    service text,
                    mu_imsi character varying(15),
                    cdr_imsi character varying(15),
                    mu_usage bigint,
                    cdr_usage bigint,
                    mu_cdr_variance bigint)
                LANGUAGE 'plpgsql'
                COST 100
                VOLATILE PARALLEL UNSAFE
                ROWS 1000

            AS $BODY$
            BEGIN

                FOR service, mu_imsi, cdr_imsi, mu_usage, cdr_usage, mu_cdr_variance IN

                WITH cdr_sms_agg AS (
                        SELECT COUNT(cs.id) AS volume, cd.imsi
                        FROM cdr_sms AS cs
                        INNER JOIN cdr AS cd ON cs.cdr_uuid = cd.uuid
                        WHERE cs.date_sent BETWEEN start_date_param AND end_date_param
                        GROUP BY cd.imsi
                    )

                    SELECT 'DATA' AS service,
                    mu.imsi AS mu_imsi,
                    cdr.imsi AS cdr_imsi,
                    COALESCE(SUM(mu.volume), 0) AS mu_usage,
                    COALESCE(cdr.usage, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(mu.volume),0) - COALESCE(cdr.usage,0))
                    AS mu_cdr_variance
                    FROM monthly_usage_records AS mu
                    LEFT JOIN cdr_data_aggregate AS cdr ON mu.imsi = cdr.imsi
                    AND cdr.month = start_date_param
                    WHERE mu.month = start_date_param
                    AND mu.service = 'DATA'
                    GROUP BY mu.imsi,cdr.imsi, cdr.usage

                    UNION

                    SELECT 'DATA' AS service,mu.imsi AS mu_imsi, cdr.imsi AS cdr_imsi,
                    COALESCE(SUM(mu.volume), 0) AS mu_usage,
                    COALESCE(cdr.usage, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(mu.volume),0) - COALESCE(cdr.usage,0))
                    AS mu_cdr_variance
                    FROM cdr_data_aggregate AS cdr
                    LEFT JOIN monthly_usage_records AS mu ON mu.imsi = cdr.imsi
                    AND mu.month = start_date_param
                    AND mu.service = 'DATA'
                    WHERE cdr.month = start_date_param
                    GROUP BY mu.imsi,cdr.imsi, cdr.usage

                    UNION

                    SELECT 'VOICE' AS service,mu.imsi AS mu_imsi,cdr.imsi AS cdr_imsi,
                    COALESCE(SUM(mu.volume),0) AS mu_usage,
                    COALESCE(cdr.voice_usage, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(mu.volume),0) - COALESCE(cdr.voice_usage,0))
                    AS mu_cdr_variance
                    FROM monthly_usage_records AS mu
                    LEFT JOIN cdr_voice_aggregate AS cdr ON mu.imsi=cdr.imsi
                    AND cdr.month=start_date_param
                    WHERE mu.month=start_date_param
                    AND (mu.service='VOICE_MO' OR mu.service='VOICE_MT')
                    GROUP BY mu.imsi,cdr.imsi, cdr.voice_usage

                    UNION

                    SELECT 'VOICE' AS service,mu.imsi AS mu_imsi,cdr.imsi AS cdr_imsi,
                    COALESCE(SUM(mu.volume),0) AS mu_usage,
                    COALESCE(cdr.voice_usage, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(mu.volume),0) - COALESCE(cdr.voice_usage,0))
                    AS mu_cdr_variance
                    FROM cdr_voice_aggregate AS cdr
                    LEFT JOIN monthly_usage_records AS mu ON mu.imsi=cdr.imsi
                    AND mu.month=start_date_param
                    AND (mu.service='VOICE_MO' OR mu.service='VOICE_MT')
                    WHERE cdr.month=start_date_param
                    GROUP BY mu.imsi,cdr.imsi, cdr.voice_usage

                    UNION

                    SELECT 'SMS' AS service,mu.imsi AS mu_imsi,cds.imsi AS cdr_imsi,
                    COALESCE(SUM(mu.volume),0) AS mu_usage,
                    COALESCE(cds.volume, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(mu.volume),0) - COALESCE(cds.volume,0))
                    AS mu_cdr_variance
                    FROM monthly_usage_records AS mu
                    LEFT JOIN cdr_sms_agg AS cds ON mu.imsi=cds.imsi
                    WHERE mu.month=start_date_param
                    AND (mu.service='SMS_MO' OR mu.service='SMS_MT')
                    GROUP BY mu.imsi,cds.imsi, cds.volume

                    UNION

                    SELECT 'SMS' AS service,mu.imsi AS mu_imsi,cds.imsi AS cdr_imsi,
                    COALESCE(SUM(mu.volume),0) AS mu_usage,
                    COALESCE(cds.volume, 0)
                    AS cdr_usage,
                    (COALESCE(SUM(mu.volume),0) - COALESCE(cds.volume,0))
                    AS mu_cdr_variance
                    FROM cdr_sms_agg AS cds
                    LEFT JOIN monthly_usage_records AS mu
                    ON mu.imsi=cds.imsi and mu.month=start_date_param
                    AND (mu.service='SMS_MO' OR mu.service='SMS_MT')
                    GROUP BY mu.imsi,cds.imsi, cds.volume
                LOOP
                    -- Return the fetched values
                    IF mu_cdr_variance = 0  THEN
                        IF cdr_imsi IS NULL OR mu_imsi IS NULL THEN
                            RETURN NEXT;
                        END IF;
                    ELSEIF mu_cdr_variance != 0  THEN
                        RETURN NEXT;
                    END IF;
                END LOOP;

                RETURN;
            END;
            $BODY$;
        """
    )
