"""modify rate_plan_by_accounts function with view

Revision ID: 55b38e141c29
Revises: 963dd9a973ad
Create Date: 2025-02-05 01:10:57.585985

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "55b38e141c29"
down_revision = "963dd9a973ad"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS rate_plan_by_accounts(varchar, integer[], text,
        text, integer, integer);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION rate_plan_by_accounts(
            search_term varchar DEFAULT NULL,
            account_ids_param integer[] DEFAULT NULL,
            order_by_column TEXT DEFAULT 'id',
            order_direction TEXT DEFAULT 'DESC',
            page_offset INT DEFAULT 0,
            page_size INT DEFAULT 50,
            rate_model_codes varchar[] DEFAULT NULL,
            rate_plan_service service DEFAULT 'DATA'::service
        )
        RETURNS TABLE (
            account_id integer,
            account_name character varying,
            logo_key character varying,
            id integer,
            name character varying,
            access_fee numeric,
            currency character,
            rate_model_id integer,
            sim_limit integer,
            is_default bool,
            allowance_used numeric
        )
        LANGUAGE 'plpgsql'
        COST 100
        VOLATILE
        PARALLEL UNSAFE
        ROWS 1000
        AS $BODY$
        DECLARE
            sql_query TEXT;
            final_order_by_column TEXT := COALESCE(order_by_column, 'account_id');
            final_order_direction TEXT := UPPER(COALESCE(order_direction, 'DESC'));
        BEGIN

            sql_query := $$
                WITH PaginatedAccounts AS (
                    SELECT
                        a.id,
                        a.name,
                        a.logo_key,
                        a.currency
                    FROM account AS a
                    JOIN rate_plan ON rate_plan.account_id = a.id
                    WHERE (
                        $1 IS NULL OR
                        a.name ILIKE '%' || $1 || '%' OR
                        rate_plan.name ILIKE '%' || $1 || '%'
                    )
                    AND (
                        $2 IS NULL OR
                        a.id = ANY($2)
                    )
                    group by a.id,
                        a.name,
                        a.logo_key,
                        a.currency
                    LIMIT $4 OFFSET $5
                )
                SELECT
                    pa.id AS account_id,
                    pa.name AS account_name,
                    pa.logo_key,
                    grpd.id,
                    grpd.name,
                    grpd.access_fee,
                    pa.currency,
                    CASE
                        WHEN grpd.rate_model_code != ALL($6) THEN grpd.rate_model_id
                        ELSE NULL
                    END as rate_model_id,
                    grpd.sim_limit,
                    grpd.is_default,
                    COALESCE(allowance.allowance_used, 0) AS allowance_used
                FROM
                    get_rate_plans_detail_view grpd
                INNER JOIN
                    PaginatedAccounts pa ON grpd.account_id = pa.id
                LEFT JOIN
                    get_allowance_usage($2, $6, $7) AS allowance
                    ON allowance.rate_plan_id = grpd.id
                WHERE (
                    $1 IS NULL OR
                    pa.name ILIKE '%' || $1 || '%' OR
                    grpd.name ILIKE '%' || $1 || '%'
                )
                    AND (
                        $2 IS NULL OR
                        pa.id = ANY($2)
                    )
                    AND(
                        $7 IS NULL OR grpd.service = $7
                    )
                GROUP BY
                    pa.id, grpd.id, allowance.allowance_used, grpd.rate_model_id,
                    pa.name, pa.logo_key, pa.currency, grpd.rate_model_code, grpd.name,
                    grpd.access_fee,
                    grpd.sim_limit,
                    grpd.is_default
            $$;

            sql_query := sql_query || ' ORDER BY ' ||
            quote_ident(final_order_by_column) || ' ' || final_order_direction ||
            ', is_default DESC';

            -- Execute the dynamic query and return the results
            RETURN QUERY EXECUTE sql_query USING search_term, account_ids_param,
            order_by_column, page_size, page_offset, rate_model_codes,
            rate_plan_service;
        END;
        $BODY$;


        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS rate_plan_by_accounts(varchar, integer[], text,
        text, integer, integer, varchar[], service);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION rate_plan_by_accounts(
            search_term varchar DEFAULT NULL,
            account_ids_param integer[] DEFAULT NULL,
            order_by_column TEXT DEFAULT 'id',
            order_direction TEXT DEFAULT 'DESC',
            page_offset INT DEFAULT 0,
            page_size INT DEFAULT 50
        )
        RETURNS TABLE (
            account_id integer,
            account_name character varying,
            logo_key character varying,
            id integer,
            name character varying,
            access_fee numeric,
            currency character,
            rate_model_id integer,
            sim_limit integer,
            is_default bool,
            allowance_used numeric
        )
        LANGUAGE 'plpgsql'
        COST 100
        VOLATILE
        PARALLEL UNSAFE
        ROWS 1000
        AS $BODY$
        DECLARE
            sql_query TEXT;
            final_order_by_column TEXT := COALESCE(order_by_column, 'account_id');
            final_order_direction TEXT := UPPER(COALESCE(order_direction, 'DESC'));
        BEGIN

            sql_query := $$
                WITH PaginatedAccounts AS (
                    SELECT
                        a.id,
                        a.name,
                        a.logo_key,
                        a.currency
                    FROM account AS a
                    JOIN rate_plan ON rate_plan.account_id = a.id
                    WHERE (
                        $1 IS NULL OR
                        a.name ILIKE '%' || $1 || '%' OR
                        rate_plan.name ILIKE '%' || $1 || '%'
                    )
                    AND (
                        $2 IS NULL OR
                        a.id = ANY($2)
                    )
                    group by a.id,
                        a.name,
                        a.logo_key,
                        a.currency
                    LIMIT $4 OFFSET $5
                ),
                RankedRatePlans AS (
                    SELECT
                        pa.id AS account_id,
                        pa.name AS account_name,
                        pa.logo_key,
                        r.id,
                        r.name,
                        r.access_fee,
                        r.sim_limit,
                        pa.currency,
                        r.is_default,
                        CASE
                            WHEN rg.rate_model_id IN (3, 4) THEN rg.rate_model_id
                            ELSE NULL
                        END as rate_model_id,
                        COALESCE(allowance.allowance_used, 0) AS allowance_used
                    FROM
                        rate_plan r
                    INNER JOIN
                        PaginatedAccounts pa ON r.account_id = pa.id
                    INNER JOIN
                        rate_group AS rg
                        ON rg.rate_plan_id = r.id
                    INNER JOIN
                        rate_group_service AS rgs
                        ON rgs.rate_group_id = rg.id
                        AND rgs.service = 'DATA'
                    LEFT JOIN
                        get_allowance_usage($2) AS allowance
                        ON allowance.rate_plan_id = r.id
                    WHERE (
                        $1 IS NULL OR
                        pa.name ILIKE '%' || $1 || '%' OR
                        r.name ILIKE '%' || $1 || '%'
                    )
                        AND (
                            $2 IS NULL OR
                            pa.id = ANY($2)
                        )
                    GROUP BY
                        pa.id, r.id, allowance.allowance_used, rg.rate_model_id,
                        pa.name, pa.logo_key, pa.currency
                    ORDER BY r.is_default DESC
                )
                SELECT
                    rp.account_id,
                    rp.account_name,
                    rp.logo_key,
                    rp.id,
                    rp.name,
                    rp.access_fee,
                    rp.currency,
                    rp.rate_model_id,
                    rp.sim_limit,
                    rp.is_default,
                    COALESCE(rp.allowance_used, 0) AS allowance_used
                FROM
                    RankedRatePlans rp
            $$;

            sql_query := sql_query || ' ORDER BY ' ||
            quote_ident(final_order_by_column) || ' ' || final_order_direction ||
            ', is_default DESC';

            -- Execute the dynamic query and return the results
            RETURN QUERY EXECUTE sql_query USING search_term, account_ids_param,
            order_by_column, page_size, page_offset;
        END;
        $BODY$;



        """
    )
