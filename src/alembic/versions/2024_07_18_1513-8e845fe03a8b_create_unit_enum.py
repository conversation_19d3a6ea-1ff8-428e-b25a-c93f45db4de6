"""create_unit_enum

Revision ID: 8e845fe03a8b
Revises: 7592d7587d5f
Create Date: 2024-07-18 15:13:41.955398

"""
import sqlalchemy as sa
from alembic import op

from common.types import Unit

# revision identifiers, used by Alembic.
revision = "8e845fe03a8b"
down_revision = "7592d7587d5f"
branch_labels = None
depends_on = None


def upgrade() -> None:
    unit = sa.Enum(Unit, name="unit")
    unit.create(op.get_bind(), checkfirst=True)


def downgrade() -> None:
    unit = sa.Enum(Unit, name="unit", checkfirst=True)
    unit.drop(op.get_bind())
