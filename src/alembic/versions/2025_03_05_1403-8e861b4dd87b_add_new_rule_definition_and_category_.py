"""add new rule definition and category for monthly pool rule

Revision ID: 8e861b4dd87b
Revises: 198c1811405f
Create Date: 2025-02-10 14:03:43.871242

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "8e861b4dd87b"
down_revision = "198c1811405f"
branch_labels = None
depends_on = None


def upgrade() -> None:
    conn = op.get_bind()

    # Inserting new 'Monthly Pooled Data Usage' category as per the below rule_type_id
    rule_type_id = conn.execute(
        "SELECT id FROM rule_type WHERE rule_type = 'Usage Monitoring' "
    ).scalar_one()

    query = sa.text(
        """
        INSERT INTO rule_category(rule_type_id, category)
        VALUES (:rule_type_id, 'Monthly Pooled Data Usage')
    """
    )
    conn.execute(query, {"rule_type_id": rule_type_id})

    # Inserting new 'Monthly billable data usage threshold' definition as per
    # the below rule_category_id
    rule_category_id = conn.execute(
        "SELECT id FROM rule_category WHERE category = 'Monthly Pooled Data Usage' "
    ).scalar_one()

    query = sa.text(
        """
            INSERT INTO rule_definition (rule_category_id, definition)
            VALUES (:rule_category_id, 'Monthly billable data usage threshold')
            """
    )
    conn.execute(query, {"rule_category_id": rule_category_id})


def downgrade() -> None:
    # delete newly added definition
    op.execute(
        """
        DELETE
            FROM
        rule_definition
            WHERE definition = 'Monthly billable data usage threshold'
    """
    )
    # delete newly added category
    op.execute(
        """
        DELETE
            FROM
        rule_category
            WHERE category = 'Monthly Pooled Data Usage'
    """
    )
