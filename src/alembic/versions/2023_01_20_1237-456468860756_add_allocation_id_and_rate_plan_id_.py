"""Add allocation_id and rate_plan_id fields to SIMCard model

Revision ID: 456468860756
Revises: 06e0e8277de0
Create Date: 2023-01-20 12:37:01.150842

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "456468860756"
down_revision = "06e0e8277de0"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("sim_card", sa.Column("allocation_id", sa.Integer(), nullable=True))
    op.add_column("sim_card", sa.Column("rate_plan_id", sa.Integer(), nullable=True))
    op.create_foreign_key(
        op.f("fk_sim_card_rate_plan_id_rate_plan"),
        "sim_card",
        "rate_plan",
        ["rate_plan_id"],
        ["id"],
    )
    op.create_foreign_key(
        op.f("fk_sim_card_allocation_id_allocation"),
        "sim_card",
        "allocation",
        ["allocation_id"],
        ["id"],
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f("fk_sim_card_allocation_id_allocation"), "sim_card", type_="foreignkey"
    )
    op.drop_constraint(
        op.f("fk_sim_card_rate_plan_id_rate_plan"), "sim_card", type_="foreignkey"
    )
    op.drop_column("sim_card", "rate_plan_id")
    op.drop_column("sim_card", "allocation_id")
    # ### end Alembic commands ###
