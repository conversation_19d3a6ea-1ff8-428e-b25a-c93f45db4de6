"""create rules_action_mapping table

Revision ID: 412417afb60a
Revises: 0fe3043da336
Create Date: 2025-02-07 16:32:20.933346

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "412417afb60a"
down_revision = "0fe3043da336"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "rules_action_mapping",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("rules_uuid", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("source", sa.Integer(), nullable=True),
        sa.Column("target", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["rules_uuid"],
            ["rules.uuid"],
            name=op.f("fk_rules_action_mapping_uuid_rules"),
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_rules_action_mapping")),
    )


def downgrade() -> None:
    op.drop_table("rules_action_mapping")
