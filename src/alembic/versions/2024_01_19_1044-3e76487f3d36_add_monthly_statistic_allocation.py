"""add_monthly_statistic_allocation

Revision ID: 3e76487f3d36
Revises: 89fe42011328
Create Date: 2024-01-19 10:44:33.886473

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "3e76487f3d36"
down_revision = "89fe42011328"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """INSERT INTO sim_monthly_statistic
        (sim_card_id,month,sim_status,is_first_activation)
        SELECT sc.id, date_trunc('month', CURRENT_DATE)::date,sc.sim_status,TRUE
        FROM allocation AS al
        INNER JOIN sim_card AS sc ON al.imsi = sc.imsi
        WHERE TO_CHAR(al.created_at, 'YYYY-MM') = TO_CHAR(
            date_trunc('month', CURRENT_DATE), 'YYYY-MM')
        AND NOT EXISTS (SELECT sms.sim_card_id
        FROM sim_monthly_statistic AS sms
        WHERE month=date_trunc('month', CURRENT_DATE)::date AND sms.sim_card_id=sc.id);

        UPDATE sim_monthly_statistic SET is_first_activation=false
        where month=date_trunc('month', CURRENT_DATE)::date;

        UPDATE sim_monthly_statistic
        SET is_first_activation = true
        FROM sim_card AS sc
        INNER JOIN allocation AS al ON al.id = sc.allocation_id
        WHERE sim_monthly_statistic.sim_card_id = sc.id
        AND sim_monthly_statistic.MONTH = date_trunc('month', CURRENT_DATE)::date
        AND TO_CHAR(al.created_at, 'YYYY-MM') = TO_CHAR(date_trunc(
            'month', CURRENT_DATE), 'YYYY-MM');"""
    )

    op.execute(
        """
            CREATE OR REPLACE FUNCTION insert_monthly_statistic(imsi_array text[])
            RETURNS VOID AS $$
            BEGIN
                INSERT INTO sim_monthly_statistic
                (sim_card_id, month, sim_status, is_first_activation)
                SELECT
                    sc.id,
                    date_trunc('month', CURRENT_DATE)::date,
                    sc.sim_status,
                    true
                FROM
                    sim_card AS sc
                WHERE
                    sc.imsi = ANY(imsi_array);
            END;
            $$ LANGUAGE plpgsql;
        """
    )


def downgrade() -> None:
    op.execute(
        """
            DROP FUNCTION IF EXISTS insert_monthly_statistic(text[]);
        """
    )
