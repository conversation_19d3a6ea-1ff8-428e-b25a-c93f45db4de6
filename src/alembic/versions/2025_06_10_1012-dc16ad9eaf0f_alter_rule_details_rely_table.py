"""alter rule details rely table

Revision ID: dc16ad9eaf0f
Revises: 6a591dbe2ec8
Create Date: 2025-05-30 10:12:27.079778

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "dc16ad9eaf0f"
down_revision = "6a591dbe2ec8"
branch_labels = None
depends_on = None


def insert_rule_details(conn, definition_code, data):
    """Insert rule details for a given definition code."""
    result = conn.execute(
        sa.text("SELECT id FROM rule_definition WHERE rule_definition_code = :code"),
        {"code": definition_code},
    )
    definition_id = result.scalar()

    if definition_id:
        data["rule_definition_id"] = definition_id
        conn.execute(
            sa.text(
                """
                INSERT INTO rule_details_rely (
                    rule_definition_id, data_volume, data_unit, sms_unit, voice_unit,
                    threshold, data_percentage, view_deactivate_sim,
                    required_deactivate_sim, view_rate_plan_change,
                    required_rate_plan_change, add_any_rate_plan,
                    is_monthly_pool, view_email, view_sms, view_push,
                    voice_mo_percentage, sms_percentage, voice_momt_percentage,
                    voice_mt_percentage, data, voice_mo, voice_mt, sms
                ) VALUES (
                    :rule_definition_id, :data_volume, :data_unit, :sms_unit,
                    :voice_unit, :threshold, :data_percentage, :view_deactivate_sim,
                    :required_deactivate_sim, :view_rate_plan_change,
                    :required_rate_plan_change, :add_any_rate_plan,
                    :is_monthly_pool, :view_email, :view_sms, :view_push,
                    :voice_mo_percentage, :sms_percentage, :voice_momt_percentage,
                    :voice_mt_percentage, :data, :voice_mo, :voice_mt, :sms
                )
            """
            ),
            data,
        )


def upgrade() -> None:
    op.alter_column(
        "rule_details_rely", "percentage_unit", new_column_name="data_percentage"
    )
    op.add_column(
        "rule_details_rely",
        sa.Column(
            "voice_mo_percentage", sa.Boolean, nullable=False, server_default="false"
        ),
    )
    op.add_column(
        "rule_details_rely",
        sa.Column(
            "voice_mt_percentage", sa.Boolean, nullable=False, server_default="false"
        ),
    )
    op.add_column(
        "rule_details_rely",
        sa.Column(
            "voice_momt_percentage", sa.Boolean, nullable=False, server_default="false"
        ),
    )
    op.add_column(
        "rule_details_rely",
        sa.Column("sms_percentage", sa.Boolean, nullable=False, server_default="false"),
    )
    op.add_column(
        "rule_details_rely",
        sa.Column("data", sa.Boolean, nullable=False, server_default="false"),
    )
    op.add_column(
        "rule_details_rely",
        sa.Column("voice_mo", sa.Boolean, nullable=False, server_default="false"),
    )
    op.add_column(
        "rule_details_rely",
        sa.Column("voice_mt", sa.Boolean, nullable=False, server_default="false"),
    )
    op.add_column(
        "rule_details_rely",
        sa.Column("sms", sa.Boolean, nullable=False, server_default="false"),
    )
    op.add_column(
        "rule_details_rely",
        sa.Column("rule_definition_id", sa.Integer, nullable=True),
    )
    op.execute(
        """
        UPDATE rule_details_rely AS rdr
        SET rule_definition_id = rd.id
        FROM rule_definition AS rd
        WHERE rdr.rule_category_id = rd.rule_category_id and
        rd.rule_definition_code in ('DUESL', 'MBDUT');
    """
    )
    op.drop_column("rule_details_rely", "rule_category_id")
    conn = op.get_bind()

    insert_rule_details(
        conn,
        "MBVUT",
        {
            "data_volume": False,
            "data_unit": False,
            "sms_unit": False,
            "voice_unit": False,
            "threshold": True,
            "data_percentage": False,
            "view_deactivate_sim": True,
            "required_deactivate_sim": False,
            "view_rate_plan_change": True,
            "required_rate_plan_change": False,
            "add_any_rate_plan": False,
            "is_monthly_pool": True,
            "view_email": True,
            "view_sms": False,
            "view_push": False,
            "voice_momt_percentage": True,
            "voice_mo_percentage": False,
            "voice_mt_percentage": False,
            "sms_percentage": False,
            "data": False,
            "voice_mo": True,
            "voice_mt": True,
            "sms": False,
        },
    )

    insert_rule_details(
        conn,
        "MBVMOUT",
        {
            "data_volume": False,
            "data_unit": False,
            "sms_unit": False,
            "voice_unit": False,
            "threshold": True,
            "data_percentage": False,
            "view_deactivate_sim": True,
            "required_deactivate_sim": False,
            "view_rate_plan_change": True,
            "required_rate_plan_change": False,
            "add_any_rate_plan": False,
            "is_monthly_pool": True,
            "view_email": True,
            "view_sms": False,
            "view_push": False,
            "voice_mo_percentage": True,
            "voice_momt_percentage": False,
            "voice_mt_percentage": False,
            "sms_percentage": False,
            "data": False,
            "voice_mo": True,
            "voice_mt": False,
            "sms": False,
        },
    )

    insert_rule_details(
        conn,
        "MBVMTUT",
        {
            "data_volume": False,
            "data_unit": False,
            "sms_unit": False,
            "voice_unit": False,
            "threshold": True,
            "data_percentage": False,
            "view_deactivate_sim": True,
            "required_deactivate_sim": False,
            "view_rate_plan_change": True,
            "required_rate_plan_change": False,
            "add_any_rate_plan": False,
            "is_monthly_pool": True,
            "view_email": True,
            "view_sms": False,
            "view_push": False,
            "voice_mo_percentage": False,
            "voice_momt_percentage": False,
            "voice_mt_percentage": True,
            "sms_percentage": False,
            "data": False,
            "voice_mo": False,
            "voice_mt": True,
            "sms": False,
        },
    )

    insert_rule_details(
        conn,
        "MBSUT",
        {
            "data_volume": False,
            "data_unit": False,
            "sms_unit": False,
            "voice_unit": False,
            "threshold": True,
            "data_percentage": False,
            "view_deactivate_sim": True,
            "required_deactivate_sim": False,
            "view_rate_plan_change": True,
            "required_rate_plan_change": False,
            "add_any_rate_plan": False,
            "is_monthly_pool": True,
            "view_email": True,
            "view_sms": False,
            "view_push": False,
            "voice_mo_percentage": False,
            "voice_momt_percentage": False,
            "voice_mt_percentage": False,
            "sms_percentage": True,
            "data": False,
            "voice_mo": False,
            "voice_mt": False,
            "sms": True,
        },
    )
    op.alter_column("rule_details_rely", "rule_definition_id", nullable=False)
    op.create_foreign_key(
        "fk_rule_details_rely_rule_definition_id_rule_definition",
        "rule_details_rely",
        "rule_definition",
        ["rule_definition_id"],
        ["id"],
    )
    op.execute(
        """
        UPDATE rule_details_rely AS rdr
        SET data = true
        FROM rule_definition AS rd
        WHERE rdr.rule_definition_id = rd.id and
        rd.rule_definition_code = 'MBDUT';
    """
    )
    op.create_unique_constraint(
        op.f("uq_rule_details_rely_rule_definition_id"),
        "rule_details_rely",
        ["rule_definition_id"],
    )


def downgrade() -> None:
    conn = op.get_bind()
    op.add_column(
        "rule_details_rely",
        sa.Column("rule_category_id", sa.Integer, nullable=True),
    )

    conn.execute(
        """
        UPDATE rule_details_rely AS rdr
        SET rule_category_id = rc.id
        FROM rule_definition AS rd
        JOIN rule_category AS rc ON rd.rule_category_id = rc.id
        WHERE rdr.rule_definition_id = rd.id
          AND rd.rule_definition_code IN ('DUESL', 'MBDUT');
        """
    )

    conn.execute(
        """
        DELETE FROM rule_details_rely
        WHERE rule_definition_id IN (
            SELECT id FROM rule_definition
            WHERE rule_definition_code IN ('MBVUT', 'MBSUT', 'MBVMOUT', 'MBVMTUT')
        );
        """
    )

    op.drop_column("rule_details_rely", "rule_definition_id")
    op.drop_column("rule_details_rely", "sms_percentage")
    op.drop_column("rule_details_rely", "voice_mo_percentage")
    op.drop_column("rule_details_rely", "voice_momt_percentage")
    op.drop_column("rule_details_rely", "voice_mt_percentage")
    op.drop_column("rule_details_rely", "data")
    op.drop_column("rule_details_rely", "voice_mo")
    op.drop_column("rule_details_rely", "voice_mt")
    op.drop_column("rule_details_rely", "sms")

    op.alter_column("rule_details_rely", "rule_category_id", nullable=False)
    op.create_foreign_key(
        "fk_rule_details_rely_rule_category_id_rule_category",
        "rule_details_rely",
        "rule_category",
        ["rule_category_id"],
        ["id"],
    )
    op.alter_column(
        "rule_details_rely", "data_percentage", new_column_name="percentage_unit"
    )
