"""add default value to payment_terms

Revision ID: 4446b0d9e65f
Revises: cb9faa5c5eee
Create Date: 2023-05-03 12:35:04.374609

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "4446b0d9e65f"
down_revision = "cb9faa5c5eee"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        """UPDATE account SET payment_terms = '30' WHERE payment_terms IS NULL"""
    )
    op.alter_column(
        "account",
        "payment_terms",
        existing_type=sa.INTEGER(),
        server_default="30",
        nullable=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "account", "payment_terms", existing_type=sa.INTEGER(), nullable=True
    )
    # ### end Alembic commands ###
