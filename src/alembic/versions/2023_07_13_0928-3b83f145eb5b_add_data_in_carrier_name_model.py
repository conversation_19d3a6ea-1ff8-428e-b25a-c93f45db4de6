"""add data in carrier_name model

Revision ID: 3b83f145eb5b
Revises: 3dc820681d03
Create Date: 2023-07-13 09:28:22.883058

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "3b83f145eb5b"
down_revision = "3dc820681d03"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        INSERT INTO carrier_name (carrier, carrier_name)
        SELECT 'GBRMT', 'EE'
        WHERE NOT EXISTS (
            SELECT ID FROM carrier_name WHERE carrier = 'GBRMT'
        )
        """
    )


def downgrade() -> None:
    pass
