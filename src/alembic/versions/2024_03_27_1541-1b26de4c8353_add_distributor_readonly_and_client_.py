"""Add Distributor_ReadOnly and <PERSON><PERSON>_ReadOnly as default role

Revision ID: 1b26de4c8353
Revises: 5845f7a2c740
Create Date: 2024-03-27 15:41:40.564802

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "1b26de4c8353"
down_revision = "5845f7a2c740"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Insert multiple rows in a single statement
    op.execute(
        "INSERT INTO user_role (role, rolegroup, is_default, created_by) VALUES "
        "('Distributor_ReadOnly', 'MY_ORGANIZATION', true, 'System Admin'), "
        "('Client_ReadOnly', 'ACCOUNT', true, 'System Admin');"
    )


def downgrade() -> None:
    op.execute(
        "DELETE FROM user_role where role in "
        "('Distributor_ReadOnly', 'Client_ReadOnly');"
    )
