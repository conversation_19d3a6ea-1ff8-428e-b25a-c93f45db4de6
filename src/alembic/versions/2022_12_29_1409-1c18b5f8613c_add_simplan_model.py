"""Add SimPlan model

Revision ID: 1c18b5f8613c
Revises: 2ecb316a141a
Create Date: 2022-12-29 14:09:59.093471

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "1c18b5f8613c"
down_revision = "2ecb316a141a"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "sim_plan",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("imsi", sa.String(length=15), nullable=False),
        sa.Column("rate_plan_id", sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_sim_plan")),
        sa.UniqueConstraint("imsi", name=op.f("uq_sim_plan_imsi")),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("sim_plan")
    # ### end Alembic commands ###
