"""remove_constraints_cdr_reference_cdr_data_voice_aggregate

Revision ID: 004e6cd5c6ac
Revises: 0c44c6cee267
Create Date: 2025-01-01 14:30:43.492696

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "004e6cd5c6ac"
down_revision = "0c44c6cee267"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        DO $$
        BEGIN
            IF EXISTS (
                SELECT 1
                FROM information_schema.table_constraints
                WHERE constraint_type = 'FOREIGN KEY'
                AND table_name = 'cdr_references'
                AND constraint_name = 'fk_cdr_references_cdr_uuid'
            ) THEN
                ALTER TABLE cdr_references
                DROP CONSTRAINT fk_cdr_references_cdr_uuid;
            END IF;
        END $$;

        """
    )

    op.execute(
        """
        DO $$
        BEGIN
            IF EXISTS (
                SELECT 1
                FROM information_schema.table_constraints
                WHERE constraint_type = 'FOREIGN KEY'
                AND table_name = 'cdr_data_aggregate'
                AND constraint_name = 'fk_cdr_uuid'
            ) THEN
                ALTER TABLE cdr_data_aggregate
                DROP CONSTRAINT fk_cdr_uuid;
            END IF;
        END $$;

        """
    )

    op.execute(
        """
        DO $$
        BEGIN
            IF EXISTS (
                SELECT 1
                FROM information_schema.table_constraints
                WHERE constraint_type = 'FOREIGN KEY'
                AND table_name = 'cdr_voice_aggregate'
                AND constraint_name = 'fk_cdr_uuid'
            ) THEN
                ALTER TABLE cdr_voice_aggregate
                DROP CONSTRAINT fk_cdr_uuid;
            END IF;
        END $$;

        """
    )


def downgrade() -> None:
    pass
