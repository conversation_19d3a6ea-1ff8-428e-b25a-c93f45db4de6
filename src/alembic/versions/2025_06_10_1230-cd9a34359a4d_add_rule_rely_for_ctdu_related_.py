"""add rule rely for CTDU related definitions

Revision ID: cd9a34359a4d
Revises: 620c6126db4e
Create Date: 2025-05-30 12:30:41.215567

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "cd9a34359a4d"
down_revision = "620c6126db4e"
branch_labels = None
depends_on = None


def insert_rule_details(conn, definition_code, data):
    """Insert rule details for a given definition code."""
    result = conn.execute(
        sa.text("SELECT id FROM rule_definition WHERE rule_definition_code = :code"),
        {"code": definition_code},
    )
    definition_id = result.scalar()

    if definition_id:
        data["rule_definition_id"] = definition_id
        conn.execute(
            sa.text(
                """
                INSERT INTO rule_details_rely (
                    rule_definition_id, data_volume, data_unit, sms_unit, voice_unit,
                    threshold, data_percentage, view_deactivate_sim,
                    required_deactivate_sim, view_rate_plan_change,
                    required_rate_plan_change, add_any_rate_plan,
                    is_monthly_pool, view_email, view_sms, view_push,
                    voice_momt_percentage, sms_percentage, voice_mo_percentage,
                    voice_mt_percentage, data, voice_mo, voice_mt, sms
                ) VALUES (
                    :rule_definition_id, :data_volume, :data_unit, :sms_unit,
                    :voice_unit, :threshold, :data_percentage, :view_deactivate_sim,
                    :required_deactivate_sim, :view_rate_plan_change,
                    :required_rate_plan_change, :add_any_rate_plan,
                    :is_monthly_pool, :view_email, :view_sms, :view_push,
                    :voice_momt_percentage, :sms_percentage, :voice_mo_percentage,
                    :voice_mt_percentage, :data, :voice_mo, :voice_mt, :sms
                )
            """
            ),
            data,
        )


def upgrade() -> None:
    conn = op.get_bind()
    insert_rule_details(
        conn,
        "VUESL",
        {
            "data_volume": True,
            "data_unit": False,
            "sms_unit": False,
            "voice_unit": True,
            "threshold": False,
            "data_percentage": False,
            "view_deactivate_sim": True,
            "required_deactivate_sim": False,
            "view_rate_plan_change": True,
            "required_rate_plan_change": False,
            "add_any_rate_plan": True,
            "is_monthly_pool": False,
            "view_email": True,
            "view_sms": False,
            "view_push": False,
            "voice_mo_percentage": False,
            "voice_momt_percentage": False,
            "voice_mt_percentage": False,
            "sms_percentage": False,
            "data": False,
            "voice_mo": False,
            "voice_mt": False,
            "sms": False,
        },
    )

    insert_rule_details(
        conn,
        "SUESL",
        {
            "data_volume": True,
            "data_unit": False,
            "sms_unit": True,
            "voice_unit": False,
            "threshold": False,
            "data_percentage": False,
            "view_deactivate_sim": True,
            "required_deactivate_sim": False,
            "view_rate_plan_change": True,
            "required_rate_plan_change": False,
            "add_any_rate_plan": True,
            "is_monthly_pool": False,
            "view_email": True,
            "view_sms": False,
            "view_push": False,
            "voice_mo_percentage": False,
            "voice_momt_percentage": False,
            "voice_mt_percentage": False,
            "sms_percentage": False,
            "data": False,
            "voice_mo": False,
            "voice_mt": False,
            "sms": False,
        },
    )


def downgrade() -> None:
    conn = op.get_bind()

    conn.execute(
        """
        DELETE FROM rule_details_rely
        WHERE rule_definition_id IN (
            SELECT id FROM rule_definition
            WHERE rule_definition_code IN ('VUESL', 'SUESL')
        );
        """
    )
