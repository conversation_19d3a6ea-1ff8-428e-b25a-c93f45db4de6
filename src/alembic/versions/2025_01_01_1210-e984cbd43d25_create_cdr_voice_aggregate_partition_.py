"""create_cdr_voice_aggregate_partition_table

Revision ID: e984cbd43d25
Revises: 6b9882998aac
Create Date: 2025-01-01 12:10:15.796135

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

from common.types import ICCID, IMSI

# revision identifiers, used by Alembic.
revision = "e984cbd43d25"
down_revision = "6b9882998aac"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "cdr_voice_aggregate_new",
        sa.Column(
            "id",
            sa.Integer,
            sa.Identity(always=False, start=1, increment=1),
            primary_key=True,
            autoincrement=True,
        ),
        sa.Column("cdr_uuid", postgresql.UUID(), nullable=False),
        sa.Column("imsi", sa.String(IMSI.max_length), nullable=False),
        sa.Column("iccid", sa.String(ICCID.max_length), nullable=False),
        sa.Column("voice_usage", sa.<PERSON>nteger, nullable=False),
        sa.Column("month", sa.Date(), primary_key=True, nullable=False),
        sa.PrimaryKeyConstraint("id", "month", name="pk_cdr_voice_aggregate_new"),
        sa.UniqueConstraint("iccid", "month", name="uq_cdr_voice_aggregate_new_iccid"),
        postgresql_partition_by="RANGE (month)",
    )

    # Creating partitions for the CDR_SMS Table.

    op.execute(
        """
        DO $$
        DECLARE
            start_date DATE;
            end_date DATE := date_trunc('month',
            current_date + interval '1 month')::DATE;
            partition_date DATE;
            partition_name TEXT;
        BEGIN
            -- Select the smallest month from the cdr_voice_aggregate table
            -- and set it to the first day of its month
            SELECT date_trunc('month', MIN(month))::DATE
            INTO start_date FROM cdr_voice_aggregate;

            partition_date := start_date;
            WHILE partition_date < end_date LOOP
                partition_name := 'cdr_voice_aggregate_new_' || to_char(
                partition_date, 'YYYY_MM');
                EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF \
                cdr_voice_aggregate_new FOR VALUES FROM (%L) TO (%L)',
                            partition_name,
                            partition_date::DATE,
                            (partition_date + interval '1 month')::DATE);
                partition_date := partition_date + interval '1 month';
            END LOOP;
        END $$;

        """
    )

    # Creating index for the cdr_voice_aggregate_new Table.
    op.create_index(
        "idx_cdr_voice_aggregate_new_month", "cdr_voice_aggregate_new", ["month"]
    )


def downgrade() -> None:

    op.drop_table("cdr_voice_aggregate_new")
