"""add new field to cdr model

Revision ID: 90f599373d00
Revises: 4446b0d9e65f
Create Date: 2023-05-05 10:38:28.836046

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "90f599373d00"
down_revision = "4446b0d9e65f"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column(
        "cdr",
        sa.Column("imsi", sa.String(length=15), nullable=True),
    )
    op.add_column(
        "cdr_data_aggregate",
        sa.Column("imsi", sa.String(length=15), nullable=True),
    )


def downgrade() -> None:
    op.drop_column("cdr", "imsi")
    op.drop_column("cdr_data_aggregate", "imsi")
