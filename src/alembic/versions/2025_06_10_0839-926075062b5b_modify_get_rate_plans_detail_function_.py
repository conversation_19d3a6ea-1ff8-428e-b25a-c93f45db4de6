"""modify_get_rate_plans_detail_function_for_sms_aggregate

Revision ID: 926075062b5b
Revises: c22641a180ed
Create Date: 2025-05-30 08:39:58.188637

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "926075062b5b"
down_revision = "c22641a180ed"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS get_rate_plans_detail(integer[], varchar[],
        varchar, service);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION public.get_rate_plans_detail(
            account_ids_param integer[] DEFAULT NULL::integer[],
            rate_model_codes character varying[] DEFAULT NULL::character varying[],
            search_term character varying DEFAULT NULL::character varying,
            rate_plan_service service DEFAULT 'DATA'::service
        )
        RETURNS TABLE (
            account_id integer,
            account_name character varying,
            logo_key character varying,
            id integer,
            name character varying,
            access_fee numeric,
            currency character,
            rate_model_id integer,
            rate_plan_model character varying,
            sim_limit integer,
            range_from integer,
            range_to numeric,
            value numeric,
            range_unit unit,
            price_unit unit,
            overage_fee numeric,
            overage_unit unit,
            isoverage boolean,
            overage_per integer,
            service service,
            is_default boolean,
            allowance_used numeric
        )
        LANGUAGE plpgsql AS
        $$
        BEGIN
            RETURN QUERY
            SELECT
                rp.account_id,
                rp.account_name,
                rp.logo_key,
                rp.id,
                rp.name,
                rp.access_fee,
                rp.currency,
                rp.rate_model_id,
                rp.rate_model_code,
                rp.sim_limit,
                rp.range_from,
                rp.range_to,
                rp.value,
                rp.range_unit,
                rp.price_unit,
                rp.overage_fee,
                rp.overage_unit,
                rp.isoverage,
                rp.overage_per,
                rp.service,
                rp.is_default,
                COALESCE(allowance.allowance_used, 0) AS allowance_used
            FROM
                get_rate_plans_detail_view rp
            LEFT JOIN
                get_allowance_usage(account_ids_param, NULL, NULL) AS allowance
                ON allowance.rate_plan_id = rp.id
                AND allowance.service = rp.service::text
            WHERE
                (account_ids_param IS NULL OR rp.account_id = ANY(account_ids_param))
                AND (
                    search_term IS NULL OR
                    rp.account_name ILIKE '%' || search_term || '%' OR
                    rp.name ILIKE '%' || search_term || '%' OR
                    rp.access_fee::text ILIKE '%' || search_term || '%'
                )
            ORDER BY
                rp.account_id,
                rp.is_default DESC,
                rp.id,
                rp.range_from ASC NULLS FIRST,
                rp.range_to ASC NULLS LAST;
        END;
        $$;
        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS get_rate_plans_detail(integer[], varchar[],
        varchar, service);
        """
    )
    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_rate_plans_detail(
            account_ids_param integer[] DEFAULT NULL,
            rate_model_codes varchar[] DEFAULT NULL,
            search_term varchar DEFAULT NULL,
            rate_plan_service service DEFAULT 'DATA'::service
        )
        RETURNS TABLE (
            account_id integer,
            account_name character varying,
            logo_key character varying,
            id integer,
            name character varying,
            access_fee numeric,
            currency character,
            rate_model_id integer,
            rate_plan_model integer,
            sim_limit integer,
            range_from integer,
            range_to numeric,
            value numeric,
            range_unit unit,
            price_unit unit,
            overage_fee numeric,
            overage_unit unit,
            isoverage bool,
            overage_per integer,
            service service,
            is_default bool,
            allowance_used numeric
        )
        LANGUAGE plpgsql AS
        $$
        BEGIN
            RETURN QUERY
            SELECT
                rp.account_id,
                rp.account_name,
                rp.logo_key,
                rp.id,
                rp.name,
                rp.access_fee,
                rp.currency,
                rp.rate_model_id,
                CASE
                    WHEN rp.rate_model_code != ALL(rate_model_codes)
                    AND rp.service = rate_plan_service THEN rp.rate_model_id
                    ELSE NULL
                END as rate_plan_model,
                rp.sim_limit,
                rp.range_from,
                rp.range_to,
                rp.value,
                rp.range_unit,
                rp.price_unit,
                rp.overage_fee,
                rp.overage_unit,
                rp.isoverage,
                rp.overage_per,
                rp.service,
                rp.is_default,
                COALESCE(allowance.allowance_used, 0) AS allowance_used
            FROM
                get_rate_plans_detail_view rp
            LEFT JOIN
                get_allowance_usage(account_ids_param, rate_model_codes,
                rate_plan_service) AS allowance
                ON allowance.rate_plan_id = rp.id
            WHERE
                (account_ids_param IS NULL OR rp.account_id = ANY(account_ids_param))
                AND (search_term IS NULL OR
                    rp.account_name ILIKE '%' || search_term || '%' OR
                    rp.name ILIKE '%' || search_term || '%' OR
                    rp.access_fee::text ILIKE '%' || search_term || '%')
            ORDER BY
                rp.account_id,
                rp.is_default DESC,
                rp.id,
                rp.range_from ASC NULLS FIRST,
                rp.range_to ASC NULLS LAST;
        END;
        $$;
        """
    )
