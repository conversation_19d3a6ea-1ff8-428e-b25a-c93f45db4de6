"""add definitions for CTDU category

Revision ID: b1730a12d6c5
Revises: fa469aa3f423
Create Date: 2025-05-30 12:02:13.691205

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "b1730a12d6c5"
down_revision = "fa469aa3f423"
branch_labels = None
depends_on = None


def upgrade() -> None:
    conn = op.get_bind()
    rule_category_id = conn.execute(
        "SELECT id FROM rule_category WHERE rule_category_code = 'CTDU' "
    ).scalar_one()

    query = sa.text(
        """
    INSERT INTO rule_definition (rule_category_id, definition, rule_definition_code)
    VALUES (:rule_category_id, 'Voice usage exceeds a specified limit','VUESL'),
            (:rule_category_id, 'SMS usage exceeds a specified limit','SUESL')
            """
    )
    conn.execute(query, {"rule_category_id": rule_category_id})


def downgrade() -> None:
    op.execute(
        """
        DELETE
            FROM
        rule_definition
            WHERE rule_definition_code in ('VUESL', 'SUESL')
    """
    )
