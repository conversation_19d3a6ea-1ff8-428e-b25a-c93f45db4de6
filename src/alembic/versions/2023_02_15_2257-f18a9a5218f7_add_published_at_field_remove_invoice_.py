"""Add published_at field, remove invoice_date field for Invoice

Revision ID: f18a9a5218f7
Revises: e9ff22c71853
Create Date: 2023-02-15 22:57:47.643038

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "f18a9a5218f7"
down_revision = "e9ff22c71853"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("invoice", sa.Column("published_at", sa.DateTime(), nullable=True))
    op.drop_column("invoice", "invoice_date")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "invoice",
        sa.Column(
            "invoice_date",
            sa.DATE(),
            autoincrement=False,
            server_default=sa.text("'2023-01-01'"),
            nullable=False,
        ),
    )
    op.drop_column("invoice", "published_at")
    # ### end Alembic commands ###
