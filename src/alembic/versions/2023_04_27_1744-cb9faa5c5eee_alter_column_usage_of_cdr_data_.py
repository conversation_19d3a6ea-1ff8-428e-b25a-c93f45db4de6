"""alter column usage of cdr_data_aggregate table

Revision ID: cb9faa5c5eee
Revises: d532d051ad91
Create Date: 2023-04-27 17:44:37.027097

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "cb9faa5c5eee"
down_revision = "d532d051ad91"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "cdr_data_aggregate",
        "usage",
        existing_type=sa.Integer,
        type_=sa.BigInteger,
    )
    op.alter_column(
        "cdr_data",
        "data_volume",
        existing_type=sa.Integer,
        type_=sa.BigInteger,
    )


def downgrade():
    op.alter_column(
        "cdr_data_aggregate",
        "usage",
        existing_type=sa.BigInteger,
        type_=sa.Integer,
    )
    op.alter_column(
        "cdr_data",
        "data_volume",
        existing_type=sa.BigInteger,
        type_=sa.Integer,
    )
