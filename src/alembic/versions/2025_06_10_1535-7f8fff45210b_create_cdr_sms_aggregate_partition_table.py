"""create_cdr_sms_aggregate_partition_table

Revision ID: 7f8fff45210b
Revises: 009508ddfdb1
Create Date: 2025-05-30 15:35:44.581606

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

from common.types import ICCID, IMSI

# revision identifiers, used by Alembic.
revision = "7f8fff45210b"
down_revision = "009508ddfdb1"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "cdr_sms_aggregate",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False, start=1, increment=1),
            nullable=False,
        ),
        sa.Column("cdr_uuid", postgresql.UUID(), nullable=False),
        sa.Column("imsi", sa.String(IMSI.max_length), nullable=False),
        sa.Column("iccid", sa.String(ICCID.max_length), nullable=False),
        sa.Column("total_sms", sa.BigInteger, nullable=False),
        sa.Column("month", sa.Date(), nullable=False),
        sa.PrimaryKeyConstraint("id", "month", name="pk_cdr_sms_aggregate"),
        sa.UniqueConstraint("iccid", "month", name="uq_cdr_sms_aggregate"),
        postgresql_partition_by="RANGE (month)",
    )

    op.execute(
        """
        DO $$
        DECLARE
            start_date DATE;
            end_date DATE := date_trunc('month',
            current_date + interval '1 month')::DATE;
            partition_date DATE;
            partition_name TEXT;
        BEGIN
            -- Select the smallest date_sent from the cdr_sms table
            -- and set it to the first day of its date_sent
            SELECT date_trunc('month', MIN(date_sent))::DATE
            INTO start_date FROM cdr_sms;

            partition_date := start_date;
            WHILE partition_date < end_date LOOP
                partition_name := 'cdr_sms_aggregate_' || to_char(
                partition_date, 'YYYY_MM');
                EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF \
                cdr_sms_aggregate FOR VALUES FROM (%L) TO (%L)',
                            partition_name,
                            partition_date::DATE,
                            (partition_date + interval '1 month')::DATE);
                partition_date := partition_date + interval '1 month';
            END LOOP;
        END $$;

        """
    )

    op.create_index(
        "idx_cdr_sms_aggregate_month",
        "cdr_sms_aggregate",
        ["month"],
    )


def downgrade() -> None:
    op.drop_table("cdr_sms_aggregate")
