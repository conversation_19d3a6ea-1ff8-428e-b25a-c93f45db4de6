"""remove unwanted column and tables

Revision ID: c24bcd1ae2c0
Revises: db3d729b5608
Create Date: 2024-04-03 19:52:36.420630

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "c24bcd1ae2c0"
down_revision = "db3d729b5608"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("sim_card", "allocation_id_old")
    op.drop_table("allocation_deprecated")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "allocation_deprecated",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("title", sa.String(length=64), nullable=False),
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.Column("range_id", sa.Integer(), nullable=True),
        sa.Column("rate_plan_id", sa.Integer(), nullable=True),
        sa.Column("quantity", sa.Integer(), nullable=True),
        sa.Column("imsi_first", sa.String(length=15), nullable=True),
        sa.Column("imsi_last", sa.String(length=15), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["range_id"], ["range.id"], name=op.f("fk_allocation_range_id_range")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_allocation")),
    )
    op.add_column(
        "allocation_deprecated",
        sa.Column("allocation_details_id", sa.Integer(), nullable=True),
    )
    op.create_foreign_key(
        op.f("fk_allocation_allocation_details_id_allocation_details"),
        "allocation_deprecated",
        "allocation_details",
        ["allocation_details_id"],
        ["id"],
    )
    op.create_foreign_key(
        op.f("fk_allocation_rate_plan_id_rate_plan"),
        "allocation_deprecated",
        "rate_plan",
        ["rate_plan_id"],
        ["id"],
        ondelete="RESTRICT",
    )
    op.create_foreign_key(
        op.f("fk_allocation_account_id_account"),
        "allocation_deprecated",
        "account",
        ["account_id"],
        ["id"],
        ondelete="RESTRICT",
    )
    # ### end Alembic commands ###

    op.add_column(
        "sim_card", sa.Column("allocation_id_old", sa.Integer(), nullable=True)
    )
    # ### end Alembic commands ###
