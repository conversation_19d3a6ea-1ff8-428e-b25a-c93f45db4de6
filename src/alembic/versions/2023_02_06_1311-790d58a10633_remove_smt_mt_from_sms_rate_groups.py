"""Remove SMT MT from SMS rate groups

Revision ID: 790d58a10633
Revises: 140d09da17e1
Create Date: 2023-02-06 13:11:00.757176

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "790d58a10633"
down_revision = "140d09da17e1"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute("""DELETE FROM rate_group_service WHERE service = 'SMS_MT'""")


def downgrade() -> None:
    pass
