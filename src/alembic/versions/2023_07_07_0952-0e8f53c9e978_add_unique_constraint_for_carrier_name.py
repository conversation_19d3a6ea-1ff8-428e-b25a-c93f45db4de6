"""add unique constraint for carrier_name

Revision ID: 0e8f53c9e978
Revises: 7fc769669b03
Create Date: 2023-07-07 09:52:43.274044

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "0e8f53c9e978"
down_revision = "7fc769669b03"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint(
        op.f("uq_carrier_name_carrier"), "carrier_name", ["carrier"]
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(op.f("uq_carrier_name_carrier"), "carrier_name", type_="unique")
    # ### end Alembic commands ###
