"""create_allocation_info_function

Revision ID: cbd1135f4447
Revises: a5063813c0d5
Create Date: 2024-08-05 14:38:35.711328

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "cbd1135f4447"
down_revision = "a5063813c0d5"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        CREATE OR REPLACE FUNCTION allocation_info()
            RETURNS TABLE (
                id integer,
                title character varying,
                account_id integer,
                name character varying,
                country character,
                provider character varying,
                form_factor character varying,
                quantity integer,
                created_at TIMESTAMP,
                logo_key character varying
            )
            LANGUAGE 'plpgsql'
            COST 100
            VOLATILE
            PARALLEL UNSAFE
            ROWS 1000
        AS $BODY$
        BEGIN
            RETURN QUERY
            SELECT
                ad.id AS id,
                a.title AS title,
                acc.id AS account_id,
                acc.name AS name,
                acc.country AS country,
                'NR'::character varying AS provider,
                r.form_factor AS form_factor,
                ad.total_sim AS quantity,
                MAX(a.created_at) AS created_at,
                acc.logo_key AS logo_key
            FROM
                allocation a
            JOIN
                allocation_details ad ON a.allocation_details_id = ad.id
            JOIN
                account acc ON a.account_id = acc.id
            JOIN
                range r ON a.range_id = r.id
            GROUP BY
                ad.id, a.title, acc.id, acc.name, acc.country,
                r.form_factor, ad.total_sim
            ORDER BY
                created_at DESC;
        END;
        $BODY$;
        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS allocation_info();
        """
    )
