"""add change rate plan in master actions table

Revision ID: d66207348887
Revises: 412417afb60a
Create Date: 2025-02-07 22:17:27.117071

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "d66207348887"
down_revision = "412417afb60a"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        sa.text("INSERT INTO actions(action) VALUES(:action)").params(
            action="Change Rate Plan"
        )
    )


def downgrade() -> None:
    action_name = "Change Rate Plan"

    op.execute(
        sa.text(
            "DELETE FROM rules_action WHERE actions_id = ("
            "SELECT id FROM actions WHERE action = :action)"
        ).params(action=action_name)
    )

    op.execute(
        sa.text("DELETE FROM actions WHERE action = :action").params(action=action_name)
    )
