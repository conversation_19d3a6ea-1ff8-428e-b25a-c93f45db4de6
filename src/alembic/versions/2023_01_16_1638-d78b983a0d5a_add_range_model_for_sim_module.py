"""Add Range model for Sim module

Revision ID: d78b983a0d5a
Revises: fc792b9b1162
Create Date: 2023-01-16 16:38:56.572017

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "d78b983a0d5a"
down_revision = "fc792b9b1162"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "range",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("title", sa.String(length=64), nullable=False),
        sa.Column(
            "form_factor",
            sa.Enum(
                "STANDARD",
                "MICRO",
                "NANO",
                name="form_factor",
                native_enum=False,
                create_constraint=True,
            ),
            nullable=True,
        ),
        sa.Column("quantity", sa.Integer(), nullable=True),
        sa.Column("imsi_first", sa.String(length=15), nullable=True),
        sa.Column("imsi_last", sa.String(length=15), nullable=True),
        sa.Column("remaining", sa.Integer(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("created_by", sa.String(length=128), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_range")),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("range")
    # ### end Alembic commands ###
