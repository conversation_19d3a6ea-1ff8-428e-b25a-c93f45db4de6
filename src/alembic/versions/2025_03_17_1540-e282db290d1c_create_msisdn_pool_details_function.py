"""create msisdn_pool_details_function

Revision ID: e282db290d1c
Revises: d2c7e82bbfc5
Create Date: 2025-03-17 15:40:46.003137

"""
# import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "e282db290d1c"
down_revision = "d2c7e82bbfc5"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        CREATE OR REPLACE FUNCTION msisdn_pool_details(
            page_offset INT DEFAULT NULL,
            page_size INT DEFAULT NULL,
            search_term varchar DEFAULT NULL
        )
        RETURNS TABLE (
            msisdn varchar,
            sim_profile sim_profile,
            msisdn_factor msisdn_factor,
            created_at TIMESTAMP,
            allocation_id integer,
            uploaded_by varchar,
            imsi varchar,
            account_name varchar,
            logo_key varchar,
            country character
        )
        LANGUAGE 'plpgsql'
        COST 100
        VOLATILE
        PARALLEL UNSAFE
        ROWS 1000
        AS $BODY$
        DECLARE
            sql_query TEXT;
            -- final_order_by_column TEXT := COALESCE(order_by_column, 'created_at');
            -- final_order_direction TEXT := UPPER(COALESCE(order_direction, 'DESC'));
        BEGIN

            sql_query := $$
                SELECT
                    mpd.msisdn,
                    mpd.sim_profile,
                    mpd.msisdn_factor,
                    mpd.created_at,
                    mpd.allocation_id,
                    mpd.uploaded_by,
                    mpd.imsi,
                    mpd.account_name,
                    mpd.logo_key,
                    mpd.country
                FROM
                    msisdn_pool_details_view mpd
                WHERE
                    $1 IS NULL OR
                    mpd.msisdn ILIKE '%' || $1 || '%' OR
                    mpd.sim_profile::text ILIKE '%' || $1 || '%' OR
                    mpd.msisdn_factor::text ILIKE '%' || $1 || '%' OR
                    mpd.uploaded_by ILIKE '%' || $1 || '%' OR
                    mpd.account_name ILIKE '%' || $1 || '%'
                ORDER BY
                    mpd.imsi NULLS FIRST,
                    mpd.created_at DESC
            $$;

            -- sql_query := sql_query || ' ORDER BY ' || 'allocation_id NULLS FIRST '
            -- || ', ' || 'imsi NULLS FIRST ' || ', '
            -- || quote_ident(final_order_by_column) || ' '
            -- || final_order_direction;

            IF page_size IS NOT NULL AND page_offset IS NOT NULL THEN
                sql_query := sql_query || ' LIMIT ' || page_size ||
                ' OFFSET ' || page_offset;
            END IF;

            -- Execute the dynamic query and return the results
            RETURN QUERY EXECUTE sql_query USING search_term;
        END;
        $BODY$;
        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS msisdn_pool_details(integer,
        integer, varchar);
        """
    )
