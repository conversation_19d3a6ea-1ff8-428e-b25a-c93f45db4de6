"""Add invoice model

Revision ID: 6d873ace0a71
Revises: 1c18b5f8613c
Create Date: 2023-01-01 13:00:18.542992

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "6d873ace0a71"
down_revision = "1c18b5f8613c"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "invoice",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("billing_cycle_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("account_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("invoice_date", sa.Date(), nullable=False),
        sa.Column("due_days", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["billing_cycle_id"],
            ["billing_cycle.id"],
            name=op.f("fk_invoice_billing_cycle_id_billing_cycle"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_invoice")),
        sa.UniqueConstraint(
            "billing_cycle_id", "account_id", name=op.f("uq_invoice_billing_cycle_id")
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("invoice")
    # ### end Alembic commands ###
