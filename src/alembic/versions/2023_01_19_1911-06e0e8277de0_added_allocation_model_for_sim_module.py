"""Added Allocation model for Sim module

Revision ID: 06e0e8277de0
Revises: 2a666b543763
Create Date: 2023-01-19 19:11:33.011848

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "06e0e8277de0"
down_revision = "2a666b543763"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "allocation",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("title", sa.String(length=64), nullable=False),
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.Column("range_id", sa.Integer(), nullable=True),
        sa.Column("rate_plan_id", sa.Integer(), nullable=True),
        sa.Column("quantity", sa.Integer(), nullable=True),
        sa.Column("imsi_first", sa.String(length=15), nullable=True),
        sa.Column("imsi_last", sa.String(length=15), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["range_id"], ["range.id"], name=op.f("fk_allocation_range_id_range")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_allocation")),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("allocation")
    # ### end Alembic commands ###
