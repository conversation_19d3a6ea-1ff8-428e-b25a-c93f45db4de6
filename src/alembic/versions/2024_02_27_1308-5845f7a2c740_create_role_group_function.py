"""create role_group function

Revision ID: 5845f7a2c740
Revises: cd735b595a0d
Create Date: 2024-02-27 13:08:32.940476

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "5845f7a2c740"
down_revision = "cd735b595a0d"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
            CREATE OR REPLACE FUNCTION role_group_function(
                input_role_id INT,
                input_role_uuid UUID)
                RETURNS void
                LANGUAGE 'plpgsql'
                COST 100
                VOLATILE PARALLEL UNSAFE
            AS $BODY$
                        BEGIN
                            INSERT INTO role_group (role_id, organization_id, role_uuid)
                            SELECT input_role_id, id, input_role_uuid FROM account;
                        END;

            $BODY$;
"""
    )


def downgrade() -> None:
    op.execute(
        """
            DROP FUNCTION IF EXISTS role_group_function(
                INT, UUID
                );
        """
    )
