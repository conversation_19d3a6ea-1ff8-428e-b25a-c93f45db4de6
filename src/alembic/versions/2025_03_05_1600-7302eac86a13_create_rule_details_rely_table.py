"""create rule_details_rely table

Revision ID: 7302eac86a13
Revises: cafc71514d4d
Create Date: 2025-02-11 16:00:24.443681

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "7302eac86a13"
down_revision = "cafc71514d4d"
branch_labels = None
depends_on = None


def upgrade():
    op.create_table(
        "rule_details_rely",
        sa.<PERSON>umn(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("rule_category_id", sa.Integer(), nullable=False),
        sa.Column("data_volume", sa.<PERSON>(), nullable=False),
        sa.<PERSON>umn("data_unit", sa.<PERSON>(), nullable=False),
        sa.Column("sms_unit", sa.<PERSON>an(), nullable=False),
        sa.<PERSON>umn("voice_unit", sa.<PERSON>(), nullable=False),
        sa.Column("threshold", sa.<PERSON>(), nullable=False),
        sa.Column("percentage_unit", sa.<PERSON>(), nullable=False),
        sa.Column("view_deactivate_sim", sa.Boolean(), nullable=False),
        sa.Column("required_deactivate_sim", sa.Boolean(), nullable=False),
        sa.Column("view_rate_plan_change", sa.Boolean(), nullable=False),
        sa.Column("required_rate_plan_change", sa.Boolean(), nullable=False),
        sa.Column("add_any_rate_plan", sa.Boolean(), nullable=False),
        sa.Column("is_monthly_pool", sa.Boolean(), nullable=False),
        sa.Column("view_email", sa.Boolean(), nullable=False),
        sa.Column("view_sms", sa.Boolean(), nullable=False),
        sa.Column("view_push", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ["rule_category_id"],
            ["rule_category.id"],
            name=op.f("fk_rule_details_rely_rule_category_id_rule_category"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_rule_details_rely")),
        sa.UniqueConstraint(
            "rule_category_id", name=op.f("uq_rule_details_rely_rule_category_id")
        ),
    )


def downgrade():
    op.drop_table("rule_details_rely")
