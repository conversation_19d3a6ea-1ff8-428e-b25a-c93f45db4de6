"""create table last_session_cdr

Revision ID: 0bff9dce7e60
Revises: a26f30b17966
Create Date: 2023-06-05 13:24:51.506784

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "0bff9dce7e60"
down_revision = "a26f30b17966"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "last_session_cdr",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False, start=1, increment=1),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column(
            "iccid",
            sa.String(),
            nullable=False,
        ),
        sa.Column(
            "imsi",
            sa.String(),
            nullable=False,
        ),
        sa.Column(
            "last_session",
            sa.DateTime(),
            nullable=False,
        ),
    )


def downgrade() -> None:
    op.drop_table("last_session_cdr")
