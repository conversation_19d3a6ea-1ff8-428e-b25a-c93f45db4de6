"""Add new fields to sim model

Revision ID: 18f2cfa3a584
Revises: 8f16b100a4b7
Create Date: 2023-04-20 12:47:02.791643

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

from sim.domain.model import SimStatus

# revision identifiers, used by Alembic.
revision = "18f2cfa3a584"
down_revision = "8f16b100a4b7"
branch_labels = None
depends_on = None


def upgrade() -> None:
    sim_status = postgresql.ENUM(SimStatus, name="sim_status")
    sim_status.create(op.get_bind(), checkfirst=True)
    op.add_column(
        "sim_card",
        sa.Column(
            "sim_status",
            sim_status,
            server_default="READY_FOR_ACTIVATION",
            nullable=True,
        ),
    )
    op.execute("""UPDATE sim_card SET sim_status='READY_FOR_ACTIVATION';""")


def downgrade() -> None:
    op.drop_column("sim_card", "sim_status")

    sim_status = postgresql.ENUM(SimStatus, name="sim_status")
    sim_status.drop(op.get_bind())
