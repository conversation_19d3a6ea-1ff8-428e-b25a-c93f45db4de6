"""alter allocation table

Revision ID: e4e5c7babc99
Revises: 7eb13d8924e2
Create Date: 2024-01-02 15:44:55.874532

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "e4e5c7babc99"
down_revision = "7eb13d8924e2"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.drop_constraint(
        op.f("fk_sim_card_allocation_id_allocation"), "sim_card", type_="foreignkey"
    )
    op.rename_table("allocation", new_table_name="allocation_deprecated")
    op.create_table(
        "allocation",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("title", sa.String(length=64), nullable=False),
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.Column("range_id", sa.Integer(), nullable=True),
        sa.Column("rate_plan_id", sa.Integer(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("imsi", sa.String(length=15), nullable=True),
        sa.Column("allocation_details_id", sa.Integer(), nullable=True),
        sa.ForeignKeyConstraint(
            ["range_id"], ["range.id"], name=op.f("fk_allocation_range_id_range")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk1_allocation")),
    )
    op.create_foreign_key(
        op.f("fk_allocation_rate_plan_id_rate_plan"),
        "allocation",
        "rate_plan",
        ["rate_plan_id"],
        ["id"],
        ondelete="RESTRICT",
    )
    op.create_foreign_key(
        op.f("fk_allocation_account_id_account"),
        "allocation",
        "account",
        ["account_id"],
        ["id"],
        ondelete="RESTRICT",
    )
    op.create_foreign_key(
        op.f("fk_allocation_allocation_details_id_allocation_details"),
        "allocation",
        "allocation_details",
        ["allocation_details_id"],
        ["id"],
    )
    op.execute(
        """
        INSERT INTO allocation
        (title, account_id, range_id, rate_plan_id,
            created_at, allocation_details_id, imsi)
        SELECT
            title,
            account_id,
            range_id,
            rate_plan_id,
            created_at,
            allocation_details_id,
            generated_number AS imsi
        FROM (
            SELECT
                title,
                account_id,
                range_id,
                rate_plan_id,
                created_at,
                allocation_details_id,
                generate_series(
                    CAST("imsi_first" AS BIGINT),
                    CAST("imsi_last" AS BIGINT)
                ) AS generated_number
            FROM allocation_deprecated
            where imsi_first is not null and imsi_last is not null
        ) AS subquery
        ORDER BY created_at;
    """
    )
    op.execute(
        """
            INSERT INTO allocation
            (title,account_id,range_id,rate_plan_id,
                created_at,imsi, allocation_details_id)
                SELECT al.title,
                al.account_id,
                al.range_id,
                al.rate_plan_id,
                al.created_at,
                sc.imsi,
                al.allocation_details_id
                FROM sim_card AS sc
                JOIN allocation_deprecated AS al ON sc.allocation_id = al.id
                WHERE sc.allocation_id IS NOT NULL
                AND al.imsi_first IS NULL
                AND al.imsi_last IS NULL;
    """
    )
    op.alter_column(
        "sim_card", "allocation_id", nullable=True, new_column_name="allocation_id_old"
    )
    op.add_column("sim_card", sa.Column("allocation_id", sa.Integer(), nullable=True))
    op.execute(
        """
            UPDATE sim_card AS sc
            SET allocation_id = al.id
            FROM allocation AS al
            WHERE al.imsi = sc.imsi;
         """
    )

    op.create_foreign_key(
        op.f("fk_sim_card_allocation_id_allocation"),
        "sim_card",
        "allocation",
        ["allocation_id"],
        ["id"],
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f("fk_sim_card_allocation_id_allocation"), "sim_card", type_="foreignkey"
    )
    op.drop_constraint(
        op.f("fk_allocation_allocation_details_id_allocation_details"),
        "allocation",
        type_="foreignkey",
    )
    op.drop_constraint(
        op.f("fk_allocation_account_id_account"), "allocation", type_="foreignkey"
    )
    op.drop_constraint(
        op.f("fk_allocation_rate_plan_id_rate_plan"), "allocation", type_="foreignkey"
    )
    op.drop_table("allocation")
    op.rename_table("allocation_deprecated", new_table_name="allocation")
    op.drop_column("sim_card", "allocation_id")
    op.alter_column(
        "sim_card", "allocation_id_old", nullable=True, new_column_name="allocation_id"
    )
    op.create_foreign_key(
        op.f("fk_sim_card_allocation_id_allocation"),
        "sim_card",
        "allocation",
        ["allocation_id"],
        ["id"],
    )

    # ### end Alembic commands ###
