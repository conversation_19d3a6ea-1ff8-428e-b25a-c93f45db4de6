"""Update Subscription model in billing

Revision ID: d9e3bf2ff131
Revises: 1fc607a5405f
Create Date: 2023-05-16 14:50:52.277383

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy import false, true

# revision identifiers, used by Alembic.
revision = "d9e3bf2ff131"
down_revision = "1fc607a5405f"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "subscription",
        sa.Column("sims_total", sa.Integer(), server_default="0", nullable=False),
    )
    op.add_column(
        "subscription_sim",
        sa.Column(
            "first_time_activated", sa.<PERSON>(), nullable=False, server_default=false()
        ),
    )
    op.drop_column("subscription_sim", "was_active")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "subscription_sim",
        sa.Column(
            "was_active",
            sa.BO<PERSON>N(),
            autoincrement=False,
            nullable=False,
            server_default=true(),
        ),
    )
    op.drop_column("subscription_sim", "first_time_activated")
    op.drop_column("subscription", "sims_total")
    # ### end Alembic commands ###
