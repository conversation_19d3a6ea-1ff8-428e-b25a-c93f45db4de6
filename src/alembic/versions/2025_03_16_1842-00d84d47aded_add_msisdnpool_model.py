"""Add MsisdnPool model

Revision ID: 00d84d47aded
Revises: 4e60d11717b6
Create Date: 2025-03-16 18:42:23.632637

"""
import datetime

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

from sim.domain.model import MSISDNFactor, SimProfile

# revision identifiers, used by Alembic.
revision = "00d84d47aded"
down_revision = "4e60d11717b6"
branch_labels = None
depends_on = None


def upgrade() -> None:
    sim_profile = postgresql.ENUM(SimProfile, name="sim_profile")
    msisdn_factor = postgresql.ENUM(MSISDNFactor, name="msisdn_factor")
    op.create_table(
        "msisdn_pool",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("msisdn", sa.String(length=15), nullable=False),
        sa.Column("sim_profile", sim_profile, nullable=False),
        sa.Column("msisdn_factor", msisdn_factor, nullable=False),
        sa.Column(
            "allocation_id", sa.Integer(), sa.ForeignKey("allocation.id"), nullable=True
        ),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column(
            "uploaded_by", sa.String(length=120), server_default="", nullable=False
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_msisdn_pool")),
    )

    op.create_index("ix_msisdn_pool_msisdn", "msisdn_pool", ["msisdn"])
    op.create_index("ix_msisdn_pool_sim_profile", "msisdn_pool", ["sim_profile"])
    op.create_index("ix_msisdn_pool_msisdn_factor", "msisdn_pool", ["msisdn_factor"])

    op.execute(
        sa.text(
            """
            INSERT INTO msisdn_pool (msisdn, sim_profile, msisdn_factor, allocation_id,
            created_at, uploaded_by)
            SELECT sc.msisdn,
                CAST(:DATA_ONLY AS sim_profile),
                CASE
                    WHEN sc.msisdn LIKE '883%' THEN
                        CAST(:INTERNATIONAL AS msisdn_factor)
                    ELSE CAST(:NATIONAL AS msisdn_factor)
                END,
                sc.allocation_id,
                :CREATED_AT,
                :UPLOADED_BY
            FROM sim_card sc
            WHERE NOT EXISTS (
                SELECT 1
                FROM msisdn_pool mp
                WHERE mp.msisdn = sc.msisdn
            );
            """
        ).params(
            DATA_ONLY=SimProfile.DATA_ONLY.name,
            NATIONAL=MSISDNFactor.NATIONAL.name,
            INTERNATIONAL=MSISDNFactor.INTERNATIONAL.name,
            CREATED_AT=datetime.datetime.now(),
            UPLOADED_BY="system",
        )
    )


def downgrade() -> None:
    # Drop the table
    op.drop_table("msisdn_pool")

    # Now drop the ENUM type
    sim_profile = postgresql.ENUM(SimProfile, name="sim_profile")
    sim_profile.drop(op.get_bind(), checkfirst=True)

    msisdn_factor = postgresql.ENUM(MSISDNFactor, name="msisdn_factor")
    msisdn_factor.drop(op.get_bind(), checkfirst=True)
