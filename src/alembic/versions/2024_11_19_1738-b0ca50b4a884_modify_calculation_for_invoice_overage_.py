"""modify calculation for invoice_overage_charge_details function

Revision ID: b0ca50b4a884
Revises: f37d727745a4
Create Date: 2024-11-19 17:38:33.285835

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "b0ca50b4a884"
down_revision = "f37d727745a4"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS invoice_overage_charge_details(integer, integer, date);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION public.invoice_overage_charge_details(
            account_id_param integer,
            invocie_id_param integer,
            month_param date)
            RETURNS void
            LANGUAGE 'plpgsql'
            COST 100
            VOLATILE PARALLEL UNSAFE
        AS $BODY$
                    BEGIN
                        -- Step 1: Drop and recreate the temporary table
                        DROP TABLE IF EXISTS TempFirstQueryResult;
                        CREATE TEMPORARY TABLE TempFirstQueryResult AS
                        WITH FirstQueryResult AS (
                            SELECT rp.id, sc.imsi, rgs.service, r.range_to,
                            r.overage_fee, r.overage_unit, r.range_unit,
                            rg.rate_model_id, r.overage_per, r.isoverage
                            FROM allocation AS al
                            INNER JOIN sim_card AS sc
                            ON sc.allocation_id = al.id
                            and al.account_id = account_id_param
                            INNER JOIN sim_monthly_statistic AS sms
                            ON sms.sim_card_id = sc.id
                            AND month=month_param
                            AND sms.sim_status != 'READY_FOR_ACTIVATION'
                            INNER JOIN rate_plan AS rp
                            ON rp.id = COALESCE(sc.rate_plan_id, al.rate_plan_id)
                            INNER JOIN rate_group AS rg
                            ON rg.rate_plan_id = rp.id
                            AND rg.rate_model_id in (3,4)
                            INNER JOIN rate_group_service AS rgs
                            ON rgs.rate_group_id = rg.id
                            INNER JOIN rate AS r ON r.rate_group_id = rg.id
                            WHERE al.account_id = account_id_param
                            AND month=month_param
                            AND sms.sim_status != 'READY_FOR_ACTIVATION'
                            AND rg.rate_model_id in (3,4)
                        )
                        SELECT * FROM FirstQueryResult;

                        -- Step 2: Get distinct IMSI count
                        DROP TABLE IF EXISTS DistinctIMSI;
                        CREATE TEMPORARY TABLE DistinctIMSI AS
                        SELECT COUNT(DISTINCT imsi) AS distinct_imsi_count
                        FROM TempFirstQueryResult;

                        DROP TABLE IF EXISTS TotalAllowanceModel3;
                        CREATE TEMPORARY TABLE TotalAllowanceModel3 AS
                        SELECT DISTINCT rate_model_id, service,
                        overage_unit, overage_fee,
                        overage_per,
                        ROUND(GREATEST(convert_unit(range_unit, range_to), 0))
                        AS total_allowance,
                        isoverage
                        FROM TempFirstQueryResult
                        WHERE rate_model_id = 3;

                        DROP TABLE IF EXISTS TotalAllowanceModel4;
                        CREATE TEMPORARY TABLE TotalAllowanceModel4 AS
                        SELECT rate_model_id, service, overage_unit,
                        overage_fee, overage_per,
                        SUM(ROUND(GREATEST(convert_unit(range_unit, range_to), 0)))
                        AS total_allowance,
                        isoverage
                        FROM TempFirstQueryResult
                        WHERE rate_model_id = 4
                        GROUP BY rate_model_id,service, overage_unit, overage_fee,
                        overage_per, isoverage;

                        DROP TABLE IF EXISTS TotalUsage;
                        CREATE TEMPORARY TABLE TotalUsage AS
                        SELECT mur.service,
                        SUM(COALESCE(mur.volume, 0)) AS total_usage
                        FROM monthly_usage_records AS mur
                        WHERE to_char(
                        date_trunc('month', month_param), 'YYYY-MM') = to_char(
                        date_trunc('month', mur.month), 'YYYY-MM')
                        AND mur.imsi IN (SELECT imsi FROM TempFirstQueryResult)
                        GROUP BY mur.service;

                        INSERT INTO invoice_overage_charge
                        (invoice_id,sim_count,service,total_allowance,
                        total_usage,usage_variance,usage_variance_coverted,total_charge,
                        rate_model_id, overage_fee, overage_per, overage_unit,
                        isoverage, overage_charge)
                        SELECT
                            invocie_id_param,
                            (SELECT distinct_imsi_count FROM DistinctIMSI)
                            AS distinct_imsi_count,
                            ta.service,
                            ta.total_allowance,
                            COALESCE(tu.total_usage,0) AS total_usage,
                            GREATEST(
                            COALESCE(tu.total_usage, 0) - ta.total_allowance, 0)
                            AS usage_difference,
                            ROUND(reverse_convert_unit(ta.overage_unit, GREATEST(
                            COALESCE(tu.total_usage, 0) - ta.total_allowance, 0)),
                            6)
                            AS usage_difference_converted,
                            (CASE
                                WHEN ta.isoverage = true
                                THEN
                                CEIL(
                                GREATEST(
                                    reverse_convert_unit(ta.overage_unit, GREATEST(
                                    COALESCE(
                                    tu.total_usage, 0
                                    ) - ta.total_allowance,
                                    0)) / ta.overage_per, 0)
                                ) * ta.overage_fee
                                ELSE
                                (ta.overage_fee/ta.overage_per/CASE
                                    WHEN ta.service = 'DATA' THEN 1024
                                    WHEN ta.service IN (
                                    'VOICE_MO', 'VOICE_MT') THEN 60
                                    WHEN ta.service IN (
                                    'SMS_MO', 'SMS_MT') THEN 1
                                    ELSE 1
                                END)*
                                (ROUND(reverse_convert_unit(ta.overage_unit, GREATEST(
                            COALESCE(tu.total_usage, 0) - ta.total_allowance, 0)),
                            6)*CASE
                                    WHEN ta.service = 'DATA' THEN 1024
                                    WHEN ta.service IN (
                                    'VOICE_MO', 'VOICE_MT') THEN 60
                                    WHEN ta.service IN (
                                    'SMS_MO', 'SMS_MT') THEN 1
                                    ELSE 1
                                END)
                            END
                            ) AS total_charge,
                        ta.rate_model_id,
                        ta.overage_fee,
                        ta.overage_per,
                        ta.overage_unit,
                        ta.isoverage,
                        0
                        FROM (
                            SELECT * FROM TotalAllowanceModel3
                            UNION ALL
                            SELECT * FROM TotalAllowanceModel4
                        ) AS ta
                        LEFT JOIN TotalUsage tu ON ta.service = tu.service;

                    END;
        $BODY$;

        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS invoice_overage_charge_details(integer, integer, date);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION public.invoice_overage_charge_details(
            account_id_param integer,
            invocie_id_param integer,
            month_param date)
            RETURNS void
            LANGUAGE 'plpgsql'
            COST 100
            VOLATILE PARALLEL UNSAFE
        AS $BODY$
                    BEGIN
                        -- Step 1: Drop and recreate the temporary table
                        DROP TABLE IF EXISTS TempFirstQueryResult;
                        CREATE TEMPORARY TABLE TempFirstQueryResult AS
                        WITH FirstQueryResult AS (
                            SELECT rp.id, sc.imsi, rgs.service, r.range_to,
                            r.overage_fee, r.overage_unit, r.range_unit,
                            rg.rate_model_id, r.overage_per, r.isoverage
                            FROM allocation AS al
                            INNER JOIN sim_card AS sc
                            ON sc.allocation_id = al.id
                            and al.account_id = account_id_param
                            INNER JOIN sim_monthly_statistic AS sms
                            ON sms.sim_card_id = sc.id
                            AND month=month_param
                            AND sms.sim_status != 'READY_FOR_ACTIVATION'
                            INNER JOIN rate_plan AS rp
                            ON rp.id = COALESCE(sc.rate_plan_id, al.rate_plan_id)
                            INNER JOIN rate_group AS rg
                            ON rg.rate_plan_id = rp.id
                            AND rg.rate_model_id in (3,4)
                            INNER JOIN rate_group_service AS rgs
                            ON rgs.rate_group_id = rg.id
                            INNER JOIN rate AS r ON r.rate_group_id = rg.id
                            WHERE al.account_id = account_id_param
                            AND month=month_param
                            AND sms.sim_status != 'READY_FOR_ACTIVATION'
                            AND rg.rate_model_id in (3,4)
                        )
                        SELECT * FROM FirstQueryResult;

                        -- Step 2: Get distinct IMSI count
                        DROP TABLE IF EXISTS DistinctIMSI;
                        CREATE TEMPORARY TABLE DistinctIMSI AS
                        SELECT COUNT(DISTINCT imsi) AS distinct_imsi_count
                        FROM TempFirstQueryResult;

                        DROP TABLE IF EXISTS TotalAllowanceModel3;
                        CREATE TEMPORARY TABLE TotalAllowanceModel3 AS
                        SELECT DISTINCT rate_model_id, service,
                        overage_unit, overage_fee,
                        overage_per,
                        ROUND(GREATEST(convert_unit(range_unit, range_to), 0))
                        AS total_allowance,
                        isoverage
                        FROM TempFirstQueryResult
                        WHERE rate_model_id = 3;

                        DROP TABLE IF EXISTS TotalAllowanceModel4;
                        CREATE TEMPORARY TABLE TotalAllowanceModel4 AS
                        SELECT rate_model_id, service, overage_unit,
                        overage_fee, overage_per,
                        SUM(ROUND(GREATEST(convert_unit(range_unit, range_to), 0)))
                        AS total_allowance,
                        isoverage
                        FROM TempFirstQueryResult
                        WHERE rate_model_id = 4
                        GROUP BY rate_model_id,service, overage_unit, overage_fee,
                        overage_per, isoverage;

                        DROP TABLE IF EXISTS TotalUsage;
                        CREATE TEMPORARY TABLE TotalUsage AS
                        SELECT mur.service,
                        SUM(COALESCE(mur.volume, 0)) AS total_usage
                        FROM monthly_usage_records AS mur
                        WHERE to_char(
                        date_trunc('month', month_param), 'YYYY-MM') = to_char(
                        date_trunc('month', mur.month), 'YYYY-MM')
                        AND mur.imsi IN (SELECT imsi FROM TempFirstQueryResult)
                        GROUP BY mur.service;

                        INSERT INTO invoice_overage_charge
                        (invoice_id,sim_count,service,total_allowance,
                        total_usage,usage_variance,usage_variance_coverted,total_charge,
                        rate_model_id, overage_fee, overage_per, overage_unit,
                        isoverage, overage_charge)
                        SELECT
                            invocie_id_param,
                            (SELECT distinct_imsi_count FROM DistinctIMSI)
                            AS distinct_imsi_count,
                            ta.service,
                            ta.total_allowance,
                            tu.total_usage,
                            GREATEST(
                            COALESCE(tu.total_usage, 0) - ta.total_allowance, 0)
                            AS usage_difference,
                            ROUND(reverse_convert_unit(ta.overage_unit, GREATEST(
                            COALESCE(tu.total_usage, 0) - ta.total_allowance, 0)),
                            6)
                            AS usage_difference_converted,
                            (CASE
                                WHEN ta.isoverage = true
                                THEN
                                CEIL(
                                GREATEST(
                                    reverse_convert_unit(ta.overage_unit, GREATEST(
                                    COALESCE(
                                    tu.total_usage, 0
                                    ) - ta.total_allowance,
                                    0)) / ta.overage_per, 0)
                                ) * ta.overage_fee
                                ELSE
                                (ta.overage_fee/ta.overage_per/CASE
                                    WHEN ta.service = 'DATA' THEN 1024
                                    WHEN ta.service IN (
                                    'VOICE_MO', 'VOICE_MT') THEN 60
                                    WHEN ta.service IN (
                                    'SMS_MO', 'SMS_MT') THEN 1
                                    ELSE 1
                                END)*
                                (ROUND(reverse_convert_unit(ta.overage_unit, GREATEST(
                            COALESCE(tu.total_usage, 0) - ta.total_allowance, 0)),
                            6)*CASE
                                    WHEN ta.service = 'DATA' THEN 1024
                                    WHEN ta.service IN (
                                    'VOICE_MO', 'VOICE_MT') THEN 60
                                    WHEN ta.service IN (
                                    'SMS_MO', 'SMS_MT') THEN 1
                                    ELSE 1
                                END)
                            END
                            ) AS total_charge,
                        ta.rate_model_id,
                        ta.overage_fee,
                        ta.overage_per,
                        ta.overage_unit,
                        ta.isoverage,
                        0
                        FROM (
                            SELECT * FROM TotalAllowanceModel3
                            UNION ALL
                            SELECT * FROM TotalAllowanceModel4
                        ) AS ta
                        LEFT JOIN TotalUsage tu ON ta.service = tu.service;

                    END;
        $BODY$;

        """
    )
