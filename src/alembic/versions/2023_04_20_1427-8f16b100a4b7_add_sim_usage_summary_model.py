"""add_cdr_data_usage_summary_model

Revision ID: 8f16b100a4b7
Revises: fbb5b7a88859
Create Date: 2023-04-20 14:27:40.647192

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "8f16b100a4b7"
down_revision = "fbb5b7a88859"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "cdr_data_aggregate",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False, start=1, increment=1),
            nullable=False,
        ),
        sa.Column("cdr_uuid", postgresql.UUID(), nullable=False),
        sa.Column("iccid", sa.String(length=20), nullable=False),
        sa.Column("usage", sa.Integer, nullable=False),
        sa.Column("month", sa.Date(), nullable=False),
        sa.ForeignKeyConstraint(
            ["cdr_uuid"],
            ["cdr.uuid"],
            name=op.f("fk_cdr_uuid"),
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("iccid", "month"),
    )


def downgrade() -> None:
    op.drop_table("cdr_data_aggregate")
