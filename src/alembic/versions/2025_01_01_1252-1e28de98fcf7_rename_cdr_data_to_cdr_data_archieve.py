"""rename_cdr_data_to_cdr_data_archieve

Revision ID: 1e28de98fcf7
Revises: c2a7add0a6d9
Create Date: 2025-01-01 12:52:12.063797

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "1e28de98fcf7"
down_revision = "c2a7add0a6d9"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.rename_table("cdr_data", "cdr_data_archieve")
    op.rename_table("cdr_data_new", "cdr_data")
    op.execute(
        """
        DO $$
        DECLARE
            partition_name TEXT;         -- Current partition name
            new_partition_name TEXT;     -- New partition name after replacement
        BEGIN
            FOR partition_name IN
                SELECT inhrelid::regclass::text
                FROM pg_inherits
                WHERE inhparent = 'cdr_data'::regclass
            LOOP
                -- Generate the new partition name
                new_partition_name := replace(
                partition_name,
                'cdr_data_new',
                'cdr_data');

                -- Log the renaming action
                RAISE NOTICE 'Renaming partition % to %',
                partition_name, new_partition_name;

                -- Rename the partition
                EXECUTE format('ALTER TABLE %I RENAME TO %I;',
                            partition_name,
                            new_partition_name);
            END LOOP;

            -- Log completion
            RAISE NOTICE 'All partitions renamed successfully.';
        END $$;


        """
    )


def downgrade() -> None:
    op.rename_table("cdr_data", "cdr_data_new")
    op.execute(
        """
        DO $$
        DECLARE
            partition_name TEXT;         -- Current partition name
            new_partition_name TEXT;     -- New partition name after replacement
        BEGIN
            FOR partition_name IN
                SELECT inhrelid::regclass::text
                FROM pg_inherits
                WHERE inhparent = 'cdr_data_new'::regclass
            LOOP
                -- Generate the new partition name
                new_partition_name := replace(
                partition_name,
                'cdr_data',
                'cdr_data_new');

                -- Log the renaming action
                RAISE NOTICE 'Renaming partition % to %',
                partition_name, new_partition_name;

                -- Rename the partition
                EXECUTE format('ALTER TABLE %I RENAME TO %I;',
                            partition_name,
                            new_partition_name);
            END LOOP;

            -- Log completion
            RAISE NOTICE 'All partitions renamed successfully.';
        END $$;

        """
    )
    op.rename_table("cdr_data_archieve", "cdr_data")
