"""add new field to provider model

Revision ID: 1fc607a5405f
Revises: bec3ba09e3c9
Create Date: 2023-05-15 17:26:47.043016

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "1fc607a5405f"
down_revision = "bec3ba09e3c9"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column(
        "sim_provider_log",
        sa.Column("client_ip", sa.String(), nullable=True),
    )


def downgrade() -> None:
    op.drop_column("sim_provider_log", "client_ip")
