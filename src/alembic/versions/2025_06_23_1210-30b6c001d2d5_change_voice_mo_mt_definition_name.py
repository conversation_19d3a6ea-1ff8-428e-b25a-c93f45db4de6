"""change voice MO MT definition name

Revision ID: 30b6c001d2d5
Revises: a3ff2dd7107a
Create Date: 2025-06-23 12:10:23.658651

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "30b6c001d2d5"
down_revision = "a3ff2dd7107a"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        UPDATE rule_definition
        SET definition = 'Monthly billable voice MO & MT usage threshold'
        WHERE rule_definition_code = 'MBVUT';
        """
    )


def downgrade() -> None:
    op.execute(
        """
        UPDATE rule_definition
        SET definition = 'Monthly billable voice MO MT usage threshold'
        WHERE rule_definition_code = 'MBVUT';
        """
    )
