"""create_rule_category_table

Revision ID: ef8fa30e9662
Revises: 2214e4abd5ef
Create Date: 2024-07-18 15:08:24.923972

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.sql import text

# revision identifiers, used by Alembic.
revision = "ef8fa30e9662"
down_revision = "2214e4abd5ef"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "rule_category",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("rule_type_id", sa.Integer(), nullable=False),
        sa.Column("category", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["rule_type_id"],
            ["rule_type.id"],
            name=op.f("fk_rule_category_rule_type_id_rule_type"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_rule_category")),
        sa.UniqueConstraint("category", name=op.f("uq_rule_category_category")),
    )

    conn = op.get_bind()
    rule_type_id = conn.execute(
        "SELECT id FROM rule_type WHERE rule_type = 'Usage Monitoring' "
    ).scalar_one()

    query = text(
        """
        INSERT INTO rule_category(rule_type_id, category)
        VALUES (:rule_type_id, 'Cycle To Date Data Usage')
    """
    )

    conn.execute(query, {"rule_type_id": rule_type_id})


def downgrade() -> None:
    op.drop_table("rule_category")
