"""add_sim_card_log_model

Revision ID: 4a936c30598e
Revises: 8d49cfd1b588
Create Date: 2023-04-12 18:18:10.754501

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "4a936c30598e"
down_revision = "8d49cfd1b588"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "sim_audit_log",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False, start=1, increment=1),
            nullable=False,
        ),
        sa.Column("uuid", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("reference", sa.String(), nullable=False),
        sa.Column("request_type", sa.String(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("created_by", sa.String(length=128), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
    )


def downgrade() -> None:
    op.drop_table("sim_audit_log")
