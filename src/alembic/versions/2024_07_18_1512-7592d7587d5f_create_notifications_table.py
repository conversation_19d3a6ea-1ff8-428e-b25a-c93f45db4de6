"""create_notifications_table

Revision ID: 7592d7587d5f
Revises: 126718e18009
Create Date: 2024-07-18 15:12:31.991652

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "7592d7587d5f"
down_revision = "126718e18009"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "notifications",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("notification", sa.String(), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_notifications")),
        sa.UniqueConstraint("notification", name=op.f("uq_notifications_notification")),
    )

    # Insert data to the notifications table
    op.execute("""INSERT INTO notifications(notification) VALUES('Send email') """)


def downgrade() -> None:
    op.drop_table("notifications")
