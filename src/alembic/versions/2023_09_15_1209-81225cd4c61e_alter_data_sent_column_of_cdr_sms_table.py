"""alter data sent column of cdr sms table

Revision ID: 81225cd4c61e
Revises: a0cf5f8549a9
Create Date: 2023-09-15 12:09:35.539835

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "81225cd4c61e"
down_revision = "a0cf5f8549a9"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("cdr_sms", "data_sent")
    op.add_column(
        "cdr_sms",
        sa.Column("date_sent", sa.DateTime(), nullable=True),
    )
    op.execute(
        """
        UPDATE cdr_sms
        SET date_sent = cdr.created_at
        FROM cdr
        WHERE cdr_uuid = cdr.uuid
    """
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "cdr_sms",
        sa.Column("data_sent", sa.Integer, nullable=True),
    )
    op.execute("UPDATE cdr_sms SET data_sent = 0")
    op.alter_column("cdr_sms", "data_sent", nullable=False)
    op.drop_column("cdr_sms", "date_sent")
    # ### end Alembic commands ###
