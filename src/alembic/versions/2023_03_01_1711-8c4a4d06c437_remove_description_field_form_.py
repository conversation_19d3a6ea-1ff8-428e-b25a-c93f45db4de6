"""Remove description field form Adjustment model

Revision ID: 8c4a4d06c437
Revises: dc66683d55cf
Create Date: 2023-03-01 17:11:42.532398

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "8c4a4d06c437"
down_revision = "dc66683d55cf"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("adjustment", "description")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "adjustment",
        sa.Column(
            "description",
            sa.VARCHAR(length=255),
            server_default="",
            autoincrement=False,
            nullable=False,
        ),
    )
    # ### end Alembic commands ###
