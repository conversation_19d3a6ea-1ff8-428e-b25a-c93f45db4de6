"""create get_account_summary_count details function

Revision ID: 0cf658516c83
Revises: a11605d7b874
Create Date: 2025-05-19 15:24:36.813054

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "0cf658516c83"
down_revision = "a11605d7b874"
branch_labels = None
depends_on = None


def upgrade() -> None:

    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_account_summary_count(account_id_param INT)
        RETURNS TABLE (
            account_sim_count bigint,
            account_rate_plan_count bigint,
            account_automation_rule_count bigint
        ) AS $$
        BEGIN
            RETURN QUERY
            SELECT
                -- Account Sim card Count
                (
                    SELECT count (*) AS account_sim_count FROM sim_card
                    JOIN allocation ON sim_card.allocation_id  = allocation.id WHERE
                    allocation.account_id = account_id_param
                ) AS account_sim_count,

                -- Account Rate Plan Count
                (
                    SELECT count (*) AS account_rate_plan_count FROM rate_plan
                    WHERE account_id = account_id_param
                ) AS account_rate_plan_count,

                -- Account Rule Count
                (
                    SELECT count (*) AS account_automation_rule_count FROM rules
                    WHERE account_id = account_id_param
                ) AS account_automation_rule_count;

        END;
        $$ LANGUAGE plpgsql;

        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS get_account_summary_count(integer);
        """
    )
