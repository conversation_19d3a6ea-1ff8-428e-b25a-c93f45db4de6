"""add ratemodel into rate_plan_by_accounts with get_allowance_usage function

Revision ID: 2eeeb2bc6ef8
Revises: e424343504d9
Create Date: 2024-12-04 12:47:50.340432

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "2eeeb2bc6ef8"
down_revision = "e424343504d9"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS rate_plan_by_accounts(varchar);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION rate_plan_by_accounts(
            search_term varchar DEFAULT NULL,
            account_ids_param integer[] DEFAULT NULL,
            order_by_column TEXT DEFAULT 'id',
            order_direction TEXT DEFAULT 'DESC'
        )
        RETURNS TABLE (
            account_id integer,
            account_name character varying,
            logo_key character varying,
            id integer,
            name character varying,
            access_fee numeric,
            currency character,
            rate_model_id integer,
            sim_limit integer,
            is_default character varying,
            allowance_used numeric
        )
        LANGUAGE 'plpgsql'
        COST 100
        VOLATILE
        PARALLEL UNSAFE
        ROWS 1000
        AS $BODY$
        DECLARE
            sql_query TEXT;
            final_order_by_column TEXT := COALESCE(order_by_column, 'account_id');
            final_order_direction TEXT := UPPER(COALESCE(order_direction, 'DESC'));
        BEGIN

            sql_query := $$
                WITH RankedRatePlans AS (
                    SELECT
                    a.id AS account_id,
                    a.name AS account_name,
                    a.logo_key,
                    r.id,
                    r.name,
                    r.access_fee,
                    r.sim_limit,
                    a.currency,
                    CASE
                        WHEN rg.rate_model_id IN (3, 4) THEN rg.rate_model_id
                        ELSE NULL
                    END as rate_model_id,
                    COALESCE(allowance.allowance_used, 0) AS allowance_used,
                    ROW_NUMBER() OVER (PARTITION BY a.id ORDER BY r.id) AS row_num
                FROM
                    rate_plan r
                JOIN
                    account a ON r.account_id = a.id
                INNER JOIN
                    rate_group AS rg
                    ON rg.rate_plan_id = r.id
                INNER JOIN
                    rate_group_service AS rgs
                    ON rgs.rate_group_id = rg.id
                    AND rgs.service = 'DATA'
                LEFT JOIN
                    get_allowance_usage($1) AS allowance
                    ON allowance.rate_plan_id = r.id
                WHERE
                    (
                        $1 IS NULL
                        OR r.account_id = ANY($1)
                    )
                    AND (
                        $2 IS NULL OR (
                        a.name ILIKE '%' || $2 || '%' OR
                        r.name ILIKE '%' || $2 || '%' OR
                        r.access_fee::text ILIKE '%' || $2 || '%'
                        )
                    )
                GROUP BY
                    a.id, r.id, allowance.allowance_used, rg.rate_model_id
            )
            SELECT
                rp.account_id,
                rp.account_name,
                rp.logo_key,
                rp.id,
                rp.name,
                rp.access_fee,
                rp.currency,
                rp.rate_model_id,
                rp.sim_limit,
                (CASE WHEN rp.row_num = 1 THEN 'True' ELSE 'False' END
                )::character varying AS is_default,
                COALESCE(rp.allowance_used, 0) AS allowance_used
            FROM
                RankedRatePlans rp
            $$;

            sql_query := sql_query || ' ORDER BY ' ||
            quote_ident(final_order_by_column) || ' ' || final_order_direction;

            -- Execute the dynamic query and return the results
            RETURN QUERY EXECUTE sql_query USING account_ids_param, search_term;
        END;
        $BODY$;


        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS rate_plan_by_accounts(varchar, integer[], text,
        text, integer, integer);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION rate_plan_by_accounts(
            search_term varchar DEFAULT NULL
        )
        RETURNS TABLE (
            account_id integer,
            account_name character varying,
            logo_key character varying,
            id integer,
            name character varying,
            access_fee numeric,
            currency character,
            is_default character varying,
            allowance_used numeric
        )
        LANGUAGE 'plpgsql'
        COST 100
        VOLATILE
        PARALLEL UNSAFE
        ROWS 1000
        AS $BODY$
        BEGIN
            RETURN QUERY
                WITH RankedRatePlans AS (
                    SELECT
                        a.id AS account_id,
                        a.name AS account_name,
                        a.logo_key,
                        r.id,
                        r.name,
                        r.access_fee,
                        a.currency,
                        ROW_NUMBER() OVER (PARTITION BY a.id ORDER BY r.id) AS row_num
                    FROM
                        rate_plan r
                    JOIN
                        account a ON r.account_id = a.id
                    WHERE
                        search_term IS NULL OR (
                            a.name ILIKE '%' || search_term || '%' OR
                            r.name ILIKE '%' || search_term || '%' OR
                            r.access_fee::text ILIKE '%' || search_term || '%'
                        )
                    GROUP BY
                        a.id, r.id
                ),
                AllowancesData AS (
                    SELECT
                        gau.account_id,
                        gau.rate_plan_id,
                        gau.allowance_used
                    FROM
                        get_allowance_usage() AS gau
                )
            SELECT
                rp.account_id,
                rp.account_name,
                rp.logo_key,
                rp.id,
                rp.name,
                rp.access_fee,
                rp.currency,
                (CASE WHEN rp.row_num = 1 THEN 'True' ELSE 'False' END
                )::character varying AS is_default,
                COALESCE(ad.allowance_used, 0) AS allowance_used
            FROM
                RankedRatePlans rp
            LEFT JOIN
                AllowancesData ad ON ad.account_id = rp.account_id
                AND ad.rate_plan_id = rp.id
            ORDER BY
                rp.account_name ASC, rp.id ASC;
        END;
        $BODY$;


        """
    )
