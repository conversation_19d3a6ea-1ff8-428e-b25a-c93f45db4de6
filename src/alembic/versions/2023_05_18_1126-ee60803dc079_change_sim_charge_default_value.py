"""change sim_charge default value

Revision ID: ee60803dc079
Revises: d9e3bf2ff131
Create Date: 2023-05-18 11:26:12.325764

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "ee60803dc079"
down_revision = "d9e3bf2ff131"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("""UPDATE account SET sim_charge = '2' WHERE sim_charge = '0'""")
    op.alter_column(
        "account",
        "sim_charge",
        existing_type=sa.Numeric(),
        server_default="2",
        nullable=False,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute("""UPDATE account SET sim_charge = '0' WHERE sim_charge = '2'""")
    op.alter_column(
        "account",
        "sim_charge",
        existing_type=sa.Numeric(),
        server_default="0",
        nullable=False,
    )
    # ### end Alembic commands ###
