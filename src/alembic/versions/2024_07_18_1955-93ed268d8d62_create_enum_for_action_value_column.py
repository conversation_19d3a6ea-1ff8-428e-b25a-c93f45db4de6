"""create enum for action value column

Revision ID: 93ed268d8d62
Revises: 832f96f33f1b
Create Date: 2024-07-18 10:58:43.673932

"""
import sqlalchemy as sa
from alembic import op

from automation.domain.model import ActionValue

# revision identifiers, used by Alembic.
revision = "93ed268d8d62"
down_revision = "832f96f33f1b"
branch_labels = None
depends_on = None


def upgrade() -> None:
    unit = sa.Enum(ActionValue, name="action_value")
    unit.create(op.get_bind(), checkfirst=True)


def downgrade() -> None:
    unit = sa.Enum(ActionValue, name="action_value", checkfirst=True)
    unit.drop(op.get_bind())
