"""create invoice_account_charge table

Revision ID: 3db42825d5a8
Revises: 4fc503e96681
Create Date: 2024-11-04 16:32:32.517203

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "3db42825d5a8"
down_revision = "4fc503e96681"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "invoice_account_charge",
        sa.<PERSON>umn(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("invoice_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("rate_plan_id", sa.Integer(), nullable=False),
        sa.Column(
            "account_charge",
            sa.Numeric(),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["invoice_id"],
            ["invoice.id"],
            name=op.f("fk_invoice_account_charge_invoice_id_invoice"),
        ),
        sa.ForeignKeyConstraint(
            ["rate_plan_id"],
            ["rate_plan.id"],
            name=op.f("fk_invoice_account_charge_rate_plan_id_rate_plan"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_invoice_account_charge")),
    )


def downgrade() -> None:
    op.drop_table("invoice_account_charge")
