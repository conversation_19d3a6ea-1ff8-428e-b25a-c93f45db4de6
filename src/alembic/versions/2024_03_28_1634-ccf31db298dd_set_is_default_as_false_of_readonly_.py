"""set is_default as false of ReadOnly roles

Revision ID: ccf31db298dd
Revises: 1b26de4c8353
Create Date: 2024-03-28 16:34:07.215841

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "ccf31db298dd"
down_revision = "1b26de4c8353"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Update existing rows to set is_default = false
    op.execute(
        "UPDATE user_role SET is_default = false "
        "WHERE role IN ('Distributor_ReadOnly', 'Client_ReadOnly');"
    )


def downgrade() -> None:
    # Update existing rows to set is_default = true
    op.execute(
        "UPDATE user_role SET is_default = true "
        "WHERE role IN ('Distributor_ReadOnly', 'Client_ReadOnly');"
    )
