"""add carrier foreign key constraint

Revision ID: 177eeff1e1ed
Revises: 0e8f53c9e978
Create Date: 2023-07-07 10:08:21.699105

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "177eeff1e1ed"
down_revision = "0e8f53c9e978"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key(
        "fk_cdr_carrier",
        "cdr",
        "carrier_name",
        ["carrier"],
        ["carrier"],
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("fk_cdr_carrier", "cdr", type_="foreignkey")
    # ### end Alembic commands ###
