"""add_order_id_column

Revision ID: 9084abb52d93
Revises: 33e616e7dd03
Create Date: 2025-07-07 14:57:02.997812

"""

import sqlalchemy as sa
from alembic import op

revision = "9084abb52d93"
down_revision = "33e616e7dd03"
branch_labels = None
depends_on = None


def upgrade():
    op.execute(
        "CREATE SEQUENCE IF NOT EXISTS orders.order_id_seq START WITH 1 INCREMENT BY 1"
    )

    op.add_column(
        "orders",
        sa.Column("order_id", sa.String(length=20), nullable=True, unique=True),
        schema="orders",
    )
    op.execute(
        """
        UPDATE orders.orders
        SET order_id = 'ORD-' || LPAD(nextval('orders.order_id_seq')::text, 6, '0')
        WHERE order_id IS NULL
    """
    )
    op.alter_column("orders", "order_id", nullable=False, schema="orders")


def downgrade():
    op.drop_column("orders", "order_id", schema="orders")
    op.execute("DROP SEQUENCE IF EXISTS orders.order_id_seq")
