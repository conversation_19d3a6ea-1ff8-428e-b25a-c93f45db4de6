"""add voice_sms percentage unit types

Revision ID: 6a591dbe2ec8
Revises: a0d4718b184a
Create Date: 2025-05-30 09:27:01.619798

"""
import sqlalchemy as sa
from alembic import op

from common.types import Unit

# revision identifiers, used by Alembic.
revision = "6a591dbe2ec8"
down_revision = "a0d4718b184a"
branch_labels = None
depends_on = None


def upgrade() -> None:

    conn = op.get_bind()
    query = sa.text(
        """
        DO $$
        BEGIN
            IF NOT EXISTS (
                SELECT 1 FROM pg_enum
                WHERE enumtypid = 'unit'::regtype
                AND enumlabel = :percentage_value
            ) THEN
                ALTER TYPE unit ADD VALUE :percentage_value;
            END IF;
        END $$;
    """
    )

    conn.execute(query, {"percentage_value": Unit.VOICE_MO_PERCENTAGE.value})
    conn.execute(query, {"percentage_value": Unit.VOICE_MT_PERCENTAGE.value})
    conn.execute(query, {"percentage_value": Unit.VOICE_MOMT_PERCENTAGE.value})
    conn.execute(query, {"percentage_value": Unit.SMS_MO_PERCENTAGE.value})


def downgrade() -> None:
    pass
