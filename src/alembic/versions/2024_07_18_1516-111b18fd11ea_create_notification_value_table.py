"""create_notification_value_table

Revision ID: 111b18fd11ea
Revises: 5ca57a045d2a
Create Date: 2024-07-18 15:16:43.585073

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "111b18fd11ea"
down_revision = "5ca57a045d2a"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "notification_value",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("rules_notification_id", sa.Integer(), nullable=False),
        sa.Column("notification_value", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["rules_notification_id"],
            ["rules_notification.id"],
            name=op.f("fk_notification_value_rules_notification_id_rules_notification"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_notification_value")),
    )


def downgrade() -> None:
    op.drop_table("notification_value")
