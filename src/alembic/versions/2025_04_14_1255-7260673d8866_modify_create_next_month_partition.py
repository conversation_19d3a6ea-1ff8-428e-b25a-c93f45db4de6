"""modify create_next_month_partition

Revision ID: 7260673d8866
Revises: e955a8172f22
Create Date: 2025-04-14 12:55:39.275438

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "7260673d8866"
down_revision = "e955a8172f22"
branch_labels = None
depends_on = None


def upgrade() -> None:

    op.execute(
        """
        DROP FUNCTION IF EXISTS create_next_month_partition(TEXT);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION create_next_month_partition(
        p_table_name TEXT,
        p_yyyy_month TEXT DEFAULT NULL
        )
        RETURNS TEXT AS $$
        DECLARE
            v_next_month TEXT;
            v_partition_table_name TEXT;
            v_partition_exists BOOLEAN;
            v_start_date DATE;
            v_end_date DATE;
        BEGIN
            -- Determine the start and end date
            IF p_yyyy_month IS NOT NULL THEN
                -- Use the provided yyyy_month value
                v_start_date := TO_DATE(p_yyyy_month, 'YYYY_MM');
                v_end_date := v_start_date + INTERVAL '1 month';
            ELSE
                -- Default to the next month
                v_start_date := DATE_TRUNC('month', NOW() + INTERVAL '1 month');
                v_end_date := DATE_TRUNC('month', NOW() + INTERVAL '2 months');
            END IF;

            -- Create the partition table name
            v_next_month := TO_CHAR(v_start_date, 'YYYY_MM');
            v_partition_table_name := p_table_name || '_' || v_next_month;

            -- Check if the partition already exists
            SELECT EXISTS (
                SELECT FROM pg_class
                WHERE relname = v_partition_table_name
            ) INTO v_partition_exists;

            IF v_partition_exists THEN
                -- Return message when partition already exists
                RETURN 'Partition already exists: ' || v_partition_table_name;
            ELSE
                -- Create the partition for the specified month
                EXECUTE format('
                    CREATE TABLE %I PARTITION OF %I
                    FOR VALUES FROM (%L) TO (%L)',
                    v_partition_table_name, p_table_name,
                    v_start_date, v_end_date
                );

                -- Return message when partition is created
                RETURN 'Partition created: ' || v_partition_table_name;
            END IF;
        EXCEPTION WHEN OTHERS THEN
            -- Handle any errors and return a message
            RETURN 'An error occurred while creating partition: ' || SQLERRM;
        END;
        $$ LANGUAGE plpgsql;
        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS create_next_month_partition(TEXT, TEXT);
        """
    )

    op.execute(
        """
            CREATE OR REPLACE FUNCTION create_next_month_partition(p_table_name text)
            RETURNS TEXT AS $$
            DECLARE
                v_next_month TEXT;
                v_partition_table_name TEXT;
                v_partition_exists BOOLEAN;
                v_start_date DATE;
                v_end_date DATE;
            BEGIN
                -- Calculate start and end dates for the next month
                v_start_date := DATE_TRUNC('month', NOW() + INTERVAL '1 month');
                v_end_date := DATE_TRUNC('month', NOW() + INTERVAL '2 months');

                -- Create the partition table name
                v_next_month := TO_CHAR(v_start_date, 'YYYY_MM');
                v_partition_table_name := p_table_name || '_' || v_next_month;

                -- Check if the partition already exists
                SELECT EXISTS (
                    SELECT FROM pg_class
                    WHERE relname = v_partition_table_name
                ) INTO v_partition_exists;

                IF v_partition_exists THEN
                    -- Return message when partition already exists
                    RETURN 'Partition already exists: ' || v_partition_table_name;
                ELSE
                    -- Create the partition for the next month
                    EXECUTE format('
                        CREATE TABLE %I PARTITION OF %I
                        FOR VALUES FROM (%L) TO (%L)',
                        v_partition_table_name, p_table_name,
                        v_start_date, v_end_date
                    );

                    -- Return message when partition is created
                    RETURN 'Partition created: ' || v_partition_table_name;
                END IF;
            EXCEPTION WHEN OTHERS THEN
                -- Handle any errors and return a message
                RETURN 'An error occurred while creating partition: ' || SQLERRM;
            END;
            $$ LANGUAGE plpgsql;
            """
    )
