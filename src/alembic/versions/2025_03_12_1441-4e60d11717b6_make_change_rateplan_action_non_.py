"""make change rateplan action non-mendatory in rule rely

Revision ID: 4e60d11717b6
Revises: db03fd62475b
Create Date: 2025-03-12 14:41:21.460217

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "4e60d11717b6"
down_revision = "db03fd62475b"
branch_labels = None
depends_on = None


def upgrade():
    conn = op.get_bind()

    # Get the category ID for "Monthly Pooled Data Usage"
    result = conn.execute(
        sa.text("SELECT id FROM rule_category WHERE rule_category_code = :name"),
        {"name": "MPDU"},
    )
    category_id = result.scalar()

    if category_id:
        # Update the required_rate_plan_change value to false
        conn.execute(
            sa.text(
                """
                UPDATE rule_details_rely
                SET required_rate_plan_change = false
                WHERE rule_category_id = :category_id
                """
            ),
            {"category_id": category_id},
        )


def downgrade():
    conn = op.get_bind()

    result = conn.execute(
        sa.text("SELECT id FROM rule_category WHERE rule_category_code = :name"),
        {"name": "MPDU"},
    )
    category_id = result.scalar()

    if category_id:
        conn.execute(
            sa.text(
                """
                UPDATE rule_details_rely
                SET required_rate_plan_change = true
                WHERE rule_category_id = :category_id
                """
            ),
            {"category_id": category_id},
        )
