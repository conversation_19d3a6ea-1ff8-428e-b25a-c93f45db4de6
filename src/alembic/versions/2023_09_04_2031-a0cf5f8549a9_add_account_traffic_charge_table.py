"""add account traffic charge table

Revision ID: a0cf5f8549a9
Revises: e5e98a66a719
Create Date: 2023-09-04 20:31:30.065377

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "a0cf5f8549a9"
down_revision = "e5e98a66a719"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "account_traffic_charges",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.Column("carrier_name", sa.String(), nullable=False),
        sa.Column(
            "threshold_charge",
            sa.Numeric(precision=18, scale=5),
            server_default="0.003",
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["account_id"],
            ["account.id"],
            name=op.f("fk_account_traffic_charges_account_id_account"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_account_traffic_charges")),
    )
    op.execute(
        """INSERT INTO account_traffic_charges \
        (account_id, carrier_name, threshold_charge) \
        SELECT id, 'EE', 0.003 FROM account;"""
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("account_traffic_charges")
    # ### end Alembic commands ###
