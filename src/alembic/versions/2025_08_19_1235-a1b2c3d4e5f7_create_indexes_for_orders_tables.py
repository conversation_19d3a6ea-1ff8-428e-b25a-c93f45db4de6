"""create_indexes_for_orders_tables

Revision ID: a1b2c3d4e5f7
Revises: 9084abb52d93
Create Date: 2025-08-19 12:35:00.000000

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "a1b2c3d4e5f7"
down_revision = "9084abb52d93"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create indexes for orders table
    op.execute(
        """
        CREATE INDEX IF NOT EXISTS idx_orders_order_date ON orders.orders (order_date);
        """
    )

    op.execute(
        """
        CREATE INDEX IF NOT EXISTS idx_orders_order_by ON orders.orders (order_by);
        """
    )

    # Create index for customers table
    op.execute(
        "CREATE INDEX IF NOT EXISTS idx_customers_customer_account_id "
        "ON orders.customers (customer_account_id);"
    )

    # Create index for shipping_details table
    op.execute(
        "CREATE INDEX IF NOT EXISTS idx_shipping_details_order_id "
        "ON orders.shipping_details (order_id);"
    )

    # Create index for tracking table
    op.execute(
        """
        CREATE INDEX IF NOT EXISTS idx_tracking_order_id ON orders.tracking (order_id);
        """
    )

    # Create index for items table
    op.execute(
        """
        CREATE INDEX IF NOT EXISTS idx_items_sim_type ON orders.items (sim_type);
        """
    )


def downgrade() -> None:
    # Drop indexes in reverse order
    op.execute("DROP INDEX IF EXISTS orders.idx_items_sim_type")
    op.execute("DROP INDEX IF EXISTS orders.idx_tracking_order_id")
    op.execute("DROP INDEX IF EXISTS orders.idx_shipping_details_order_id")
    op.execute("DROP INDEX IF EXISTS orders.idx_customers_customer_account_id")
    op.execute("DROP INDEX IF EXISTS orders.idx_orders_order_by")
    op.execute("DROP INDEX IF EXISTS orders.idx_orders_order_date")
