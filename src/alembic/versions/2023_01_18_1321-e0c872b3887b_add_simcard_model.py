"""Add SIMCard model

Revision ID: e0c872b3887b
Revises: d78b983a0d5a
Create Date: 2023-01-18 13:21:19.020332

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "e0c872b3887b"
down_revision = "d78b983a0d5a"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "sim_card",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("range_id", sa.Integer(), nullable=False),
        sa.Column("iccid", sa.String(length=20), nullable=False),
        sa.Column("imsi", sa.String(length=15), nullable=False),
        sa.Column("msisdn", sa.String(length=15), nullable=False),
        sa.Column("pin_1", sa.String(length=8), nullable=True),
        sa.Column("pin_2", sa.String(length=8), nullable=True),
        sa.Column("puk_1", sa.String(length=8), nullable=True),
        sa.Column("puk_2", sa.String(length=8), nullable=True),
        sa.ForeignKeyConstraint(
            ["range_id"], ["range.id"], name=op.f("fk_sim_card_range_id_range")
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_sim_card")),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("sim_card")
    # ### end Alembic commands ###
