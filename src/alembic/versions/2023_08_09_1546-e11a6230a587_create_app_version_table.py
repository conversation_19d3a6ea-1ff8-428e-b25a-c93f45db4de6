"""create app_version table

Revision ID: e11a6230a587
Revises: 235d536c4891
Create Date: 2023-08-09 15:46:38.631055

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "e11a6230a587"
down_revision = "235d536c4891"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "app_version",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False, start=1, increment=1),
            nullable=False,
        ),
        sa.Column("name", sa.String(length=15), nullable=False),
        sa.Column("version", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_app_version")),
    )


def downgrade() -> None:
    op.drop_table("app_version")
