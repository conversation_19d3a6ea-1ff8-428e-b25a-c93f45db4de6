"""create_rule_type_table

Revision ID: 2214e4abd5ef
Revises: 63712ac285d3
Create Date: 2024-07-18 15:05:07.996752

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.sql import column, table

# revision identifiers, used by Alembic.
revision = "2214e4abd5ef"
down_revision = "63712ac285d3"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "rule_type",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("rule_type", sa.String(), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_rule_type")),
        sa.UniqueConstraint("rule_type", name=op.f("uq_rule_type_rule_type")),
    )

    rule_type = table(
        "rule_type",
        column("rule_type", sa.String),
    )

    op.bulk_insert(
        rule_type,
        [
            {"rule_type": "Usage Monitoring"},
        ],
    )


def downgrade() -> None:
    op.drop_table("rule_type")
