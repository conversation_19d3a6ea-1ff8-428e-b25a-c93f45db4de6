"""get_actions_by_rules_uuid

Revision ID: 9e46552f0afd
Revises: 5669731cad9e
Create Date: 2024-07-18 12:24:34.410819

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "9e46552f0afd"
down_revision = "5669731cad9e"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
            CREATE OR REPLACE FUNCTION get_actions_by_rules_uuid(
                rules_uuid_param UUID[])
                RETURNS TABLE (
                    name character varying,
                    action rule_action,
                    action_value character varying,
                    rules_uuid UUID
                )
                LANGUAGE 'plpgsql'
                COST 100
                VOLATILE PARALLEL UNSAFE
                ROWS 1000
            AS $BODY$
            BEGIN
                RETURN QUERY
                SELECT
                    act.action AS name,
                    ra.action AS action,
                    ra.action_value AS action_value,
                    ra.rules_uuid As rules_uuid
                FROM
                    rules_action ra
                JOIN
                    actions act ON act.id = ra.actions_id
                WHERE
                    ra.rules_uuid = ANY(rules_uuid_param);
            END;
            $BODY$;
        """
    )


def downgrade() -> None:
    op.execute(
        """
            DROP FUNCTION IF EXISTS get_actions_by_rules_uuid(rules_uuid_param UUID[]);
    """
    )
