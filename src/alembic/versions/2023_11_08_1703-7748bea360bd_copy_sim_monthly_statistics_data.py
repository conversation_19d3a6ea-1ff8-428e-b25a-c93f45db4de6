"""copy sim monthly statistics data

Revision ID: 7748bea360bd
Revises: 81225cd4c61e
Create Date: 2023-11-08 17:03:41.413288

"""

from datetime import datetime

from alembic import op
from sqlalchemy.sql import text

from common.types import Month

# revision identifiers, used by Alembic.
revision = "7748bea360bd"
down_revision = "81225cd4c61e"
branch_labels = None
depends_on = None


def upgrade() -> None:

    today = datetime.today()
    year = today.year
    month = today.month
    current_month = Month(year=year, month=month, day=1)
    prev_month = Month(year=year - (month == 1), month=(month - 2) % 12 + 1, day=1)

    query = text(
        """
            INSERT INTO sim_monthly_statistic
                (sim_card_id, month, sim_status, is_first_activation)
            SELECT sim_card_id, :current_month AS month,
                sim_status, is_first_activation
            FROM sim_monthly_statistic
            WHERE month = :prev_month
            AND NOT EXISTS (
                SELECT inn.id
                FROM sim_monthly_statistic AS inn
                WHERE inn.month = :current_month
                    AND inn.sim_card_id = sim_monthly_statistic.sim_card_id
            )
        """
    )

    connection = op.get_bind()
    connection.execute(
        query,
        {"current_month": current_month, "prev_month": prev_month},
    )


def downgrade() -> None:
    pass
