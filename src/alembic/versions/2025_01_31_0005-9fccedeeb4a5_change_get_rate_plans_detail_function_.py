"""change get_rate_plans_detail_function for ordering and get proper response

Revision ID: 9fccedeeb4a5
Revises: c929605fce2b
Create Date: 2025-01-31 00:05:05.782280

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "9fccedeeb4a5"
down_revision = "c929605fce2b"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS get_rate_plans_detail(integer[], varchar);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_rate_plans_detail(
            account_ids_param integer[] DEFAULT NULL,
            search_term varchar DEFAULT NULL
        )
        RETURNS TABLE (
            account_id integer,
            account_name character varying,
            logo_key character varying,
            id integer,
            name character varying,
            access_fee numeric,
            currency character,
            rate_model_id integer,
            rate_plan_model integer,
            sim_limit integer,
            range_from integer,
            range_to numeric,
            value numeric,
            range_unit unit,
            price_unit unit,
            overage_fee numeric,
            overage_unit unit,
            isoverage bool,
            overage_per integer,
            service service,
            is_default bool,
            allowance_used numeric
        )
        LANGUAGE 'plpgsql'
        COST 100
        VOLATILE
        PARALLEL UNSAFE
        ROWS 1000
        AS $BODY$
        BEGIN
            RETURN QUERY
                WITH RankedRatePlans AS (
                    SELECT
                    a.id AS account_id,
                    a.name AS account_name,
                    a.logo_key,
                    r.id,
                    r.name,
                    r.access_fee,
                    r.sim_limit,
                    a.currency,
                    CASE
                        WHEN rg.rate_model_id IN (3, 4)
                        AND rgs.service = 'DATA' THEN rg.rate_model_id
                        ELSE NULL
                    END as rate_plan_model,
                    rg.rate_model_id,
                    ra.range_from,
                    ra.range_to,
                    ra.value,
                    ra.range_unit,
                    ra.price_unit,
                    ra.overage_fee,
                    ra.overage_unit,
                    ra.isoverage,
                    ra.overage_per,
                    rgs.service,
                    COALESCE(allowance.allowance_used, 0) AS allowance_used,
                    r.is_default
                FROM
                    rate_plan r
                JOIN
                    account a ON r.account_id = a.id
                INNER JOIN
                    rate_group AS rg
                    ON rg.rate_plan_id = r.id
                INNER JOIN
                    rate_group_service AS rgs
                    ON rgs.rate_group_id = rg.id
                LEFT JOIN
                    get_allowance_usage(account_ids_param) AS allowance
                    ON allowance.rate_plan_id = r.id
                LEFT JOIN
                    rate as ra
                    ON ra.rate_group_id = rg.id
                WHERE
                    (
                        account_ids_param IS NULL
                        OR r.account_id = ANY(account_ids_param)
                    )
                    AND
                    (
                        search_term IS NULL OR (
                        a.name ILIKE '%' || search_term || '%' OR
                        r.name ILIKE '%' || search_term || '%' OR
                        r.access_fee::text ILIKE '%' || search_term || '%'
                        )
                    )
                GROUP BY
                    a.id,
                    r.id,
                    allowance.allowance_used,
                    rg.rate_model_id,
                    ra.range_from,
                    ra.range_to,
                    ra.value,
                    ra.range_unit,
                    ra.price_unit,
                    ra.overage_fee,
                    ra.overage_unit,
                    ra.isoverage,
                    ra.overage_per,
                    rgs.service
            )
            SELECT
                rp.account_id,
                rp.account_name,
                rp.logo_key,
                rp.id,
                rp.name,
                rp.access_fee,
                rp.currency,
                rp.rate_model_id,
                rp.rate_plan_model,
                rp.sim_limit,
                rp.range_from,
                rp.range_to,
                rp.value,
                rp.range_unit,
                rp.price_unit,
                rp.overage_fee,
                rp.overage_unit,
                rp.isoverage,
                rp.overage_per,
                rp.service,
                rp.is_default,
                COALESCE(rp.allowance_used, 0) AS allowance_used
            FROM
                RankedRatePlans rp
            ORDER BY
                rp.account_id,
                rp.is_default DESC,
                rp.id,
                rp.range_from ASC NULLS FIRST,
                rp.range_to ASC NULLS LAST;
        END;
        $BODY$;


        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS get_rate_plans_detail(integer[], varchar);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_rate_plans_detail(
            account_ids_param integer[] DEFAULT NULL,
            search_term varchar DEFAULT NULL
        )
        RETURNS TABLE (
            account_id integer,
            account_name character varying,
            logo_key character varying,
            id integer,
            name character varying,
            access_fee numeric,
            currency character,
            rate_model_id integer,
            rate_plan_model integer,
            sim_limit integer,
            range_from integer,
            range_to numeric,
            value numeric,
            range_unit unit,
            price_unit unit,
            overage_fee numeric,
            overage_unit unit,
            isoverage bool,
            overage_per integer,
            service service,
            is_default character varying,
            allowance_used numeric
        )
        LANGUAGE 'plpgsql'
        COST 100
        VOLATILE
        PARALLEL UNSAFE
        ROWS 1000
        AS $BODY$
        BEGIN
            RETURN QUERY
                WITH RankedRatePlans AS (
                    SELECT
                    a.id AS account_id,
                    a.name AS account_name,
                    a.logo_key,
                    r.id,
                    r.name,
                    r.access_fee,
                    r.sim_limit,
                    a.currency,
                    CASE
                        WHEN rg.rate_model_id IN (3, 4)
                        AND rgs.service = 'DATA' THEN rg.rate_model_id
                        ELSE NULL
                    END as rate_plan_model,
                    rg.rate_model_id,
                    ra.range_from,
                    ra.range_to,
                    ra.value,
                    ra.range_unit,
                    ra.price_unit,
                    ra.overage_fee,
                    ra.overage_unit,
                    ra.isoverage,
                    ra.overage_per,
                    rgs.service,
                    COALESCE(allowance.allowance_used, 0) AS allowance_used,
                    ROW_NUMBER() OVER (PARTITION BY a.id ORDER BY r.id) AS row_num
                FROM
                    rate_plan r
                JOIN
                    account a ON r.account_id = a.id
                INNER JOIN
                    rate_group AS rg
                    ON rg.rate_plan_id = r.id
                INNER JOIN
                    rate_group_service AS rgs
                    ON rgs.rate_group_id = rg.id
                LEFT JOIN
                    get_allowance_usage(account_ids_param) AS allowance
                    ON allowance.rate_plan_id = r.id
                LEFT JOIN
                    rate as ra
                    ON ra.rate_group_id = rg.id
                WHERE
                    (
                        account_ids_param IS NULL
                        OR r.account_id = ANY(account_ids_param)
                    )
                    AND
                    (
                        search_term IS NULL OR (
                        a.name ILIKE '%' || search_term || '%' OR
                        r.name ILIKE '%' || search_term || '%' OR
                        r.access_fee::text ILIKE '%' || search_term || '%'
                        )
                    )
                GROUP BY
                    a.id,
                    r.id,
                    allowance.allowance_used,
                    rg.rate_model_id,
                    ra.range_from,
                    ra.range_to,
                    ra.value,
                    ra.range_unit,
                    ra.price_unit,
                    ra.overage_fee,
                    ra.overage_unit,
                    ra.isoverage,
                    ra.overage_per,
                    rgs.service
            )
            SELECT
                rp.account_id,
                rp.account_name,
                rp.logo_key,
                rp.id,
                rp.name,
                rp.access_fee,
                rp.currency,
                rp.rate_model_id,
                rp.rate_plan_model,
                rp.sim_limit,
                rp.range_from,
                rp.range_to,
                rp.value,
                rp.range_unit,
                rp.price_unit,
                rp.overage_fee,
                rp.overage_unit,
                rp.isoverage,
                rp.overage_per,
                rp.service,
                (CASE WHEN rp.row_num = 1 THEN 'True' ELSE 'False' END
                )::character varying AS is_default,
                COALESCE(rp.allowance_used, 0) AS allowance_used
            FROM
                RankedRatePlans rp;
        END;
        $BODY$;


        """
    )
