"""Add sim_charge field for Account table as Billing Setting

Revision ID: f6f7a73e5ecc
Revises: 5d0eebed5365
Create Date: 2023-04-12 21:18:59.827120

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "f6f7a73e5ecc"
down_revision = "5d0eebed5365"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "account",
        sa.Column("sim_charge", sa.Numeric(), server_default="0", nullable=False),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("account", "sim_charge")
    # ### end Alembic commands ###
