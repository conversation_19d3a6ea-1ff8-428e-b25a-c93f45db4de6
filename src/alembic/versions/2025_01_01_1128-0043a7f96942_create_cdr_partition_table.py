"""create_cdr_partition_table

Revision ID: 0043a7f96942
Revises: 4f36ad3476e9
Create Date: 2025-01-01 11:28:41.740874

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "0043a7f96942"
down_revision = "4f36ad3476e9"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "cdr_new",
        sa.Column(
            "id",
            sa.Integer,
            sa.Identity(always=False, start=1, increment=1),
            primary_key=True,
            autoincrement=True,
        ),
        sa.Column("uuid", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("iccid", sa.String(20), nullable=False),
        sa.Column("country", sa.String(3), nullable=False),
        sa.Column("carrier", sa.String(5), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(),
            primary_key=True,
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column("imsi", sa.String(15)),
        sa.Column("country_name", sa.String),
        sa.Column("cdr_object_id", sa.String(25)),
        sa.PrimaryKeyConstraint("id", "created_at", name="pk_cdr_new"),
        sa.ForeignKeyConstraint(
            ["carrier"], ["public.carrier_name.carrier"], name="fk_cdr_carrier"
        ),
        postgresql_partition_by="RANGE (created_at)",
    )

    # Creating partitions for the CDR Table.
    op.execute(
        """
        DO $$
        DECLARE
            start_date DATE;
            end_date DATE := date_trunc('month',
            current_date + interval '1 month')::DATE;
            partition_date DATE;
            partition_name TEXT;
        BEGIN
            -- Select the smallest created_date from the cdr table
            -- and set it to the first day of its month
            SELECT date_trunc('month', MIN(created_at))::DATE INTO start_date FROM cdr;

            partition_date := start_date;
            WHILE partition_date < end_date LOOP
                partition_name := 'cdr_new_' || to_char(partition_date, 'YYYY_MM');
                IF NOT EXISTS (SELECT 1 FROM information_schema.tables
                       WHERE table_name = partition_name) THEN
                    -- Dynamically create the partition table
                    EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF cdr_new
                                    FOR VALUES FROM (%L) TO (%L)',
                                    partition_name,
                                    partition_date::DATE,
                                    (partition_date + interval '1 month')::DATE);
                END IF;
                partition_date := partition_date + interval '1 month';
            END LOOP;
        END $$;

        """
    )

    # Creating index for the cdr_new Table.
    op.create_index("idx_cdr_new_created_at", "cdr_new", ["created_at"])
    op.create_index("idx_cdr_new_imsi", "cdr_new", ["imsi"])


def downgrade() -> None:

    op.drop_table("cdr_new")
