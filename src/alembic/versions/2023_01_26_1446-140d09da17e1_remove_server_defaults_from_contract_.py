"""Remove server defaults from contract_end_date and is_billible fields of Account

Revision ID: 140d09da17e1
Revises: 35c9db733b24
Create Date: 2023-01-26 14:46:03.150873

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "140d09da17e1"
down_revision = "35c9db733b24"
branch_labels = None
depends_on = ["35c9db733b24", "2a666b543763"]


def upgrade() -> None:
    op.alter_column("account", "contract_end_date", server_default=None)
    op.alter_column("account", "is_billable", server_default=None)


def downgrade() -> None:
    op.alter_column(
        "account", "contract_end_date", server_default=sa.text("'2023-12-31'")
    )
    op.alter_column("account", "is_billable", server_default=sa.text("false"))
