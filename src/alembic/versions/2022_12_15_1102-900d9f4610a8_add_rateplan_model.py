"""Add RatePlan model

Revision ID: 900d9f4610a8
Revises: 04347c954777
Create Date: 2022-12-15 11:02:51.589942

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "900d9f4610a8"
down_revision = "04347c954777"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "rate_plan",
        sa.Column("id", sa.Integer(), sa.Identity(always=False), nullable=False),
        sa.Column("account_id", sa.Integer(), nullable=True),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("access_fee", sa.Numeric(), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_rate_plan")),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("rate_plan")
    # ### end Alembic commands ###
