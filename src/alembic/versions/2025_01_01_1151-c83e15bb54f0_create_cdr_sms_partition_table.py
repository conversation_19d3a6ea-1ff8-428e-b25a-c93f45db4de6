"""create_cdr_sms_partition_table

Revision ID: c83e15bb54f0
Revises: 8e2ddc34e831
Create Date: 2025-01-01 11:51:13.189819

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "c83e15bb54f0"
down_revision = "8e2ddc34e831"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "cdr_sms_new",
        sa.Column(
            "id",
            sa.Integer,
            sa.Identity(always=False, start=1, increment=1),
            primary_key=True,
            autoincrement=True,
        ),
        sa.Column("cdr_uuid", postgresql.UUID(), nullable=False),
        sa.Column("sent_from", sa.String(), nullable=False),
        sa.Column("sent_to", sa.String(), nullable=False),
        sa.Column("date_sent", sa.DateTime(), primary_key=True, nullable=False),
        sa.PrimaryKeyConstraint("id", "date_sent", name="pk_cdr_sms_new"),
        postgresql_partition_by="RANGE (date_sent)",
    )

    # Creating partitions for the CDR_SMS Table.
    op.execute(
        """
        DO $$
        DECLARE
            start_date DATE;
            end_date DATE := date_trunc('month',
            current_date + interval '1 month')::DATE;
            partition_date DATE;
            partition_name TEXT;
        BEGIN
            -- Select the smallest date_sent from the cdr_sms table
            -- and set it to the first day of its month
            SELECT date_trunc('month', MIN(date_sent))::DATE
            INTO start_date FROM cdr_sms;

            partition_date := start_date;
            WHILE partition_date < end_date LOOP
                partition_name := 'cdr_sms_new_' || to_char(
                partition_date, 'YYYY_MM');
                EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF \
                cdr_sms_new FOR VALUES FROM (%L) TO (%L)',
                            partition_name,
                            partition_date::DATE,
                            (partition_date + interval '1 month')::DATE);
                partition_date := partition_date + interval '1 month';
            END LOOP;
        END $$;

        """
    )

    # Creating index for the cdr_sms_new Table.
    op.create_index("idx_cdr_sms_new_date_sent", "cdr_sms_new", ["date_sent"])


def downgrade() -> None:

    op.drop_table("cdr_sms_new")
