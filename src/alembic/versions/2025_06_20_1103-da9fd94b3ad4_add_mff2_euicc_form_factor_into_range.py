"""add_MFF2_EUICC_form_factor_into_range

Revision ID: da9fd94b3ad4
Revises: 79d59014771d
Create Date: 2025-06-11 11:03:35.480348

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "da9fd94b3ad4"
down_revision = "79d59014771d"
branch_labels = None
depends_on = None


def upgrade():
    op.alter_column(
        "range",
        "form_factor",
        type_=sa.Enum(
            "STANDARD",
            "MICRO",
            "NANO",
            "eSIM_MFF2",
            "eSIM_MFF2_eUICC",
            name="form_factor",
            native_enum=False,
            create_constraint=True,
        ),
        existing_type=sa.Enum(
            "STANDARD",
            "MICRO",
            "NANO",
            name="form_factor",
            native_enum=False,
            create_constraint=True,
        ),
        existing_nullable=True,
    )


def downgrade():
    op.alter_column(
        "range",
        "form_factor",
        type_=sa.Enum(
            "STANDARD",
            "MICRO",
            "NANO",
            name="form_factor",
            native_enum=False,
            create_constraint=True,
        ),
        existing_type=sa.Enum(
            "STANDARD",
            "MICRO",
            "NANO",
            "eSIM_MFF2",
            "eSIM_MFF2_eUICC",
            name="form_factor",
            native_enum=False,
            create_constraint=True,
        ),
        existing_nullable=True,
    )
