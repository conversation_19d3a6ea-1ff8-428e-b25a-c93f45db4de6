"""add is_default column in rate_plan table

Revision ID: 99d0052e7ca9
Revises: 4ae058e615ad
Create Date: 2025-01-20 12:28:23.918637

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "99d0052e7ca9"
down_revision = "4ae058e615ad"
branch_labels = None
depends_on = None


def upgrade() -> None:

    op.execute(
        """
        BEGIN;

        -- Step 1: Add the is_default column with a default value of FALSE
        ALTER TABLE rate_plan
        ADD COLUMN IF NOT EXISTS is_default BOOLEAN DEFAULT FALSE NOT NULL;

        -- Step 2: Update is_default to TRUE where ROW_NUMBER() = 1
        WITH ranked_rate_plans AS (
            SELECT
                r.id,
                r.account_id,
                ROW_NUMBER() OVER (PARTITION BY r.account_id
                ORDER BY r.id ASC) AS row_num
            FROM
                rate_plan AS r
        )
        UPDATE rate_plan
        SET is_default = TRUE
        FROM ranked_rate_plans
        WHERE rate_plan.id = ranked_rate_plans.id
        AND ranked_rate_plans.row_num = 1;

        -- Step 3: unset default value column type
        ALTER TABLE rate_plan
        ALTER COLUMN is_default DROP DEFAULT;

        COMMIT;

        """
    )


def downgrade() -> None:
    op.execute(
        """
        ALTER TABLE rate_plan DROP COLUMN IF EXISTS is_default;
        """
    )
