"""create_rule_action_enum

Revision ID: 120baaf928db
Revises: b7eeef05eefe
Create Date: 2024-07-18 15:14:59.377210

"""
import sqlalchemy as sa
from alembic import op

from automation.domain.model import RuleAction

# revision identifiers, used by Alembic.
revision = "120baaf928db"
down_revision = "b7eeef05eefe"
branch_labels = None
depends_on = None


def upgrade() -> None:
    rule_action = sa.Enum(RuleAction, name="rule_action")
    rule_action.create(op.get_bind(), checkfirst=True)


def downgrade() -> None:

    rule_action = sa.Enum(RuleAction, name="rule_action", checkfirst=True)
    rule_action.drop(op.get_bind())
