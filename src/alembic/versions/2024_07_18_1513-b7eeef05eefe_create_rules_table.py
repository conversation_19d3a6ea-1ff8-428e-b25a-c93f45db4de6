"""create_rules_table

Revision ID: b7eeef05eefe
Revises: 8e845fe03a8b
Create Date: 2024-07-18 15:13:55.031082

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

from common.types import Unit

# revision identifiers, used by Alembic.
revision = "b7eeef05eefe"
down_revision = "8e845fe03a8b"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "rules",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("uuid", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.Column("rule_type_id", sa.Integer(), nullable=False),
        sa.Column("rule_category_id", sa.Integer(), nullable=False),
        sa.Column("rule_definition_id", sa.Integer(), nullable=False),
        sa.Column("rule_name", sa.String(), nullable=False),
        sa.Column("data_volume", sa.BigInteger(), nullable=False),
        sa.Column(
            "unit",
            postgresql.ENUM(Unit, name="unit", create_type=False),
            nullable=False,
        ),
        sa.Column("status", sa.Boolean(), nullable=False),
        sa.Column("created_by", sa.String(), nullable=False),
        sa.Column(
            "created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=False
        ),
        sa.Column("ip_address", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["rule_type_id"],
            ["rule_type.id"],
            name=op.f("fk_rules_rule_type_id_rule_type"),
        ),
        sa.ForeignKeyConstraint(
            ["rule_category_id"],
            ["rule_category.id"],
            name=op.f("fk_rules_rule_category_id_rule_category"),
        ),
        sa.ForeignKeyConstraint(
            ["rule_definition_id"],
            ["rule_definition.id"],
            name=op.f("fk_rules_rule_definition_id_rule_definition"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_rules")),
        sa.UniqueConstraint(
            "account_id", "data_volume", "rule_type_id", "rule_category_id", "unit"
        ),
        sa.UniqueConstraint("uuid"),
    )


def downgrade() -> None:
    op.drop_table("rules")
