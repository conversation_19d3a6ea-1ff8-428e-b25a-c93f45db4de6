"""add forign key constrains in user activity log

Revision ID: fb115c58402a
Revises: fd4ce1427f4c
Create Date: 2023-07-24 11:29:13.213699

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "fb115c58402a"
down_revision = "fd4ce1427f4c"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_foreign_key(
        "fk_provider_log_id",
        "user_activity_log",
        "sim_provider_log",
        ["provider_log_id"],
        ["id"],
    )
    op.create_foreign_key(
        "fk_sim_audit_log_uuid",
        "user_activity_log",
        "sim_audit_log",
        ["audit_uuid"],
        ["uuid"],
    )


def downgrade() -> None:
    op.drop_constraint("fk_sim_audit_log_uuid", "user_activity_log", type_="foreignkey")
    op.drop_constraint("fk_provider_log_id", "user_activity_log", type_="foreignkey")
