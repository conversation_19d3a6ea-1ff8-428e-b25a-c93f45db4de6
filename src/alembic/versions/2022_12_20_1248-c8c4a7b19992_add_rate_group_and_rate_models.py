"""Add rate group and rate models

Revision ID: c8c4a7b19992
Revises: 900d9f4610a8
Create Date: 2022-12-20 12:48:40.410009

"""
import sqlalchemy as sa
from alembic import op

from common.db import Base

# revision identifiers, used by Alembic.
revision = "c8c4a7b19992"
down_revision = "900d9f4610a8"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "rate_group",
        sa.Column("id", sa.Integer(), sa.Identity(always=False), nullable=False),
        sa.Column("rate_plan_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["rate_plan_id"],
            ["rate_plan.id"],
            name=op.f("fk_rate_group_rate_plan_id_rate_plan"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_rate_group")),
    )
    op.create_table(
        "rate",
        sa.Column("id", sa.Integer(), sa.Identity(always=False), nullable=False),
        sa.Column("rate_group_id", sa.Integer(), nullable=False),
        sa.Column("range_from", sa.Integer(), nullable=False),
        sa.Column("range_to", sa.Integer(), nullable=True),
        sa.Column("value", sa.Numeric(), nullable=False),
        sa.ForeignKeyConstraint(
            ["rate_group_id"],
            ["rate_group.id"],
            name=op.f("fk_rate_rate_group_id_rate_group"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_rate")),
    )
    op.create_table(
        "rate_group_service",
        sa.Column("rate_group_id", sa.Integer(), nullable=False),
        sa.Column(
            "service",
            sa.Enum(
                "VOICE_MO",
                "VOICE_MT",
                "SMS_MO",
                "SMS_MT",
                "DATA",
                name="service",
                metadata=Base.metadata,
            ),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["rate_group_id"],
            ["rate_group.id"],
            name=op.f("fk_rate_group_service_rate_group_id_rate_group"),
        ),
        sa.PrimaryKeyConstraint(
            "rate_group_id", "service", name=op.f("pk_rate_group_service")
        ),
    )


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("rate_group_service")
    op.drop_table("rate")
    op.drop_table("rate_group")
    # ### end Alembic commands ###
