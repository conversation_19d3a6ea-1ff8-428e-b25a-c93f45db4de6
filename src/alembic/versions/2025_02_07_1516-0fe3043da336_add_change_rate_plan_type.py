"""add CHANGE_RATE_PLAN type

Revision ID: 0fe3043da336
Revises: ef9ee2d669a3
Create Date: 2025-02-07 15:16:09.970689

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "0fe3043da336"
down_revision = "ef9ee2d669a3"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        "DO $$ BEGIN IF NOT EXISTS (\
        SELECT 1 FROM pg_enum WHERE enumtypid =\
        'rule_action'::regtype AND enumlabel = \
        'CHANGE_RATE_PLAN') THEN ALTER TYPE rule_action \
        ADD VALUE 'CHANGE_RATE_PLAN'; END IF; END; $$;"
    )


def downgrade() -> None:
    pass
