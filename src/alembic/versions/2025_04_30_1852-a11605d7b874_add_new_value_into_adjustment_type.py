"""add new value into adjustment type

Revision ID: a11605d7b874
Revises: 48f092edae01
Create Date: 2025-04-30 18:52:07.828461

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "a11605d7b874"
down_revision = "48f092edae01"
branch_labels = None
depends_on = None


# Enum definitions for upgrade and downgrade
new_adjustment_type = sa.Enum(
    "ACCOUNT_SETUP",
    "ACTIVATION_ADJUSTMENT",
    "DATA_CHARGE_ADJUSTMENT",
    "MONTHLY_ACCOUNT_MINIMUM",
    "OTHER_CHARGE_ONE_TIME",
    "PREMIER_SUPPORT",
    "SIM_FEE",
    "SMS_CHARGE_ADJUSTMENT",
    "SUBSCRIPTION_CHARGE_ADJUSTMENT",
    "SUSPENDED_FEE",
    "TRAINING",
    "PRIVATE_APN_SETUP_ONE_OFF",
    "PRIVATE_APN_MONTHLY",
    name="adjustment_type",
    native_enum=False,
)

old_adjustment_type = sa.Enum(
    "ACCOUNT_SETUP",
    "ACTIVATION_ADJUSTMENT",
    "DATA_CHARGE_ADJUSTMENT",
    "MONTHLY_ACCOUNT_MINIMUM",
    "OTHER_CHARGE_ONE_TIME",
    "PREMIER_SUPPORT",
    "SIM_FEE",
    "SMS_CHARGE_ADJUSTMENT",
    "SUBSCRIPTION_CHARGE_ADJUSTMENT",
    "SUSPENDED_FEE",
    "TRAINING",
    name="adjustment_type",
    native_enum=False,
)


def upgrade():
    # Add new enum values only if they don't exist
    op.execute(
        """
        DO $$
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'adjustment_type') THEN
                CREATE TYPE adjustment_type AS ENUM (
                    'ACCOUNT_SETUP',
                    'ACTIVATION_ADJUSTMENT',
                    'DATA_CHARGE_ADJUSTMENT',
                    'MONTHLY_ACCOUNT_MINIMUM',
                    'OTHER_CHARGE_ONE_TIME',
                    'PREMIER_SUPPORT',
                    'SIM_FEE',
                    'SMS_CHARGE_ADJUSTMENT',
                    'SUBSCRIPTION_CHARGE_ADJUSTMENT',
                    'SUSPENDED_FEE',
                    'TRAINING',
                    'PRIVATE_APN_SETUP_ONE_OFF',
                    'PRIVATE_APN_MONTHLY'
                );
            END IF;
        END;
        $$;
        """
    )

    # Drop old constraint if exists
    op.execute(
        """
        DO $$
        BEGIN
            IF EXISTS (
                SELECT 1 FROM pg_constraint
                WHERE conname = 'ck_adjustment_adjustment_type'
            ) THEN
                ALTER TABLE adjustment
                DROP CONSTRAINT ck_adjustment_adjustment_type;
            END IF;
        END;
        $$;
        """
    )

    op.alter_column(
        "adjustment",
        "type",
        type_=new_adjustment_type,
        existing_type=sa.String(30),
        existing_nullable=True,
        postgresql_using="type::adjustment_type",
    )

    # Add new check constraint
    op.execute(
        """
        ALTER TABLE adjustment
        ADD CONSTRAINT ck_adjustment_adjustment_type
        CHECK (type::text = ANY (ARRAY[
            'ACCOUNT_SETUP'::text,
            'ACTIVATION_ADJUSTMENT'::text,
            'DATA_CHARGE_ADJUSTMENT'::text,
            'MONTHLY_ACCOUNT_MINIMUM'::text,
            'OTHER_CHARGE_ONE_TIME'::text,
            'PREMIER_SUPPORT'::text,
            'SIM_FEE'::text,
            'SMS_CHARGE_ADJUSTMENT'::text,
            'SUBSCRIPTION_CHARGE_ADJUSTMENT'::text,
            'SUSPENDED_FEE'::text,
            'TRAINING'::text,
            'PRIVATE_APN_SETUP_ONE_OFF'::text,
            'PRIVATE_APN_MONTHLY'::text
        ]));
        """
    )


def downgrade():
    op.alter_column(
        "adjustment",
        "type",
        type_=old_adjustment_type,
        existing_type=new_adjustment_type,
        existing_nullable=True,
        postgresql_using="type::adjustment_type",
    )

    # Drop new check constraint
    op.execute(
        """
        DO $$
        BEGIN
            IF EXISTS (
                SELECT 1 FROM pg_constraint
                WHERE conname = 'ck_adjustment_adjustment_type'
            ) THEN
                ALTER TABLE adjustment
                DROP CONSTRAINT ck_adjustment_adjustment_type;
            END IF;
        END;
        $$;
        """
    )

    # Add old check constraint back
    op.execute(
        """
        ALTER TABLE adjustment
        ADD CONSTRAINT ck_adjustment_adjustment_type
        CHECK (type::text = ANY (ARRAY[
            'ACCOUNT_SETUP'::text,
            'ACTIVATION_ADJUSTMENT'::text,
            'DATA_CHARGE_ADJUSTMENT'::text,
            'MONTHLY_ACCOUNT_MINIMUM'::text,
            'OTHER_CHARGE_ONE_TIME'::text,
            'PREMIER_SUPPORT'::text,
            'SIM_FEE'::text,
            'SMS_CHARGE_ADJUSTMENT'::text,
            'SUBSCRIPTION_CHARGE_ADJUSTMENT'::text,
            'SUSPENDED_FEE'::text,
            'TRAINING'::text
        ]));
        """
    )
