"""fix multiple default rate plan

Revision ID: ef9ee2d669a3
Revises: 55b38e141c29
Create Date: 2025-02-06 14:27:59.909350

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "ef9ee2d669a3"
down_revision = "55b38e141c29"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS get_allowance_usage(integer[], varchar[], service);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_allowance_usage(
            account_ids integer[] DEFAULT NULL,
            rate_model_codes VARCHAR[] DEFAULT NULL,
            rate_plan_service service DEFAULT 'DATA'::service
        )
        RETURNS TABLE (
            account_id INT,
            rate_plan_id INT,
            allowance_used NUMERIC
        ) AS $$
        DECLARE
            partition_name TEXT;
            sql_query TEXT;
        BEGIN
            -- Construct the partition name dynamically for the current month
            partition_name := 'cdr_data_aggregate_' || to_char(current_date, 'YYYY_MM');

            -- Construct the SQL query dynamically
            sql_query := '
                SELECT
                    auv.account_id,
                    auv.rate_plan_id,
                    ROUND((COALESCE(SUM(cda.usage), 0) * 100) / GREATEST(
                    auv.total_allowance, 1), 2) AS allowance_used
                FROM
                    allowance_usage_view auv
                INNER JOIN ' || partition_name || ' AS cda
                    ON cda.imsi = auv.imsi
                WHERE
                    ($1 IS NULL OR auv.account_id = ANY($1))
                    AND ($2 IS NULL OR auv.rate_model_code != ALL($2))
                    AND ($3 IS NULL OR auv.service = $3)
                GROUP BY
                    auv.account_id,
                    auv.rate_plan_id,
                    auv.total_allowance,
                    auv.service;
            ';

            -- Execute the dynamically constructed query and return the result
            RETURN QUERY EXECUTE sql_query USING account_ids, rate_model_codes,
            rate_plan_service;
        END;
        $$ LANGUAGE plpgsql;
        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS get_allowance_usage(integer[], varchar[], service);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_allowance_usage(
            account_ids integer[] DEFAULT NULL,
            rate_model_codes VARCHAR[] DEFAULT NULL,
            rate_plan_service service DEFAULT 'DATA'::service
        )
        RETURNS TABLE (
            account_id INT,
            rate_plan_id INT,
            allowance_used NUMERIC
        ) AS $$
        DECLARE
            partition_name TEXT;
            sql_query TEXT;
        BEGIN
            -- Construct the partition name dynamically for the current month
            partition_name := 'cdr_data_aggregate_' || to_char(current_date, 'YYYY_MM');

            -- Construct the SQL query dynamically
            sql_query := '
                SELECT
                    auv.account_id,
                    auv.rate_plan_id,
                    ROUND((COALESCE(SUM(cda.usage), 0) * 100) / GREATEST(
                    auv.total_allowance, 1), 2) AS allowance_used
                FROM
                    allowance_usage_view auv
                INNER JOIN ' || partition_name || ' AS cda
                    ON cda.imsi = auv.imsi
                WHERE
                    ($1 IS NULL OR auv.account_id = ANY($1))
                    AND ($2 IS NULL OR auv.rate_model_code != ALL($2))
                    AND ($3 IS NULL OR auv.service = $3)
                GROUP BY
                    auv.account_id,
                    auv.rate_plan_id,
                    auv.total_allowance,
                    auv.service,
                    auv.rate_model_code,  -- removed this in upgrade
                    auv.imsi;  -- removed this in upgrade
            ';

            -- Execute the dynamically constructed query and return the result
            RETURN QUERY EXECUTE sql_query USING account_ids, rate_model_codes,
            rate_plan_service;
        END;
        $$ LANGUAGE plpgsql;
        """
    )
