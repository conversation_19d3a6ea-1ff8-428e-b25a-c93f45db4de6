"""change_rate_model

Revision ID: 1477dc10adbb
Revises: 54595b58270b
Create Date: 2024-09-09 12:19:04.266640

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

from common.types import Unit

# revision identifiers, used by Alembic.
revision = "1477dc10adbb"
down_revision = "54595b58270b"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column("rate_group", sa.Column("rate_model_id", sa.Integer(), nullable=True))

    op.add_column(
        "rate",
        sa.Column(
            "range_unit",
            postgresql.ENUM(Unit, name="unit", create_type=False),
            nullable=True,
        ),
    )
    op.add_column(
        "rate",
        sa.Column(
            "price_unit",
            postgresql.ENUM(Unit, name="unit", create_type=False),
            nullable=True,
        ),
    )
    op.add_column("rate", sa.Column("overage_fee", sa.Numeric(), nullable=True))
    op.add_column(
        "rate",
        sa.Column(
            "overage_unit",
            postgresql.ENUM(Unit, name="unit", create_type=False),
            nullable=True,
        ),
    )
    op.add_column(
        "rate",
        sa.Column(
            "isoverage", sa.Boolean(), server_default=sa.text("false"), nullable=False
        ),
    )
    op.add_column("rate", sa.Column("overage_per", sa.Integer(), nullable=True))

    op.create_foreign_key(
        "fk_rate_group_rate_model",  # Name of the foreign key constraint
        "rate_group",  # Source table (where you're adding the foreign key)
        "rate_model",  # Referenced table (the table you're referencing)
        ["rate_model_id"],  # Local column(s) in 'rate'
        ["id"],  # Referenced column(s) in 'rate_model'
    )

    connection = op.get_bind()
    result = connection.execute(
        sa.text("SELECT id FROM rate_model WHERE model='Pay As You Go';")
    )
    rate_model_id = result.scalar()

    if rate_model_id is not None:
        connection.execute(
            sa.text("UPDATE rate_group SET rate_model_id=:rate_model_id"),
            {"rate_model_id": rate_model_id},
        )

    op.execute(
        """update rate
    set range_unit = 'MB', price_unit='GB'
    from rate_group rg
    inner join rate_group_service rgs on rgs.rate_group_id = rg.id
    where rate.rate_group_id = rg.id
    and rgs.service = 'DATA';"""
    )

    op.execute(
        """update rate
    set range_unit= 'Min', price_unit='Min'
    from rate_group rg
    inner join rate_group_service rgs on rgs.rate_group_id = rg.id
    where rate.rate_group_id = rg.id
    and rgs.service IN ('VOICE_MO','VOICE_MT');"""
    )

    op.execute(
        """update rate
    set range_unit = 'SMS', price_unit='SMS'
    from rate_group rg
    inner join rate_group_service rgs on rgs.rate_group_id = rg.id
    where rate.rate_group_id = rg.id
    and rgs.service = 'SMS_MO';"""
    )

    op.alter_column(
        "rate_group",
        column_name="rate_model_id",
        existing_type=sa.Integer(),
        existing_nullable=True,
        nullable=False,
    )
    op.alter_column(
        "rate",
        column_name="range_unit",
        existing_nullable=True,
        nullable=False,
    )
    op.alter_column(
        "rate",
        column_name="price_unit",
        existing_nullable=True,
        nullable=False,
    )
    op.alter_column(
        "rate",
        column_name="range_to",
        existing_type=sa.Integer,
        type_=sa.Numeric,
    )


def downgrade() -> None:
    op.drop_column("rate_group", "rate_model_id")
    op.drop_column("rate", "range_unit")
    op.drop_column("rate", "price_unit")
    op.drop_column("rate", "overage_fee")
    op.drop_column("rate", "overage_unit")
    op.drop_column("rate", "isoverage")
    op.drop_column("rate", "overage_per")
