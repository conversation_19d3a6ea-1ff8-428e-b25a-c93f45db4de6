"""alter column access_fees of rate_plan

Revision ID: ec1a39ce6309
Revises: 8543ab257cee
Create Date: 2023-07-03 12:03:06.533862

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "ec1a39ce6309"
down_revision = "8543ab257cee"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "rate_plan",
        "access_fee",
        existing_type=sa.Numeric,
        type_=sa.Numeric(precision=18, scale=2),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "rate_plan",
        "access_fee",
        existing_type=sa.Numeric(precision=18, scale=2),
        type_=sa.Numeric,
    )
    # ### end Alembic commands ###
