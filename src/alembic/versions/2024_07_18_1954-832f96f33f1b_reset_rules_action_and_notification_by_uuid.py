"""reset_rules_action_and_notification_by_rules_uuid_function

Revision ID: 832f96f33f1b
Revises: 111b18fd11ea
Create Date: 2024-07-18 19:54:32.109955

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "832f96f33f1b"
down_revision = "111b18fd11ea"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
            CREATE OR
            REPLACE FUNCTION reset_rules_action_and_notification(uuid_param uuid)
            RETURNS void AS $$
            BEGIN
                DELETE FROM notification_value
                WHERE rules_notification_id IN (
                    SELECT id FROM rules_notification WHERE rules_uuid = uuid_param
                );

                DELETE FROM rules_notification
                WHERE rules_uuid = uuid_param;

                DELETE FROM rules_action
                WHERE rules_uuid = uuid_param;

            END;
            $$ LANGUAGE plpgsql;
        """
    )


def downgrade() -> None:
    op.execute(
        """
            DROP FUNCTION IF EXISTS public.reset_rules_action_and_notification(uuid);
    """
    )
