"""get_notifications_by_rules_uuid

Revision ID: a5063813c0d5
Revises: 9e46552f0afd
Create Date: 2024-07-18 12:35:46.707827

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "a5063813c0d5"
down_revision = "9e46552f0afd"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_notifications_by_rules_uuid(
            rules_uuid_param UUID[]
        ) RETURNS TABLE (
            name character varying,
            notification boolean,
            email character varying[],
            rules_uuid UUID
        )
        LANGUAGE 'plpgsql'
        COST 100
        VOLATILE
        PARALLEL UNSAFE
        ROWS 1000
        AS $BODY$
        BEGIN
            RETURN QUERY
            SELECT
                notifications.notification AS name,
                rules_notification.notification AS notification,
                array_agg(notification_value.notification_value) AS email,
                rules_notification.rules_uuid AS rules_uuid
            FROM
                rules_notification
            JOIN
                notifications ON notifications.id = rules_notification.notifications_id
            JOIN
                notification_value ON
                notification_value.rules_notification_id = rules_notification.id
            WHERE
                rules_notification.rules_uuid = ANY(rules_uuid_param)
            GROUP BY
                notifications.id, rules_notification.notification,
                rules_notification.rules_uuid;
        END;
        $BODY$;
        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS
        get_notifications_by_rules_uuid(rules_uuid_param UUID[]);
        """
    )
