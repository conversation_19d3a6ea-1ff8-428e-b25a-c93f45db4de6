"""create get_rate_plans_detail view

Revision ID: 6d6f9a04c85a
Revises: 5191ce1ad280
Create Date: 2025-02-04 19:35:28.118932

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "6d6f9a04c85a"
down_revision = "5191ce1ad280"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        CREATE OR REPLACE VIEW get_rate_plans_detail_view AS
            SELECT
                a.id AS account_id,
                a.name AS account_name,
                a.logo_key,
                r.id,
                r.name,
                r.access_fee,
                r.sim_limit,
                a.currency,
                rg.rate_model_id,
                ra.range_from,
                ra.range_to,
                ra.value,
                ra.range_unit,
                ra.price_unit,
                ra.overage_fee,
                ra.overage_unit,
                ra.isoverage,
                ra.overage_per,
                rgs.service,
                r.is_default,
                rm.rate_model_code
            FROM
                rate_plan r
            JOIN
                account a ON r.account_id = a.id
            INNER JOIN
                rate_group AS rg ON rg.rate_plan_id = r.id
            INNER JOIN
                rate_group_service AS rgs ON rgs.rate_group_id = rg.id
            INNER JOIN rate_model AS rm
                ON rm.id = rg.rate_model_id
            LEFT JOIN
                rate AS ra ON ra.rate_group_id = rg.id
            GROUP BY
                a.id,
                r.id,
                rg.rate_model_id,
                ra.range_from,
                ra.range_to,
                ra.value,
                ra.range_unit,
                ra.price_unit,
                ra.overage_fee,
                ra.overage_unit,
                ra.isoverage,
                ra.overage_per,
                rgs.service,
                rm.rate_model_code;

        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP VIEW IF EXISTS get_rate_plans_detail_view;
        """
    )
