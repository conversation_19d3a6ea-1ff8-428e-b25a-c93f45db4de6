"""create valid sim_card function

Revision ID: 5793e4929773
Revises: 4250556361bd
Create Date: 2024-01-11 15:27:53.413233

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "5793e4929773"
down_revision = "4250556361bd"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        """CREATE OR REPLACE FUNCTION validate_sim_card_details(imsi_list text[])
        RETURNS TABLE(imsi text,
        allocation_id integer, account_name text, form_factor text) AS $$
            BEGIN
         RETURN QUERY
         SELECT sc.imsi::text, sc.allocation_id, acc.name::text, r.form_factor::text
         FROM sim_card AS sc
         INNER JOIN range AS r ON sc.range_id = r.id
         LEFT JOIN allocation AS al ON sc.allocation_id = al.id
         LEFT JOIN account AS acc ON al.account_id = acc.id
         WHERE sc.imsi = ANY(imsi_list);
        END;
        $$ LANGUAGE plpgsql;
        """
    )


# ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        """
    DROP FUNCTION IF EXISTS public.validate_sim_card_details(text[]);
    """
    )

    # ### end Alembic commands ###
