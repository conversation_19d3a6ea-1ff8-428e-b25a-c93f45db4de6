"""get_monthly_reconciliation_function

Revision ID: 89fe42011328
Revises: 5793e4929773
Create Date: 2024-01-12 15:47:56.768180

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "89fe42011328"
down_revision = "5793e4929773"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
            CREATE OR REPLACE FUNCTION public.monthly_recon(
                start_date_param date,
                end_date_param date)
                RETURNS TABLE(
                    service text,
                    mu_imsi character varying,
                    cda_imsi character varying,
                    mu_usage bigint,
                    cda_usage bigint,
                    mu_cda_variance bigint)
                LANGUAGE 'plpgsql'
                COST 100
                VOLATILE PARALLEL UNSAFE
                ROWS 1000

            AS $BODY$
            BEGIN

                FOR service, mu_imsi, cda_imsi, mu_usage, cda_usage, mu_cda_variance IN

                WITH cdr_sms_agg AS (
                        SELECT COUNT(cs.id) AS volume, cd.imsi
                        FROM cdr_sms AS cs
                        INNER JOIN cdr AS cd ON cs.cdr_uuid = cd.uuid
                        WHERE cs.date_sent BETWEEN start_date_param AND end_date_param
                        GROUP BY cd.imsi
                    )

                    SELECT 'DATA' AS service,
                    mu.imsi AS mu_imsi,
                    cda.imsi AS cda_imsi,
                    COALESCE(SUM(mu.volume), 0) AS mu_usage,
                    COALESCE(SUM(cda.usage), 0) AS cda_usage,
                    (COALESCE(SUM(mu.volume),0) - COALESCE(SUM(cda.usage),0))
                    AS mu_cda_variance
                    FROM monthly_usage_records AS mu
                    LEFT JOIN cdr_data_aggregate AS cda ON mu.imsi = cda.imsi
                    AND cda.month = start_date_param
                    WHERE mu.month = start_date_param
                    AND mu.service = 'DATA'
                    GROUP BY mu.imsi,cda.imsi

                    UNION

                    SELECT 'DATA' AS service,mu.imsi AS mu_imsi, cda.imsi AS cda_imsi,
                    COALESCE(SUM(mu.volume), 0) AS mu_usage,
                    COALESCE(SUM(cda.usage), 0) AS cda_usage,
                    (COALESCE(SUM(mu.volume),0) - COALESCE(SUM(cda.usage),0))
                    AS mu_cda_variance
                    FROM cdr_data_aggregate AS cda
                    LEFT JOIN monthly_usage_records AS mu ON mu.imsi = cda.imsi
                    AND mu.month = start_date_param
                    AND mu.service = 'DATA'
                    WHERE cda.month = start_date_param
                    GROUP BY mu.imsi,cda.imsi

                    UNION

                    SELECT 'VOICE' AS service,mu.imsi AS mu_imsi,cda.imsi AS cda_imsi,
                    COALESCE(SUM(mu.volume),0) AS mu_usage,
                    COALESCE(SUM(cda.voice_usage), 0) AS cda_usage,
                    (COALESCE(SUM(mu.volume),0) - COALESCE(SUM(cda.voice_usage),0))
                    AS mu_cda_variance
                    FROM monthly_usage_records AS mu
                    LEFT JOIN cdr_voice_aggregate AS cda ON mu.imsi=cda.imsi
                    AND cda.month=start_date_param
                    WHERE mu.month=start_date_param
                    AND (mu.service='VOICE_MO' OR mu.service='VOICE_MT')
                    GROUP BY mu.imsi,cda.imsi

                    UNION

                    SELECT 'VOICE' AS service,mu.imsi AS mu_imsi,cda.imsi AS cda_imsi,
                    COALESCE(SUM(mu.volume),0) AS mu_usage,
                    COALESCE(SUM(cda.voice_usage), 0) AS cda_usage,
                    (COALESCE(SUM(mu.volume),0) - COALESCE(SUM(cda.voice_usage),0))
                    AS mu_cda_variance
                    FROM cdr_voice_aggregate AS cda
                    LEFT JOIN monthly_usage_records AS mu ON mu.imsi=cda.imsi
                    AND mu.month=start_date_param
                    AND (mu.service='VOICE_MO' OR mu.service='VOICE_MT')
                    WHERE cda.month=start_date_param
                    GROUP BY mu.imsi,cda.imsi

                    UNION

                    SELECT 'SMS' AS service,mu.imsi AS mu_imsi,cds.imsi AS cda_imsi,
                    COALESCE(SUM(mu.volume),0) AS mu_usage,
                    COALESCE(SUM(cds.volume), 0) AS cda_usage,
                    (COALESCE(SUM(mu.volume),0) - COALESCE(SUM(cds.volume),0))
                    AS mu_cda_variance
                    FROM monthly_usage_records AS mu
                    LEFT JOIN cdr_sms_agg AS cds ON mu.imsi=cds.imsi
                    WHERE mu.month=start_date_param
                    AND (mu.service='SMS_MO' OR mu.service='SMS_MT')
                    GROUP BY mu.imsi,cds.imsi

                    UNION

                    SELECT 'SMS' AS service,mu.imsi AS mu_imsi,cds.imsi AS cda_imsi,
                    COALESCE(SUM(mu.volume),0) AS mu_usage,
                    COALESCE(SUM(cds.volume), 0) AS cda_usage,
                    (COALESCE(SUM(mu.volume),0) - COALESCE(SUM(cds.volume),0))
                    AS mu_cda_variance
                    FROM cdr_sms_agg AS cds
                    LEFT JOIN monthly_usage_records AS mu
                    ON mu.imsi=cds.imsi and mu.month=start_date_param
                    AND (mu.service='SMS_MO' OR mu.service='SMS_MT')
                    GROUP BY mu.imsi,cds.imsi
                LOOP
                    -- Return the fetched values
                    IF mu_cda_variance = 0  THEN
                        IF cda_imsi IS NULL OR mu_imsi IS NULL THEN
                            RETURN NEXT;
                        END IF;
                    ELSEIF mu_cda_variance != 0  THEN
                        RETURN NEXT;
                    END IF;
                END LOOP;

                RETURN;
            END;
            $BODY$;
        """
    )


def downgrade() -> None:
    op.execute(
        """
            DROP FUNCTION IF EXISTS public.monthly_recon(date, date);
    """
    )
