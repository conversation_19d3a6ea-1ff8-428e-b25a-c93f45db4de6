"""rate_plan_by_accounts

Revision ID: babf84ca4a30
Revises: cbd1135f4447
Create Date: 2024-08-06 13:07:05.773636

"""
# import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "babf84ca4a30"
down_revision = "cbd1135f4447"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        CREATE OR REPLACE FUNCTION rate_plan_by_accounts(
            search_term varchar DEFAULT NULL
        )
        RETURNS TABLE (
            account_id integer,
            account_name character varying,
            logo_key character varying,
            id integer,
            name character varying,
            access_fee numeric,
            currency character varying,
            is_default character varying
        )
        LANGUAGE 'plpgsql'
        COST 100
        VOLATILE
        PARALLEL UNSAFE
        ROWS 1000
        AS $BODY$
        BEGIN
            RETURN QUERY
                WITH RankedRatePlans AS (
                SELECT
                    a.id AS account_id,
                    a.name AS account_name,
                    a.logo_key AS logo_key,
                    r.id AS id,
                    r.name AS name,
                    r.access_fee AS access_fee,
                    'GBP'::character varying AS currency,
                    ROW_NUMBER() OVER (PARTITION BY a.id ORDER BY r.id) AS row_num
                FROM
                    rate_plan r
                JOIN
                    account a ON r.account_id = a.id
                WHERE
                    search_term IS NULL OR (
                        a.id::text LIKE '%' || search_term || '%' OR
                        a.name LIKE '%' || search_term || '%' OR
                        a.logo_key LIKE '%' || search_term || '%' OR
                        r.id::text LIKE '%' || search_term || '%' OR
                        r.name LIKE '%' || search_term || '%' OR
                        r.access_fee::text LIKE '%' || search_term || '%'
                    )
                GROUP BY
                    a.id, r.id

            )
            SELECT
            rp.account_id,
            rp.account_name,
            rp.logo_key,
            rp.id,
            rp.name,
            rp.access_fee,
            rp.currency,
            (CASE
            WHEN rp.row_num = 1 THEN 'True'
            ELSE 'False'
            END)::character varying AS is_default
            FROM
                RankedRatePlans rp
            ORDER BY
                rp.account_name ASC, rp.id ASC;

        END;
        $BODY$;


        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS rate_plan_by_accounts();
        """
    )
