"""create reset_invoice_overage_charge_details function

Revision ID: 4fc503e96681
Revises: 137e435e7d71
Create Date: 2024-11-04 16:29:52.285538

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "4fc503e96681"
down_revision = "137e435e7d71"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        CREATE OR REPLACE FUNCTION public.reset_invoice_overage_charge_details(
            invoice_id_param integer)
            RETURNS void
            LANGUAGE 'plpgsql'
            COST 100
            VOLATILE PARALLEL UNSAFE
        AS $BODY$
                    BEGIN
                    DELETE FROM invoice_overage_charge
                    WHERE
                        invoice_id = invoice_id_param;
                    END;
        $BODY$;


        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS public.reset_invoice_overage_charge_details(integer);
        """
    )
