"""modify all get_invoices function

Revision ID: 099c6b6d2498
Revises: c978cebcc9b0
Create Date: 2025-06-11 16:55:04.329622

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "099c6b6d2498"
down_revision = "c978cebcc9b0"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS get_invoice_summary(INTEGER[], DATE, INTEGER[]);
        DROP FUNCTION IF EXISTS get_invoice_adjustments(INTEGER[], DATE, INTEGER[]);
        DROP FUNCTION IF EXISTS get_invoice_subscriptions(INTEGER[], DATE, INTEGER[]);
        DROP FUNCTION IF EXISTS get_imsi_usage(INTEGER[], DATE, INTEGER[]);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_invoice_summary(
            p_billing_cycle_id INTEGER[] DEFAULT NULL,
            p_account_ids INTEGER[] DEFAULT NULL,
            p_limit INTEGER DEFAULT NULL,
            p_offset INTEGER DEFAULT NULL,
            search_term varchar DEFAULT NULL
        )
        RETURNS TABLE (
            invoice_id INTEGER,
            account_id INTEGER,
            name TEXT,
            logo_key TEXT,
            status TEXT,
            is_billable BOOLEAN,
            industry_vertical TEXT,
            currency TEXT,
            contract_end_date DATE,
            billing_cycle DATE,
            invoice_date TIMESTAMP,
            due_date TIMESTAMP,
            is_published BOOLEAN,
            rating_state TEXT,
            total_account_charge NUMERIC,
            new_sims INTEGER,
            billing_cycle_id INTEGER,
            due_days INTEGER
        )
        AS $$
        BEGIN
            RETURN QUERY
            WITH invoice_base AS (
            SELECT
                inv.id AS invoice_id,
                ac.id AS account_id,
                ac.name::text,
                ac.logo_key::text,
                ac.status::text,
                ac.is_billable,
                ac.industry_vertical::text,
                ac.currency,
                ac.contract_end_date,
                bc.month AS billing_cycle,
                COALESCE(inv.published_at, NULL) AS invoice_date,
                inv.published_at + ac.payment_terms * INTERVAL '1 day' AS due_date,
                (inv.published_at IS NOT NULL) AS is_published,
                inv.rating_state,
                inv.billing_cycle_id,
                ac.payment_terms AS due_days
            FROM invoice inv
            INNER JOIN account ac ON ac.id = inv.account_id
            LEFT JOIN billing_cycle AS bc ON bc.id=inv.billing_cycle_id
            WHERE
            (
                $1 IS NOT NULL
                AND inv.billing_cycle_id = ANY($1)
                AND (
                    $5 IS NULL OR (
                        ac.name::text ILIKE '%' || $5 || '%'
                        OR ac.industry_vertical::text ILIKE '%' || $5 || '%'
                    )
                )
            )
            OR
            (
                $1 IS NULL
                AND ($2 IS NULL OR ac.id = ANY($2))
                AND inv.published_at IS NOT NULL
            )
            ORDER BY is_published DESC, ac.is_billable DESC, inv.id DESC
            LIMIT $3 OFFSET $4
            ),
            total_account_charge AS (
            SELECT
                iac.invoice_id,
                COALESCE(SUM(iac.account_charge), 0) AS total_account_charge
            FROM invoice_account_charge AS iac
            INNER JOIN invoice_base AS inb ON inb.invoice_id = iac.invoice_id
            --INNER JOIN invoice AS inv ON inv.id = iac.invoice_id
            --WHERE iac.invoice_id IN (SELECT inb.invoice_id FROM invoice_base as inb)
            GROUP BY iac.invoice_id
            ),
            new_sims AS (
            SELECT
                ssad.invoice_id,
                COUNT(ssad.id) AS new_sims
            FROM subscription_sim_allocation_details AS ssad
            INNER JOIN invoice_base AS inb ON inb.invoice_id = ssad.invoice_id
            --INNER JOIN invoice inv ON inv.id = ssad.invoice_id
            --WHERE ssad.invoice_id IN (SELECT inb.invoice_id FROM invoice_base as inb)
            GROUP BY ssad.invoice_id
            )
            SELECT
                ib.invoice_id,
                ib.account_id,
                ib.name::text,
                ib.logo_key::text,
                ib.status::text,
                ib.is_billable,
                ib.industry_vertical::text,
                ib.currency::text,
                ib.contract_end_date,
                ib.billing_cycle,
                ib.invoice_date,
                ib.due_date,
                ib.is_published,
                ib.rating_state::text,
                COALESCE(tac.total_account_charge, 0) as total_account_charge,
                COALESCE(ns.new_sims, 0)::integer as new_sims,
                ib.billing_cycle_id,
                ib.due_days
            FROM invoice_base ib
            LEFT JOIN total_account_charge tac ON ib.invoice_id = tac.invoice_id
            LEFT JOIN new_sims ns ON ib.invoice_id = ns.invoice_id
            ORDER BY ib.is_published DESC, ib.is_billable DESC, ib.invoice_id DESC;
        END;
        $$ LANGUAGE plpgsql;
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_invoice_subscriptions(
            p_invoice_id INTEGER[]
        )
        RETURNS TABLE (
            id INTEGER,
            invoice_id INTEGER,
            rate_plan_id INTEGER,
            RatePlan TEXT,
            access_fee NUMERIC,
            sims_total INTEGER,
            sim_charge NUMERIC,
            total_active_sim INTEGER
        )
        AS $$
        BEGIN
            RETURN QUERY
            WITH filtered_invoices AS (
                SELECT
                    inv.id,
                    inv.billing_cycle_id,
                    inv.account_id,
                    inv.due_days,
                    inv.published_at,
                    inv.rating_state,
                    inv.account_id,
                    bc.month AS billing_cycle
                FROM invoice inv
                LEFT JOIN billing_cycle bc ON bc.id = inv.billing_cycle_id
                WHERE
                    inv.id = ANY(p_invoice_id)
            ),
            subscriptions AS (
                SELECT
                    sub.id,
                    sub.invoice_id,
                    sub.rate_plan_id,
                    sub.name::text AS RatePlan,
                    sub.access_fee,
                    sub.sims_total,
                    sub.sim_charge
                FROM subscription sub
                INNER JOIN filtered_invoices fi ON fi.id = sub.invoice_id
            ),
            active_sims AS (
                SELECT
                    fi.id AS invoice_id,
                    sub.rate_plan_id,
                    COUNT(subs.id) AS total_active_sim
                FROM filtered_invoices fi
                INNER JOIN subscription sub ON sub.invoice_id = fi.id
                INNER JOIN subscription_sim subs ON subs.subscription_id = sub.id
                GROUP BY fi.id, sub.rate_plan_id
            )
            SELECT
                s.id,
                s.invoice_id,
                s.rate_plan_id,
                s.RatePlan,
                s.access_fee,
                s.sims_total,
                s.sim_charge,
                COALESCE(a.total_active_sim, 0)::integer AS total_active_sim
            FROM subscriptions s
            LEFT JOIN active_sims a
                ON s.invoice_id = a.invoice_id AND s.rate_plan_id = a.rate_plan_id
            ORDER BY s.invoice_id DESC;
        END;
        $$ LANGUAGE plpgsql;
        """
    )
    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_invoice_adjustments(
            p_invoice_id INTEGER[]
        )
        RETURNS TABLE (
            id INTEGER,
            invoice_id INTEGER,
            date DATE,
            amount NUMERIC,
            type TEXT
        )
        AS $$
        BEGIN
            RETURN QUERY
            WITH filtered_invoices AS (
                SELECT inv.id
                FROM invoice inv
                LEFT JOIN billing_cycle bc ON bc.id = inv.billing_cycle_id
                WHERE
                    inv.id = ANY(p_invoice_id)
            )
            SELECT
                adj.id,
                adj.invoice_id,
                adj.date,
                adj.amount,
                adj.type::text
            FROM adjustment AS adj
            INNER JOIN filtered_invoices fi ON fi.id = adj.invoice_id
            ORDER BY adj.invoice_id DESC;
        END;
        $$ LANGUAGE plpgsql;
        """
    )
    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_imsi_usage(
            p_invoice_id INTEGER[],
            p_billing_cycle_id INTEGER[] DEFAULT NULL,
            p_account_ids INTEGER[] DEFAULT NULL
        )
        RETURNS TABLE (
            invoice_id INTEGER,
            service service,
            total_volume NUMERIC,
            total_charge NUMERIC,
            total_overage_charge NUMERIC,
            overage_charge NUMERIC
        )
        AS $$
        BEGIN
            RETURN QUERY
            WITH services AS (
                SELECT UNNEST(
                    ARRAY['DATA', 'VOICE_MO', 'VOICE_MT', 'SMS_MO', 'SMS_MT']::service[]
                ) AS service
            ),
            imsi_list AS (
                SELECT subs.imsi, subs.subscription_id
                FROM subscription_sim subs
                INNER JOIN subscription sub ON subs.subscription_id = sub.id
                INNER JOIN invoice inv ON inv.id = sub.invoice_id
                LEFT JOIN billing_cycle bc ON bc.id = inv.billing_cycle_id
                WHERE
                    inv.id = ANY(p_invoice_id)
                GROUP BY subs.imsi, subs.subscription_id
            ),
            imsi_service AS (
                SELECT i.imsi, i.subscription_id, s.service
                FROM imsi_list i
                CROSS JOIN services s
            ),
            usage_data AS (
                SELECT
                    su.imsi,
                    su.service,
                    su.billing_cycle_id,
                    SUM(su.charge) AS charge,
                    SUM(su.volume) AS volume
                FROM sim_usage su
                INNER JOIN allocation al ON al.imsi = su.imsi AND (
                    p_account_ids IS NULL OR al.account_id = ANY(p_account_ids)
                )
                WHERE
                    (
                        p_billing_cycle_id IS NOT NULL
                        AND su.billing_cycle_id = ANY(p_billing_cycle_id)
                    )
                    OR (
                        p_billing_cycle_id IS NULL
                        AND (p_account_ids IS NULL
                        OR al.account_id = ANY(p_account_ids))
                    )
                GROUP BY su.imsi, su.service, su.billing_cycle_id
            ),
            combined AS (
                SELECT
                    inv.id AS invoice_id,
                    ims.service,
                    COALESCE(ud.volume, 0) AS volume,
                    COALESCE(ud.charge, 0) AS charge,
                    COALESCE(ioc.total_charge, 0) AS total_overage_charge,
                    COALESCE(ioc.overage_charge, 0) AS overage_charge
                FROM imsi_service ims
                INNER JOIN subscription sub ON ims.subscription_id = sub.id
                INNER JOIN invoice inv ON inv.id = sub.invoice_id
                    -- AND (
                    --    p_billing_cycle_id IS NOT NULL
                    --    AND inv.billing_cycle_id = ANY(p_billing_cycle_id))
                LEFT JOIN usage_data ud ON ud.imsi = ims.imsi
                AND ud.service = ims.service
                    -- AND (
                    --    p_billing_cycle_id IS NOT NULL
                    --    AND ud.billing_cycle_id = ANY(p_billing_cycle_id))
                LEFT JOIN invoice_overage_charge ioc ON ioc.invoice_id = inv.id
                AND ioc.service = ims.service
            )
            SELECT
                c.invoice_id,
                c.service,
                SUM(c.volume) AS total_volume,
                SUM(c.charge) AS total_charge,
                SUM(c.total_overage_charge) AS total_overage_charge,
                SUM(c.overage_charge) AS overage_charge
            FROM combined c
            GROUP BY c.invoice_id, c.service
            ORDER BY c.invoice_id DESC, c.service;
        END;
        $$ LANGUAGE plpgsql;
        """
    )

    # op.execute(
    #     """
    #     CREATE OR REPLACE FUNCTION get_invoice_summary_total(
    #         p_billing_cycle_id INTEGER[] DEFAULT NULL,
    #         p_account_ids INTEGER[] DEFAULT NULL,
    #         search_term varchar DEFAULT NULL
    #     )
    #     RETURNS TABLE (
    #         total_count BIGINT
    #     )
    #     AS $$
    #     BEGIN
    #         RETURN QUERY
    #         SELECT
    #             count(*)
    #         FROM invoice inv
    #         INNER JOIN account ac ON ac.id = inv.account_id
    #         LEFT JOIN billing_cycle AS bc ON bc.id=inv.billing_cycle_id
    #         WHERE
    #         (
    #             $1 IS NOT NULL
    #             AND inv.billing_cycle_id = ANY($1)
    #             AND (
    #                 $3 IS NULL OR (
    #                     ac.name::text ILIKE '%' || $3 || '%'
    #                     OR ac.industry_vertical::text ILIKE '%' || $3 || '%'
    #                 )
    #             )
    #         )
    #         OR
    #         (
    #             $1 IS NULL
    #             AND ($2 IS NULL OR ac.id = ANY($2))
    #             AND inv.published_at IS NOT NULL
    #         );
    #     END;
    #     $$ LANGUAGE plpgsql;
    #     """
    # )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_invoice_summary_total(
            p_billing_cycle_id INTEGER[] DEFAULT NULL,
            p_account_ids INTEGER[] DEFAULT NULL,
            search_term varchar DEFAULT NULL
        )
        RETURNS TABLE (
            total_invoice BIGINT,
            total_sims BIGINT,
            new_sims BIGINT,
            total_active_sims BIGINT,
            total_charge NUMERIC,
            new_sim_charge NUMERIC,
            adjustment NUMERIC,
            service_charge NUMERIC,
            overage_charge NUMERIC,
            total_invoice_charge NUMERIC,
            data_volume NUMERIC,
            voice_volume NUMERIC,
            sms_volume NUMERIC
        )
        AS $$
        BEGIN
            RETURN QUERY
            WITH
            -- Invoice Base
            invoice_base_cte AS (
                SELECT
                    inv.id,
                    inv.billing_cycle_id,
                    inv.account_id
                FROM invoice inv
                INNER JOIN account ac ON ac.id = inv.account_id
                LEFT JOIN billing_cycle AS bc ON bc.id=inv.billing_cycle_id
                WHERE
                (
                    $1 IS NOT NULL
                    AND inv.billing_cycle_id = ANY($1)
                    AND (
                        $3 IS NULL OR (
                            ac.name::text ILIKE '%' || $3 || '%'
                            OR ac.industry_vertical::text ILIKE '%' || $3 || '%'
                        )
                    )
                )
                OR
                (
                    $1 IS NULL
                    AND ($2 IS NULL OR ac.id = ANY($2))
                    AND inv.published_at IS NOT NULL
                )
            ),

            -- Total Invoice
            total_invoice_cte AS (
                SELECT COALESCE(COUNT(invb.id), 0) AS total_invoice
                FROM invoice_base_cte AS invb
            ),

            -- Total Sims
            total_sims_cte AS (
                SELECT COALESCE(SUM(sub.sims_total), 0) AS total_sims
                FROM invoice_base_cte AS invb
                INNER JOIN subscription AS sub ON sub.invoice_id = invb.id
            ),

            -- New Sims
            new_sims_cte AS (
                SELECT COUNT(ssad.id) AS new_sims
                FROM subscription_sim_allocation_details AS ssad
                INNER JOIN invoice_base_cte AS invb ON invb.id = ssad.invoice_id
            ),

            -- Total Active Sims
            active_sims_cte AS (
                SELECT COALESCE(COUNT(subs.id), 0) AS total_active_sims
                FROM invoice_base_cte AS invb
                INNER JOIN subscription AS sub ON sub.invoice_id = invb.id
                INNER JOIN subscription_sim AS subs ON subs.subscription_id = sub.id
            ),

            -- Charge = active_sims * access_fee

            subscriptions AS (
                SELECT
                    sub.id,
                    sub.invoice_id,
                    sub.rate_plan_id,
                    sub.name::text AS RatePlan,
                    sub.access_fee,
                    sub.sims_total,
                    sub.sim_charge
                FROM subscription sub
                INNER JOIN invoice_base_cte AS invb ON invb.id = sub.invoice_id
            ),
            active_sims AS (
                SELECT
                    invb.id AS invoice_id,
                    sub.rate_plan_id,
                    COUNT(subs.id) AS total_active_sim
                FROM invoice_base_cte AS invb
                INNER JOIN subscription sub ON sub.invoice_id = invb.id
                INNER JOIN subscription_sim subs ON subs.subscription_id = sub.id
                GROUP BY invb.id, sub.rate_plan_id
            ),
            charge_cte AS (
            SELECT
                COALESCE(SUM(s.access_fee * COALESCE(a.total_active_sim, 0)), 0)
                AS total_charge
            FROM subscriptions s
            LEFT JOIN active_sims a
                ON s.invoice_id = a.invoice_id
                AND s.rate_plan_id = a.rate_plan_id
            ),


            -- New SIM Charge = new_sims * sim_charge

            new_sim_charge_cte AS (
                SELECT
                    COALESCE(SUM(subquery.new_sims), 0) AS new_sim_charge
                FROM (
                    SELECT
                        COUNT(ssad.id) * sub.sim_charge AS new_sims
                    FROM subscription_sim_allocation_details AS ssad
                    INNER JOIN invoice_base_cte AS invb ON invb.id = ssad.invoice_id
                    INNER JOIN subscription AS sub ON invb.id = sub.invoice_id
                    GROUP BY invb.id, sub.rate_plan_id, sub.sim_charge
                ) AS subquery
            ),

            -- Adjustment
            adjustment_cte AS (
                SELECT COALESCE(SUM(adj.amount), 0) AS adjustment
                FROM invoice_base_cte AS invb
                INNER JOIN adjustment AS adj ON invb.id = adj.invoice_id
            ),

            -- Service Charge
            service_charge_cte AS (
                SELECT COALESCE(SUM(su.charge), 0) AS service_charge
                    FROM invoice_base_cte AS invb
                INNER JOIN subscription sub ON sub.invoice_id = invb.id
                INNER JOIN subscription_sim subs ON subs.subscription_id = sub.id
                INNER JOIN sim_usage AS su
                ON su.billing_cycle_id = invb.billing_cycle_id and subs.imsi = su.imsi
            ),

            -- Invoice Overage Charge and Total Charge
            invoice_overage_cte AS (
                SELECT
                    COALESCE(SUM(ioc.overage_charge), 0) AS overage_charge,
                    COALESCE(SUM(ioc.total_charge), 0) AS total_invoice_charge
                FROM invoice_base_cte AS invb
                INNER JOIN invoice_overage_charge AS ioc ON invb.id = ioc.invoice_id
            ),

            -- Service-Wise Volume Summary
            service_wise_cte AS (
                SELECT
                    COALESCE(SUM(CASE WHEN su.service = 'DATA'
                    THEN su.volume ELSE 0 END), 0) AS data_volume,
                    COALESCE(SUM(CASE WHEN su.service IN ('VOICE_MO', 'VOICE_MT')
                    THEN su.volume ELSE 0 END), 0) AS voice_volume,
                    COALESCE(SUM(CASE WHEN su.service IN ('SMS_MO', 'SMS_MT')
                    THEN su.volume ELSE 0 END), 0) AS sms_volume
                FROM invoice_base_cte AS invb
                INNER JOIN sim_usage AS su
                    ON su.billing_cycle_id = invb.billing_cycle_id
                INNER JOIN allocation AS al ON al.account_id = invb.account_id
                    AND su.imsi = al.imsi
            )

            -- Final SELECT combining all CTEs
            SELECT
                ti.total_invoice,
                ts.total_sims,
                ns.new_sims,
                act.total_active_sims,
                ch.total_charge,
                nsc.new_sim_charge,
                adj.adjustment,
                sc.service_charge,
                ioc.overage_charge,
                ioc.total_invoice_charge,
                sw.data_volume,
                sw.voice_volume,
                sw.sms_volume
            FROM total_invoice_cte ti,
                total_sims_cte ts,
                new_sims_cte ns,
                active_sims_cte act,
                charge_cte ch,
                new_sim_charge_cte nsc,
                adjustment_cte adj,
                service_charge_cte sc,
                invoice_overage_cte ioc,
                service_wise_cte sw;
        END;
        $$ LANGUAGE plpgsql;
        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS get_invoice_summary(INTEGER[], INTEGER[],
        INTEGER, INTEGER, varchar);
        DROP FUNCTION IF EXISTS get_invoice_adjustments(INTEGER[]);
        DROP FUNCTION IF EXISTS get_invoice_subscriptions(INTEGER[]);
        DROP FUNCTION IF EXISTS get_imsi_usage(INTEGER[], INTEGER[], INTEGER[]);
        DROP FUNCTION IF EXISTS get_invoice_summary_total(INTEGER[],
        INTEGER[], varchar);
    """
    )

    op.execute(
        """
    CREATE OR REPLACE FUNCTION get_invoice_summary(
    p_billing_cycle_id INTEGER[] DEFAULT NULL,
    p_billing_cycle_date DATE DEFAULT NULL,  -- Not used currently
    p_account_ids INTEGER[] DEFAULT NULL
)
RETURNS TABLE (
    invoice_id INTEGER,
    account_id INTEGER,
    name TEXT,
    logo_key TEXT,
    status TEXT,
    is_billable BOOLEAN,
    industry_vertical TEXT,
    currency TEXT,
    contract_end_date DATE,
    billing_cycle DATE,
    invoice_date TIMESTAMP,
    due_date TIMESTAMP,
    is_published BOOLEAN,
    rating_state TEXT,
    total_account_charge NUMERIC,
    new_sims INTEGER
)
AS $$
BEGIN
    RETURN QUERY
    WITH invoice_base AS (
    SELECT
        inv.id AS invoice_id,
        ac.id AS account_id,
        ac.name::text,
        ac.logo_key::text,
        ac.status::text,
        ac.is_billable,
        ac.industry_vertical::text,
        ac.currency,
        ac.contract_end_date,
        bc.month AS billing_cycle,
        COALESCE(inv.published_at, NULL) AS invoice_date,
        inv.published_at + ac.payment_terms * INTERVAL '1 day' AS due_date,
        (inv.published_at IS NOT NULL) AS is_published,
        inv.rating_state
    FROM invoice inv
    INNER JOIN account ac ON ac.id = inv.account_id
    LEFT JOIN billing_cycle AS bc ON bc.id=inv.billing_cycle_id
    WHERE
    (
        $1 IS NOT NULL
        AND inv.billing_cycle_id = ANY($1)
    )
    OR
    (
        $1 IS NULL
        AND ($3 IS NULL OR ac.id = ANY($3))
        AND inv.published_at IS NOT NULL
    )
    --GROUP BY inv.id
    ),
    total_account_charge AS (
    SELECT
        iac.invoice_id,
        COALESCE(SUM(iac.account_charge), 0) AS total_account_charge
    FROM invoice_account_charge AS iac
    INNER JOIN invoice AS inv ON inv.id = iac.invoice_id
    WHERE
    (
        $1 IS NOT NULL
        AND inv.billing_cycle_id = ANY($1)
    )
    OR
    (
        $1 IS NULL
        AND ($3 IS NULL OR inv.account_id = ANY($3))
        AND inv.published_at IS NOT NULL
    )
    GROUP BY iac.invoice_id
    ),
    new_sims AS (
    SELECT
        ssad.invoice_id,
        COUNT(ssad.id) AS new_sims
    FROM subscription_sim_allocation_details AS ssad
    INNER JOIN invoice inv ON inv.id = ssad.invoice_id
    WHERE
    (
        $1 IS NOT NULL
        AND inv.billing_cycle_id = ANY($1)
    )
    OR
    (
        $1 IS NULL
        AND ($3 IS NULL OR inv.account_id = ANY($3))
        AND inv.published_at IS NOT NULL
    )
    GROUP BY ssad.invoice_id
    )
    SELECT
        ib.invoice_id,
            ib.account_id,
            ib.name::text,
            ib.logo_key::text,
            ib.status::text,
            ib.is_billable,
            ib.industry_vertical::text,
            ib.currency::text,
            ib.contract_end_date,
            ib.billing_cycle,
            ib.invoice_date,
            ib.due_date,
            ib.is_published,
            ib.rating_state::text,
        COALESCE(tac.total_account_charge, 0) as total_account_charge,
        COALESCE(ns.new_sims, 0)::integer as new_sims
    FROM invoice_base ib
    LEFT JOIN total_account_charge tac ON ib.invoice_id = tac.invoice_id
    LEFT JOIN new_sims ns ON ib.invoice_id = ns.invoice_id;
END;
$$ LANGUAGE plpgsql;
    """
    )

    op.execute(
        """
    CREATE OR REPLACE FUNCTION get_invoice_subscriptions(
    p_billing_cycle_id INTEGER[] DEFAULT NULL,
    p_billing_cycle_date DATE DEFAULT NULL,
    p_account_ids INTEGER[] DEFAULT NULL
)
RETURNS TABLE (
    id INTEGER,
    invoice_id INTEGER,
    rate_plan_id INTEGER,
    RatePlan TEXT,
    access_fee NUMERIC,
    sims_total INTEGER,
    sim_charge NUMERIC,
    total_active_sim INTEGER
)
AS $$
BEGIN
    RETURN QUERY
    WITH filtered_invoices AS (
        SELECT inv.*, inv.account_id, bc.month AS billing_cycle
        FROM invoice inv
        LEFT JOIN billing_cycle bc ON bc.id = inv.billing_cycle_id
        WHERE
            (
                p_billing_cycle_id IS NOT NULL
                AND inv.billing_cycle_id = ANY(p_billing_cycle_id)
            )
            OR (
                p_billing_cycle_id IS NULL
                AND (p_account_ids IS NULL OR inv.account_id = ANY(p_account_ids))
                AND inv.published_at IS NOT NULL
            )
    ),
    subscriptions AS (
        SELECT
            sub.id,
            sub.invoice_id,
            sub.rate_plan_id,
            sub.name::text AS RatePlan,
            sub.access_fee,
            sub.sims_total,
            sub.sim_charge
        FROM subscription sub
        INNER JOIN filtered_invoices fi ON fi.id = sub.invoice_id
    ),
    active_sims AS (
        SELECT
            fi.id AS invoice_id,
            sub.rate_plan_id,
            COUNT(subs.id) AS total_active_sim
        FROM filtered_invoices fi
        INNER JOIN subscription sub ON sub.invoice_id = fi.id
        INNER JOIN subscription_sim subs ON subs.subscription_id = sub.id
        GROUP BY fi.id, sub.rate_plan_id
    )
    SELECT
        s.id,
        s.invoice_id,
        s.rate_plan_id,
        s.RatePlan,
        s.access_fee,
        s.sims_total,
        s.sim_charge,
        COALESCE(a.total_active_sim, 0)::integer AS total_active_sim
    FROM subscriptions s
    LEFT JOIN active_sims a
        ON s.invoice_id = a.invoice_id AND s.rate_plan_id = a.rate_plan_id;
END;
$$ LANGUAGE plpgsql;
    """
    )
    op.execute(
        """
    CREATE OR REPLACE FUNCTION get_invoice_adjustments(
    p_billing_cycle_id INTEGER[] DEFAULT NULL,
    p_billing_cycle_date DATE DEFAULT NULL,
    p_account_ids INTEGER[] DEFAULT NULL
)
RETURNS TABLE (
    id INTEGER,
    invoice_id INTEGER,
    date DATE,
    amount NUMERIC,
    type TEXT
)
AS $$
BEGIN
    RETURN QUERY
    WITH filtered_invoices AS (
        SELECT inv.id
        FROM invoice inv
        LEFT JOIN billing_cycle bc ON bc.id = inv.billing_cycle_id
        WHERE
            (
                p_billing_cycle_id IS NULL
                AND inv.billing_cycle_id = ANY(p_billing_cycle_id)
            )
            OR (
                p_billing_cycle_id IS NULL
                AND (p_account_ids IS NULL OR inv.account_id = ANY(p_account_ids))
                AND inv.published_at IS NOT NULL
            )
    )
    SELECT
        adj.id,
        adj.invoice_id,
        adj.date,
        adj.amount,
        adj.type::text
    FROM adjustment AS adj
    INNER JOIN filtered_invoices fi ON fi.id = adj.invoice_id;
END;
$$ LANGUAGE plpgsql;
"""
    )
    op.execute(
        """
    CREATE OR REPLACE FUNCTION get_imsi_usage(
    p_billing_cycle_id INTEGER[] DEFAULT NULL,
    p_billing_cycle_date DATE DEFAULT NULL,
    p_account_ids INTEGER[] DEFAULT NULL
)
RETURNS TABLE (
    invoice_id INTEGER,
    service service,
    total_volume NUMERIC,
    total_charge NUMERIC,
    total_overage_charge NUMERIC,
    overage_charge NUMERIC
)
AS $$
BEGIN
    RETURN QUERY
    WITH services AS (
        SELECT UNNEST(
            ARRAY['DATA', 'VOICE_MO', 'VOICE_MT', 'SMS_MO', 'SMS_MT']::service[]
        ) AS service
    ),
    imsi_list AS (
        SELECT subs.imsi, subs.subscription_id
        FROM subscription_sim subs
        INNER JOIN subscription sub ON subs.subscription_id = sub.id
        INNER JOIN invoice inv ON inv.id = sub.invoice_id
        LEFT JOIN billing_cycle bc ON bc.id = inv.billing_cycle_id
        WHERE
            (
                p_billing_cycle_id IS NOT NULL
                AND inv.billing_cycle_id = ANY(p_billing_cycle_id)
            )
            OR (
                p_billing_cycle_id IS NULL
                AND (p_account_ids IS NULL OR inv.account_id = ANY(p_account_ids))
                AND inv.published_at IS NOT NULL
            )
        GROUP BY subs.imsi, subs.subscription_id
    ),
    imsi_service AS (
        SELECT i.imsi, i.subscription_id, s.service
        FROM imsi_list i
        CROSS JOIN services s
    ),
    usage_data AS (
        SELECT
            su.imsi,
            su.service,
            su.billing_cycle_id,
            SUM(su.charge) AS charge,
            SUM(su.volume) AS volume
        FROM sim_usage su
        INNER JOIN allocation al ON al.imsi = su.imsi AND (
            p_account_ids IS NULL OR al.account_id = ANY(p_account_ids)
        )
        WHERE
            (
                p_billing_cycle_id IS NOT NULL
                AND su.billing_cycle_id = ANY(p_billing_cycle_id)
            )
            OR (
                p_billing_cycle_id IS NULL
                AND (p_account_ids IS NULL OR al.account_id = ANY(p_account_ids))
            )
        GROUP BY su.imsi, su.service, su.billing_cycle_id
    ),
    combined AS (
        SELECT
            inv.id AS invoice_id,
            ims.service,
            COALESCE(ud.volume, 0) AS volume,
            COALESCE(ud.charge, 0) AS charge,
            COALESCE(ioc.total_charge, 0) AS total_overage_charge,
            COALESCE(ioc.overage_charge, 0) AS overage_charge
        FROM imsi_service ims
        INNER JOIN subscription sub ON ims.subscription_id = sub.id
        INNER JOIN invoice inv ON inv.id = sub.invoice_id
            -- AND (
            --    p_billing_cycle_id IS NOT NULL
            --    AND inv.billing_cycle_id = ANY(p_billing_cycle_id))
        LEFT JOIN usage_data ud ON ud.imsi = ims.imsi AND ud.service = ims.service
            -- AND (
            --    p_billing_cycle_id IS NOT NULL
            --    AND ud.billing_cycle_id = ANY(p_billing_cycle_id))
        LEFT JOIN invoice_overage_charge ioc ON ioc.invoice_id = inv.id
        AND ioc.service = ims.service
    )
    SELECT
        c.invoice_id,
        c.service,
        SUM(c.volume) AS total_volume,
        SUM(c.charge) AS total_charge,
        SUM(c.total_overage_charge) AS total_overage_charge,
        SUM(c.overage_charge) AS overage_charge
    FROM combined c
    GROUP BY c.invoice_id, c.service
    ORDER BY c.invoice_id, c.service;
END;
$$ LANGUAGE plpgsql;
"""
    )
