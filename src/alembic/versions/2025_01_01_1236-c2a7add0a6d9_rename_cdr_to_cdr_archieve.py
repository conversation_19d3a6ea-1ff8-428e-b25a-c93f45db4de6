"""rename_cdr_to_cdr_archieve

Revision ID: c2a7add0a6d9
Revises: e984cbd43d25
Create Date: 2025-01-01 12:36:20.888400

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "c2a7add0a6d9"
down_revision = "e984cbd43d25"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.rename_table("cdr", "cdr_archieve")
    op.execute(
        """
            ALTER TABLE cdr_archieve RENAME CONSTRAINT
            uq_cdr_uuid TO uq_cdr_archieve_uuid;
        """
    )
    op.rename_table("cdr_new", "cdr")

    op.execute(
        """
        DO $$
        DECLARE
            partition_name TEXT;         -- Current partition name
            new_partition_name TEXT;     -- New partition name after replacement
        BEGIN
            FOR partition_name IN
                SELECT inhrelid::regclass::text
                FROM pg_inherits
                WHERE inhparent = 'cdr'::regclass
            LOOP
                -- Generate the new partition name
                new_partition_name := replace(partition_name, 'cdr_new', 'cdr');

                -- Log the renaming action
                RAISE NOTICE 'Renaming partition % to %',
                partition_name, new_partition_name;

                -- Rename the partition
                EXECUTE format('ALTER TABLE %I RENAME TO %I;',
                            partition_name,
                            new_partition_name);
            END LOOP;

            -- Log completion
            RAISE NOTICE 'All partitions renamed successfully.';
        END $$;

        """
    )


def downgrade() -> None:
    op.rename_table("cdr", "cdr_new")

    op.execute(
        """
        DO $$
        DECLARE
            partition_name TEXT;         -- Current partition name
            new_partition_name TEXT;     -- New partition name after replacement
        BEGIN
            FOR partition_name IN
                SELECT inhrelid::regclass::text
                FROM pg_inherits
                WHERE inhparent = 'cdr_new'::regclass
            LOOP
                -- Generate the new partition name
                new_partition_name := replace(partition_name, 'cdr', 'cdr_new');

                -- Log the renaming action
                RAISE NOTICE 'Renaming partition % to %',
                partition_name, new_partition_name;

                -- Rename the partition
                EXECUTE format('ALTER TABLE %I RENAME TO %I;',
                            partition_name,
                            new_partition_name);
            END LOOP;

            -- Log completion
            RAISE NOTICE 'All partitions renamed successfully.';
        END $$;

        """
    )
    op.rename_table("cdr_archieve", "cdr")
    op.execute("ALTER TABLE cdr RENAME CONSTRAINT uq_cdr_archieve_uuid TO uq_cdr_uuid;")
