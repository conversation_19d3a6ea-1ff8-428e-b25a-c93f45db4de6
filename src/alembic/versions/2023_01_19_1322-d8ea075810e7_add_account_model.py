"""Add account model

Revision ID: d8ea075810e7
Revises: e0c872b3887b
Create Date: 2023-01-19 13:22:37.181550

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "d8ea075810e7"
down_revision = "e0c872b3887b"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "account",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("name", sa.String(length=256), nullable=False),
        sa.Column("agreement_number", sa.String(length=12), nullable=False),
        sa.Column(
            "status",
            sa.Enum("UNKNOWN", "ACTIVE", "CLOSED", name="account_status"),
            nullable=False,
        ),
        sa.Column("currency", sa.CHAR(length=3), nullable=False),
        sa.Column("country", sa.CHAR(length=2), nullable=False),
        sa.Column(
            "industry_vertical",
            sa.Enum(
                "AEROSPACE",
                "AGGREGATOR",
                "AGRICULTURE",
                "CONSTRUCTION",
                "DEFENCE",
                "EDUCATION",
                "ENVIRONMENT",
                "FOOD",
                "HEALTH_CARE",
                "LOCAL_GOVT_OR_PUBLIC_SECTOR",
                "LOGISTICS_AND_DISTRIBUTION",
                "MANUFACTURING",
                "RETAIL_OR_MARKETING",
                "SECURITY",
                "TELECOMMUNICATIONS",
                "TRANSPORT",
                "UTILITIES",
                name="industry_vertical",
            ),
            nullable=False,
        ),
        sa.Column(
            "sales_channel",
            sa.Enum("WHOLESALE", "INDIRECT", "INTERNAL", "TRIAL", name="sales_channel"),
            nullable=False,
        ),
        sa.Column("sales_person", sa.String(), nullable=False),
        sa.Column("organization_id", sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_account")),
    )
    op.create_table(
        "account_product_type",
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.Column(
            "product_type",
            sa.Enum("NATIONAL_ROAMING", name="product_type"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["account_id"],
            ["account.id"],
            name=op.f("fk_account_product_type_account_id_account"),
        ),
        sa.PrimaryKeyConstraint(
            "account_id", "product_type", name=op.f("pk_account_product_type")
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("account_product_type")
    op.drop_table("account")
    # ### end Alembic commands ###
