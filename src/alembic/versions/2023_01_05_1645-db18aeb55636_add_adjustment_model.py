"""Add Adjustment model

Revision ID: db18aeb55636
Revises: 6d873ace0a71
Create Date: 2023-01-05 16:45:09.673777

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "db18aeb55636"
down_revision = "6d873ace0a71"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "adjustment",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("invoice_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("date", sa.Date(), nullable=False),
        sa.Column("description", sa.String(length=255), nullable=False),
        sa.Column("amount", sa.Numeric(), nullable=False),
        sa.ForeignKeyConstraint(
            ["invoice_id"],
            ["invoice.id"],
            name=op.f("fk_adjustment_invoice_id_invoice"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_adjustment")),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("adjustment")
    # ### end Alembic commands ###
