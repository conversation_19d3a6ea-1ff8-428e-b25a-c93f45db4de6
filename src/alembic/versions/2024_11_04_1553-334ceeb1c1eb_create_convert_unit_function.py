"""create convert_unit function

Revision ID: 334ceeb1c1eb
Revises: eaf96e271ac5
Create Date: 2024-11-04 15:53:05.088039

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "334ceeb1c1eb"
down_revision = "eaf96e271ac5"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        CREATE OR REPLACE FUNCTION convert_unit(range_unit Unit, range_to NUMERIC)
        RETURNS NUMERIC AS $$
        BEGIN
            RETURN CASE
                WHEN range_unit = 'Min' THEN range_to * 60
                WHEN range_unit = 'SMS' THEN range_to * 1
                WHEN range_unit = 'KB' THEN range_to * 1024
                WHEN range_unit = 'MB' THEN range_to * 1024 * 1024
                WHEN range_unit = 'GB' THEN range_to * 1024 * 1024 * 1024
                WHEN range_unit = 'TB' THEN range_to * 1024 * 1024 * 1024 * 1024
                WHEN range_unit = 'PB' THEN range_to * 1024 * 1024 * 1024 * 1024 * 1024
                ELSE range_to
            END;
        END;
        $$ LANGUAGE plpgsql;

        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS convert_unit(Unit, NUMERIC);
        """
    )
