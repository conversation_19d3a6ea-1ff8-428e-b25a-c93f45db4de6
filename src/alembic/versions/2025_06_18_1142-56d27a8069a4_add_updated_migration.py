"""add updated migration

Revision ID: 56d27a8069a4
Revises: 099c6b6d2498
Create Date: 2025-06-18 11:42:36.967814

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "56d27a8069a4"
down_revision = "099c6b6d2498"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS get_imsi_usage(INTEGER[], INTEGER[], INTEGER[]);
    """
    )
    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_imsi_usage(
            p_invoice_id INTEGER[],
            p_billing_cycle_id INTEGER[] DEFAULT NULL,
            p_account_ids INTEGER[] DEFAULT NULL
        )
        RETURNS TABLE (
            invoice_id INTEGER,
            service service,
            total_volume NUMERIC,
            total_charge NUMERIC,
            total_overage_charge NUMERIC,
            overage_charge NUMERIC
        )
        AS $$
        BEGIN
            RETURN QUERY
            WITH services AS (
                SELECT UNNEST(
                    ARRAY['DATA', 'VOICE_MO', 'VOICE_MT', 'SMS_MO', 'SMS_MT']::service[]
                ) AS service
            ),
            imsi_list AS (
                SELECT subs.imsi, subs.subscription_id
                FROM subscription_sim subs
                INNER JOIN subscription sub ON subs.subscription_id = sub.id
                INNER JOIN invoice inv ON inv.id = sub.invoice_id
                LEFT JOIN billing_cycle bc ON bc.id = inv.billing_cycle_id
                WHERE
                    inv.id = ANY(p_invoice_id)
                GROUP BY subs.imsi, subs.subscription_id
            ),
            imsi_service AS (
                SELECT i.imsi, i.subscription_id, s.service
                FROM imsi_list i
                CROSS JOIN services s
            ),
            usage_data AS (
                SELECT
                    su.imsi,
                    su.service,
                    su.billing_cycle_id,
                    SUM(su.charge) AS charge,
                    SUM(su.volume) AS volume
                from invoice as inv
                inner join subscription as sub on sub.invoice_id=inv.id
                inner join subscription_sim as subs on subs.subscription_id=sub.id
                inner join sim_usage as su on su.imsi=subs.imsi and
                su.billing_cycle_id=inv.billing_cycle_id
                WHERE
                    (
                        p_billing_cycle_id IS NOT NULL
                        AND su.billing_cycle_id = ANY(p_billing_cycle_id)
                        AND inv.id = ANY(p_invoice_id)
                    )
                    OR (
                        p_billing_cycle_id IS NULL
                        AND inv.account_id = ANY(p_account_ids)
                        AND inv.id = ANY(p_invoice_id)
                    )
                GROUP BY su.imsi, su.service, su.billing_cycle_id
            ),
            combined AS (
                SELECT
                    inv.id AS invoice_id,
                    ims.service,
                    COALESCE(ud.volume, 0) AS volume,
                    COALESCE(ud.charge, 0) AS charge,
                    COALESCE(ioc.total_charge, 0) AS total_overage_charge,
                    COALESCE(ioc.overage_charge, 0) AS overage_charge
                FROM imsi_service ims
                INNER JOIN subscription sub ON ims.subscription_id = sub.id
                INNER JOIN invoice inv ON inv.id = sub.invoice_id
                LEFT JOIN usage_data ud ON ud.imsi = ims.imsi
                AND ud.billing_cycle_id=inv.billing_cycle_id
                AND ud.service = ims.service
                LEFT JOIN invoice_overage_charge ioc ON ioc.invoice_id = inv.id
                AND ioc.service = ims.service
            )
            SELECT
                c.invoice_id,
                c.service,
                SUM(c.volume) AS total_volume,
                SUM(c.charge) AS total_charge,
                SUM(c.total_overage_charge) AS total_overage_charge,
                SUM(c.overage_charge) AS overage_charge
            FROM combined c
            GROUP BY c.invoice_id, c.service
            ORDER BY c.invoice_id DESC, c.service;
        END;
        $$ LANGUAGE plpgsql;
        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS get_imsi_usage(INTEGER[], INTEGER[], INTEGER[]);
    """
    )
    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_imsi_usage(
            p_invoice_id INTEGER[],
            p_billing_cycle_id INTEGER[] DEFAULT NULL,
            p_account_ids INTEGER[] DEFAULT NULL
        )
        RETURNS TABLE (
            invoice_id INTEGER,
            service service,
            total_volume NUMERIC,
            total_charge NUMERIC,
            total_overage_charge NUMERIC,
            overage_charge NUMERIC
        )
        AS $$
        BEGIN
            RETURN QUERY
            WITH services AS (
                SELECT UNNEST(
                    ARRAY['DATA', 'VOICE_MO', 'VOICE_MT', 'SMS_MO', 'SMS_MT']::service[]
                ) AS service
            ),
            imsi_list AS (
                SELECT subs.imsi, subs.subscription_id
                FROM subscription_sim subs
                INNER JOIN subscription sub ON subs.subscription_id = sub.id
                INNER JOIN invoice inv ON inv.id = sub.invoice_id
                LEFT JOIN billing_cycle bc ON bc.id = inv.billing_cycle_id
                WHERE
                    inv.id = ANY(p_invoice_id)
                GROUP BY subs.imsi, subs.subscription_id
            ),
            imsi_service AS (
                SELECT i.imsi, i.subscription_id, s.service
                FROM imsi_list i
                CROSS JOIN services s
            ),
            usage_data AS (
                SELECT
                    su.imsi,
                    su.service,
                    su.billing_cycle_id,
                    SUM(su.charge) AS charge,
                    SUM(su.volume) AS volume
                FROM sim_usage su
                INNER JOIN allocation al ON al.imsi = su.imsi AND (
                    p_account_ids IS NULL OR al.account_id = ANY(p_account_ids)
                )
                WHERE
                    (
                        p_billing_cycle_id IS NOT NULL
                        AND su.billing_cycle_id = ANY(p_billing_cycle_id)
                    )
                    OR (
                        p_billing_cycle_id IS NULL
                        AND (p_account_ids IS NULL
                        OR al.account_id = ANY(p_account_ids))
                    )
                GROUP BY su.imsi, su.service, su.billing_cycle_id
            ),
            combined AS (
                SELECT
                    inv.id AS invoice_id,
                    ims.service,
                    COALESCE(ud.volume, 0) AS volume,
                    COALESCE(ud.charge, 0) AS charge,
                    COALESCE(ioc.total_charge, 0) AS total_overage_charge,
                    COALESCE(ioc.overage_charge, 0) AS overage_charge
                FROM imsi_service ims
                INNER JOIN subscription sub ON ims.subscription_id = sub.id
                INNER JOIN invoice inv ON inv.id = sub.invoice_id
                LEFT JOIN usage_data ud ON ud.imsi = ims.imsi
                AND ud.service = ims.service
                LEFT JOIN invoice_overage_charge ioc ON ioc.invoice_id = inv.id
                AND ioc.service = ims.service
            )
            SELECT
                c.invoice_id,
                c.service,
                SUM(c.volume) AS total_volume,
                SUM(c.charge) AS total_charge,
                SUM(c.total_overage_charge) AS total_overage_charge,
                SUM(c.overage_charge) AS overage_charge
            FROM combined c
            GROUP BY c.invoice_id, c.service
            ORDER BY c.invoice_id DESC, c.service;
        END;
        $$ LANGUAGE plpgsql;
        """
    )
