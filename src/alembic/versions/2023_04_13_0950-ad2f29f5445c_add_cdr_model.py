"""add_cdr_model

Revision ID: ad2f29f5445c
Revises: 4ed3b652ebf5
Create Date: 2023-04-13 09:50:28.326537

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "ad2f29f5445c"
down_revision = "4ed3b652ebf5"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "cdr",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False, start=1, increment=1),
            nullable=False,
        ),
        sa.Column("uuid", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("iccid", sa.String(length=20), nullable=False),
        sa.Column("country", sa.String(length=3), nullable=False),
        sa.Column("carrier", sa.String(length=5), nullable=False),
        sa.Column(
            "created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
    )


def downgrade() -> None:
    op.drop_table("cdr")
