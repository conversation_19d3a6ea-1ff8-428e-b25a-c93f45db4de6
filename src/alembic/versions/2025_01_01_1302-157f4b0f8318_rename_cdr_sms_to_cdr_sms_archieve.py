"""rename_cdr_sms_to_cdr_sms_archieve

Revision ID: 157f4b0f8318
Revises: 090582ba7165
Create Date: 2025-01-01 13:02:39.539883

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "157f4b0f8318"
down_revision = "090582ba7165"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.rename_table("cdr_sms", "cdr_sms_archieve")
    op.rename_table("cdr_sms_new", "cdr_sms")
    op.execute(
        """
        DO $$
        DECLARE
            partition_name TEXT;         -- Current partition name
            new_partition_name TEXT;     -- New partition name after replacement
        BEGIN
            FOR partition_name IN
                SELECT inhrelid::regclass::text
                FROM pg_inherits
                WHERE inhparent = 'cdr_sms'::regclass
            LOOP
                -- Generate the new partition name
                new_partition_name := replace(partition_name, 'cdr_sms_new', 'cdr_sms');

                -- Log the renaming action
                RAISE NOTICE 'Renaming partition % to %',
                partition_name, new_partition_name;

                -- Rename the partition
                EXECUTE format('ALTER TABLE %I RENAME TO %I;',
                            partition_name,
                            new_partition_name);
            END LOOP;

            -- Log completion
            RAISE NOTICE 'All partitions renamed successfully.';
        END $$;


        """
    )


def downgrade() -> None:
    op.rename_table("cdr_sms", "cdr_sms_new")
    op.execute(
        """
        DO $$
        DECLARE
            partition_name TEXT;         -- Current partition name
            new_partition_name TEXT;     -- New partition name after replacement
        BEGIN
            FOR partition_name IN
                SELECT inhrelid::regclass::text
                FROM pg_inherits
                WHERE inhparent = 'cdr_sms_new'::regclass
            LOOP
                -- Generate the new partition name
                new_partition_name := replace(partition_name, 'cdr_sms', 'cdr_sms_new');

                -- Log the renaming action
                RAISE NOTICE 'Renaming partition % to %',
                partition_name, new_partition_name;

                -- Rename the partition
                EXECUTE format('ALTER TABLE %I RENAME TO %I;',
                            partition_name,
                            new_partition_name);
            END LOOP;

            -- Log completion
            RAISE NOTICE 'All partitions renamed successfully.';
        END $$;


        """
    )
    op.rename_table("cdr_sms_archieve", "cdr_sms")
