"""create_invoice_reconciliation_function

Revision ID: 4250556361bd
Revises: e4e5c7babc99
Create Date: 2024-01-05 12:42:55.752082

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "4250556361bd"
down_revision = "e4e5c7babc99"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
            CREATE OR REPLACE FUNCTION public.invoice_recon(
                start_date_param date,
                end_date_param date,
                billing_cycle_id_param integer)
                RETURNS TABLE(
                    service text,
                    su_imsi character varying,
                    cda_imsi character varying,
                    su_usage bigint,
                    cda_usage bigint,
                    su_cda_variance bigint)
                LANGUAGE 'plpgsql'
                COST 100
                VOLATILE PARALLEL UNSAFE
                ROWS 1000

            AS $BODY$
            BEGIN

                FOR service, su_imsi, cda_imsi, su_usage, cda_usage, su_cda_variance IN

                WITH cdr_sms_agg AS (
                        SELECT COUNT(cs.id) AS volume, cd.imsi
                        FROM cdr_sms AS cs
                        INNER JOIN cdr AS cd ON cs.cdr_uuid = cd.uuid
                        WHERE cs.date_sent BETWEEN start_date_param AND end_date_param
                        GROUP BY cd.imsi
                    )
                    SELECT 'DATA' AS service,
                    su.imsi AS su_imsi,
                    cda.imsi AS cda_imsi,
                    COALESCE(SUM(su.volume), 0) AS su_usage,
                    COALESCE(SUM(cda.usage), 0) AS cda_usage,
                    (COALESCE(SUM(su.volume),0) - COALESCE(SUM(cda.usage),0))
                    AS su_cda_variance
                    FROM sim_usage AS su
                    LEFT JOIN cdr_data_aggregate AS cda ON su.imsi = cda.imsi
                    AND cda.month = start_date_param
                    WHERE su.billing_cycle_id = billing_cycle_id_param
                    AND su.service = 'DATA'
                    GROUP BY su.imsi,cda.imsi

                    UNION

                    SELECT 'DATA' AS service,su.imsi AS su_imsi, cda.imsi AS cda_imsi,
                    COALESCE(SUM(su.volume), 0) AS su_usage,
                    COALESCE(SUM(cda.usage), 0) AS cda_usage,
                    (COALESCE(SUM(su.volume),0) - COALESCE(SUM(cda.usage),0))
                    AS su_cda_variance
                    FROM cdr_data_aggregate AS cda
                    LEFT JOIN sim_usage AS su ON su.imsi = cda.imsi
                    AND su.billing_cycle_id = billing_cycle_id_param
                    AND su.service = 'DATA'
                    WHERE cda.month = start_date_param
                    GROUP BY su.imsi,cda.imsi

                    UNION

                    SELECT 'VOICE' AS service,su.imsi AS su_imsi,cda.imsi AS cda_imsi,
                    COALESCE(SUM(su.volume),0) AS su_usage,
                    COALESCE(SUM(cda.voice_usage), 0) AS cda_usage,
                    (COALESCE(SUM(su.volume),0) - COALESCE(SUM(cda.voice_usage),0))
                    AS su_cda_variance
                    FROM sim_usage AS su
                    LEFT JOIN cdr_voice_aggregate AS cda ON su.imsi=cda.imsi
                    AND cda.month=start_date_param
                    WHERE su.billing_cycle_id=billing_cycle_id_param
                    AND (su.service='VOICE_MO' OR su.service='VOICE_MT')
                    GROUP BY su.imsi,cda.imsi

                    UNION

                    SELECT 'VOICE' AS service,su.imsi AS su_imsi,cda.imsi AS cda_imsi,
                    COALESCE(SUM(su.volume),0) AS su_usage,
                    COALESCE(SUM(cda.voice_usage), 0) AS cda_usage,
                    (COALESCE(SUM(su.volume),0) - COALESCE(SUM(cda.voice_usage),0))
                    AS su_cda_variance
                    FROM cdr_voice_aggregate AS cda
                    LEFT JOIN sim_usage AS su ON su.imsi=cda.imsi
                    AND su.billing_cycle_id=billing_cycle_id_param
                    AND (su.service='VOICE_MO' OR su.service='VOICE_MT')
                    WHERE cda.month=start_date_param
                    GROUP BY su.imsi,cda.imsi



                    UNION

                    SELECT 'SMS' AS service,su.imsi AS su_imsi,cds.imsi AS cda_imsi,
                    COALESCE(SUM(su.volume),0) AS su_usage,
                    COALESCE(SUM(cds.volume), 0) AS cda_usage,
                    (COALESCE(SUM(su.volume),0) - COALESCE(SUM(cds.volume),0))
                    AS su_cda_variance
                    FROM sim_usage AS su
                    LEFT JOIN cdr_sms_agg AS cds ON su.imsi=cds.imsi
                    WHERE su.billing_cycle_id=billing_cycle_id_param
                    AND (su.service='SMS_MO' OR su.service='SMS_MT')
                    GROUP BY su.imsi,cds.imsi

                    UNION

                    SELECT 'SMS' AS service,su.imsi AS su_imsi,cds.imsi AS cda_imsi,
                    COALESCE(SUM(su.volume),0) AS su_usage,
                    COALESCE(SUM(cds.volume), 0) AS cda_usage,
                    (COALESCE(SUM(su.volume),0) - COALESCE(SUM(cds.volume),0))
                    AS su_cda_variance
                    FROM cdr_sms_agg AS cds
                    LEFT JOIN sim_usage AS su
                    ON su.imsi=cds.imsi and su.billing_cycle_id=billing_cycle_id_param
                    AND (su.service='SMS_MO' OR su.service='SMS_MT')
                    GROUP BY su.imsi,cds.imsi
                LOOP
                    -- Return the fetched values
                    IF su_cda_variance = 0  THEN
                        IF cda_imsi IS NULL OR su_imsi IS NULL THEN
                            RETURN NEXT;
                        END IF;
                    ELSEIF su_cda_variance != 0  THEN
                        RETURN NEXT;
                    END IF;
                END LOOP;

                RETURN;
            END;
            $BODY$;
        """
    )


def downgrade() -> None:
    op.execute(
        """
            DROP FUNCTION IF EXISTS public.invoice_recon(date, date, integer);
    """
    )
