"""Remove calculation_state field from the BillingCycle model

Revision ID: d27b310dfe84
Revises: 594fc1e13f04
Create Date: 2023-05-25 14:45:28.704838

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "d27b310dfe84"
down_revision = "594fc1e13f04"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("billing_cycle", "calculation_state")
    # ### end Alembic commands ###


def downgrade() -> None:
    op.add_column(
        "billing_cycle",
        sa.Column(
            "calculation_state",
            sa.VARCHAR(length=11),
            autoincrement=False,
            nullable=True,
        ),
    )
    op.execute(
        """
        UPDATE billing_cycle
        SET calculation_state = (
            CASE
                WHEN EXISTS(
                    SELECT *
                    FROM invoice
                    WHERE invoice.billing_cycle_id = billing_cycle.id
                        AND invoice.rating_state = 'FAILED'
                    ) THEN 'FAILED'
                WHEN EXISTS(
                    SELECT *
                    FROM invoice
                    WHERE invoice.billing_cycle_id = billing_cycle.id
                        AND invoice.rating_state = 'IN_PROGRESS'
                    ) THEN 'IN_PROGRESS'
                ELSE
                    'DONE'
            END
            )
        """
    )
