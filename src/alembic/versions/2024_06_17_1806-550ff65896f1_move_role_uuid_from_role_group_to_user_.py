"""move role_uuid from role_group to user_role

Revision ID: 550ff65896f1
Revises: cfebd8f99ce7
Create Date: 2024-06-06 18:06:38.627308

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "550ff65896f1"
down_revision = "cfebd8f99ce7"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Step 1: Add the uuid column to the user_role table
    op.add_column("user_role", sa.Column("role_uuid", postgresql.UUID(), nullable=True))

    # Step 2: Migrate data from role_group to user_role
    connection = op.get_bind()
    connection.execute(
        """
        WITH cte AS (
            SELECT ur.id, rg.role_uuid::uuid
            FROM user_role as ur
            LEFT JOIN role_group as rg ON rg.role_id = ur.id
        )
        UPDATE user_role
        SET role_uuid = cte.role_uuid
        FROM cte
        WHERE user_role.id = cte.id;
    """
    )

    # Step 3: Remove the role_uuid column from the role_group table
    op.drop_column("role_group", "role_uuid")


def downgrade() -> None:
    # Step 1: Add the role_uuid column back to the role_group table
    op.add_column(
        "role_group", sa.Column("role_uuid", postgresql.UUID(), nullable=True)
    )

    # Step 2: Migrate data back from user_role to role_group
    connection = op.get_bind()
    connection.execute(
        """
        WITH cte AS (
            SELECT ur.id, ur.role_uuid
            FROM user_role as ur
        )
        UPDATE role_group
        SET role_uuid = cte.role_uuid
        FROM cte
        WHERE role_group.role_id = cte.id;
    """
    )

    # Step 3: Remove the uuid column from the user_role table
    op.drop_column("user_role", "role_uuid")
