"""create msisdn_pool_details_view

Revision ID: d2c7e82bbfc5
Revises: f800ca583740
Create Date: 2025-03-17 15:27:40.529070

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "d2c7e82bbfc5"
down_revision = "f800ca583740"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        CREATE OR REPLACE VIEW msisdn_pool_details_view AS
            SELECT
                msisdn_pool.msisdn,
                msisdn_pool.sim_profile,
                msisdn_pool.msisdn_factor,
                msisdn_pool.created_at,
                msisdn_pool.allocation_id,
                msisdn_pool.uploaded_by,
                sim_card.imsi,
                account.name as account_name,
                account.logo_key,
                account.country
            FROM msisdn_pool
            LEFT JOIN
                sim_card ON
                sim_card.msisdn = msisdn_pool.msisdn
            LEFT JOIN
                allocation ON
                allocation.id = sim_card.allocation_id
            LEFT JOIN
                account ON
                account.id = allocation.account_id;

        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP VIEW IF EXISTS msisdn_pool_details_view;
        """
    )
