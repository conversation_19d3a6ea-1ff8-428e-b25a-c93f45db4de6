"""modify rate_plan_by_accounts function get allowance usage change

Revision ID: 97ab3cb35238
Revises: c0f7c03ec05b
Create Date: 2024-11-26 20:56:29.346429

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "97ab3cb35238"
down_revision = "c0f7c03ec05b"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS rate_plan_by_accounts(varchar);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION rate_plan_by_accounts(
            search_term varchar DEFAULT NULL
        )
        RETURNS TABLE (
            account_id integer,
            account_name character varying,
            logo_key character varying,
            id integer,
            name character varying,
            access_fee numeric,
            currency character,
            is_default character varying,
            allowance_used numeric
        )
        LANGUAGE 'plpgsql'
        COST 100
        VOLATILE
        PARALLEL UNSAFE
        ROWS 1000
        AS $BODY$
        BEGIN
            RETURN QUERY
                WITH RankedRatePlans AS (
                    SELECT
                        a.id AS account_id,
                        a.name AS account_name,
                        a.logo_key,
                        r.id,
                        r.name,
                        r.access_fee,
                        a.currency,
                        ROW_NUMBER() OVER (PARTITION BY a.id ORDER BY r.id) AS row_num
                    FROM
                        rate_plan r
                    JOIN
                        account a ON r.account_id = a.id
                    WHERE
                        search_term IS NULL OR (
                            a.name ILIKE '%' || search_term || '%' OR
                            r.name ILIKE '%' || search_term || '%' OR
                            r.access_fee::text ILIKE '%' || search_term || '%'
                        )
                    GROUP BY
                        a.id, r.id
                ),
                AllowancesData AS (
                    SELECT
                        gau.account_id,
                        gau.rate_plan_id,
                        gau.allowance_used
                    FROM
                        get_allowance_usage() AS gau
                )
            SELECT
                rp.account_id,
                rp.account_name,
                rp.logo_key,
                rp.id,
                rp.name,
                rp.access_fee,
                rp.currency,
                (CASE WHEN rp.row_num = 1 THEN 'True' ELSE 'False' END
                )::character varying AS is_default,
                COALESCE(ad.allowance_used, 0) AS allowance_used
            FROM
                RankedRatePlans rp
            LEFT JOIN
                AllowancesData ad ON ad.account_id = rp.account_id
                AND ad.rate_plan_id = rp.id
            ORDER BY
                rp.account_name ASC, rp.id ASC;
        END;
        $BODY$;


        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS rate_plan_by_accounts(varchar);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION rate_plan_by_accounts(
            search_term varchar DEFAULT NULL
        )
        RETURNS TABLE (
            account_id integer,
            account_name character varying,
            logo_key character varying,
            id integer,
            name character varying,
            access_fee numeric,
            currency character,
            is_default character varying,
            allowance_used numeric
        )
        LANGUAGE 'plpgsql'
        COST 100
        VOLATILE
        PARALLEL UNSAFE
        ROWS 1000
        AS $BODY$
        DECLARE
            start_of_month date := date_trunc('month', CURRENT_DATE);
            end_of_month date := (date_trunc('month',
            CURRENT_DATE) + interval '1 month')::date;
        BEGIN
            RETURN QUERY
                WITH RankedRatePlans AS (
                    SELECT
                        a.id AS account_id,
                        a.name AS account_name,
                        a.logo_key,
                        r.id,
                        r.name,
                        r.access_fee,
                        a.currency,
                        ROW_NUMBER() OVER (PARTITION BY a.id ORDER BY r.id) AS row_num
                    FROM
                        rate_plan r
                    JOIN
                        account a ON r.account_id = a.id
                    WHERE
                        search_term IS NULL OR (
                            a.name ILIKE '%' || search_term || '%' OR
                            r.name ILIKE '%' || search_term || '%' OR
                            r.access_fee::text ILIKE '%' || search_term || '%'
                        )
                    GROUP BY
                        a.id, r.id
                ),
                AllowancesData AS (
                    SELECT
                        gau.account_id,
                        gau.rate_plan_id,
                        gau.allowance_used
                    FROM
                        get_allowance_usage(start_of_month, end_of_month) AS gau
                )
            SELECT
                rp.account_id,
                rp.account_name,
                rp.logo_key,
                rp.id,
                rp.name,
                rp.access_fee,
                rp.currency,
                (CASE WHEN rp.row_num = 1 THEN 'True' ELSE 'False' END
                )::character varying AS is_default,
                COALESCE(ad.allowance_used, 0) AS allowance_used
            FROM
                RankedRatePlans rp
            LEFT JOIN
                AllowancesData ad ON ad.account_id = rp.account_id
                AND ad.rate_plan_id = rp.id
            ORDER BY
                rp.account_name ASC, rp.id ASC;
        END;
        $BODY$;


        """
    )
