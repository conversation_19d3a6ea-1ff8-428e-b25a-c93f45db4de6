"""create_index_in_sim_card_table

Revision ID: f4ffd3b28ac4
Revises: 0cf658516c83
Create Date: 2025-06-04 10:36:41.976543

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "f4ffd3b28ac4"
down_revision = "0cf658516c83"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
    CREATE INDEX IF NOT EXISTS idx_simcard_msisdn ON sim_card (msisdn);
    """
    )

    op.execute(
        """
    CREATE INDEX IF NOT EXISTS idx_simcard_imsi ON sim_card (imsi);
    """
    )


def downgrade() -> None:
    op.execute("drop index if exists idx_simcard_msisdn")
    op.execute("drop index if exists idx_simcard_imsi")
