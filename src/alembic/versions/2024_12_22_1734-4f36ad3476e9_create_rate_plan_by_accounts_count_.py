"""create_rate_plan_by_accounts_count_function

Revision ID: 4f36ad3476e9
Revises: f7ad14421417
Create Date: 2024-12-22 17:34:25.249717

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "4f36ad3476e9"
down_revision = "f7ad14421417"
branch_labels = None
depends_on = None


def upgrade() -> None:

    op.execute(
        """
        CREATE OR REPLACE FUNCTION rate_plan_by_accounts_count(
            search_term varchar DEFAULT NULL,
            account_ids_param integer[] DEFAULT NULL
        )
        RETURNS TABLE (
            unique_account_count bigint
        )
        LANGUAGE 'plpgsql'
        COST 100
        VOLATILE
        PARALLEL UNSAFE
        ROWS 1000
        AS $BODY$
        DECLARE
            sql_query TEXT;
        BEGIN

            sql_query := $$
                SELECT COUNT(*) AS account_count
                    FROM (
                        SELECT rate_plan.account_id
                        FROM account AS a
                        JOIN rate_plan ON rate_plan.account_id = a.id
                        WHERE (
                            $1 IS NULL OR
                            a.name ILIKE '%' || $1 || '%' OR
                            rate_plan.name ILIKE '%' || $1 || '%'
                        )
                        AND (
                            $2 IS NULL OR
                            a.id = ANY($2)
                        )
                        GROUP BY rate_plan.account_id
                    ) AS grouped_accounts;
            $$;

            -- Execute the dynamic query and return the results
            RETURN QUERY EXECUTE sql_query USING search_term, account_ids_param;
        END;
        $BODY$;



        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS rate_plan_by_accounts_count(varchar, integer[]);
        """
    )
