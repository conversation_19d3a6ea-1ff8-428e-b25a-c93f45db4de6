"""modify_get_allowance_usage_function_for_cdr_sms_aggreagate_partition

Revision ID: c22641a180ed
Revises: 8f323733720c
Create Date: 2025-05-30 08:01:21.073354

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "c22641a180ed"
down_revision = "8f323733720c"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS get_allowance_usage(integer[], varchar[], service);
        """
    )
    op.execute(
        """
    CREATE OR REPLACE FUNCTION public.get_allowance_usage(
        account_ids integer[] DEFAULT NULL::integer[],
        rate_model_codes character varying[] DEFAULT NULL::character varying[],
        rate_plan_service service DEFAULT NULL::service
    )
    RETURNS TABLE(
        account_id integer,
        rate_plan_id integer,
        service text,
        allowance_used numeric
    )
    AS $$

    DECLARE
        data_partition TEXT :=
    'cdr_data_aggregate_' || to_char(current_date, 'YYYY_MM');
        voice_partition TEXT :=
    'cdr_voice_aggregate_' || to_char(current_date, 'YYYY_MM');
        sms_partition TEXT :=
    'cdr_sms_aggregate_' || to_char(current_date, 'YYYY_MM');

        -- data_partition TEXT := 'cdr_data_aggregate_2024_05';
        -- voice_partition TEXT := 'cdr_voice_aggregate_2024_05';
        -- sms_partition TEXT := 'cdr_sms_aggregate_2024_05';
        sql_query TEXT;
    BEGIN
        sql_query := '
            SELECT
                auv.account_id,
                auv.rate_plan_id,
                auv.service::text,
                ROUND(
                    COALESCE(SUM(usage_data.usage_val), 0) * 100.0 /
                    GREATEST(auv.total_allowance, 1), 2
                ) AS allowance_used
            FROM allowance_usage_view auv
            LEFT JOIN (
                SELECT imsi, usage AS usage_val, ''DATA'' AS service
                FROM ' || data_partition || '
                UNION ALL
                SELECT imsi, voice_usage AS usage_val, ''VOICE_MO'' AS service
                FROM ' || voice_partition || '
                UNION ALL
                SELECT imsi, voice_usage AS usage_val, ''VOICE_MT'' AS service
                FROM ' || voice_partition || '
                UNION ALL
                SELECT imsi, total_sms AS usage_val, ''SMS_MO'' AS service
                FROM ' || sms_partition || '
            ) AS usage_data
            ON usage_data.imsi = auv.imsi AND usage_data.service = auv.service::text
            WHERE ($1 IS NULL OR auv.account_id = ANY($1))
            GROUP BY auv.account_id, auv.rate_plan_id, auv.service, auv.total_allowance
            ORDER BY auv.account_id, auv.service;
        ';

        RETURN QUERY EXECUTE sql_query USING account_ids;
    END;
    $$ LANGUAGE plpgsql;
    """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS get_allowance_usage(integer[], varchar[], service);
        """
    )
    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_allowance_usage(
            account_ids integer[] DEFAULT NULL,
            rate_model_codes VARCHAR[] DEFAULT NULL,
            rate_plan_service service DEFAULT 'DATA'::service
        )
        RETURNS TABLE (
            account_id INT,
            rate_plan_id INT,
            allowance_used NUMERIC
        ) AS $$
        DECLARE
            partition_name TEXT;
            sql_query TEXT;
        BEGIN
            -- Construct the partition name dynamically for the current month
            partition_name := 'cdr_data_aggregate_' ||
                              to_char(current_date, 'YYYY_MM');

            -- Construct the SQL query dynamically
            sql_query := '
                SELECT
                    auv.account_id,
                    auv.rate_plan_id,
                    ROUND(
                        (COALESCE(SUM(cda.usage), 0) * 100) /
                        GREATEST(auv.total_allowance, 1),
                        2
                    ) AS allowance_used
                FROM
                    allowance_usage_view auv
                INNER JOIN ' || partition_name || ' AS cda
                    ON cda.imsi = auv.imsi
                WHERE
                    ($1 IS NULL OR auv.account_id = ANY($1)) AND
                    ($2 IS NULL OR auv.rate_model_code != ALL($2)) AND
                    ($3 IS NULL OR auv.service = $3)
                GROUP BY
                    auv.account_id,
                    auv.rate_plan_id,
                    auv.total_allowance,
                    auv.service;
            ';

            -- Execute the dynamically constructed query and return the result
            RETURN QUERY EXECUTE sql_query
                USING account_ids, rate_model_codes, rate_plan_service;
        END;
        $$ LANGUAGE plpgsql;
        """
    )
