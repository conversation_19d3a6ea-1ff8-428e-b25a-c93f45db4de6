"""insert data into allocation_details

Revision ID: e8311f4ecfd1
Revises: 54ba29f7f2e0
Create Date: 2024-01-02 15:27:23.283153

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "e8311f4ecfd1"
down_revision = "54ba29f7f2e0"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.execute(
        """
            insert into allocation_details
            (file_name,total_sim,error_sim)
            select CONCAT(id,'_',title),quantity,0 from allocation;
            """
    )


# ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
