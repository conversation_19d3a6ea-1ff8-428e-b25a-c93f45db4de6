"""add carrier name model

Revision ID: 7fc769669b03
Revises: ec1a39ce6309
Create Date: 2023-07-03 15:30:06.495652

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "7fc769669b03"
down_revision = "ec1a39ce6309"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "carrier_name",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False, start=1, increment=1),
            nullable=False,
        ),
        sa.Column("carrier", sa.String(length=5), nullable=False),
        sa.Column("carrier_name", sa.String(), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_carrier_name")),
    )

    # Insert multiple rows in a single statement
    op.execute(
        "INSERT INTO carrier_name (carrier, carrier_name) VALUES "
        "('GBRME', 'EE'), "
        "('GBROR', 'EE'), "
        "('GBRVF', 'Vodafone'), "
        "('GBRCN', 'O2')"
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("carrier_name")
    # ### end Alembic commands ###
