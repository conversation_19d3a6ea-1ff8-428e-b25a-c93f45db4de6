"""Remove carrier from sim usage model

Revision ID: fc792b9b1162
Revises: 3c4294d3bd3e
Create Date: 2023-01-09 14:55:32.859079

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "fc792b9b1162"
down_revision = "3c4294d3bd3e"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("sim_usage", "carrier")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "sim_usage",
        sa.Column("carrier", sa.CHAR(length=5), autoincrement=False, nullable=False),
    )
    # ### end Alembic commands ###
