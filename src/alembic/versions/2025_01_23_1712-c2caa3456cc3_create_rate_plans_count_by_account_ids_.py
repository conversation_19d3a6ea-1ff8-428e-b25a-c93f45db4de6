"""create rate_plans_count_by_account_ids function

Revision ID: c2caa3456cc3
Revises: 19bd2d7af2e1
Create Date: 2025-01-23 17:12:58.075779

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "c2caa3456cc3"
down_revision = "19bd2d7af2e1"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        CREATE OR REPLACE FUNCTION rate_plans_count_by_account_ids(
            account_ids_param integer[] DEFAULT NULL
        )
        RETURNS TABLE (
            unique_account_count bigint
        )
        LANGUAGE 'plpgsql'
        COST 100
        VOLATILE
        PARALLEL UNSAFE
        ROWS 1000
        AS $BODY$
        DECLARE
            sql_query TEXT;
        BEGIN

            sql_query := $$
                select count(*) from rate_plan where $1 IS NULL OR account_id = ANY($1)
            $$;

            -- Execute the dynamic query and return the results
            RETURN QUERY EXECUTE sql_query USING account_ids_param;
        END;
        $BODY$;



        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS rate_plans_count_by_account_ids(integer[]);
        """
    )
