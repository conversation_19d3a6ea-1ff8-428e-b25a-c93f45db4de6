"""stop alembic migration from this point

Revision ID: 4ae058e615ad
Revises: cac30912c33e
Create Date: 2025-01-16 14:08:41.884022

"""

# revision identifiers, used by Alembic.


from app.db.base import TEST_ENV

revision = "4ae058e615ad"
down_revision = "cac30912c33e"
branch_labels = None
depends_on = None


def upgrade() -> None:
    pass


def downgrade() -> None:
    # This condition will execute error only when running alembic downgrade
    if TEST_ENV is False:
        raise Exception("Downgrade is not allowed beyond this point.")
    else:
        pass
