"""create_cdr_data_partition_table

Revision ID: 1ed5a558a37c
Revises: 0043a7f96942
Create Date: 2025-01-01 11:37:27.835878

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "1ed5a558a37c"
down_revision = "0043a7f96942"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "cdr_data_new",
        sa.Column(
            "id",
            sa.Integer,
            sa.Identity(always=False, start=1, increment=1),
            primary_key=True,
            autoincrement=True,
        ),
        sa.Column("cdr_uuid", postgresql.UUID(), nullable=False),
        sa.Column("session_starttime", sa.DateTime(), nullable=False),
        sa.Column("session_endtime", sa.DateTime(), primary_key=True, nullable=False),
        sa.Column("duration", sa.Integer, nullable=False),
        sa.Column("data_volume", sa.<PERSON>, nullable=False),
        sa.PrimaryKeyConstraint("id", "session_endtime", name="pk_cdr_data_new"),
        postgresql_partition_by="RANGE (session_endtime)",
    )

    # Creating partitions for the CDR_DATA Table.

    op.execute(
        """
        DO $$
        DECLARE
            start_date DATE;
            end_date DATE := date_trunc('month',
            current_date + interval '1 month')::DATE;
            partition_date DATE;
            partition_name TEXT;
        BEGIN
            -- Select the smallest session_endtime from the cdr_data table
            -- and set it to the first day of its month
            SELECT date_trunc('month', MIN(session_endtime))::DATE
            INTO start_date FROM cdr_data;

            partition_date := start_date;
            WHILE partition_date < end_date LOOP
                partition_name := 'cdr_data_new_' || to_char(partition_date, 'YYYY_MM');
                EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF \
                cdr_data_new FOR VALUES FROM (%L) TO (%L)',
                            partition_name,
                            partition_date::DATE,
                            (partition_date + interval '1 month')::DATE);
                partition_date := partition_date + interval '1 month';
            END LOOP;
        END $$;

        """
    )

    # Creating index for the cdr_data_new Table.
    op.create_index(
        "idx_cdr_data_new_session_endtime", "cdr_data_new", ["session_endtime"]
    )
    op.create_index("idx_cdr_data_new_cdr_uuid", "cdr_data_new", ["cdr_uuid"])


def downgrade() -> None:

    op.drop_table("cdr_data_new")
