"""add new filed to sim provider log model

Revision ID: d532d051ad91
Revises: 18f2cfa3a584
Create Date: 2023-04-20 12:49:20.506562

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

from sim.domain.model import SimStatus

# revision identifiers, used by Alembic.
revision = "d532d051ad91"
down_revision = "18f2cfa3a584"
branch_labels = None
depends_on = None


def upgrade() -> None:
    sim_status = postgresql.ENUM(SimStatus, name="sim_status")
    op.add_column(
        "sim_provider_log",
        sa.Column(
            "prior_status",
            sim_status,
            nullable=True,
        ),
    )
    op.execute("""UPDATE sim_provider_log SET prior_status='READY_FOR_ACTIVATION';""")


def downgrade() -> None:
    op.drop_column("sim_provider_log", "prior_status")
