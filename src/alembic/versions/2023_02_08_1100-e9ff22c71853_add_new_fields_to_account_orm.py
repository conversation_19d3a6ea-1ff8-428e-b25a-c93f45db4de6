"""Add new fields to Account orm

Revision ID: e9ff22c71853
Revises: 790d58a10633
Create Date: 2023-02-08 11:00:33.259664

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "e9ff22c71853"
down_revision = "790d58a10633"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "account", sa.Column("contact_name", sa.String(length=60), nullable=True)
    )
    op.add_column("account", sa.Column("email", sa.String(), nullable=True))
    op.add_column("account", sa.Column("phone", sa.String(length=16), nullable=True))
    op.add_column("account", sa.Column("job_title", sa.String(), nullable=True))
    op.add_column("account", sa.Column("state_region", sa.String(), nullable=True))
    op.add_column("account", sa.Column("city", sa.String(), nullable=True))
    op.add_column("account", sa.Column("address1", sa.String(), nullable=True))
    op.add_column("account", sa.Column("address2", sa.String(), nullable=True))
    op.add_column("account", sa.Column("postcode", sa.String(length=15), nullable=True))
    op.alter_column(
        "account", "country", existing_type=sa.CHAR(length=2), nullable=True
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "account", "country", existing_type=sa.CHAR(length=2), nullable=False
    )
    op.drop_column("account", "postcode")
    op.drop_column("account", "address2")
    op.drop_column("account", "address1")
    op.drop_column("account", "city")
    op.drop_column("account", "state_region")
    op.drop_column("account", "job_title")
    op.drop_column("account", "phone")
    op.drop_column("account", "email")
    op.drop_column("account", "contact_name")
    # ### end Alembic commands ###
