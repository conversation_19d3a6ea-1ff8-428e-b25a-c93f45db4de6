"""create_rules_notification_table

Revision ID: 5ca57a045d2a
Revises: 120baaf928db
Create Date: 2024-07-18 15:16:16.699149

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "5ca57a045d2a"
down_revision = "120baaf928db"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "rules_notification",
        sa.<PERSON>umn(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("rules_uuid", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("notifications_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("notification", sa.<PERSON>(), nullable=False),
        sa.ForeignKeyConstraint(
            ["rules_uuid"],
            ["rules.uuid"],
            name=op.f("fk_rules_notification_rules_uuid_rules"),
        ),
        sa.ForeignKeyConstraint(
            ["notifications_id"],
            ["notifications.id"],
            name=op.f("fk_rules_notification_notifications_id_notifications"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_rules_notification")),
    )


def downgrade() -> None:
    op.drop_table("rules_notification")
