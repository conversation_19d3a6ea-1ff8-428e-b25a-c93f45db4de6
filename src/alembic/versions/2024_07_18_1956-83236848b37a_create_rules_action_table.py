"""create rules action table

Revision ID: 83236848b37a
Revises: 93ed268d8d62
Create Date: 2024-07-18 12:11:07.765144

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

from automation.domain.model import RuleAction

# revision identifiers, used by Alembic.
revision = "83236848b37a"
down_revision = "93ed268d8d62"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "rules_action",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("rules_uuid", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("actions_id", sa.Integer(), nullable=False),
        sa.Column(
            "action",
            postgresql.ENUM(RuleAction, name="rule_action", create_type=False),
            nullable=False,
        ),
        sa.<PERSON>umn(
            "action_value",
            sa.String(),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["rules_uuid"],
            ["rules.uuid"],
            name=op.f("fk_rules_action_rules_uuid_rules"),
        ),
        sa.ForeignKeyConstraint(
            ["actions_id"],
            ["actions.id"],
            name=op.f("fk_rules_action_actions_id_actions"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_rules_action")),
    )


def downgrade() -> None:
    op.drop_table("rules_action")
