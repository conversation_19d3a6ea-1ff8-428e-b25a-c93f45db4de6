"""Add payment_terms field for Account table as Billing Setting

Revision ID: 4ed3b652ebf5
Revises: f6f7a73e5ecc
Create Date: 2023-04-13 18:19:55.356964

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "4ed3b652ebf5"
down_revision = "f6f7a73e5ecc"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("account", sa.Column("payment_terms", sa.Integer(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("account", "payment_terms")
    # ### end Alembic commands ###
