"""Add Subscription and SubscriptionSIM models

Revision ID: 6a9d3f0df596
Revises: 90f599373d00
Create Date: 2023-05-08 18:25:07.304068

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "6a9d3f0df596"
down_revision = "90f599373d00"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "subscription",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("invoice_id", sa.Integer(), nullable=False),
        sa.Column("rate_plan_id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("access_fee", sa.Numeric(), nullable=False),
        sa.ForeignKeyConstraint(
            ["invoice_id"],
            ["invoice.id"],
            name=op.f("fk_subscription_invoice_id_invoice"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_subscription")),
    )
    op.create_table(
        "subscription_sim",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("sim_id", sa.Integer(), nullable=False),
        sa.Column("subscription_id", sa.Integer(), nullable=False),
        sa.Column("iccid", sa.String(length=20), nullable=False),
        sa.Column("imsi", sa.String(length=15), nullable=False),
        sa.Column("msisdn", sa.String(length=15), nullable=False),
        sa.Column("was_active", sa.Boolean(), nullable=False),
        sa.ForeignKeyConstraint(
            ["subscription_id"],
            ["subscription.id"],
            name=op.f("fk_subscription_sim_subscription_id_subscription"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_subscription_sim")),
    )
    op.drop_table("sim_plan")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "sim_plan",
        sa.Column(
            "id",
            sa.INTEGER(),
            sa.Identity(
                always=False,
                start=1,
                increment=1,
                minvalue=1,
                maxvalue=2147483647,
                cycle=False,
                cache=1,
            ),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("imsi", sa.VARCHAR(length=15), autoincrement=False, nullable=False),
        sa.Column("rate_plan_id", sa.INTEGER(), autoincrement=False, nullable=False),
        sa.PrimaryKeyConstraint("id", name="pk_sim_plan"),
        sa.UniqueConstraint("imsi", name="uq_sim_plan_imsi"),
    )
    op.drop_table("subscription_sim")
    op.drop_table("subscription")
    # ### end Alembic commands ###
