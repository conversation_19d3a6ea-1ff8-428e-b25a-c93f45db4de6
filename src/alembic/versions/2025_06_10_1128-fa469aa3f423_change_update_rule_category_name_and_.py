"""change/update rule category name and code

Revision ID: fa469aa3f423
Revises: dc16ad9eaf0f
Create Date: 2025-05-30 11:28:43.594060

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "fa469aa3f423"
down_revision = "dc16ad9eaf0f"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        UPDATE rule_category
        SET category = 'Cycle To Date Usage',
            rule_category_code = 'CTDU'
        WHERE rule_category_code = 'CTDDU';
        """
    )
    op.execute(
        """
        UPDATE rule_category
        SET category = 'Monthly Pooled Usage',
            rule_category_code = 'MPU'
        WHERE rule_category_code = 'MPDU';
        """
    )


def downgrade() -> None:
    op.execute(
        """
        UPDATE rule_category
        SET category = 'Cycle To Date Data Usage',
            rule_category_code = 'CTDDU'
        WHERE rule_category_code = 'CTDU';
        """
    )
    op.execute(
        """
        UPDATE rule_category
        SET category = 'Monthly Pooled Data Usage',
            rule_category_code = 'MPDU'
        WHERE rule_category_code = 'MPU';
        """
    )
