"""rename_cdr_voice_to_cdr_voice_archieve

Revision ID: 090582ba7165
Revises: 1e28de98fcf7
Create Date: 2025-01-01 12:58:18.707555

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "090582ba7165"
down_revision = "1e28de98fcf7"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.rename_table("cdr_voice", "cdr_voice_archieve")
    op.rename_table("cdr_voice_new", "cdr_voice")
    op.execute(
        """
        DO $$
        DECLARE
            partition_name TEXT;         -- Current partition name
            new_partition_name TEXT;     -- New partition name after replacement
        BEGIN
            FOR partition_name IN
                SELECT inhrelid::regclass::text
                FROM pg_inherits
                WHERE inhparent = 'cdr_voice'::regclass
            LOOP
                -- Generate the new partition name
                new_partition_name := replace(
                partition_name,
                'cdr_voice_new',
                'cdr_voice');

                -- Log the renaming action
                RAISE NOTICE 'Renaming partition % to %',
                partition_name, new_partition_name;

                -- Rename the partition
                EXECUTE format('ALTER TABLE %I RENAME TO %I;',
                            partition_name,
                            new_partition_name);
            END LOOP;

            -- Log completion
            RAISE NOTICE 'All partitions renamed successfully.';
        END $$;


        """
    )


def downgrade() -> None:
    op.rename_table("cdr_voice", "cdr_voice_new")
    op.execute(
        """
        DO $$
        DECLARE
            partition_name TEXT;         -- Current partition name
            new_partition_name TEXT;     -- New partition name after replacement
        BEGIN
            FOR partition_name IN
                SELECT inhrelid::regclass::text
                FROM pg_inherits
                WHERE inhparent = 'cdr_voice_new'::regclass
            LOOP
                -- Generate the new partition name
                new_partition_name := replace(
                partition_name,
                'cdr_voice',
                'cdr_voice_new');

                -- Log the renaming action
                RAISE NOTICE 'Renaming partition % to %',
                partition_name, new_partition_name;

                -- Rename the partition
                EXECUTE format('ALTER TABLE %I RENAME TO %I;',
                            partition_name,
                            new_partition_name);
            END LOOP;

            -- Log completion
            RAISE NOTICE 'All partitions renamed successfully.';
        END $$;


        """
    )
    op.rename_table("cdr_voice_archieve", "cdr_voice")
