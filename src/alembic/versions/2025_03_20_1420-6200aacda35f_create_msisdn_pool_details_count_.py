"""create msisdn_pool_details_count function

Revision ID: 6200aacda35f
Revises: e282db290d1c
Create Date: 2025-03-20 14:20:41.545316

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "6200aacda35f"
down_revision = "e282db290d1c"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        CREATE OR REPLACE FUNCTION msisdn_pool_details_count(
            search_term varchar DEFAULT NULL
        )
        RETURNS TABLE (
            msisdn_pool_count bigint
        )
        LANGUAGE 'plpgsql'
        COST 100
        VOLATILE
        PARALLEL UNSAFE
        ROWS 1000
        AS $BODY$
        DECLARE
            sql_query TEXT;
            -- final_order_by_column TEXT := COALESCE(order_by_column, 'created_at');
            -- final_order_direction TEXT := UPPER(COALESCE(order_direction, 'DESC'));
        BEGIN

            sql_query := $$
                SELECT
                    count(*) as msisdn_pool_count
                FROM
                    msisdn_pool_details_view mpd
                WHERE
                    $1 IS NULL OR
                    mpd.msisdn ILIKE '%' || $1 || '%' OR
                    mpd.sim_profile::text ILIKE '%' || $1 || '%' OR
                    mpd.msisdn_factor::text ILIKE '%' || $1 || '%' OR
                    mpd.uploaded_by ILIKE '%' || $1 || '%' OR
                    mpd.account_name ILIKE '%' || $1 || '%';
            $$;

            -- Execute the dynamic query and return the results
            RETURN QUERY EXECUTE sql_query USING search_term;
        END;
        $BODY$;
        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS msisdn_pool_details_count(varchar);
        """
    )
