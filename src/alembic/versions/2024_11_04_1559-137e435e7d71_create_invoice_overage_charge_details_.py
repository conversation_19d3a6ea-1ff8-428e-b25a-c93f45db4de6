"""create invoice_overage_charge_details function

Revision ID: 137e435e7d71
Revises: c60e96ef6e95
Create Date: 2024-11-04 15:59:02.857484

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "137e435e7d71"
down_revision = "c60e96ef6e95"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        CREATE OR REPLACE FUNCTION public.invoice_overage_charge_details(
            account_id_param integer,
            invocie_id_param integer,
            month_param date)
            RETURNS void
            LANGUAGE 'plpgsql'
            COST 100
            VOLATILE PARALLEL UNSAFE
        AS $BODY$
                        BEGIN
                            -- Step 1: Drop and recreate the temporary table
                            DROP TABLE IF EXISTS TempFirstQueryResult;
                            CREATE TEMPORARY TABLE TempFirstQueryResult AS
                            WITH FirstQueryResult AS (
                                SELECT rp.id, sc.imsi, rgs.service, r.range_to,
                                r.overage_fee, r.overage_unit, r.range_unit,
                                rg.rate_model_id, r.overage_per, r.isoverage
                                FROM allocation AS al
                                INNER JOIN sim_card AS sc
                                ON sc.allocation_id = al.id
                                and al.account_id = account_id_param
                                INNER JOIN sim_monthly_statistic AS sms
                                ON sms.sim_card_id = sc.id
                                AND month=month_param
                                AND sms.sim_status != 'READY_FOR_ACTIVATION'
                                INNER JOIN rate_plan AS rp
                                ON rp.id = COALESCE(sc.rate_plan_id, al.rate_plan_id)
                                INNER JOIN rate_group AS rg
                                ON rg.rate_plan_id = rp.id
                                AND rg.rate_model_id in (2,3,4)
                                INNER JOIN rate_group_service AS rgs
                                ON rgs.rate_group_id = rg.id
                                INNER JOIN rate AS r ON r.rate_group_id = rg.id
                                WHERE al.account_id = account_id_param
                                AND month=month_param
                                AND sms.sim_status != 'READY_FOR_ACTIVATION'
                                AND rg.rate_model_id in (2,3,4)
                            )
                            SELECT * FROM FirstQueryResult;

                            -- Step 2: Get distinct IMSI count
                            DROP TABLE IF EXISTS DistinctIMSI;
                            CREATE TEMPORARY TABLE DistinctIMSI AS
                            SELECT COUNT(DISTINCT imsi) AS distinct_imsi_count
                            FROM TempFirstQueryResult;

                            -- Step 3: Get total allowance for rate_model_id = 2
                            DROP TABLE IF EXISTS TotalAllowanceModel2;
                            CREATE TEMPORARY TABLE TotalAllowanceModel2 AS
                            SELECT tqr.service,tqr.overage_unit, tqr.overage_fee,
                            tqr.overage_per,tqr.isoverage
                            FROM TempFirstQueryResult as tqr
                            inner join monthly_usage_records AS mur
                            ON mur.imsi=tqr.imsi AND mur.month = month_param
                            and mur.service=tqr.service
                            WHERE tqr.rate_model_id = 2 AND mur.month = month_param
                            AND (
                                COALESCE(mur.volume, 0) - ROUND(
                                GREATEST(convert_unit(tqr.range_unit, tqr.range_to), 0))
                            ) > 0
                            GROUP BY tqr.service,tqr.overage_unit, tqr.overage_fee,
                            tqr.overage_per,tqr.isoverage;

                            DROP TABLE IF EXISTS TotalAllowanceModel3;
                            CREATE TEMPORARY TABLE TotalAllowanceModel3 AS
                            SELECT DISTINCT rate_model_id, service,
                            overage_unit, overage_fee,
                            overage_per,
                            ROUND(GREATEST(convert_unit(range_unit, range_to), 0))
                            AS total_allowance,
                            isoverage
                            FROM TempFirstQueryResult
                            WHERE rate_model_id = 3;

                            DROP TABLE IF EXISTS TotalAllowanceModel4;
                            CREATE TEMPORARY TABLE TotalAllowanceModel4 AS
                            SELECT rate_model_id, service, overage_unit,
                            overage_fee, overage_per,
                            SUM(ROUND(GREATEST(convert_unit(range_unit, range_to), 0)))
                            AS total_allowance,
                            isoverage
                            FROM TempFirstQueryResult
                            WHERE rate_model_id = 4
                            GROUP BY rate_model_id,service, overage_unit, overage_fee,
                            overage_per, isoverage;

                            DROP TABLE IF EXISTS TotalUsage;
                            CREATE TEMPORARY TABLE TotalUsage AS
                            SELECT mur.service,
                            SUM(COALESCE(mur.volume, 0)) AS total_usage
                            FROM monthly_usage_records AS mur
                            WHERE to_char(
                            date_trunc('month', month_param), 'YYYY-MM') = to_char(
                            date_trunc('month', mur.month), 'YYYY-MM')
                            AND mur.imsi IN (SELECT imsi FROM TempFirstQueryResult)
                            GROUP BY mur.service;

                            INSERT INTO invoice_overage_charge
                            (invoice_id, sim_count, service, total_allowance,
                            total_usage, usage_variance,
                            usage_variance_coverted, total_charge,
                            rate_model_id, overage_fee, overage_per, overage_unit,
                            isoverage, overage_charge)
                            SELECT
                                invocie_id_param,
                                (SELECT distinct_imsi_count FROM DistinctIMSI),
                                service, 0, 0, 0, 0, 0, 2,
                                overage_fee, overage_per, overage_unit,
                                isoverage, ((overage_fee) * (SELECT distinct_imsi_count
                                FROM DistinctIMSI))
                            FROM TotalAllowanceModel2;

                            INSERT INTO invoice_overage_charge
                            (invoice_id,sim_count,service,total_allowance,
                            total_usage,usage_variance,usage_variance_coverted,total_charge,
                            rate_model_id, overage_fee, overage_per, overage_unit,
                            isoverage, overage_charge)
                            SELECT
                                invocie_id_param,
                                (SELECT distinct_imsi_count FROM DistinctIMSI)
                                AS distinct_imsi_count,
                                ta.service,
                                ta.total_allowance,
                                tu.total_usage,
                                GREATEST(
                                COALESCE(tu.total_usage, 0) - ta.total_allowance, 0)
                                AS usage_difference,
                                ROUND(reverse_convert_unit(ta.overage_unit, GREATEST(
                                COALESCE(tu.total_usage, 0) - ta.total_allowance, 0)),
                                6)
                                AS usage_difference_converted,
                                CEIL(
                                GREATEST(
                                    reverse_convert_unit(ta.overage_unit, GREATEST(
                                    COALESCE(tu.total_usage, 0) - ta.total_allowance,
                                    0)) / ta.overage_per, 0)
                            ) * ta.overage_fee AS total_charge,
                            ta.rate_model_id,
                            ta.overage_fee,
                            ta.overage_per,
                            ta.overage_unit,
                            ta.isoverage,
                            (CASE
                            WHEN ta.isoverage
                            AND (GREATEST(
                            COALESCE(tu.total_usage, 0) - ta.total_allowance, 0) > 0)
                    THEN ((ta.overage_fee) * (SELECT distinct_imsi_count
                    FROM DistinctIMSI))
                    ELSE 0
                    END)
                            FROM (
                                SELECT * FROM TotalAllowanceModel3
                                UNION ALL
                                SELECT * FROM TotalAllowanceModel4
                            ) AS ta
                            LEFT JOIN TotalUsage tu ON ta.service = tu.service;

                        END;
        $BODY$;

        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS invoice_overage_charge_details(integer, integer, date);
        """
    )
