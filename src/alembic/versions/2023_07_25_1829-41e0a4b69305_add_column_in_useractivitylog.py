"""add column in useractivitylog

Revision ID: 41e0a4b69305
Revises: fb115c58402a
Create Date: 2023-07-25 18:29:46.932549

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "41e0a4b69305"
down_revision = "fb115c58402a"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column(
        "user_activity_log",
        sa.Column(
            "created_by", sa.String(length=120), server_default="", nullable=False
        ),
    )


def downgrade() -> None:
    op.drop_column("user_activity_log", "created_by")
