"""remove table

Revision ID: 235d536c4891
Revises: 7d77c994a6d4
Create Date: 2023-08-08 13:32:18.819066

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

from sim.domain.model import SimStatus

# revision identifiers, used by Alembic.
revision = "235d536c4891"
down_revision = "7d77c994a6d4"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.drop_constraint(
        op.f("fk_provider_log_id"), "user_activity_log", type_="foreignkey"
    )
    op.drop_constraint(
        op.f("fk_sim_audit_log_uuid"), "user_activity_log", type_="foreignkey"
    )
    op.drop_constraint(
        op.f("fk_sim_audit_log_uuid"), "sim_provider_log", type_="foreignkey"
    )

    op.drop_table("user_activity_log")
    op.drop_table("sim_audit_log")

    op.drop_column("sim_provider_log", "sim_audit_log_uuid")
    op.drop_column("sim_provider_log", "client_ip")


def downgrade() -> None:
    sim_status = postgresql.ENUM(SimStatus, name="sim_status", create_type=False)
    op.create_table(
        "user_activity_log",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False, start=1, increment=1),
            nullable=False,
        ),
        sa.Column("provider_log_id", sa.Integer(), nullable=False),
        sa.Column("audit_uuid", postgresql.UUID(), nullable=False),
        sa.Column("prior_value", sim_status, nullable=False),
        sa.Column("new_value", sim_status, nullable=False),
        sa.Column("created_by", sa.String(length=120), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    op.add_column(
        "sim_provider_log",
        sa.Column("sim_audit_log_uuid", postgresql.UUID(), nullable=False),
    )
    op.add_column(
        "sim_provider_log",
        sa.Column("client_ip", sa.String(), nullable=True),
    )
    op.create_table(
        "sim_audit_log",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False, start=1, increment=1),
            nullable=False,
        ),
        sa.Column("uuid", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("reference", sa.String(), nullable=False),
        sa.Column("request_type", sa.String(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.Column("created_by", sa.String(length=128), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
    )
    op.create_foreign_key(
        "fk_sim_audit_log_uuid",
        "sim_provider_log",
        "sim_audit_log",
        ["sim_audit_log_uuid"],
        ["uuid"],
    )

    op.create_foreign_key(
        "fk_provider_log_id",
        "user_activity_log",
        "sim_provider_log",
        ["provider_log_id"],
        ["id"],
    )
    op.create_foreign_key(
        "fk_sim_audit_log_uuid",
        "user_activity_log",
        "sim_audit_log",
        ["audit_uuid"],
        ["uuid"],
    )
