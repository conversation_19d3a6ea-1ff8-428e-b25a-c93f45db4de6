"""create shipping_details table

Revision ID: d4e5f6a7b8c9
Revises: c3d4e5f6a7b8
Create Date: 2025-06-25 17:48:00.000000

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects.postgresql import UUID

# revision identifiers, used by Alembic.
revision = "d4e5f6a7b8c9"
down_revision = "c3d4e5f6a7b8"
branch_labels = None
depends_on = None


def upgrade():
    # shipping_details
    op.create_table(
        "shipping_details",
        sa.Column("id", sa.Integer, primary_key=True, autoincrement=True),
        sa.Column("uuid", UUID(as_uuid=True), nullable=False),
        sa.Column("contact_name", sa.String(length=255)),
        sa.Column("address_line1", sa.String(length=255)),
        sa.Column("address_line2", sa.String(length=255)),
        sa.Column("city", sa.String(length=100)),
        sa.Column("state_or_region", sa.String(length=100)),
        sa.Column("postal_code", sa.String(length=20)),
        sa.Column("country", sa.String(length=100)),
        sa.Column("other_information", sa.Text),
        sa.Column("order_id", UUID, sa.ForeignKey("orders.orders.uuid")),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
        schema="orders",
    )


def downgrade():
    op.drop_table("shipping_details", schema="orders")
