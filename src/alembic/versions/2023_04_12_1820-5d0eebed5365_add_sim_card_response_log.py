"""add_sim_card_response_log

Revision ID: 5d0eebed5365
Revises: 4a936c30598e
Create Date: 2023-04-12 18:20:54.894252

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "5d0eebed5365"
down_revision = "4a936c30598e"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "sim_provider_log",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False, start=1, increment=1),
            nullable=False,
        ),
        sa.Column("sim_audit_log_uuid", postgresql.UUID(), nullable=False),
        sa.Column("audit_date", sa.DateTime, nullable=False),
        sa.Column("message", sa.String(), nullable=False),
        sa.Column("status", sa.String(), nullable=False),
        sa.Column("work_id", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["sim_audit_log_uuid"],
            ["sim_audit_log.uuid"],
            name=op.f("fk_sim_audit_log_uuid"),
        ),
        sa.PrimaryKeyConstraint("id"),
    )


def downgrade() -> None:
    op.drop_table("sim_provider_log")
