"""create table user_role with default roles

Revision ID: 962a6790dcce
Revises: 363dfa147b4c
Create Date: 2024-02-27 11:41:15.914793

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

from authorization.domain.model import RoleGroup

# revision identifiers, used by Alembic.
revision = "962a6790dcce"
down_revision = "363dfa147b4c"
branch_labels = None
depends_on = None


def upgrade() -> None:
    role_group = postgresql.ENUM(RoleGroup, name="rolegroup")
    op.create_table(
        "user_role",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("role", sa.String(length=50), nullable=False),
        sa.Column("rolegroup", role_group, nullable=False),
        sa.Column("is_default", sa.<PERSON>(), nullable=False),
        sa.Column(
            "created_at", sa.DateTime(), server_default=sa.func.now(), nullable=False
        ),
        sa.Column("created_by", sa.String(length=128), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("role"),
    )

    # Insert multiple rows in a single statement
    op.execute(
        "INSERT INTO user_role (role, rolegroup, is_default, created_by) VALUES "
        "('DistributorAdmin', 'MY_ORGANIZATION', true, 'System Admin'), "
        "('DistributorUser', 'MY_ORGANIZATION', true, 'System Admin'), "
        "('ClientAdmin', 'ACCOUNT', true, 'System Admin'), "
        "('ClientUser', 'ACCOUNT', true, 'System Admin')"
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    op.drop_table("user_role")
    role_group = postgresql.ENUM(RoleGroup, name="rolegroup")
    role_group.drop(op.get_bind())
