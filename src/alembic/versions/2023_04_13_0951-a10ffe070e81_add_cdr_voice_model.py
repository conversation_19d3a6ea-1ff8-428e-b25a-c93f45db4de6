"""add_cdr_voice_model

Revision ID: a10ffe070e81
Revises: 03e0464cf6b6
Create Date: 2023-04-13 09:51:47.534835

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "a10ffe070e81"
down_revision = "03e0464cf6b6"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "cdr_voice",
        sa.<PERSON>umn(
            "id",
            sa.Integer(),
            sa.Identity(always=False, start=1, increment=1),
            nullable=False,
        ),
        sa.Column("cdr_uuid", postgresql.UUID(), nullable=False),
        sa.<PERSON>umn("call_date", sa.DateTime(), nullable=False),
        sa.Column("call_number", sa.String(), nullable=False),
        sa.Column("call_minutes", sa.Integer, nullable=False),
        sa.ForeignKeyConstraint(
            ["cdr_uuid"],
            ["cdr.uuid"],
            name=op.f("fk_cdr_uuid"),
        ),
        sa.PrimaryKeyConstraint("id"),
    )


def downgrade() -> None:
    op.drop_table("cdr_voice")
