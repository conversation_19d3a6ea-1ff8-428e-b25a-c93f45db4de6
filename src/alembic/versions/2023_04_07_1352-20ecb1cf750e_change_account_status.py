"""change account_status

Revision ID: 20ecb1cf750e
Revises: cc98fb02e4ec
Create Date: 2023-04-07 13:52:27.450155

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "20ecb1cf750e"
down_revision = "cc98fb02e4ec"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute("""UPDATE account SET status = 'ACTIVE' WHERE status = 'UNKNOWN'""")
    op.execute("""ALTER TYPE account_status RENAME VALUE 'UNKNOWN' TO 'SUSPENDED'""")


def downgrade() -> None:
    op.execute("""ALTER TYPE account_status RENAME VALUE 'SUSPENDED' TO 'UNKNOWN'""")
