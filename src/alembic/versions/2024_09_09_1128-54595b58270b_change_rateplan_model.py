"""change_rateplan_model

Revision ID: 54595b58270b
Revises: babf84ca4a30
Create Date: 2024-09-09 11:28:26.798242

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "54595b58270b"
down_revision = "babf84ca4a30"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
            BEGIN;
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1
                    FROM pg_enum
                    WHERE enumlabel = 'Min'
                    AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'unit')
                ) THEN
                    ALTER TYPE unit ADD VALUE 'Min';
                END IF;
            END$$;
            COMMIT;
        """
    )
    op.execute(
        """
            BEGIN;
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1
                    FROM pg_enum
                    WHERE enumlabel = 'SMS'
                    AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'unit')
                ) THEN
                    ALTER TYPE unit ADD VALUE 'SMS';
                END IF;
            END$$;
            COMMIT;
        """
    )

    op.add_column("rate_plan", sa.Column("sim_limit", sa.Integer(), nullable=True))
    op.create_table(
        "rate_model",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("model", sa.String(length=50), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_rate_model")),
    )
    op.execute(
        "INSERT INTO rate_model (model) VALUES "
        "('Pay As You Go'), "
        "('Individual Plan'), "
        "('Fixed Pool'), "
        "('Flexible Pool')"
    )


def downgrade() -> None:
    op.drop_column("rate_plan", "sim_limit")
    op.drop_table("rate_model")
