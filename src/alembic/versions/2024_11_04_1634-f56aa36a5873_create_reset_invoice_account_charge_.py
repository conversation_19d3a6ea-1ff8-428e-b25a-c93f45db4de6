"""create reset_invoice_account_charge_details function

Revision ID: f56aa36a5873
Revises: 3db42825d5a8
Create Date: 2024-11-04 16:34:27.149205

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "f56aa36a5873"
down_revision = "3db42825d5a8"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        CREATE OR REPLACE FUNCTION public.reset_invoice_account_charge_details(
            invoice_id_param integer)
            RETURNS void
            LANGUAGE 'plpgsql'
            COST 100
            VOLATILE PARALLEL UNSAFE
        AS $BODY$
                BEGIN
                DELETE FROM invoice_account_charge
                WHERE
                    invoice_id = invoice_id_param;
                END;
        $BODY$;


        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS public.reset_invoice_account_charge_details(integer);
        """
    )
