"""update function get_sim_card_msisdn_pool_details_count function

Revision ID: e955a8172f22
Revises: be825380e5f9
Create Date: 2025-04-09 17:09:50.323501

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "e955a8172f22"
down_revision = "be825380e5f9"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS get_sim_card_msisdn_pool_details_count();
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_sim_card_msisdn_pool_details_count()
        RETURNS TABLE (
            duplicate_iccids bigint,
            duplicate_imsis bigint,
            duplicate_msisdns bigint,
            duplicate_msisdn_pool_msisdns bigint,
            sim_card_msisdn_missing_msisdn_pool bigint,
            sim_card_allocation_id_mismatch_msisdn_pool bigint,
            create_range_count bigint,
            add_msisdn_pool_count bigint
        ) AS $$
        BEGIN
            RETURN QUERY
            SELECT
                -- Sim card duplicate ICCIDs
                (
                    WITH sim_card_duplicate_iccids AS (
                        SELECT iccid
                        FROM sim_card
                        GROUP BY iccid
                        HAVING COUNT(iccid) > 1
                    )
                    SELECT COUNT(iccid) FROM sim_card_duplicate_iccids
                ) AS duplicate_iccids,

                -- Sim card duplicate IMSIs
                (
                    WITH sim_card_duplicate_imsis AS (
                        SELECT imsi
                        FROM sim_card
                        GROUP BY imsi
                        HAVING COUNT(imsi) > 1
                    )
                    SELECT COUNT(imsi) FROM sim_card_duplicate_imsis
                ) AS duplicate_imsis,

                -- Sim card duplicate MSISDNs
                (
                    WITH sim_card_duplicate_msisdns AS (
                        SELECT msisdn
                        FROM sim_card
                        GROUP BY msisdn
                        HAVING COUNT(msisdn) > 1
                    )
                    SELECT COUNT(msisdn) FROM sim_card_duplicate_msisdns
                ) AS duplicate_msisdns,

                -- Msisdn Pool duplicate MSISDNs
                (
                    WITH msisdn_pool_duplicate_msisdns AS (
                        SELECT msisdn
                        FROM msisdn_pool
                        GROUP BY msisdn
                        HAVING COUNT(msisdn) > 1
                    )
                    SELECT COUNT(msisdn) FROM msisdn_pool_duplicate_msisdns
                ) AS duplicate_msisdn_pool_msisdns,

                -- sim_card.msisdn not in msisdn_pool.msisdn
                (
                    SELECT COUNT(sc.msisdn)
                    FROM sim_card sc
                    WHERE NOT EXISTS (
                        SELECT 1
                        FROM msisdn_pool mp
                        WHERE mp.msisdn = sc.msisdn
                    )
                ) AS sim_card_msisdn_missing_msisdn_pool,

                -- sim_card.msisdn = msisdn_pool.msisdn but allocation_id mismatch
                (
                    SELECT COUNT(sc.msisdn)
                    FROM sim_card sc
                    JOIN msisdn_pool mp ON mp.msisdn = sc.msisdn
                    WHERE coalesce(mp.allocation_id,0) != coalesce(sc.allocation_id,0)
                ) AS sim_card_allocation_id_mismatch_msisdn_pool,

                -- Last 5 days record count in 'range' table
                (
                    SELECT COUNT(id) FROM range
                    WHERE created_at BETWEEN CURRENT_DATE - INTERVAL '4 day'
                    AND CURRENT_DATE + INTERVAL '1 day'
                ) AS create_range_count,

                -- Last 5 days record count in 'msisdn_pool' table
                (
                    SELECT COUNT(id) FROM msisdn_pool
                    WHERE created_at BETWEEN CURRENT_DATE - INTERVAL '4 day'
                    AND CURRENT_DATE + INTERVAL '1 day'
                ) AS add_msisdn_pool_count;

        END;
        $$ LANGUAGE plpgsql;
        """
    )


def downgrade() -> None:

    op.execute(
        """
        DROP FUNCTION IF EXISTS get_sim_card_msisdn_pool_details_count();
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_sim_card_msisdn_pool_details_count()
        RETURNS TABLE (
            duplicate_iccids bigint,
            duplicate_imsis bigint,
            duplicate_msisdns bigint,
            duplicate_msisdn_pool_msisdns bigint,
            sim_card_msisdn_missing_msisdn_pool bigint,
            sim_card_allocation_id_mismatch_msisdn_pool bigint,
            range_count_last_5_days bigint,
            msisdn_pool_count_last_5_days bigint
        ) AS $$
        BEGIN
            RETURN QUERY
            SELECT
                -- Sim card duplicate ICCIDs
                (
                    WITH sim_card_duplicate_iccids AS (
                        SELECT iccid
                        FROM sim_card
                        GROUP BY iccid
                        HAVING COUNT(iccid) > 1
                    )
                    SELECT COUNT(iccid) FROM sim_card_duplicate_iccids
                ) AS duplicate_iccids,

                -- Sim card duplicate IMSIs
                (
                    WITH sim_card_duplicate_imsis AS (
                        SELECT imsi
                        FROM sim_card
                        GROUP BY imsi
                        HAVING COUNT(imsi) > 1
                    )
                    SELECT COUNT(imsi) FROM sim_card_duplicate_imsis
                ) AS duplicate_imsis,

                -- Sim card duplicate MSISDNs
                (
                    WITH sim_card_duplicate_msisdns AS (
                        SELECT msisdn
                        FROM sim_card
                        GROUP BY msisdn
                        HAVING COUNT(msisdn) > 1
                    )
                    SELECT COUNT(msisdn) FROM sim_card_duplicate_msisdns
                ) AS duplicate_msisdns,

                -- Msisdn Pool duplicate MSISDNs
                (
                    WITH msisdn_pool_duplicate_msisdns AS (
                        SELECT msisdn
                        FROM msisdn_pool
                        GROUP BY msisdn
                        HAVING COUNT(msisdn) > 1
                    )
                    SELECT COUNT(msisdn) FROM msisdn_pool_duplicate_msisdns
                ) AS duplicate_msisdn_pool_msisdns,

                -- sim_card.msisdn not in msisdn_pool.msisdn
                (
                    SELECT COUNT(sc.msisdn)
                    FROM sim_card sc
                    WHERE NOT EXISTS (
                        SELECT 1
                        FROM msisdn_pool mp
                        WHERE mp.msisdn = sc.msisdn
                    )
                ) AS sim_card_msisdn_missing_msisdn_pool,

                -- sim_card.msisdn = msisdn_pool.msisdn but allocation_id mismatch
                (
                    SELECT COUNT(sc.msisdn)
                    FROM sim_card sc
                    JOIN msisdn_pool mp ON mp.msisdn = sc.msisdn
                    WHERE coalesce(mp.allocation_id,0) != coalesce(sc.allocation_id,0)
                ) AS sim_card_allocation_id_mismatch_msisdn_pool,

                -- Last 5 days record count in 'range' table
                (
                    SELECT COUNT(id) FROM range
                    WHERE created_at BETWEEN CURRENT_DATE - INTERVAL '5 day'
                    AND CURRENT_DATE + INTERVAL '1 day'
                ) AS range_count_last_5_days,

                -- Last 5 days record count in 'msisdn_pool' table
                (
                    SELECT COUNT(id) FROM msisdn_pool
                    WHERE created_at BETWEEN CURRENT_DATE - INTERVAL '5 day'
                    AND CURRENT_DATE + INTERVAL '1 day'
                ) AS msisdn_pool_count_last_5_days;
        END;
        $$ LANGUAGE plpgsql;
        """
    )
