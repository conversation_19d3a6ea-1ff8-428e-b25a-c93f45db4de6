"""add account traffic threshold table

Revision ID: e5e98a66a719
Revises: b5d1f164bdee
Create Date: 2023-09-04 12:22:07.477224

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "e5e98a66a719"
down_revision = "b5d1f164bdee"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "account_traffic_thresholds",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.Column("carrier_name", sa.String(), nullable=False),
        sa.Column(
            "warning_threshold", sa.Integer(), server_default="90", nullable=False
        ),
        sa.ForeignKeyConstraint(
            ["account_id"],
            ["account.id"],
            name=op.f("fk_account_traffic_thresholds_account_id_account"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_account_traffic_thresholds")),
    )

    op.execute(
        """INSERT INTO account_traffic_thresholds \
        (account_id, carrier_name, warning_threshold) \
        SELECT id, 'EE', 90 FROM account;"""
    )


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("account_traffic_thresholds")
    # ### end Alembic commands ####
