"""add account_id fk in sim_allocation

Revision ID: 371562797ea5
Revises: d27b310dfe84
Create Date: 2023-05-26 11:16:45.152861

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "371562797ea5"
down_revision = "d27b310dfe84"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key(
        op.f("fk_allocation_account_id_account"),
        "allocation",
        "account",
        ["account_id"],
        ["id"],
        ondelete="RESTRICT",
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f("fk_allocation_account_id_account"), "allocation", type_="foreignkey"
    )
    # ### end Alembic commands ###
