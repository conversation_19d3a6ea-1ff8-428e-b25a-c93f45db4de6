"""add cdr_id column into cdr

Revision ID: b5d1f164bdee
Revises: e11a6230a587
Create Date: 2023-08-18 10:29:47.785977

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "b5d1f164bdee"
down_revision = "e11a6230a587"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column(
        "cdr",
        sa.Column("cdr_object_id", sa.String(25), nullable=True),
    )


def downgrade() -> None:
    op.drop_column("cdr", "cdr_object_id")
