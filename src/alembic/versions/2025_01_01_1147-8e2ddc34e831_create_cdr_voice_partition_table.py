"""create_cdr_voice_partition_table

Revision ID: 8e2ddc34e831
Revises: 1ed5a558a37c
Create Date: 2025-01-01 11:47:21.617302

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "8e2ddc34e831"
down_revision = "1ed5a558a37c"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "cdr_voice_new",
        sa.Column(
            "id",
            sa.Integer,
            sa.Identity(always=False, start=1, increment=1),
            primary_key=True,
            autoincrement=True,
        ),
        sa.Column("cdr_uuid", postgresql.UUID(), nullable=False),
        sa.Column("call_date", sa.DateTime(), primary_key=True, nullable=False),
        sa.Column("call_number", sa.String(), nullable=False),
        sa.Column("call_minutes", sa.Integer, nullable=False),
        sa.PrimaryKeyConstraint("id", "call_date", name="pk_cdr_voice_new"),
        postgresql_partition_by="RANGE (call_date)",
    )

    # Creating partitions for the CDR_VOICE Table.

    op.execute(
        """
        DO $$
        DECLARE
            start_date DATE;
            end_date DATE := date_trunc('month',
            current_date + interval '1 month')::DATE;
            partition_date DATE;
            partition_name TEXT;
        BEGIN
            -- Select the smallest call_date from the cdr_voice table
            -- and set it to the first day of its month
            SELECT date_trunc('month', MIN(call_date))::DATE
            INTO start_date FROM cdr_voice;

            partition_date := start_date;
            WHILE partition_date < end_date LOOP
                partition_name := 'cdr_voice_new_' || to_char(
                partition_date, 'YYYY_MM');
                EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF \
                cdr_voice_new FOR VALUES FROM (%L) TO (%L)',
                            partition_name,
                            partition_date::DATE,
                            (partition_date + interval '1 month')::DATE);
                partition_date := partition_date + interval '1 month';
            END LOOP;
        END $$;

        """
    )

    # Creating index for the cdr_voice_new Table.
    op.create_index("idx_cdr_voice_new_call_date", "cdr_voice_new", ["call_date"])


def downgrade() -> None:

    op.drop_table("cdr_voice_new")
