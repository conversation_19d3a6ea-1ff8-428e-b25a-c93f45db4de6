"""add rate_model_code column in rate_model table

Revision ID: 9ff332e2b577
Revises: 9fccedeeb4a5
Create Date: 2025-02-03 16:54:34.737946

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "9ff332e2b577"
down_revision = "9fccedeeb4a5"
branch_labels = None
depends_on = None


def upgrade() -> None:

    # Add the new column with default value
    op.add_column(
        "rate_model",
        sa.Column("rate_model_code", sa.String(), nullable=False, server_default=""),
    )

    # If needed, update values after adding the column
    op.execute(
        """
        UPDATE rate_model
        SET rate_model_code =
            CASE
                WHEN model = 'Pay As You Go' THEN 'PAYG'
                WHEN model = 'Individual Plan' THEN 'INDI'
                WHEN model = 'Fixed Pool' THEN 'FIXED'
                WHEN model = 'Flexible Pool' THEN 'FLEXI'
                ELSE rate_model_code
            END
    """
    )

    # Remove the default constraint after initial population
    op.alter_column("rate_model", "rate_model_code", server_default=None)


def downgrade() -> None:
    op.drop_column("rate_model", "rate_model_code")
