"""insert data into allocation table

Revision ID: 7eb13d8924e2
Revises: e8311f4ecfd1
Create Date: 2024-01-02 15:39:50.055413

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "7eb13d8924e2"
down_revision = "e8311f4ecfd1"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "allocation", sa.Column("allocation_details_id", sa.Integer(), nullable=True)
    )
    op.create_foreign_key(
        op.f("fk_allocation_allocation_details_id_allocation_details"),
        "allocation",
        "allocation_details",
        ["allocation_details_id"],
        ["id"],
    )
    op.execute(
        """
            UPDATE allocation AS al
            SET allocation_details_id = ad.id
            FROM allocation_details AS ad
            WHERE ad.file_name = CONCAT(al.id, '_', al.title);
            """
    )
    op.execute(
        """
            UPDATE allocation_details SET file_name=null;
            """
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f("fk_allocation_allocation_details_id_allocation_details"),
        "allocation",
        type_="foreignkey",
    )
    op.drop_column("allocation", "allocation_details_id")
    # ### end Alembic commands ###
