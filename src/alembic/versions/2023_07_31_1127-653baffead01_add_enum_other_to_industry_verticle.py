"""Add enum 'Other' to industry verticle

Revision ID: 653baffead01
Revises: 41e0a4b69305
Create Date: 2023-07-31 11:27:36.995373

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "653baffead01"
down_revision = "41e0a4b69305"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        "DO $$ BEGIN IF NOT EXISTS (\
        SELECT 1 FROM pg_enum WHERE enumtypid =\
        'industry_vertical'::regtype AND enumlabel = \
        'OTHERS') THEN ALTER TYPE industry_vertical \
        ADD VALUE 'OTHERS'; END IF; END; $$;"
    )


def downgrade() -> None:
    pass
