"""create_index_on_allocation_table

Revision ID: 32df956fded0
Revises: f4ffd3b28ac4
Create Date: 2025-06-05 10:21:05.567834

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "32df956fded0"
down_revision = "f4ffd3b28ac4"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
       CREATE INDEX IF NOT EXISTS idx_allocation_imsi ON allocation (imsi);
       """
    )


def downgrade() -> None:
    op.execute("drop index if exists idx_allocation_imsi")
