"""create_actions_table

Revision ID: 126718e18009
Revises: 2028bb4f380f
Create Date: 2024-07-18 15:11:13.693797

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "126718e18009"
down_revision = "2028bb4f380f"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "actions",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("action", sa.String(), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_actions")),
        sa.UniqueConstraint("action", name=op.f("uq_actions_action")),
    )

    # Insert data to the actions table
    op.execute("""INSERT INTO actions(action) VALUES('Deactivate SIM') """)


def downgrade() -> None:
    op.drop_table("actions")
