"""create_cdr_voice_mo_mt_partition_table

Revision ID: c978cebcc9b0
Revises: 71c92792cf1a
Create Date: 2025-05-30 15:49:01.626749

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "c978cebcc9b0"
down_revision = "71c92792cf1a"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "cdr_voice_mo_mt_incamel",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False, start=1, increment=1),
            primary_key=True,
            nullable=False,
        ),
        sa.Column("imsi", sa.String(length=15), nullable=False),
        sa.Column("iccid", sa.String(length=20), nullable=False),
        sa.Column("usage", sa.BigInteger(), nullable=False),
        sa.Column("type", sa.String(length=15), nullable=False),
        sa.Column("month", sa.Date(), primary_key=True, nullable=False),
        sa.PrimaryKeyConstraint("id", "month", name="pk_cdr_voice_mo_mt_incamel"),
        sa.UniqueConstraint(
            "iccid", "month", "type", name="uq_cdr_voice_mo_mt_incamel_iccid_month_type"
        ),
        postgresql_partition_by="RANGE (month)",
    )

    op.execute(
        """
        DO $$
        DECLARE
            start_date DATE;
            end_date DATE := date_trunc('month',
            current_date + interval '1 month')::DATE;
            partition_date DATE;
            partition_name TEXT;
        BEGIN
            -- Select the smallest date_sent from the cdr_sms table
            -- and set it to the first day of its date_sent
            SELECT date_trunc('month', MIN(date_sent))::DATE
            INTO start_date FROM cdr_sms;

            partition_date := start_date;
            WHILE partition_date < end_date LOOP
                partition_name := 'cdr_sms_aggregate_' || to_char(
                partition_date, 'YYYY_MM');
                EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF \
                cdr_sms_aggregate FOR VALUES FROM (%L) TO (%L)',
                            partition_name,
                            partition_date::DATE,
                            (partition_date + interval '1 month')::DATE);
                partition_date := partition_date + interval '1 month';
            END LOOP;
        END $$;

        """
    )

    op.create_index(
        "idx_cdr_voice_mo_mt_incamel_month_type",
        "cdr_voice_mo_mt_incamel",
        ["month", "type"],
    )


def downgrade() -> None:
    op.drop_table("cdr_voice_mo_mt_incamel")
