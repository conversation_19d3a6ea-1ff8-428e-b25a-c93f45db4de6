"""modify get_allowance_usage function for targeting cdr partition table

Revision ID: c2e61f1f0973
Revises: 879c8924d726
Create Date: 2025-01-02 12:46:12.687579

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "c2e61f1f0973"
down_revision = "879c8924d726"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS get_allowance_usage(integer[]);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_allowance_usage(
            account_ids integer[] DEFAULT NULL
        )
        RETURNS TABLE (
            account_id INT,
            rate_plan_id INT,
            -- allowances INT,
            -- range_unit TEXT,
            -- total_allowances INT,
            -- final_rate_plan_id INT,
            -- data_usage NUMERIC,
            allowance_used NUMERIC
        ) AS $$
        DECLARE
            partition_name TEXT;
            sql_query TEXT;
        BEGIN
            -- Dynamically construct the partition name for the current month
            partition_name := 'cdr_data_aggregate_' || to_char(current_date, 'YYYY_MM');

            -- Construct the dynamic SQL query
            sql_query :=
                'WITH cte_usage AS (
                    SELECT
                        al.account_id,
                        COALESCE(sc.rate_plan_id, al.rate_plan_id) AS rate_plan_id,
                        r.range_to,
                        r.range_unit,
                        COALESCE(SUM(cda.usage), 0) AS total_data_volume,
                        convert_unit(r.range_unit, r.range_to) AS total_allowance
                    FROM
                        allocation AS al
                    INNER JOIN sim_card AS sc
                        ON sc.allocation_id = al.id
                    INNER JOIN rate_plan AS rp
                        ON rp.id = COALESCE(sc.rate_plan_id, al.rate_plan_id)
                    INNER JOIN rate_group AS rg
                        ON rg.rate_plan_id = rp.id
                    INNER JOIN rate_group_service AS rgs
                        ON rgs.rate_group_id = rg.id
                        AND rgs.service = ''DATA''
                        AND rg.rate_model_id NOT IN (1, 2)
                    INNER JOIN rate AS r
                        ON rgs.rate_group_id = r.rate_group_id
                    INNER JOIN ' || partition_name || ' AS cda
                        ON cda.imsi = al.imsi
                    WHERE
                        ($1 IS NULL OR al.account_id = ANY($1))
                    GROUP BY
                        al.account_id,
                        sc.rate_plan_id,
                        al.rate_plan_id,
                        r.range_to,
                        r.range_unit
                )
                SELECT
                    ct.account_id AS account_id,
                    ct.rate_plan_id,
                    ROUND((ct.total_data_volume * 100) / GREATEST(
                    ct.total_allowance, 1), 2) AS allowance_used
                FROM
                    cte_usage AS ct';

            -- Execute the dynamically constructed query with parameters
            RETURN QUERY EXECUTE sql_query USING account_ids;
        END;
        $$ LANGUAGE plpgsql;

        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS get_allowance_usage(integer[]);
        """
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION get_allowance_usage(
            account_ids integer[] DEFAULT NULL
        )
        RETURNS TABLE (
            account_id INT,
            rate_plan_id INT,
            -- allowances INT,
            -- range_unit TEXT,
            -- total_allowances INT,
            -- final_rate_plan_id INT,
            -- data_usage NUMERIC,
            allowance_used NUMERIC
        ) AS $$
        BEGIN
            RETURN QUERY
            WITH cte_usage AS (
                SELECT
                    al.account_id,
                    COALESCE(sc.rate_plan_id, al.rate_plan_id) AS rate_plan_id,
                    r.range_to,
                    r.range_unit,
                    COALESCE(SUM(cda.usage),0) AS total_data_volume,
                    convert_unit(r.range_unit, r.range_to) AS total_allowance
                FROM
                    allocation AS al
                INNER JOIN sim_card AS sc
                    ON sc.allocation_id = al.id
                INNER JOIN rate_plan AS rp
                    ON rp.id = COALESCE(sc.rate_plan_id, al.rate_plan_id)
                INNER JOIN rate_group AS rg
                    ON rg.rate_plan_id = rp.id
                INNER JOIN rate_group_service AS rgs
                    ON rgs.rate_group_id = rg.id
                    AND rgs.service = 'DATA'
                    AND rg.rate_model_id NOT IN (1, 2)
                INNER JOIN rate AS r
                    ON rgs.rate_group_id = r.rate_group_id
                INNER JOIN cdr_data_aggregate AS cda
                ON cda.imsi = al.imsi
                    WHERE
                        cda.month = (SELECT TO_CHAR(
                        date_trunc('month', CURRENT_DATE), 'YYYY-MM-DD'))::date
                        AND (account_ids IS NULL OR al.account_id = ANY(account_ids))
                GROUP BY
                al.account_id,
                sc.rate_plan_id,
                al.rate_plan_id,
                r.range_to,
                r.range_unit
            )
            SELECT
                ct.account_id AS account_id,
                ct.rate_plan_id,
                ROUND((ct.total_data_volume * 100) / GREATEST(
                ct.total_allowance, 1), 2) AS allowance_used
            FROM
                cte_usage AS ct;
        END;
        $$ LANGUAGE plpgsql;

        """
    )
