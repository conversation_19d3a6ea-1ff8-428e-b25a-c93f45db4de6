"""Add sim usage model

Revision ID: 3c4294d3bd3e
Revises: db18aeb55636
Create Date: 2023-01-06 14:28:20.164775

"""
import sqlalchemy as sa
from alembic import op

from common.db import Base

# revision identifiers, used by Alembic.
revision = "3c4294d3bd3e"
down_revision = "db18aeb55636"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "sim_usage",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("billing_cycle_id", sa.Integer(), nullable=True),
        sa.Column("imsi", sa.String(length=15), nullable=False),
        sa.Column(
            "service",
            sa.Enum(
                "VOICE_MO",
                "VOICE_MT",
                "SMS_MO",
                "SMS_MT",
                "DATA",
                name="service",
                metadata=Base.metadata,
            ),
            nullable=False,
        ),
        sa.Column("carrier", sa.CHAR(length=5), nullable=False),
        sa.Column("volume", sa.BigInteger(), nullable=False),
        sa.Column("charge", sa.Numeric(), nullable=True),
        sa.ForeignKeyConstraint(
            ["billing_cycle_id"],
            ["billing_cycle.id"],
            name=op.f("fk_sim_usage_billing_cycle_id_billing_cycle"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_sim_usage")),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("sim_usage")
    # ### end Alembic commands ###
