"""billing functions added

Revision ID: f8d178c947a3
Revises: 7260673d8866
Create Date: 2025-04-15 18:33:06.283617

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "f8d178c947a3"
down_revision = "7260673d8866"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
    CREATE OR REPLACE FUNCTION get_invoice_summary(
    p_billing_cycle_id INTEGER,
    p_billing_cycle_date DATE,
    p_account_ids INTEGER[] DEFAULT NULL
)
    RETURNS TABLE (
        invoice_id INTEGER,
        account_id INTEGER,
        name TEXT,
        logo_key TEXT,
        status TEXT,
        is_billable BOOLEAN,
        industry_vertical TEXT,
        currency TEXT,
        contract_end_date DATE,
        billing_cycle DATE,
        invoice_date TIMESTAMP,
        due_date TIMESTAMP,
        is_published BOOLEAN,
        rating_state TEXT,
        total_account_charge NUMERIC,
        new_sims INTEGER
    )
    AS $$
    BEGIN
        RETURN QUERY
        WITH invoice_base AS (
        SELECT
            inv.id AS invoice_id,
            ac.id AS account_id,
            ac.name::text,
            ac.logo_key::text,
            ac.status::text,
            ac.is_billable,
            ac.industry_vertical::text,
            ac.currency,
            ac.contract_end_date,
            p_billing_cycle_date AS billing_cycle,
            COALESCE(inv.published_at, NULL) AS invoice_date,
            inv.published_at + ac.payment_terms * INTERVAL '1 day' AS due_date,
            (inv.published_at IS NOT NULL) AS is_published,
            inv.rating_state
        FROM invoice inv
        INNER JOIN account ac ON ac.id = inv.account_id
        WHERE inv.billing_cycle_id = p_billing_cycle_id
        AND (p_account_ids IS NULL OR ac.id = ANY(p_account_ids))
        ),
        total_account_charge AS (
        SELECT
            iac.invoice_id,
            COALESCE(SUM(iac.account_charge), 0) AS total_account_charge
        FROM invoice_account_charge AS iac
        INNER JOIN invoice AS inv ON inv.id = iac.invoice_id
        WHERE inv.billing_cycle_id = p_billing_cycle_id
        AND (p_account_ids IS NULL OR inv.account_id = ANY(p_account_ids))
        GROUP BY iac.invoice_id
        ),
        new_sims AS (
        SELECT
            ssad.invoice_id,
            COUNT(ssad.id) AS new_sims
        FROM subscription_sim_allocation_details AS ssad
        INNER JOIN invoice inv ON inv.id = ssad.invoice_id
        WHERE inv.billing_cycle_id = p_billing_cycle_id
        AND (p_account_ids IS NULL OR inv.account_id = ANY(p_account_ids))
        GROUP BY ssad.invoice_id
        )
        SELECT
            ib.invoice_id,
                ib.account_id,
                ib.name::text,
                ib.logo_key::text,
                ib.status::text,
                ib.is_billable,
                ib.industry_vertical::text,
                ib.currency::text,
                ib.contract_end_date,
                p_billing_cycle_date AS billing_cycle,
                ib.invoice_date,
                ib.due_date,
                ib.is_published,
                ib.rating_state::text,
            COALESCE(tac.total_account_charge, 0),
            COALESCE(ns.new_sims, 0)::integer
        FROM invoice_base ib
        LEFT JOIN total_account_charge tac ON ib.invoice_id = tac.invoice_id
        LEFT JOIN new_sims ns ON ib.invoice_id = ns.invoice_id;
    END;
    $$ LANGUAGE plpgsql;
    """
    )

    op.execute(
        """
    CREATE OR REPLACE FUNCTION get_invoice_adjustments(
    p_billing_cycle_id INTEGER,
    p_account_ids INTEGER[] DEFAULT NULL
)
    RETURNS TABLE (
        id INTEGER,
        invoice_id INTEGER,
        date DATE,
        amount NUMERIC,
        type TEXT
    )
    AS $$
    BEGIN
        RETURN QUERY
        SELECT adj.id, adj.invoice_id, adj.date, adj.amount, adj.type::text
        FROM adjustment AS adj
        INNER JOIN invoice inv ON inv.id = adj.invoice_id
        WHERE inv.billing_cycle_id = p_billing_cycle_id
        AND (p_account_ids IS NULL OR inv.account_id = ANY(p_account_ids));
    END;
    $$ LANGUAGE plpgsql;
    """
    )

    op.execute(
        """
    CREATE OR REPLACE FUNCTION get_invoice_subscriptions(
    p_billing_cycle_id INTEGER,
    p_account_ids INTEGER[] DEFAULT NULL
)
    RETURNS TABLE (
        id INTEGER,
        invoice_id INTEGER,
        rate_plan_id INTEGER,
        RatePlan TEXT,
        access_fee NUMERIC,
        sims_total INTEGER,
        sim_charge NUMERIC,
        total_active_sim INTEGER
    )
    AS $$
    BEGIN
    RETURN QUERY
    WITH subscriptions AS (
        SELECT
            sub.id, sub.invoice_id, sub.rate_plan_id, sub.name::text AS RatePlan,
            sub.access_fee, sub.sims_total, sub.sim_charge
        FROM subscription sub
        INNER JOIN invoice inv ON inv.id = sub.invoice_id
        WHERE inv.billing_cycle_id = p_billing_cycle_id
        AND (p_account_ids IS NULL OR inv.account_id = ANY(p_account_ids))
    ),
    active_sims AS (
        SELECT
            inv.id AS invoice_id,
            sub.rate_plan_id,
            COUNT(subs.id) AS total_active_sim
        FROM invoice inv
        INNER JOIN subscription sub ON sub.invoice_id = inv.id
        INNER JOIN subscription_sim subs ON subs.subscription_id = sub.id
        WHERE inv.billing_cycle_id = p_billing_cycle_id
        AND (p_account_ids IS NULL OR inv.account_id = ANY(p_account_ids))
        GROUP BY inv.id, sub.rate_plan_id
    )
    SELECT
        s.id, s.invoice_id, s.rate_plan_id, s.RatePlan,
        s.access_fee, s.sims_total, s.sim_charge,
        COALESCE(a.total_active_sim, 0)::integer
    FROM subscriptions s
    LEFT JOIN active_sims a ON s.invoice_id = a.invoice_id
    AND s.rate_plan_id = a.rate_plan_id;
    END;
    $$ LANGUAGE plpgsql;
    """
    )

    op.execute(
        """
    CREATE OR REPLACE FUNCTION get_imsi_usage(
    p_billing_cycle_id INTEGER,
    p_account_ids INTEGER[] DEFAULT NULL
)
    RETURNS TABLE (
        invoice_id INTEGER,
        service service,
        charge NUMERIC,
        volume NUMERIC,
        total_overage_charge NUMERIC,
        bulk_overage_charge NUMERIC
    )
    AS $$
    BEGIN
    RETURN QUERY
    WITH services AS (
        SELECT UNNEST(
            ARRAY['DATA', 'VOICE_MO', 'VOICE_MT', 'SMS_MO', 'SMS_MT']::service[]
            ) AS service
    ),
    imsi_list AS (
        SELECT subs.imsi, subs.subscription_id
        FROM subscription_sim subs
        INNER JOIN subscription sub ON subs.subscription_id = sub.id
        INNER JOIN invoice inv ON inv.id = sub.invoice_id
        WHERE inv.billing_cycle_id = p_billing_cycle_id
        AND (p_account_ids IS NULL OR inv.account_id = ANY(p_account_ids))
        GROUP BY subs.imsi, subs.subscription_id
    ),
    imsi_service AS (
        SELECT i.imsi, i.subscription_id, s.service
        FROM imsi_list i
        CROSS JOIN services s
    ),
    usage_data AS (
        SELECT su.imsi, su.service, su.billing_cycle_id,
            SUM(su.charge) AS charge, SUM(su.volume) AS volume
        FROM sim_usage su
        INNER JOIN allocation AS al ON al.imsi=su.imsi
            AND (p_account_ids IS NULL OR al.account_id = ANY(p_account_ids))
        WHERE su.billing_cycle_id = p_billing_cycle_id
            AND (p_account_ids IS NULL OR al.account_id = ANY(p_account_ids))
        GROUP BY su.imsi, su.service, su.billing_cycle_id
    ),
    combined AS (
        SELECT
            inv.id,
            ims.service,
            COALESCE(ud.volume, 0) as volume,
            COALESCE(ud.charge, 0) as charge,
            COALESCE(ioc.total_charge, 0) as total_overage_charge,
            COALESCE(ioc.overage_charge, 0) as overage_charge
        FROM imsi_service ims
        INNER JOIN subscription sub ON ims.subscription_id = sub.id
        INNER JOIN invoice inv ON inv.id = sub.invoice_id
        AND inv.billing_cycle_id = p_billing_cycle_id
        LEFT JOIN usage_data ud
            ON ud.imsi = ims.imsi AND ud.service = ims.service
            AND ud.billing_cycle_id = p_billing_cycle_id
--			AND (p_account_ids IS NULL OR inv.account_id = ANY(p_account_ids))
        LEFT JOIN invoice_overage_charge ioc
            ON ioc.invoice_id = inv.id AND ioc.service = ims.service
    )
    SELECT
        combined.id as invoice_id,
        combined.service,
        SUM(combined.volume) AS total_volume,
        SUM(combined.charge) AS total_charge,
        SUM(combined.total_overage_charge) AS total_overage_charge,
        SUM(combined.overage_charge) AS overage_charge
    FROM combined
    GROUP BY combined.id, combined.service
    ORDER BY combined.id, combined.service;
    END;
    $$ LANGUAGE plpgsql;
    """
    )


def downgrade() -> None:
    op.execute(
        """
    DROP FUNCTION IF EXISTS get_invoice_summary(INTEGER, DATE, INTEGER[]);
    DROP FUNCTION IF EXISTS get_invoice_adjustments(INTEGER, INTEGER[]);
    DROP FUNCTION IF EXISTS get_invoice_subscriptions(INTEGER, INTEGER[]);
    DROP FUNCTION IF EXISTS get_imsi_usage(INTEGER, INTEGER[]);
    """
    )
