"""add_cdr_sms_model

Revision ID: fbb5b7a88859
Revises: a10ffe070e81
Create Date: 2023-04-13 09:52:30.253790

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "fbb5b7a88859"
down_revision = "a10ffe070e81"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "cdr_sms",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False, start=1, increment=1),
            nullable=False,
        ),
        sa.Column("cdr_uuid", postgresql.UUID(), nullable=False),
        sa.Column("data_sent", sa.Integer, nullable=False),
        sa.Column("sent_from", sa.String(), nullable=False),
        sa.Column("sent_to", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["cdr_uuid"],
            ["cdr.uuid"],
            name=op.f("fk_cdr_uuid"),
        ),
        sa.PrimaryKeyConstraint("id"),
    )


def downgrade() -> None:
    op.drop_table("cdr_sms")
