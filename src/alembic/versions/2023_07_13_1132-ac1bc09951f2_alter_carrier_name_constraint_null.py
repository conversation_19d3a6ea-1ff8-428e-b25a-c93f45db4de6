"""alter carrier name constraint null

Revision ID: ac1bc09951f2
Revises: 3b83f145eb5b
Create Date: 2023-07-13 11:32:04.554856

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "ac1bc09951f2"
down_revision = "3b83f145eb5b"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.alter_column(
        "carrier_name", "carrier_name", existing_type=sa.String(), nullable=True
    )


def downgrade() -> None:
    op.alter_column(
        "carrier_name", "carrier_name", existing_type=sa.String(), nullable=False
    )
