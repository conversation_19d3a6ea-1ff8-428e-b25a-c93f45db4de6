"""create invoice_overage_charge table

Revision ID: eaf96e271ac5
Revises: 1477dc10adbb
Create Date: 2024-10-22 18:20:19.662469

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

from common.types import Service, Unit

# revision identifiers, used by Alembic.
revision = "eaf96e271ac5"
down_revision = "1477dc10adbb"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "invoice_overage_charge",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("invoice_id", sa.Integer(), nullable=False),
        sa.Column("sim_count", sa.Integer(), nullable=False),
        sa.Column(
            "service",
            postgresql.ENUM(Service, name="service", create_type=False),
            nullable=False,
        ),
        sa.Column(
            "total_allowance",
            sa.BigInteger(),
            nullable=False,
        ),
        sa.Column(
            "total_usage",
            sa.BigInteger(),
            nullable=False,
        ),
        sa.Column(
            "usage_variance",
            sa.BigInteger(),
            nullable=False,
        ),
        sa.Column(
            "usage_variance_coverted",
            sa.Numeric(),
            nullable=False,
        ),
        sa.Column(
            "total_charge",
            sa.Numeric(),
            nullable=False,
        ),
        sa.Column(
            "rate_model_id",
            sa.Integer(),
            nullable=False,
        ),
        sa.Column(
            "overage_fee",
            sa.Numeric(),
            nullable=False,
        ),
        sa.Column(
            "overage_per",
            sa.Integer(),
            nullable=False,
        ),
        sa.Column(
            "overage_unit",
            postgresql.ENUM(Unit, name="unit", create_type=False),
            nullable=False,
        ),
        sa.Column(
            "isoverage", sa.Boolean(), server_default=sa.text("false"), nullable=False
        ),
        sa.Column(
            "overage_charge",
            sa.Numeric(),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["invoice_id"],
            ["invoice.id"],
            name=op.f("fk_invoice_overage_charge_invoice_id_invoice"),
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_invoice_overage_charge")),
    )


def downgrade() -> None:
    op.drop_table("invoice_overage_charge")
