"""add_subscription_sim_allocation_details

Revision ID: 195704919f6c
Revises: 3e76487f3d36
Create Date: 2024-01-19 10:47:48.046011

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "195704919f6c"
down_revision = "3e76487f3d36"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "subscription_sim_allocation_details",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("invoice_id", sa.Integer(), nullable=False),
        sa.Column("sim_id", sa.Integer(), nullable=False),
        sa.Column("allocation_id", sa.Integer(), nullable=False),
        sa.Column("allocation_date", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["invoice_id"],
            ["invoice.id"],
            name=op.f("fk_subscription_sim_allocation_details_invoice_id_invoice"),
        ),
        sa.PrimaryKeyConstraint(
            "id", name=op.f("pk_subscription_sim_allocation_details")
        ),
    )

    op.execute(
        """
        CREATE OR REPLACE FUNCTION reset_subscription_sim_allocation_details(
            invoice_id_param INT
        )
        RETURNS VOID AS $$
            BEGIN
            DELETE FROM subscription_sim_allocation_details
            WHERE
                invoice_id = invoice_id_param;
            END;
            $$ LANGUAGE plpgsql;
        """
    )

    op.execute(
        """
            CREATE OR REPLACE FUNCTION subscription_sim_allocation_details(
            invoice_id_param INT,
            imsi_array TEXT[]
        )
        RETURNS VOID AS $$
        BEGIN
            INSERT INTO subscription_sim_allocation_details
                (invoice_id, sim_id, allocation_id, allocation_date)
            SELECT
                invoice_id_param,
                sc.id,
                sc.allocation_id,
                al.created_at
            FROM
                sim_card AS sc
            INNER JOIN
                allocation AS al ON al.id = sc.allocation_id
            WHERE
                sc.imsi = ANY(imsi_array) AND NOT EXISTS
                (
                    select id from subscription_sim_allocation_details
                    where invoice_id = invoice_id_param AND
                    subscription_sim_allocation_details.sim_id=sc.id);
        END;
        $$ LANGUAGE plpgsql;
        """
    )


def downgrade() -> None:
    op.drop_table("subscription_sim_allocation_details")
    op.execute(
        """
            DROP FUNCTION IF EXISTS subscription_sim_allocation_details(INT, TEXT[]);
        """
    )
    op.execute(
        """
            DROP FUNCTION IF EXISTS reset_subscription_sim_allocation_details(INT);
        """
    )
