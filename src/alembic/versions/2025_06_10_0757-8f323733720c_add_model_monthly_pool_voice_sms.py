"""add_model_monthly_pool_voice_sms

Revision ID: 8f323733720c
Revises: 32df956fded0
Create Date: 2025-05-30 07:57:43.205121

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "8f323733720c"
down_revision = "32df956fded0"
branch_labels = None
depends_on = None


def upgrade() -> None:
    conn = op.get_bind()
    rule_category_id = conn.execute(
        "SELECT id FROM rule_category WHERE rule_category_code = 'MPDU' "
    ).scalar_one()

    query = sa.text(
        """
    INSERT INTO rule_definition (rule_category_id, definition, rule_definition_code)
    VALUES (:rule_category_id, 'Monthly billable voice MO MT usage threshold','MBVUT'),
    (:rule_category_id, 'Monthly billable voice MO usage threshold','MBVMOUT'),
    (:rule_category_id, 'Monthly billable voice MT usage threshold','MBVMTUT'),
            (:rule_category_id, 'Monthly billable SMS usage threshold','MBSUT')
            """
    )
    conn.execute(query, {"rule_category_id": rule_category_id})


def downgrade() -> None:
    op.execute(
        """
        DELETE
            FROM
        rule_definition
            WHERE rule_definition_code in ('MBVUT', 'MBVMOUT', 'MBVMTUT', 'MBSUT')
    """
    )
