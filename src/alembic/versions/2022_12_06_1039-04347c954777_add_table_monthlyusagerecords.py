"""Add table MonthlyUsageRecords

Revision ID: 04347c954777
Revises:
Create Date: 2022-12-06 10:39:52.746041

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "04347c954777"
down_revision = None
branch_labels = None
depends_on = None

SERVICE_ENUM = sa.Enum(
    "VOICE_MO", "VOICE_MT", "SMS_MO", "SMS_MT", "DATA", name="service"
)


def upgrade() -> None:
    op.create_table(
        "monthly_usage_records",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("imsi", sa.String(length=15), nullable=False),
        sa.Column("service", SERVICE_ENUM, nullable=False),
        sa.Column("operator", sa.CHAR(length=5), nullable=False),
        sa.Column("month", sa.Date(), nullable=False),
        sa.Column("volume", sa.<PERSON>nteger(), nullable=False),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_monthly_usage_records")),
    )


def downgrade() -> None:
    op.drop_table("monthly_usage_records")
    SERVICE_ENUM.drop(op.get_bind())
