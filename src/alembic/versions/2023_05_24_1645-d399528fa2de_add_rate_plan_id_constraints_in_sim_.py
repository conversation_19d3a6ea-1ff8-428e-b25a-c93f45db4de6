"""add rate_plan_id constraints in sim_card and sim_allocation

Revision ID: d399528fa2de
Revises: ee60803dc079
Create Date: 2023-05-24 16:45:22.035939

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "d399528fa2de"
down_revision = "ee60803dc079"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_foreign_key(
        op.f("fk_allocation_rate_plan_id_rate_plan"),
        "allocation",
        "rate_plan",
        ["rate_plan_id"],
        ["id"],
        ondelete="RESTRICT",
    )
    op.drop_constraint(
        "fk_sim_card_rate_plan_id_rate_plan", "sim_card", type_="foreignkey"
    )
    op.create_foreign_key(
        op.f("fk_sim_card_rate_plan_id_rate_plan"),
        "sim_card",
        "rate_plan",
        ["rate_plan_id"],
        ["id"],
        ondelete="RESTRICT",
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(
        op.f("fk_sim_card_rate_plan_id_rate_plan"), "sim_card", type_="foreignkey"
    )
    op.create_foreign_key(
        "fk_sim_card_rate_plan_id_rate_plan",
        "sim_card",
        "rate_plan",
        ["rate_plan_id"],
        ["id"],
    )
    op.drop_constraint(
        op.f("fk_allocation_rate_plan_id_rate_plan"), "allocation", type_="foreignkey"
    )
    # ### end Alembic commands ###
