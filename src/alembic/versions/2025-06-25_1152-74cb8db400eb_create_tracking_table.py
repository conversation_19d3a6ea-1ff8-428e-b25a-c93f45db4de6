"""create_tracking_table

Revision ID: 74cb8db400eb
Revises: e5f6a7b8c9d0
Create Date: 2025-06-25 11:52:10.162863

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects.postgresql import UUID

# revision identifiers, used by Alembic.
revision = "74cb8db400eb"
down_revision = "e5f6a7b8c9d0"
branch_labels = None
depends_on = None


def upgrade():
    # tracking
    op.create_table(
        "tracking",
        sa.Column("id", sa.Integer, primary_key=True, autoincrement=True),
        sa.Column("uuid", UUID(as_uuid=True), nullable=False),
        sa.Column("reference_id", sa.String(length=100)),
        sa.<PERSON>umn("reference_url", sa.String(length=100)),
        sa.Column("order_id", UUID, sa.Foreign<PERSON>ey("orders.orders.uuid")),
        sa.<PERSON>KeyConstraint("id"),
        sa.UniqueConstraint("uuid"),
        schema="orders",
    )


def downgrade():
    op.drop_table("tracking", schema="orders")
