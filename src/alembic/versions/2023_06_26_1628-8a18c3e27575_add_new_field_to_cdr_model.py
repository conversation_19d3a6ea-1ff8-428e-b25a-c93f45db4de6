"""add new field to cdr model

Revision ID: 8a18c3e27575
Revises: c9841a9b9747
Create Date: 2023-06-26 16:28:58.683747

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "8a18c3e27575"
down_revision = "c9841a9b9747"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.add_column(
        "cdr",
        sa.Column("country_name", sa.String(), nullable=True),
    )

    op.execute(
        """UPDATE cdr SET country_name = 'United Kingdom' WHERE country = 'GBR'"""
    )


def downgrade() -> None:
    op.drop_column("cdr", "country_name")
