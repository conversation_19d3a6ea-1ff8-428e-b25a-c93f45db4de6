"""create reverse_convert_unit function

Revision ID: c60e96ef6e95
Revises: 334ceeb1c1eb
Create Date: 2024-11-04 15:56:25.739571

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "c60e96ef6e95"
down_revision = "334ceeb1c1eb"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
        CREATE OR REPLACE FUNCTION reverse_convert_unit(
        overage_unit Unit,
        usage_difference NUMERIC
        )
        RETURNS NUMERIC AS $$
        BEGIN
            RETURN CASE
                WHEN overage_unit = 'Min' THEN usage_difference / 60
                WHEN overage_unit = 'SMS' THEN usage_difference / 1
                WHEN overage_unit = 'BYTES' THEN usage_difference / 1
                WHEN overage_unit = 'KB' THEN usage_difference / 1024
                WHEN overage_unit = 'MB' THEN usage_difference / 1024 / 1024
                WHEN overage_unit = 'GB' THEN usage_difference / 1024 / 1024 / 1024
                WHEN overage_unit = 'TB'
                THEN usage_difference / 1024 / 1024 / 1024 / 1024
                WHEN overage_unit = 'PB'
                THEN usage_difference / 1024 / 1024 / 1024 / 1024 / 1024
                ELSE 0 -- Handle any unknown units
            END;
        END;
        $$ LANGUAGE plpgsql;

        """
    )


def downgrade() -> None:
    op.execute(
        """
        DROP FUNCTION IF EXISTS reverse_convert_unit(Unit, NUMERIC);
        """
    )
