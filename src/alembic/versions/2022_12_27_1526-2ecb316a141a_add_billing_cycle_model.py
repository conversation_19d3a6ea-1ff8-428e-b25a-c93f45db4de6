"""Add billing cycle model

Revision ID: 2ecb316a141a
Revises: c8c4a7b19992
Create Date: 2022-12-27 15:26:28.270931

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "2ecb316a141a"
down_revision = "c8c4a7b19992"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "billing_cycle",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False),
            autoincrement=True,
            nullable=False,
        ),
        sa.Column("month", sa.Date(), nullable=False),
        sa.Column(
            "calculation_state",
            sa.Enum(
                "IN_PROGRESS",
                "DONE",
                "FAILED",
                name="calculation_state",
                native_enum=False,
                create_constraint=True,
            ),
            nullable=True,
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_billing_cycle")),
        sa.UniqueConstraint("month", name=op.f("uq_billing_cycle_month")),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("billing_cycle")
    # ### end Alembic commands ###
