"""add code in rule type category and definition tables

Revision ID: cafc71514d4d
Revises: 8e861b4dd87b
Create Date: 2025-02-12 12:16:20.443532

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "cafc71514d4d"
down_revision = "8e861b4dd87b"
branch_labels = None
depends_on = None


def upgrade() -> None:

    # Add the new column with default value in rule_type table
    op.add_column(
        "rule_type",
        sa.Column("rule_type_code", sa.String(), nullable=False, server_default=""),
    )

    op.execute(
        """
        UPDATE rule_type
        SET rule_type_code =
            CASE
                WHEN rule_type = 'Usage Monitoring' THEN 'USMO'
                ELSE rule_type_code
            END
    """
    )

    # Remove the default constraint after setting the value in rule_type table
    op.alter_column("rule_type", "rule_type_code", server_default=None)

    # Add the new column with default value in rule_category table
    op.add_column(
        "rule_category",
        sa.Column("rule_category_code", sa.String(), nullable=False, server_default=""),
    )

    op.execute(
        """
        UPDATE rule_category
        SET rule_category_code =
            CASE
                WHEN category = 'Cycle To Date Data Usage' THEN 'CTDDU'
                WHEN category = 'Monthly Pooled Data Usage' THEN 'MPDU'
                ELSE rule_category_code
            END
    """
    )

    # Remove the default constraint after setting the value in rule_category table
    op.alter_column("rule_category", "rule_category_code", server_default=None)

    # Add the new column with default value in rule_definition table
    op.add_column(
        "rule_definition",
        sa.Column(
            "rule_definition_code", sa.String(), nullable=False, server_default=""
        ),
    )

    op.execute(
        """
        UPDATE rule_definition
        SET rule_definition_code =
            CASE
                WHEN definition = 'Data usage exceeds a specified limit' THEN 'DUESL'
                WHEN definition = 'Monthly billable data usage threshold' THEN 'MBDUT'
                ELSE rule_definition_code
            END
    """
    )

    # Remove the default constraint after setting the value in rule_definition table
    op.alter_column("rule_definition", "rule_definition_code", server_default=None)


def downgrade() -> None:
    op.drop_column("rule_definition", "rule_definition_code")
    op.drop_column("rule_category", "rule_category_code")
    op.drop_column("rule_type", "rule_type_code")
