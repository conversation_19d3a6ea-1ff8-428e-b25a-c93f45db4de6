"""change_fun_subscription_sim_allocation_details

Revision ID: 77430d9a648a
Revises: 0694da469e9b
Create Date: 2024-02-01 11:28:47.994418

"""
from alembic import op

# revision identifiers, used by Alembic.
revision = "77430d9a648a"
down_revision = "0694da469e9b"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.execute(
        """
            DROP FUNCTION IF EXISTS subscription_sim_allocation_details(INT, TEXT[]);
        """
    )
    op.execute(
        """
            CREATE OR REPLACE FUNCTION subscription_sim_allocation_details(
            invoice_id_param INT,
            imsi_array TEXT[],
            month_param DATE
        )
        RETURNS VOID AS $$
        BEGIN
            INSERT INTO subscription_sim_allocation_details
                (invoice_id, sim_id, allocation_id, allocation_date)
            SELECT
                invoice_id_param,
                sc.id,
                sc.allocation_id,
                al.created_at
            FROM
                sim_card AS sc
            INNER JOIN
                allocation AS al ON al.id = sc.allocation_id
            WHERE
                to_char(
                    date_trunc('month', month_param), 'YYYY-MM'
                    )=to_char(
                        date_trunc('month', al.created_at), 'YYYY-MM'
                        )
                and
                sc.imsi = ANY(imsi_array) AND NOT EXISTS
                (
                    select id from subscription_sim_allocation_details
                    where invoice_id = invoice_id_param AND
                    subscription_sim_allocation_details.sim_id=sc.id);
        END;
        $$ LANGUAGE plpgsql;
        """
    )


def downgrade() -> None:
    op.execute(
        """
            DROP FUNCTION IF EXISTS subscription_sim_allocation_details(
                INT, TEXT[], DATE
                );
        """
    )
