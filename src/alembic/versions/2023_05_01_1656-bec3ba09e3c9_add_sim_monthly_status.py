"""add sim monthly status

Revision ID: bec3ba09e3c9
Revises: 6a9d3f0df596
Create Date: 2023-04-26 16:56:48.051387

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

from sim.domain.model import SimStatus

# revision identifiers, used by Alembic.
revision = "bec3ba09e3c9"
down_revision = "6a9d3f0df596"
branch_labels = None
depends_on = None


def upgrade() -> None:
    sim_status = postgresql.ENUM(SimStatus, name="sim_status", create_type=False)
    op.create_table(
        "sim_monthly_statistic",
        sa.Column(
            "id",
            sa.Integer(),
            sa.Identity(always=False, start=1, increment=1),
            nullable=False,
        ),
        sa.Column("sim_card_id", sa.Integer, nullable=False),
        sa.Column("month", sa.Date, nullable=False),
        sa.Column("sim_status", sim_status, nullable=False),
        sa.Column("is_first_activation", sa.<PERSON>(), nullable=False),
        sa.ForeignKeyConstraint(
            ["sim_card_id"],
            ["sim_card.id"],
            name=op.f("fk_sim_card_id"),
        ),
        sa.PrimaryKeyConstraint("id"),
    )


def downgrade() -> None:
    op.drop_table("sim_monthly_statistic")
