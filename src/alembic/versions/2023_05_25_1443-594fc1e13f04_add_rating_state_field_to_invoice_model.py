"""Add rating state field to Invoice model

Revision ID: 594fc1e13f04
Revises: d399528fa2de
Create Date: 2023-05-25 14:43:14.682189

"""
import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "594fc1e13f04"
down_revision = "d399528fa2de"
branch_labels = None
depends_on = None

INVOICE_RATING_STATE_ENUM = sa.Enum(
    "IN_PROGRESS",
    "GENERATED",
    "FAILED",
    name="invoice_rating_state",
)


def upgrade() -> None:
    INVOICE_RATING_STATE_ENUM.create(op.get_bind())
    op.add_column(
        "invoice",
        sa.Column(
            "rating_state",
            INVOICE_RATING_STATE_ENUM,
            nullable=True,
        ),
    )
    op.execute(
        """
        UPDATE invoice
        SET rating_state = (
            CASE
                WHEN invoice.published_at IS NOT NULL THEN 'GENERATED'
                WHEN billing_cycle.calculation_state = 'DONE' THEN 'GENERATED'
                WHEN billing_cycle.calculation_state = 'IN_PROGRESS' THEN 'IN_PROGRESS'
                WHEN billing_cycle.calculation_state = 'FAILED' THEN 'FAILED'
                END
            )::invoice_rating_state
        FROM billing_cycle
        WHERE billing_cycle.id = invoice.billing_cycle_id
        """
    )
    op.alter_column(
        "invoice",
        column_name="rating_state",
        existing_type=INVOICE_RATING_STATE_ENUM,
        existing_nullable=True,
        nullable=False,
    )


def downgrade() -> None:
    op.drop_column("invoice", "rating_state")
    INVOICE_RATING_STATE_ENUM.drop(op.get_bind())
