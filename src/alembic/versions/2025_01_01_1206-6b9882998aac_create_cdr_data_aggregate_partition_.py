"""create_cdr_data_aggregate_partition_table

Revision ID: 6b9882998aac
Revises: c83e15bb54f0
Create Date: 2025-01-01 12:06:04.690373

"""
import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

from common.types import ICCID, IMSI

# revision identifiers, used by Alembic.
revision = "6b9882998aac"
down_revision = "c83e15bb54f0"
branch_labels = None
depends_on = None


def upgrade() -> None:
    op.create_table(
        "cdr_data_aggregate_new",
        sa.Column(
            "id",
            sa.Integer,
            sa.Identity(always=False, start=1, increment=1),
            primary_key=True,
            autoincrement=True,
        ),
        sa.Column("cdr_uuid", postgresql.UUID(), nullable=False),
        sa.Column("iccid", sa.String(ICCID.max_length), nullable=False),
        sa.Column("usage", sa.BigInteger, nullable=False),
        sa.Column("month", sa.Date(), primary_key=True, nullable=False),
        sa.Column("imsi", sa.String(IMSI.max_length), nullable=True),
        sa.PrimaryKeyConstraint("id", "month", name="pk_cdr_data_aggregate_new"),
        sa.UniqueConstraint("iccid", "month", name="uq_cdr_data_aggregate_new_iccid"),
        postgresql_partition_by="RANGE (month)",
    )

    # Creating partitions for the CDR_SMS Table.

    op.execute(
        """
        DO $$
        DECLARE
            start_date DATE;
            end_date DATE := date_trunc('month',
            current_date + interval '1 month')::DATE;
            partition_date DATE;
            partition_name TEXT;
        BEGIN
            -- Select the smallest month from the cdr_data_aggregate table
            -- and set it to the first day of its month
            SELECT date_trunc('month', MIN(month))::DATE
            INTO start_date FROM cdr_data_aggregate;

            partition_date := start_date;
            WHILE partition_date < end_date LOOP
                partition_name := 'cdr_data_aggregate_new_' || to_char(
                partition_date, 'YYYY_MM');
                EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF \
                cdr_data_aggregate_new FOR VALUES FROM (%L) TO (%L)',
                            partition_name,
                            partition_date::DATE,
                            (partition_date + interval '1 month')::DATE);
                partition_date := partition_date + interval '1 month';
            END LOOP;
        END $$;

        """
    )

    # Creating index for the cdr_data_aggregate_new Table.
    op.create_index(
        "idx_cdr_data_aggregate_new_month", "cdr_data_aggregate_new", ["month"]
    )


def downgrade() -> None:

    op.drop_table("cdr_data_aggregate_new")
