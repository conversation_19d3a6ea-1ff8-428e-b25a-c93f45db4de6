from dataclasses import InitVar, dataclass, field
from decimal import Decimal
from typing import Optional, Sequence, cast

from pydantic import BaseModel, NonNegativeInt

from common.types import Service, ServiceSet


@dataclass
class RatePlan:
    id: int = field(init=False)
    account_id: int | None
    name: str
    access_fee: Decimal
    is_default: bool
    sim_limit: int | None
    _rate_groups: list["RateGroup"] = field(default_factory=list)
    _id: InitVar[int | None] = None

    def __post_init__(self, _id: int | None):
        if _id is not None:
            self.id = _id

    @property
    def rate_groups(self) -> list["RateGroup"]:
        return self._rate_groups + [
            RateGroup(self.sms_mt, 1, cast(ServiceSet, {Service.SMS_MT}))
        ]

    @property
    def voice_mo(self) -> Sequence["Rate"]:
        return self._get_service_rates(Service.VOICE_MO)

    @voice_mo.setter
    def voice_mo(self, rates: Sequence["Rate"]) -> None:
        self._set_service_rates(rates, Service.VOICE_MO)

    @property
    def voice_mt(self) -> Sequence["Rate"]:
        return self._get_service_rates(Service.VOICE_MT)

    @voice_mt.setter
    def voice_mt(self, rates: Sequence["Rate"]) -> None:
        self._set_service_rates(rates, Service.VOICE_MT)

    @property
    def sms_mo(self) -> Sequence["Rate"]:
        return self._get_service_rates(Service.SMS_MO)

    @sms_mo.setter
    def sms_mo(self, rates: Sequence["Rate"]) -> None:
        self._set_service_rates(rates, Service.SMS_MO)

    @property
    def sms_mt(self) -> Sequence["Rate"]:
        # SMS MT is always free
        return [
            Rate(
                range_from=0,
                range_to=None,
                value=Decimal("0"),
                range_unit="SMS",
                price_unit="SMS",
                isoverage=False,
            )
        ]

    @property
    def data(self) -> Sequence["Rate"]:
        return self._get_service_rates(Service.DATA)

    @data.setter
    def data(self, rates: Sequence["Rate"]) -> None:
        self._set_service_rates(rates, Service.DATA)

    def _set_service_rates(self, rates: Sequence["Rate"], *services: Service) -> None:
        existing = self._get_service_group(*services)
        if existing:
            self._rate_groups.remove(existing)
        self._rate_groups.append(
            RateGroup(
                rate_model_id=rates.rate_model,  # type: ignore
                rates=rates.rates,  # type: ignore
                services=cast(ServiceSet, set(services)),
            )
        )

    def _get_service_rates(self, *services: Service) -> Sequence["Rate"]:
        group = self._get_service_group(*services)
        return group.rates if group else []

    def _get_service_group(self, *services: Service) -> Optional["RateGroup"]:
        return next((g for g in self._rate_groups if g.services == set(services)), None)


@dataclass
class RatePlans(RatePlan):
    allowance_used: Decimal = Decimal("0.00")
    sim_count: int = 0
    rate_plan_model: int | None = None


@dataclass
class RateGroup:
    rates: Sequence["Rate"]
    rate_model_id: int
    services: ServiceSet
    rate_model_code: str | None = None
    allowance_used: Decimal = Decimal("0.00")


# TODO: move to dataclasses with pydantic 2
# https://github.com/pydantic/pydantic/issues/4286#issuecomment-**********
class Rate(BaseModel):
    range_from: NonNegativeInt
    range_to: NonNegativeInt | None = None
    value: Decimal
    range_unit: str
    price_unit: str
    overage_fee: Decimal | None = None
    overage_unit: str | None = None
    isoverage: bool
    overage_per: int | None = None


@dataclass
class AccountRatePlan:
    id: int
    name: str
    access_fee: Decimal
    currency: str
    is_default: bool
    allowance_used: Decimal
    rate_plan_model: int | None = None


@dataclass
class AccountRatePlanDetails:
    account_id: int
    account_name: str
    logo_key: str | None = None
    logo_url: str | None = None
    rate_plans: list[AccountRatePlan] = field(default_factory=list)


@dataclass
class RatePlanModel:
    title: str
    rate_model_code: str


@dataclass
class RateModel(RatePlanModel):
    value: int
    _id: InitVar[int | None] = None

    def __post_init__(self, _id: int | None):
        if _id is not None:
            self.value = _id


@dataclass
class RatePlanModels:
    result: list[RateModel]
