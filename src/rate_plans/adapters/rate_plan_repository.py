import logging
from abc import ABC, abstractmethod
from copy import deepcopy
from decimal import Decima<PERSON>
from secrets import randbelow
from typing import Iterable, Iterator, cast, no_type_check

from sqlalchemy import and_, distinct, func, select
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session
from sqlalchemy.sql.functions import count

from app.config import logger
from common.constants import FIXED, FLEXI, INDI, PAYG
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from common.types import ServiceSet
from rate_plans.adapters import orm
from rate_plans.adapters.exceptions import RatePlanCreationError, RatePlanDeletionError
from rate_plans.domain import model
from rate_plans.exceptions import (
    RatePlanError,
    RatePlanModelDeletionError,
    RatePlanModelNotFound,
)


class AbstractRatePlanRepository(ABC):
    @abstractmethod
    def add(self, rate_plan: model.RatePlan) -> int:
        """Create rate plan."""

    @abstractmethod
    def get(self, id_: int) -> model.RatePlan | None:
        """Get rate plan by id"""

    @abstractmethod
    def update(self, rate_plan: model.RatePlan) -> None:
        """Update rate plan"""

    @abstractmethod
    def delete(self, rate_plan: model.RatePlan) -> None:
        """Remove rate plan"""

    @abstractmethod
    def query(self, account_ids: list[int] = None):
        """Query rate plans."""

    @abstractmethod
    def account_ids(self) -> list[int]:
        """Return all account IDs associated with rate plans."""

    @abstractmethod
    def count_for_account(self, account_id: int) -> int:
        """Returns the number of all/accounts plans"""

    @abstractmethod
    def rate_plans_by_accounts(
        self,
        account_ids: list[int] | None = None,
        searching: Searching | None = None,
        ordering: Ordering | None = None,
        pagination: Pagination | None = None,
    ) -> tuple[list[model.AccountRatePlanDetails], int]:
        ...

    @abstractmethod
    def get_rate_plans_models(self) -> model.RatePlanModels:
        ...

    @abstractmethod
    def get_rate_plans_model_by_id(self, id: int) -> model.RateModel:
        ...

    @abstractmethod
    def update_rate_plans_model_by_id(
        self, id: int, model: model.RatePlanModel
    ) -> model.RateModel:
        ...

    @abstractmethod
    def create_rate_plan_model(self, model: model.RatePlanModel) -> model.RateModel:
        ...

    @abstractmethod
    def delete_rate_plan_model(self, model: model.RateModel) -> None:
        ...

    @abstractmethod
    def get_account_rate_plan(self, account_id: int, rate_plan_id: list[int]) -> bool:
        ...

    @abstractmethod
    def set_default_rate_plan(self, id: int) -> None:
        ...

    @abstractmethod
    def unset_default_rate_plan(self, account_id: int) -> None:
        ...

    @abstractmethod
    def rate_plans_count_by_account_ids(self, account_ids: list[int]) -> int:
        ...

    @abstractmethod
    def validate_fixed_flexi_plan(
        self, rate_plan_id: list[int], service: list[str]
    ) -> bool:
        ...


class InMemoryRatePlanRepository(AbstractRatePlanRepository):
    def __init__(self, rate_plans: list[model.RatePlan]):
        self.rate_plans = rate_plans

    def add(self, rate_plan: model.RatePlan) -> int:
        rate_plan = deepcopy(rate_plan)
        rate_plan.id = randbelow(100) + 1
        self.rate_plans.append(rate_plan)
        return rate_plan.id

    def update(self, rate_plan: model.RatePlan) -> None:
        rp = self.get(id_=rate_plan.id)
        if rp:
            index = self.rate_plans.index(rp)
            self.rate_plans[index] = rate_plan

    def delete(self, rate_plan: model.RatePlan) -> None:
        for index, rate_plan_ in enumerate(self.rate_plans):
            if rate_plan.id == rate_plan_.id:
                del self.rate_plans[index]

    def get(self, id_: int) -> model.RatePlan | None:
        return next((r for r in self.rate_plans if r.id == id_), None)

    def query(self, account_ids: list[int] = None) -> Iterator[model.RatePlans]:
        rate_plans = self.rate_plans
        if account_ids:
            rate_plans = [
                rate_plan
                for rate_plan in rate_plans
                if rate_plan.account_id in account_ids
            ]
        rate_plan_details = []
        for row in rate_plans:
            rate_plan_details.append(
                model.RatePlans(
                    _id=row.id,
                    account_id=row.account_id,
                    name=row.name,
                    access_fee=row.access_fee,
                    is_default=row.is_default,
                    sim_limit=row.sim_limit,
                    allowance_used=Decimal(0),
                    sim_count=0,
                    _rate_groups=row._rate_groups,
                )
            )
        yield from rate_plan_details

    def account_ids(self) -> list[int]:
        return list(sorted({p.account_id for p in self.rate_plans if p.account_id}))

    def count_for_account(self, account_id: int) -> int:
        return len(
            tuple(
                filter(
                    lambda rate_plan: rate_plan.account_id == account_id,
                    self.rate_plans,
                )
            )
        )

    def rate_plans_by_accounts(
        self,
        account_ids: list[int] | None = None,
        searching: Searching | None = None,
        ordering: Ordering | None = None,
        pagination: Pagination | None = None,
    ) -> tuple[list[model.AccountRatePlanDetails], int]:
        ...

    def get_rate_plans_models(self) -> model.RatePlanModels:
        ...

    def get_rate_plans_model_by_id(self, id: int) -> model.RateModel:
        ...

    def update_rate_plans_model_by_id(
        self, id: int, model: model.RatePlanModel
    ) -> model.RateModel:
        ...

    def create_rate_plan_model(self, model: model.RatePlanModel) -> model.RateModel:
        ...

    def delete_rate_plan_model(self, model: model.RateModel) -> None:
        ...

    def get_account_rate_plan(self, account_id: int, rate_plan_id: list[int]) -> bool:
        ...

    def set_default_rate_plan(self, id: int) -> None:
        ...

    def unset_default_rate_plan(self, account_id: int) -> None:
        ...

    def rate_plans_count_by_account_ids(self, account_ids: list[int]) -> int:
        ...

    def validate_fixed_flexi_plan(
        self, rate_plan_id: list[int], service: list[str]
    ) -> bool:
        ...


class DatabaseRatePlanRepository(AbstractRatePlanRepository):
    def add(self, rate_plan: model.RatePlan) -> int:
        try:
            rate_plan_db = orm.RatePlan.from_domain(rate_plan)
            self.session.add(rate_plan_db)
            self.session.commit()
            self.session.refresh(rate_plan_db)
            if rate_plan_db.id is None:
                raise AssertionError("Saved rate plan must have the ID")
            return rate_plan_db.id
        except IntegrityError as e:
            logger.error(f"Cannot create rate plan: {e}")
            raise RatePlanCreationError(
                ("Rate plan could not be created." "As invalid rate_model is passed.")
            )

    def __init__(self, session: Session):
        self.session = session

    def get(self, id_: int) -> model.RatePlan | None:
        rate_plan: orm.RatePlan = (
            self.session.query(orm.RatePlan).filter_by(id=id_).scalar()
        )
        if rate_plan:
            return rate_plan.to_domain()
        return None

    def update(self, rate_plan: model.RatePlan) -> None:
        rate_plan_db: orm.RatePlan = (
            self.session.query(orm.RatePlan).filter_by(id=rate_plan.id).one()
        )
        rate_plan_db.update_from_domain(rate_plan)
        self.session.add(rate_plan_db)
        self.session.commit()

    def delete(self, rate_plan: model.RatePlan) -> None:
        rate_plan_db: orm.RatePlan = (
            self.session.query(orm.RatePlan).filter_by(id=rate_plan.id).one()
        )
        try:
            self.session.delete(rate_plan_db)
            self.session.commit()
        except IntegrityError as e:
            logger.error(f"Cannot delete rate plan: {e}")
            raise RatePlanDeletionError(
                (
                    "Rate plan could not be deleted."
                    "The rate plan is assigned to existing allocations or sim_cards."
                )
            )

    @no_type_check
    def _map_response_to_rate_plans(
        self, response: list[dict]
    ) -> Iterable[model.RatePlans]:
        rate_plans_dict = {}

        for record in response:
            plan_id = record.id
            if plan_id not in rate_plans_dict:
                rate_plan = model.RatePlans(
                    _id=plan_id,
                    account_id=record.account_id,
                    name=record.name,
                    access_fee=record.access_fee,
                    sim_limit=record.sim_limit,
                    is_default=record.is_default,
                    rate_plan_model=record.rate_plan_model,
                    allowance_used=record.allowance_used,
                )
                rate_plans_dict[plan_id] = rate_plan
            else:
                if record.rate_plan_model is not None:
                    rate_plans_dict[plan_id].rate_plan_model = record.rate_plan_model

            rate = model.Rate(
                range_from=record.range_from,
                range_to=record.range_to,
                value=record.value,
                range_unit=record.range_unit,
                price_unit=record.price_unit,
                overage_fee=record.overage_fee,
                overage_unit=record.overage_unit,
                isoverage=record.isoverage,
                overage_per=record.overage_per,
            )

            # Find or create a RateGroup for this rate_model_id
            rate_plan = rate_plans_dict[plan_id]

            if not hasattr(rate_plan, "_rate_groups_map"):
                rate_plan._rate_groups_map = {}

            group_key = (record.rate_model_id, (record.service,))
            if group_key not in rate_plan._rate_groups_map:
                new_group = model.RateGroup(
                    rates=[rate],
                    rate_model_id=record.rate_model_id,
                    rate_model_code=record.rate_plan_model,
                    allowance_used=record.allowance_used,
                    services=cast(ServiceSet, {record.service}),
                )
                rate_plan._rate_groups.append(new_group)
                rate_plan._rate_groups_map[group_key] = new_group
            else:
                rate_plan._rate_groups_map[group_key].rates.append(rate)

        result = list(rate_plans_dict.values())
        return result

    def query(self, account_ids: list[int] = None):

        logging.info(f"Account_ids: {account_ids}")
        """
        [PAYG, INDI] list of constants in below function is passed
        rate_model_code constants which we do not want to inclued in the response type
        """
        rateplan_by_accounts = func.get_rate_plans_detail(account_ids, [PAYG, INDI])

        query = select("*").select_from(rateplan_by_accounts)

        # Execute query and return results
        result = self.session.execute(query).all()
        rate_plan_details = self._map_response_to_rate_plans(response=result)
        yield from rate_plan_details

    def account_ids(self) -> list[int]:
        query = (
            select(distinct(orm.RatePlan.account_id).label("a_id"))
            .filter(orm.RatePlan.account_id.is_not(None))
            .order_by("a_id")
        )
        return self.session.execute(query).scalars().all()

    def count_for_account(self, account_id: int) -> int:
        table = orm.RatePlan
        query = (
            select(count()).select_from(table).filter(table.account_id == account_id)
        )
        return self.session.execute(query).scalar_one()

    def rate_plans_by_accounts(
        self,
        account_ids: list[int] | None = None,
        searching: Searching | None = None,
        ordering: Ordering | None = None,
        pagination: Pagination | None = None,
    ) -> tuple[list[model.AccountRatePlanDetails], int]:
        rateplan_by_accounts = func.rate_plan_by_accounts(
            searching.search if searching else None,
            account_ids,
            ordering.field if ordering else None,
            ordering.order.lower() if ordering else None,
            pagination.offset if pagination else None,
            pagination.page_size if pagination else None,
            [PAYG, INDI],
        )
        query = select("*").select_from(rateplan_by_accounts)
        rate_plan_account_info = self.session.execute(query).all()
        rate_plan_account_data = []

        account_wise_map = {}

        for row in rate_plan_account_info:
            if not row.id:
                continue
            rate_plan = model.AccountRatePlan(
                id=row.id,
                name=row.name,
                access_fee=row.access_fee,
                currency=row.currency,
                is_default=row.is_default,
                allowance_used=row.allowance_used,
                rate_plan_model=row.rate_model_id,
            )
            if row.account_id not in account_wise_map:
                account_wise_map[row.account_id] = model.AccountRatePlanDetails(
                    account_id=row.account_id,
                    account_name=row.account_name,
                    logo_key=row.logo_key,
                    rate_plans=[rate_plan],
                )
            else:
                account_wise_map[row.account_id].rate_plans.append(rate_plan)
        rate_plan_account_data = list(account_wise_map.values())

        rateplan_by_accounts_count = func.rate_plan_by_accounts_count(
            searching.search if searching else None,
            account_ids,
        )
        count_query = select("*").select_from(rateplan_by_accounts_count)
        rate_plan_account_count = self.session.execute(count_query).scalar_one()
        return rate_plan_account_data, rate_plan_account_count

    def get_rate_plans_models(self) -> model.RatePlanModels:
        rate_model = orm.RateModel
        query = select(
            (rate_model.id).label("value"),
            (rate_model.model).label("title"),
            (rate_model.rate_model_code).label("rate_model_code"),
        ).order_by(rate_model.id)
        response = self.session.execute(query).all()
        result = list(
            map(
                lambda ratemodel: model.RateModel(
                    title=ratemodel.title,
                    value=ratemodel.value,
                    rate_model_code=ratemodel.rate_model_code,
                ),
                response,
            )
        )
        return model.RatePlanModels(result=result)

    def get_rate_plans_model_by_id(self, id: int) -> model.RateModel:
        rate_model = orm.RateModel
        query = (
            select(
                (rate_model.id).label("value"),
                (rate_model.model).label("title"),
                (rate_model.rate_model_code).label("rate_model_code"),
            )
            .filter(rate_model.id == id)
            .order_by(rate_model.id)
        )
        response = self.session.execute(query).first()
        if not response:
            raise RatePlanModelNotFound(f"RatePlan model not found for id {id}")
        return model.RateModel(
            title=response.title,
            value=response.value,
            rate_model_code=response.rate_model_code,
        )

    def update_rate_plans_model_by_id(
        self, id: int, model: model.RatePlanModel
    ) -> model.RateModel:
        rate_model = orm.RateModel
        self.session.query(rate_model).filter(rate_model.id == id).update(
            {
                rate_model.model: model.title,
                rate_model.rate_model_code: model.rate_model_code,
            }
        )
        self.session.commit()
        updated_rateplan_model = self.get_rate_plans_model_by_id(id=id)

        return updated_rateplan_model

    def create_rate_plan_model(self, model: model.RatePlanModel) -> model.RateModel:
        rate_model = orm.RateModel.from_domain(model)
        self.session.add(rate_model)
        self.session.commit()
        self.session.refresh(rate_model)
        if rate_model.id is None:
            raise AssertionError("Saved rate plan model must have the ID.")
        new_rateplan_model = self.get_rate_plans_model_by_id(id=rate_model.id)
        return new_rateplan_model

    def delete_rate_plan_model(self, model: model.RateModel) -> None:
        rate_plan_db: orm.RateModel = (
            self.session.query(orm.RateModel).filter_by(id=model.value).one()
        )
        try:
            self.session.delete(rate_plan_db)
            self.session.commit()
        except IntegrityError as e:
            logger.error(f"Cannot delete rate plan model: {e}")
            raise RatePlanModelDeletionError(
                (
                    "Rate plan model could not be deleted."
                    "The rate plan model is assigned to existing"
                    " allocations or sim_cards."
                )
            )

    def unset_default_rate_plan(self, account_id: int) -> None:
        rate_plan = orm.RatePlan
        self.session.query(rate_plan).filter(
            and_(rate_plan.account_id == account_id, rate_plan.is_default == "true")
        ).update({rate_plan.is_default: False})
        return None

    def set_default_rate_plan(self, id: int) -> None:
        rate_plan = orm.RatePlan
        self.session.query(rate_plan).filter(rate_plan.id == id).update(
            {rate_plan.is_default: True}
        )
        self.session.commit()
        return None

    def rate_plans_count_by_account_ids(self, account_ids: list[int]) -> int:
        rate_plans_count_by_account_ids_query = func.rate_plans_count_by_account_ids(
            account_ids
        )
        query = select("*").select_from(rate_plans_count_by_account_ids_query)
        response = self.session.execute(query).scalar_one()
        return response

    def get_account_rate_plan(self, account_id: int, rate_plan_id: list[int]) -> bool:
        rate_plan = orm.RatePlan
        if len(rate_plan_id) == 1:
            query = (
                select(rate_plan.id)
                .select_from(rate_plan)
                .filter(rate_plan.account_id == account_id)
            )
            response = self.session.execute(query).all()
            if len(response) <= 1:
                raise AssertionError(
                    "Expected the response to contain more than 1 element"
                )
            results = [row.id for row in response]
            return any(item in rate_plan_id for item in results)
        else:
            query = (
                select(rate_plan.account_id)
                .select_from(rate_plan)
                .filter(rate_plan.id.in_(rate_plan_id))
            )
            response = self.session.execute(query).all()
            if len(response) != 2:
                raise AssertionError(
                    "Expected the response to contain exactly 2 elements"
                )
            result = all(row[0] == account_id for row in response)
            return result

    def validate_fixed_flexi_plan(
        self, rate_plan_id: list[int], service: list[str]
    ) -> bool:
        rate_group = orm.RateGroup
        rate_group_service = orm.RateGroupService
        rate_model = orm.RateModel
        rate_plan = orm.RatePlan

        query = (
            select(rate_group.rate_plan_id, rate_plan.name)
            .select_from(rate_group)
            .join(rate_plan, rate_plan.id == rate_group.rate_plan_id)
            .join(rate_model, rate_model.id == rate_group.rate_model_id)
            .join(rate_group_service, rate_group_service.rate_group_id == rate_group.id)
            .where(
                rate_group.rate_plan_id.in_(rate_plan_id),
                rate_model.rate_model_code.in_(["FIXED", "FLEXI"]),
                rate_group_service.service.in_(service),
            )
            .group_by(rate_group.rate_plan_id, rate_plan.name)
            .having(
                func.count(func.distinct(rate_group_service.service))
                == len(set(service))
            )
        )
        rate_plan_details = self.session.execute(query).all()
        if len(rate_plan_details) != len(rate_plan_id):
            logger.error(
                "Some or all requested rate plans are not "
                f"{FIXED} or {FLEXI}, rate_plan_ids:- {rate_plan_id}"
            )
            raise RatePlanError(f"Requested rate plans are not {FIXED} or {FLEXI}")
        return True
