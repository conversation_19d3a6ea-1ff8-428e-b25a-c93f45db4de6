from decimal import Decimal

from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Identity,
    Integer,
    Numeric,
    String,
)
from sqlalchemy.ext.associationproxy import association_proxy
from sqlalchemy.orm import relationship

from common.db import Base
from common.types import Service, ServiceSet, Unit
from rate_plans.domain import model
from rate_plans.domain import model as rateplan_model


class RatePlan(Base):
    __tablename__ = "rate_plan"

    id = Column(Integer, Identity(), primary_key=True)
    account_id = Column(Integer, nullable=True)
    name: str = Column(String(255), nullable=False)
    access_fee: Decimal = Column(Numeric, nullable=False)
    is_default = Column(Boolean, nullable=False)
    sim_limit = Column(Integer, nullable=True)

    _rate_groups: list["RateGroup"] = relationship(
        "RateGroup", back_populates="rate_plan", cascade="all, delete-orphan"
    )

    def to_domain(self) -> model.RatePlan:
        return model.RatePlan(
            _id=self.id,
            account_id=self.account_id,
            name=self.name,
            access_fee=self.access_fee,
            is_default=self.is_default,  # type: ignore
            sim_limit=self.sim_limit,
            _rate_groups=list(map(RateGroup.to_domain, self._rate_groups)),
        )

    @classmethod
    def from_domain(cls, rate_plan: model.RatePlan) -> "RatePlan":
        return cls(
            id=getattr(rate_plan, "id", None),
            account_id=rate_plan.account_id,
            name=rate_plan.name,
            access_fee=rate_plan.access_fee,
            sim_limit=rate_plan.sim_limit,
            is_default=rate_plan.is_default,
            _rate_groups=list(map(RateGroup.from_domain, rate_plan._rate_groups)),
        )

    def update_from_domain(self, rate_plan: model.RatePlan) -> None:
        self.name = rate_plan.name
        self.access_fee = rate_plan.access_fee
        self.is_default = rate_plan.is_default
        self.sim_limit = rate_plan.sim_limit
        self._rate_groups = list(map(RateGroup.from_domain, rate_plan._rate_groups))


class RateGroup(Base):
    __tablename__ = "rate_group"

    id = Column(Integer, Identity(), primary_key=True)
    rate_plan_id = Column(Integer, ForeignKey("rate_plan.id"), nullable=False)
    rate_model_id = Column(Integer, ForeignKey("rate_model.id"), nullable=False)

    rate_plan: "RatePlan" = relationship("RatePlan", back_populates="_rate_groups")
    rates: list["Rate"] = relationship(
        "Rate", back_populates="rate_group", cascade="all, delete-orphan"
    )
    rate_group_services: set["RateGroupService"] = relationship(
        "RateGroupService",
        back_populates="rate_group",
        cascade="all, delete-orphan",
        collection_class=set,
    )
    services: ServiceSet = association_proxy(
        "rate_group_services",
        "service",
        creator=lambda s: RateGroupService(service=s),
    )

    def to_domain(self) -> model.RateGroup:
        sorted_rates = sorted(self.rates, key=lambda r: r.id)  # type: ignore
        return model.RateGroup(
            services=self.services,
            rates=list(map(Rate.to_domain, sorted_rates)),
            rate_model_id=self.rate_model_id,  # type: ignore
        )

    @classmethod
    def from_domain(cls, rate_group: model.RateGroup) -> "RateGroup":
        return cls(
            services=rate_group.services,  # type: ignore
            rates=list(map(Rate.from_domain, rate_group.rates)),
            rate_model_id=rate_group.rate_model_id,
        )


class RateGroupService(Base):
    __tablename__ = "rate_group_service"

    rate_group_id = Column(Integer, ForeignKey("rate_group.id"), primary_key=True)
    service = Column(
        Enum(Service, name="service", metadata=Base.metadata), primary_key=True
    )

    rate_group: "RateGroup" = relationship(
        "RateGroup", back_populates="rate_group_services"
    )


class Rate(Base):
    __tablename__ = "rate"

    id = Column(Integer, Identity(), primary_key=True)
    rate_group_id = Column(Integer, ForeignKey("rate_group.id"), nullable=False)
    range_from: int = Column(Integer, nullable=False)
    range_to: int | None = Column(Numeric, nullable=True)
    value: Decimal = Column(Numeric, nullable=False)

    range_unit = Column(Enum(Unit), nullable=False)
    price_unit = Column(Enum(Unit), nullable=False)
    overage_fee: Decimal = Column(Numeric, nullable=True)
    overage_unit = Column(Enum(Unit), nullable=True)
    isoverage = Column(Boolean, nullable=False)
    overage_per = Column(Integer, nullable=True)

    rate_group: "RateGroup" = relationship("RateGroup", back_populates="rates")

    def to_domain(self) -> model.Rate:
        return model.Rate(
            range_from=self.range_from,
            range_to=self.range_to,
            value=self.value,
            range_unit=self.range_unit,
            price_unit=self.price_unit,
            overage_fee=self.overage_fee,
            overage_unit=self.overage_unit,
            isoverage=self.isoverage,
            overage_per=self.overage_per,
        )

    @classmethod
    def from_domain(cls, rate: model.Rate) -> "Rate":
        return cls(**rate.dict())


class RateModel(Base):
    __tablename__ = "rate_model"

    id = Column(Integer, Identity(), primary_key=True)
    model = Column(String(50), nullable=False)
    rate_model_code = Column(String(), nullable=False)

    @classmethod
    def from_domain(cls, rate_model: rateplan_model.RatePlanModel) -> "RateModel":
        return cls(
            id=getattr(rate_model, "value", None),
            model=rate_model.title,
            rate_model_code=rate_model.rate_model_code,
        )
