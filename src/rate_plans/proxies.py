from typing import Iterable, List

from api.rate_plans.schemas import OriginationGroup
from app.config import logger
from auth.dto import AuthenticatedUser
from auth.exceptions import ForbiddenError, NotFound
from auth.permissions import (
    is_account_member,
    is_distributor_client,
    is_distributor_staff,
)
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from rate_plans.domain.model import (
    AccountRatePlanDetails,
    RateModel,
    RatePlan,
    RatePlanModel,
    RatePlanModels,
    RatePlans,
)
from rate_plans.services import AbstractRatePlanService


class RatePlanServiceAuthProxy(AbstractRatePlanService):
    def __init__(
        self, rate_plan_service: AbstractRatePlanService, user: AuthenticatedUser
    ):
        self.rate_plan_service = rate_plan_service
        self.user = user

    def get_account_rate_plan(self, account_id: int, rate_plan_id: list[int]) -> bool:
        return self.rate_plan_service.get_account_rate_plan(account_id, rate_plan_id)

    def validate_fixed_flexi_plan(
        self, rate_plan_id: list[int], service: list[str]
    ) -> bool:
        return self.rate_plan_service.validate_fixed_flexi_plan(
            rate_plan_id=rate_plan_id, service=service
        )

    def create(self, rate_plan: RatePlan) -> int:
        if is_distributor_staff(self.user):
            return self.rate_plan_service.create(rate_plan)
        else:
            raise ForbiddenError

    def get(self, rate_plan_id: int) -> RatePlan:
        rate_plan = self.rate_plan_service.get(rate_plan_id)
        is_distributor_client_rate_plan = (
            rate_plan.account_id is not None
            and is_account_member(self.user, rate_plan.account_id)
        )
        if not is_distributor_staff(self.user) and not is_distributor_client_rate_plan:
            raise NotFound
        return rate_plan

    def list(self, account_ids: list[int] = None) -> Iterable[RatePlans]:

        if is_distributor_staff(self.user):
            return self.rate_plan_service.list(account_ids)
        elif is_distributor_client(self.user):
            user_account_id = self.user.organization.account.id  # type: ignore
            logger.info(f"Rate Plan - Client Account id passed:- {account_ids}")
            logger.info("Rate Plan - Client Account id:- " f"{user_account_id}")
            if account_ids and len(account_ids) > 1:  # type: ignore
                logger.info(
                    "Rate Plan - Trying to pass more than 1 "
                    f"account ids:- {account_ids}"
                )
                raise ForbiddenError
            elif account_ids and user_account_id != account_ids[0]:
                raise ForbiddenError
            return self.rate_plan_service.list([user_account_id])
        else:
            raise ForbiddenError

    def update(self, rate_plan_id: int, rate_plan: RatePlan) -> None:
        if is_distributor_staff(self.user):
            return self.rate_plan_service.update(rate_plan_id, rate_plan)
        else:
            raise NotFound

    def delete(self, rate_plan_id: int) -> None:
        if is_distributor_staff(self.user):
            return self.rate_plan_service.delete(rate_plan_id)
        else:
            raise NotFound

    def rate_plans_by_accounts(
        self,
        searching: Searching | None = None,
        ordering: Ordering | None = None,
        pagination: Pagination | None = None,
    ) -> tuple[Iterable[AccountRatePlanDetails], int]:
        return self.rate_plan_service.rate_plans_by_accounts(
            searching=searching,
            ordering=ordering,
            pagination=pagination,
        )

    def get_rate_plans_models(self) -> RatePlanModels:
        return self.rate_plan_service.get_rate_plans_models()

    def get_rate_plans_model_by_id(self, id: int) -> RateModel:
        return self.rate_plan_service.get_rate_plans_model_by_id(id=id)

    def update_rate_plans_model_by_id(self, id: int, model: RatePlanModel) -> RateModel:
        return self.rate_plan_service.update_rate_plans_model_by_id(id=id, model=model)

    def create_rate_plan_model(self, model: RatePlanModel) -> RateModel:
        return self.rate_plan_service.create_rate_plan_model(model=model)

    def delete_rate_plan_model(self, rate_plan_model_id: int) -> None:
        return self.rate_plan_service.delete_rate_plan_model(
            rate_plan_model_id=rate_plan_model_id
        )

    def get_allocation_count_by_rate_plan(self, rate_plan_id: int) -> int:
        return self.rate_plan_service.get_allocation_count_by_rate_plan(
            rate_plan_id=rate_plan_id
        )

    def set_default_rate_plan(self, account_id: int, id: int) -> None:
        return self.rate_plan_service.set_default_rate_plan(
            account_id=account_id, id=id
        )

    def validate_fixed_flexi_redis_rate_plan(
        self, rate_plan_group: List[OriginationGroup], rate_plan_id: int
    ) -> bool:
        return self.rate_plan_service.validate_fixed_flexi_redis_rate_plan(
            rate_plan_group, rate_plan_id
        )
