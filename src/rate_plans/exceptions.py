class RatePlanDoesNotExist(Exception):
    ...


class ReadOnlyFieldsModified(Exception):
    ...


class MediaError(Exception):
    ...


class RatePlanModelNotFound(Exception):
    ...


class RatePlanModelDeletionError(Exception):
    ...


class SimLimitCountError(Exception):
    ...


class AccountRatePlanNotFound(Exception):
    ...


class AlreadyDefaultRatePlan(Exception):
    ...


class DefaultRatePlanError(Exception):
    ...


class RatePlanError(Exception):
    ...
