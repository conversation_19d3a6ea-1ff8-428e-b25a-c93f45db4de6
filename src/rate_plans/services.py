import io
import uuid
from abc import ABC, abstractmethod
from typing import Iterable, List

from defusedxml.lxml import fromstring
from fastapi import UploadFile
from PIL import Image, UnidentifiedImageError
from pydantic import AnyHttpUrl
from svgutils.transform import SVGFigure

from api.rate_plans.schemas import OriginationGroup
from app.config import logger
from common.constants import FIXED, FLEXI
from common.file_storage import AbstractFileStorage
from common.ordering import Ordering
from common.pagination import Pagination
from common.searching import Searching
from common.types import ContentType, ImageFormat, Service
from rate_plans.adapters.rate_plan_repository import AbstractRatePlanRepository
from rate_plans.domain.model import (
    AccountRatePlanDetails,
    RateModel,
    RatePlan,
    RatePlanModel,
    RatePlanModels,
    RatePlans,
)
from rate_plans.exceptions import (
    AccountRatePlanNotFound,
    AlreadyDefaultRatePlan,
    DefaultRatePlanError,
    MediaError,
    RatePlanDoesNotExist,
    RatePlanError,
    RatePlanModelNotFound,
    ReadOnlyFieldsModified,
    SimLimitCountError,
)
from sim.adapters.repository import AbstractSimRepository


class MediaService:
    def __init__(self, file_storage: AbstractFileStorage):
        self.file_storage = file_storage

    @staticmethod
    def _parse_svg_image(content: bytes, width: int, height: int) -> bytes:
        fig = SVGFigure()
        fig.root = fromstring(content)
        fig.set_size([str(width), str(height)])
        return fig.to_str()

    @staticmethod
    def _parse_raster_image(content: bytes, width: int, height: int) -> bytes:
        try:
            image = Image.open(io.BytesIO(content))
        except UnidentifiedImageError:
            raise MediaError("Image format not supported.")

        if image.height > height or image.width > width:
            image.thumbnail((width, height), Image.Resampling.LANCZOS)

        image_buf = io.BytesIO()
        image.save(image_buf, format=image.format)
        return image_buf.getvalue()

    def upload_image(
        self,
        file: UploadFile,
        folder_path: str,
        max_width: int = 100,
        max_height: int = 100,
    ) -> str:
        content_type = ContentType.parse(file.content_type)
        if content_type.tail not in ImageFormat.__dict__.values():
            raise MediaError(
                f"Bad content type: '{file.content_type}', expected "
                f"'image/png or image/svg'."
            )
        parser = (
            self._parse_svg_image
            if content_type.mime_type == "image/svg+xml"
            else self._parse_raster_image
        )
        file_obj = parser(file.file.read(), max_width, max_height)
        image_key = f"{folder_path}/{uuid.uuid4().hex}{content_type.extension}"
        self.file_storage.upload_file(
            filename=image_key,
            file_obj=file_obj,
            content_type=content_type.mime_type,
        )
        return image_key

    def get_file_url(self, file_key: str) -> AnyHttpUrl:
        url = self.file_storage.generate_file_url(file_key)
        return AnyHttpUrl(url, scheme="https")

    def check_if_file_exits(self, file_key: str) -> None:
        if not self.file_storage.file_exists(file_key):
            raise MediaError(f"File not found: {file_key}.")


class FakeMediaService:
    def __init__(self):
        self.files: dict[str, dict[str, bytes]] = {}

    @staticmethod
    def _parse_svg_image(content: bytes, width: int, height: int) -> bytes:
        fig = SVGFigure()
        fig.root = fromstring(content)
        fig.set_size([str(width), str(height)])
        return fig.to_str()

    @staticmethod
    def _parse_raster_image(content: bytes, width: int, height: int) -> bytes:
        try:
            image = Image.open(io.BytesIO(content))
        except UnidentifiedImageError:
            raise MediaError("Image format not supported.")

        if image.height > height or image.width > width:
            image.thumbnail((width, height), Image.Resampling.LANCZOS)

        image_buf = io.BytesIO()
        image.save(image_buf, format=image.format)
        return image_buf.getvalue()

    def upload_image(
        self,
        file: UploadFile,
        folder_path: str,
        max_width: int = 100,
        max_height: int = 100,
    ) -> str:
        content_type = ContentType.parse(file.content_type)
        if content_type.tail not in ImageFormat.__dict__.values():
            raise MediaError(
                f"Bad content type: '{file.content_type}', expected "
                f"'image/png or image/svg'."
            )
        parser = (
            self._parse_svg_image
            if content_type.mime_type == "image/svg+xml"
            else self._parse_raster_image
        )
        file_obj = parser(file.file.read(), max_width, max_height)
        image_key = f"{folder_path}/{uuid.uuid4().hex}{content_type.extension}"

        self.files[image_key] = {
            "content": file_obj,
            "content_type": content_type.mime_type.encode(),
        }

        return image_key

    def get_file_url(self, file_key: str) -> AnyHttpUrl:
        return AnyHttpUrl(f"https://fake-storage/{file_key}", scheme="https")

    def check_if_file_exits(self, file_key: str) -> None:
        if file_key not in self.files:
            raise MediaError(f"File not found: {file_key}.")


class AbstractRatePlanService(ABC):
    @abstractmethod
    def get_account_rate_plan(self, account_id: int, rate_plan_id: list[int]) -> bool:
        ...

    @abstractmethod
    def validate_fixed_flexi_plan(
        self, rate_plan_id: list[int], service: list[str]
    ) -> bool:
        ...

    @abstractmethod
    def create(self, rate_plan: RatePlan) -> int:
        ...

    @abstractmethod
    def get(self, rate_plan_id: int) -> RatePlan:
        ...

    @abstractmethod
    def list(self, account_ids: list[int] = None) -> Iterable[RatePlans]:
        ...

    @abstractmethod
    def update(self, rate_plan_id: int, rate_plan: RatePlan) -> None:
        ...

    @abstractmethod
    def delete(self, rate_plan_id: int) -> None:
        ...

    @abstractmethod
    def rate_plans_by_accounts(
        self,
        searching: Searching | None = None,
        ordering: Ordering | None = None,
        pagination: Pagination | None = None,
    ) -> tuple[Iterable[AccountRatePlanDetails], int]:
        ...

    @abstractmethod
    def get_rate_plans_models(self) -> RatePlanModels:
        ...

    @abstractmethod
    def get_rate_plans_model_by_id(self, id: int) -> RateModel:
        ...

    @abstractmethod
    def update_rate_plans_model_by_id(self, id: int, model: RatePlanModel) -> RateModel:
        ...

    @abstractmethod
    def create_rate_plan_model(self, model: RatePlanModel) -> RateModel:
        ...

    @abstractmethod
    def delete_rate_plan_model(self, rate_plan_model_id: int) -> None:
        ...

    @abstractmethod
    def get_allocation_count_by_rate_plan(self, rate_plan_id: int) -> int:
        ...

    @abstractmethod
    def set_default_rate_plan(self, account_id: int, id: int) -> None:
        ...

    @abstractmethod
    def validate_fixed_flexi_redis_rate_plan(
        self, rate_plan_group: List[OriginationGroup], rate_plan_id: int
    ) -> bool:
        ...


class RatePlanService(AbstractRatePlanService):
    def __init__(
        self,
        rate_plan_repository: AbstractRatePlanRepository,
        media_service: MediaService,
        sim_repository: AbstractSimRepository,
    ) -> None:
        self.rate_plan_repository = rate_plan_repository
        self.media = media_service
        self.sim_repository = sim_repository

    def get_account_rate_plan(self, account_id: int, rate_plan_id: list[int]) -> bool:
        response = self.rate_plan_repository.get_account_rate_plan(
            account_id, rate_plan_id
        )
        if response is False:
            raise RatePlanDoesNotExist()
        return response

    def validate_fixed_flexi_plan(
        self, rate_plan_id: list[int], service: list[str]
    ) -> bool:
        response = self.rate_plan_repository.validate_fixed_flexi_plan(
            rate_plan_id=rate_plan_id, service=service
        )
        return response

    def _get_logo_url(self, logo_key: str | None) -> AnyHttpUrl | None:
        if logo_key is None:
            return logo_key
        return self.media.get_file_url(logo_key)

    def _check_sim_limit(self, sim_count: int, sim_limit: int | None = None):
        if sim_limit and (sim_limit < sim_count):
            raise SimLimitCountError(
                "Unable to change SIM Limit. There are currently"
                f" {sim_count} SIMs assigned"
                " to this rate plan"
            )

    def create(self, rate_plan: RatePlan) -> int:

        # Checking default rate plan
        logger.info(f"Creating rate plan account id:- {rate_plan.account_id}")
        if rate_plan.account_id:
            account_rate_plans_count = (
                self.rate_plan_repository.rate_plans_count_by_account_ids(
                    account_ids=[rate_plan.account_id]
                )
            )
            if account_rate_plans_count == 0 and not rate_plan.is_default:
                logger.warning(
                    f"Account with id {rate_plan.account_id} rate "
                    f"plan count {account_rate_plans_count}."
                )
                logger.warning(f"New rate plan default status: {rate_plan.is_default}")
                logger.warning(
                    "There should be at most one default rate plan" " in an account."
                )
                raise DefaultRatePlanError(
                    "There should be at most one default rate plan" " in an account."
                )
            if account_rate_plans_count != 0 and rate_plan.is_default:
                logger.info(
                    "Unsetting existing default rate plan for account "
                    f"id {rate_plan.account_id}"
                )
                self.rate_plan_repository.unset_default_rate_plan(
                    account_id=rate_plan.account_id
                )

        return self.rate_plan_repository.add(rate_plan)

    def get(self, rate_plan_id: int) -> RatePlan:
        rate_plan = self.rate_plan_repository.get(rate_plan_id)
        if rate_plan is None:
            raise RatePlanDoesNotExist()
        return rate_plan

    def get_allocation_count_by_rate_plan(self, rate_plan_id: int) -> int:
        rate_plan_sim_count = self.sim_repository.get_allocation_count_by_rate_plan(
            id=rate_plan_id
        )
        return rate_plan_sim_count

    def list(self, account_ids: list[int] = None) -> Iterable[RatePlans]:
        response = self.rate_plan_repository.query(account_ids)
        return response

    def update(self, rate_plan_id: int, updated_rate_plan: RatePlan) -> None:
        existing_plan = self.rate_plan_repository.get(rate_plan_id)
        rate_plan_sim_count = self.sim_repository.get_allocation_count_by_rate_plan(
            id=rate_plan_id
        )
        if not existing_plan:
            raise RatePlanDoesNotExist()
        if not (
            existing_plan.id == updated_rate_plan.id
            and existing_plan.account_id == updated_rate_plan.account_id
        ):
            raise ReadOnlyFieldsModified()

        # Checking default rate plan
        logger.info(f"updating rate plan account id:- {updated_rate_plan.account_id}")
        if updated_rate_plan.account_id:
            account_rate_plans_count = (
                self.rate_plan_repository.rate_plans_count_by_account_ids(
                    account_ids=[updated_rate_plan.account_id]
                )
            )

            if (account_rate_plans_count == 1 and not updated_rate_plan.is_default) or (
                existing_plan.is_default and not updated_rate_plan.is_default
            ):
                logger.warning(
                    f"Account with id {updated_rate_plan.account_id} rate "
                    f"plan count {account_rate_plans_count}."
                )
                logger.warning(
                    f"Update rate plan default status: {updated_rate_plan.is_default}"
                )
                logger.warning(
                    "There should be at most one default rate plan" " in an account."
                )
                raise DefaultRatePlanError(
                    "There should be at most one default rate plan" " in an account."
                )
            if account_rate_plans_count != 0 and updated_rate_plan.is_default:
                logger.info(
                    "Unsetting existing default rate plan for account "
                    f"id {updated_rate_plan.account_id}"
                )
                self.rate_plan_repository.unset_default_rate_plan(
                    account_id=updated_rate_plan.account_id
                )

        self._check_sim_limit(
            sim_count=rate_plan_sim_count, sim_limit=updated_rate_plan.sim_limit
        )
        self.rate_plan_repository.update(updated_rate_plan)

    def delete(self, rate_plan_id: int) -> None:
        rate_plan = self.rate_plan_repository.get(rate_plan_id)
        if rate_plan is None:
            raise RatePlanDoesNotExist(rate_plan_id)

        # Checking default rate plan
        if rate_plan.is_default:
            logger.warning(
                f"Requested rate plan with account id {rate_plan.account_id}"
                " is default"
            )
            logger.warning(
                "There should be at most one default rate plan" " in an account."
            )
            raise DefaultRatePlanError(
                "Default Rate Plan can't be deleted. If you "
                "want to delete choosen Rate Plan, please "
                "set another Rate Plan as a default "
                "for the Account"
            )

        self.rate_plan_repository.delete(rate_plan)

    def rate_plans_by_accounts(
        self,
        searching: Searching | None = None,
        ordering: Ordering | None = None,
        pagination: Pagination | None = None,
    ) -> tuple[Iterable[AccountRatePlanDetails], int]:
        result, total_count = self.rate_plan_repository.rate_plans_by_accounts(
            searching=searching,
            ordering=ordering,
            pagination=pagination,
        )

        for rate_plan in result:
            rate_plan.logo_url = self._get_logo_url(rate_plan.logo_key)
        return result, total_count

    def get_rate_plans_models(self) -> RatePlanModels:
        response = self.rate_plan_repository.get_rate_plans_models()
        if not response:
            raise RatePlanModelNotFound("No Rateplan models found.")
        return response

    def get_rate_plans_model_by_id(self, id: int) -> RateModel:
        response = self.rate_plan_repository.get_rate_plans_model_by_id(id=id)
        return response

    def update_rate_plans_model_by_id(self, id: int, model: RatePlanModel) -> RateModel:
        self.get_rate_plans_model_by_id(id=id)
        response = self.rate_plan_repository.update_rate_plans_model_by_id(
            id=id, model=model
        )
        return response

    def create_rate_plan_model(self, model: RatePlanModel) -> RateModel:
        response = self.rate_plan_repository.create_rate_plan_model(model=model)
        return response

    def delete_rate_plan_model(self, rate_plan_model_id: int) -> None:
        rate_plan_model = self.rate_plan_repository.get_rate_plans_model_by_id(
            rate_plan_model_id
        )
        if rate_plan_model is None:
            raise RatePlanDoesNotExist(rate_plan_model_id)
        self.rate_plan_repository.delete_rate_plan_model(rate_plan_model)

    def set_default_rate_plan(self, account_id: int, id: int) -> None:
        rate_plan_details = self.get(rate_plan_id=id)
        if rate_plan_details.account_id != account_id:
            raise AccountRatePlanNotFound()
        if rate_plan_details.is_default:
            raise AlreadyDefaultRatePlan(
                f"The requested rate plan with ID {id} is "
                "already set as the default rate plan."
            )

        self.rate_plan_repository.unset_default_rate_plan(account_id=account_id)
        self.rate_plan_repository.set_default_rate_plan(id=id)
        return None

    def validate_fixed_flexi_redis_rate_plan(
        self, rate_plan_group: List[OriginationGroup], rate_plan_id: int
    ) -> bool:
        for rate_plan in rate_plan_group:
            if any(
                getattr(rate_plan, service.name.lower(), None)
                and getattr(rate_plan, service.name.lower()).rate_model in (3, 4)
                for service in [
                    Service.DATA,
                    Service.VOICE_MO,
                    Service.VOICE_MT,
                    Service.SMS_MO,
                ]
            ):
                return True
        logger.error(
            "Some or all requested rate plans are not "
            f"{FIXED} or {FLEXI}, rate_plan_ids:- {rate_plan_id}"
        )
        raise RatePlanError(f"Requested rate plans are not {FIXED} or {FLEXI}")


class MappedAccount:
    def __init__(self, id: int, name: str, logo_url: AnyHttpUrl | None):
        self.id = id
        self.name = name
        self.logo_url = logo_url
