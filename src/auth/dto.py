from enum import Enum
from typing import Annotated, Literal, cast, get_args
from uuid import UUID

from more_itertools import first
from pydantic import BaseModel, EmailStr, Field, validator


class BaseUser(BaseModel):
    active: bool


class Anonymous(BaseUser):
    active: Literal[False] = False


class OrganizationType(str, Enum):
    CLIENT = "CLIENT"
    DISTRIBUTOR = "DISTRIBUTOR"


class BaseUserOrganization(BaseModel):
    id: int
    type: OrganizationType


class Distributor(BaseUserOrganization):
    type: Literal[OrganizationType.DISTRIBUTOR] = OrganizationType.DISTRIBUTOR


class Account(BaseModel):
    id: int


class Client(BaseUserOrganization):
    type: Literal[OrganizationType.CLIENT] = OrganizationType.CLIENT
    account: Account | None


UserOrganization = Annotated[Distributor | Client, Field(discriminator="type")]


UserRole = Literal["Admin", "User"]


class AuthenticatedUser(BaseUser):
    id: UUID = Field(alias="sub")
    organization: UserOrganization
    role: UserRole = Field(alias="realm_access")
    active: Literal[True] = True
    email: EmailStr

    class Config:
        allow_population_by_field_name = True

    @validator("role", pre=True)
    def get_role_from_payload(cls, v: dict[str, list] | UserRole) -> UserRole:
        if v in get_args(UserRole):
            return cast(UserRole, v)

        if "roles" not in v:
            raise ValueError("Payload must contain roles")

        auth_user_roles = map(cls._org_role_to_user_role, v["roles"])  # type: ignore
        filtered_roles = [r for r in auth_user_roles if r is not None]
        if not filtered_roles:
            raise ValueError("Not found correct role")

        # ["User", "Admin"] -> "Admin"
        # ["User"] -> "User"
        roles_by_priority = sorted(
            filtered_roles, key=lambda r: r == "Admin", reverse=True
        )
        role = first(roles_by_priority)
        return cast(UserRole, role)

    @classmethod
    def _org_role_to_user_role(cls, org_role: str) -> str | None:
        client_org_roles = {
            "DistributorAdmin": "Admin",
            "DistributorUser": "User",
            "ClientAdmin": "Admin",
            "ClientUser": "User",
        }
        return client_org_roles.get(org_role)


User = Annotated[Anonymous | AuthenticatedUser, Field(discriminator="active")]


class Service(BaseUser):
    active: Literal[True] = True
    client_id: str


Actor = User | Service
AuthenticatedActor = AuthenticatedUser | Service
