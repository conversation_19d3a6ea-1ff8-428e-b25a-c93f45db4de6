from typing import cast

from auth.dto import (
    Authenticated<PERSON>ctor,
    AuthenticatedUser,
    Client,
    Distributor,
    Service,
    User,
)


def is_authenticated(user: User) -> bool:
    return isinstance(user, AuthenticatedUser)


def is_authorized_service(actor: AuthenticatedActor) -> bool:
    return isinstance(actor, Service)


def is_distributor_staff(actor: AuthenticatedActor) -> bool:
    if not is_authorized_service(actor):
        user = cast(AuthenticatedUser, actor)
        return is_authenticated(user) and isinstance(user.organization, Distributor)
    return False


def is_distributor_client(user: AuthenticatedUser) -> bool:
    is_client = is_authenticated(user) and isinstance(user.organization, Client)
    if is_client:
        if user.organization.account is None:  # type: ignore
            raise AssertionError("The distributor client must have an account.")
    return is_client


def is_distributor_admin(actor: AuthenticatedActor) -> bool:
    return is_distributor_staff(actor) and actor.role == "Admin"  # type: ignore


def is_account_member(user: AuthenticatedUser, account_id: int) -> bool:
    if (
        isinstance(user.organization, Client)
        and user.organization.account is not None
        and user.organization.account.id == account_id
    ):
        return True
    return False


def is_account_admin(user: AuthenticatedUser, account_id: int) -> bool:
    return is_account_member(user, account_id) and user.role == "Admin"


def is_org_member(user: AuthenticatedUser, account_id: int) -> bool:
    if (
        isinstance(user.organization, Client)
        and user.organization.account is not None
        and user.organization.id == account_id
    ):
        return True
    return False
