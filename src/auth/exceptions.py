from app.exceptions import Conflict


class AuthException(Exception):
    ...


class Unauthorized(AuthException):
    ...


class ForbiddenError(AuthException):
    ...


class NotFound(AuthException):
    ...


class ConflictException(Conflict):
    # client_message = "This email has already been taken."
    client_message = "The user with the email address {} is already a member of the {}."

    def __init__(self, email_id: str) -> None:
        self.client_message = self.client_message.format(email_id, "Nextgen Clearing")


class ConflictBTSAFException(Conflict):
    client_message = "The user with the email address {} is already Register At BT Saf"

    def __init__(self, email_id: str) -> None:
        self.client_message = self.client_message.format(email_id)
